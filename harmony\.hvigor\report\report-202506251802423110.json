{"version": "2.0", "ppid": 21780, "events": [{"head": {"id": "b11c6c04-7cdf-4a9f-bc99-0784933f5ac1", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991793502300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a896ccc-82b4-4f9f-855b-ec97fbf609c0", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32122291082600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8af7bfb6-d2aa-43a7-a487-bf9812afd0f9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32122294149900, "endTime": 32122294218000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d5d26c75-de79-4907-b382-103d86f06bfc", "logId": "563b4c66-0236-40da-a9a6-9b8df10ecfe4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "563b4c66-0236-40da-a9a6-9b8df10ecfe4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32122294149900, "endTime": 32122294218000}, "additional": {"logType": "info", "children": [], "durationId": "8af7bfb6-d2aa-43a7-a487-bf9812afd0f9"}}, {"head": {"id": "c8e6c72b-5f03-4d7b-b427-6791912489f6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32123351364000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a45f12f3-50d8-4e91-8a09-f5edeed6253d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32123352703700, "endTime": 32123352727300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d5d26c75-de79-4907-b382-103d86f06bfc", "logId": "2c66cf6b-8027-4946-925d-8fe2e5d6b452"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c66cf6b-8027-4946-925d-8fe2e5d6b452", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32123352703700, "endTime": 32123352727300}, "additional": {"logType": "info", "children": [], "durationId": "a45f12f3-50d8-4e91-8a09-f5edeed6253d"}}, {"head": {"id": "739f9eb0-6a5a-494a-8de1-28af5240f0f6", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32123754350000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0741f123-f185-4576-bcfd-2a165f5c5b87", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32123755812500, "endTime": 32123755854600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d5d26c75-de79-4907-b382-103d86f06bfc", "logId": "1a3aa806-4a4e-4918-a8a7-3b8173ecebfb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a3aa806-4a4e-4918-a8a7-3b8173ecebfb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32123755812500, "endTime": 32123755854600}, "additional": {"logType": "info", "children": [], "durationId": "0741f123-f185-4576-bcfd-2a165f5c5b87"}}, {"head": {"id": "1ddc49a5-9985-4daa-ba8c-790740538db2", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32161877211800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d16e933-1cdc-415d-a300-1d90f8c110d6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32161881970100, "endTime": 32161882043500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d5d26c75-de79-4907-b382-103d86f06bfc", "logId": "cdceaa73-876d-437d-9a99-852807f1ee5a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cdceaa73-876d-437d-9a99-852807f1ee5a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32161881970100, "endTime": 32161882043500}, "additional": {"logType": "info", "children": [], "durationId": "6d16e933-1cdc-415d-a300-1d90f8c110d6"}}, {"head": {"id": "eee64a25-f63c-4115-932a-3e100c64a604", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32163746766300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2af5d7b5-ca7c-43a5-917a-daaf4d3295cd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32163748597800, "endTime": 32163748624900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d5d26c75-de79-4907-b382-103d86f06bfc", "logId": "a7b10aea-6f64-4b91-9acb-4a72a57b9212"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7b10aea-6f64-4b91-9acb-4a72a57b9212", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32163748597800, "endTime": 32163748624900}, "additional": {"logType": "info", "children": [], "durationId": "2af5d7b5-ca7c-43a5-917a-daaf4d3295cd"}}, {"head": {"id": "1ba69450-7b5e-449d-9d6f-99b04b7f18d4", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32204359469500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d689f17a-26a8-4b63-b8ac-892e17b9169c", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32204359831400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bbf2f5b-cd2e-4bcf-8736-a30a7f8191ea", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205057050700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffed86e6-cfbc-44a8-b8fc-7e5d354efd5d", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205064636300, "endTime": 32205217470800}, "additional": {"children": ["b1b4bd82-9bc6-4c31-ad76-3277a3ef2787", "9c1c3de0-7706-4096-a4f0-2b8135745303", "386b143a-13fe-4eb5-a09b-10812b28f9a0", "c6c3ba96-460d-4c05-b7d9-a1b02068735a", "73ed52f4-cea6-407d-9cf3-fa7663f0add5", "7930023c-74a8-4211-8fae-4e5b82094c3a", "7851ca4a-3e3c-40cf-92c6-c701d94839ea"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "3981d711-0a4d-423a-8254-beff3f21bd7e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b1b4bd82-9bc6-4c31-ad76-3277a3ef2787", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205064638000, "endTime": 32205077334600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ffed86e6-cfbc-44a8-b8fc-7e5d354efd5d", "logId": "128b4bfe-5c28-4d96-9186-b3fe81bd2322"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c1c3de0-7706-4096-a4f0-2b8135745303", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205077363200, "endTime": 32205216433500}, "additional": {"children": ["22478e1e-5977-4fa5-ba0c-fabba63447cd", "2c75a5b5-0b7f-4e52-9fea-f08c3f5b736d", "1bed0d11-c88f-4ad7-82f2-81bdcb68cf32", "a73788ae-33e9-4c66-be5b-a751477c6220", "6247df05-05ec-4d51-890f-3a7d427faac8", "54204041-d2ae-4620-a59e-a98f9225cc90", "7e45c4c9-abce-4adf-b9a7-62df2a896cec", "96e4c1e3-feeb-4dff-9846-daf6184fc25d", "e1cdff0b-2bdb-4299-900c-47557acac29c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ffed86e6-cfbc-44a8-b8fc-7e5d354efd5d", "logId": "d95f8558-f43c-4e6c-9f10-931458476a2d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "386b143a-13fe-4eb5-a09b-10812b28f9a0", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205216454200, "endTime": 32205217463400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ffed86e6-cfbc-44a8-b8fc-7e5d354efd5d", "logId": "7b3abaaa-c8a2-4eff-8b39-5ef80b1da895"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c6c3ba96-460d-4c05-b7d9-a1b02068735a", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205217466800, "endTime": 32205217468600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ffed86e6-cfbc-44a8-b8fc-7e5d354efd5d", "logId": "1835efce-6151-44d4-8705-e5d6aba36e49"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73ed52f4-cea6-407d-9cf3-fa7663f0add5", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205068644700, "endTime": 32205068679000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ffed86e6-cfbc-44a8-b8fc-7e5d354efd5d", "logId": "3cb5d4eb-ac59-4750-957a-4f486d9292f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3cb5d4eb-ac59-4750-957a-4f486d9292f8", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205068644700, "endTime": 32205068679000}, "additional": {"logType": "info", "children": [], "durationId": "73ed52f4-cea6-407d-9cf3-fa7663f0add5", "parent": "3981d711-0a4d-423a-8254-beff3f21bd7e"}}, {"head": {"id": "7930023c-74a8-4211-8fae-4e5b82094c3a", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205073074100, "endTime": 32205073093000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ffed86e6-cfbc-44a8-b8fc-7e5d354efd5d", "logId": "8782a0fe-81ca-41ce-afaa-7f97e7f7ade6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8782a0fe-81ca-41ce-afaa-7f97e7f7ade6", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205073074100, "endTime": 32205073093000}, "additional": {"logType": "info", "children": [], "durationId": "7930023c-74a8-4211-8fae-4e5b82094c3a", "parent": "3981d711-0a4d-423a-8254-beff3f21bd7e"}}, {"head": {"id": "76be52e8-7cdf-4b07-9b41-d3d740482678", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205073143300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c342cb4c-63d7-4faa-b5a1-35c7b8f4ed8f", "name": "Cache service initialization finished in 3 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205076259000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "128b4bfe-5c28-4d96-9186-b3fe81bd2322", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205064638000, "endTime": 32205077334600}, "additional": {"logType": "info", "children": [], "durationId": "b1b4bd82-9bc6-4c31-ad76-3277a3ef2787", "parent": "3981d711-0a4d-423a-8254-beff3f21bd7e"}}, {"head": {"id": "22478e1e-5977-4fa5-ba0c-fabba63447cd", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205082649100, "endTime": 32205082658900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c1c3de0-7706-4096-a4f0-2b8135745303", "logId": "cf2b8ea3-8cc5-4436-ba17-4771424853f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c75a5b5-0b7f-4e52-9fea-f08c3f5b736d", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205082674900, "endTime": 32205086213200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c1c3de0-7706-4096-a4f0-2b8135745303", "logId": "2ff9f480-789f-49a4-b30c-ade77a06adb7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1bed0d11-c88f-4ad7-82f2-81bdcb68cf32", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205086228300, "endTime": 32205163056000}, "additional": {"children": ["a947609a-70cd-4eeb-91c3-e6b379f652a8", "2ea8befb-7756-4018-8a20-63baae3b0c1f", "a055e05c-61bf-4905-a4ae-df2cba5d55e7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c1c3de0-7706-4096-a4f0-2b8135745303", "logId": "a57529ef-c3cf-40ed-8673-9c3530224a80"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a73788ae-33e9-4c66-be5b-a751477c6220", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205163092900, "endTime": 32205185158500}, "additional": {"children": ["6cdcbeda-0ab6-4330-a3a9-574a2461481a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c1c3de0-7706-4096-a4f0-2b8135745303", "logId": "55087ed0-e417-4a14-b8f7-1ee77a24f91f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6247df05-05ec-4d51-890f-3a7d427faac8", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205185166400, "endTime": 32205198132900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c1c3de0-7706-4096-a4f0-2b8135745303", "logId": "bf2a3355-d45e-4abd-8b6e-1f9d6398002c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54204041-d2ae-4620-a59e-a98f9225cc90", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205199231900, "endTime": 32205206849900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c1c3de0-7706-4096-a4f0-2b8135745303", "logId": "1281f20e-9b9b-4822-893f-238f9a8e5d02"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e45c4c9-abce-4adf-b9a7-62df2a896cec", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205206875000, "endTime": 32205216275000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c1c3de0-7706-4096-a4f0-2b8135745303", "logId": "71877774-df48-464c-9414-f71529b46301"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96e4c1e3-feeb-4dff-9846-daf6184fc25d", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205216295700, "endTime": 32205216419300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c1c3de0-7706-4096-a4f0-2b8135745303", "logId": "e1dcc4c8-4fff-4bee-98e1-487553ffb584"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf2b8ea3-8cc5-4436-ba17-4771424853f5", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205082649100, "endTime": 32205082658900}, "additional": {"logType": "info", "children": [], "durationId": "22478e1e-5977-4fa5-ba0c-fabba63447cd", "parent": "d95f8558-f43c-4e6c-9f10-931458476a2d"}}, {"head": {"id": "2ff9f480-789f-49a4-b30c-ade77a06adb7", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205082674900, "endTime": 32205086213200}, "additional": {"logType": "info", "children": [], "durationId": "2c75a5b5-0b7f-4e52-9fea-f08c3f5b736d", "parent": "d95f8558-f43c-4e6c-9f10-931458476a2d"}}, {"head": {"id": "a947609a-70cd-4eeb-91c3-e6b379f652a8", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205087496100, "endTime": 32205087520100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1bed0d11-c88f-4ad7-82f2-81bdcb68cf32", "logId": "8d44d3f4-8252-4414-b0b1-fb35662f326a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d44d3f4-8252-4414-b0b1-fb35662f326a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205087496100, "endTime": 32205087520100}, "additional": {"logType": "info", "children": [], "durationId": "a947609a-70cd-4eeb-91c3-e6b379f652a8", "parent": "a57529ef-c3cf-40ed-8673-9c3530224a80"}}, {"head": {"id": "2ea8befb-7756-4018-8a20-63baae3b0c1f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205089645600, "endTime": 32205161923600}, "additional": {"children": ["2e9b7161-71e7-4e8b-b451-f8a3aca7d26f", "f23939dd-7527-4795-92ab-b0ea3d4298d6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1bed0d11-c88f-4ad7-82f2-81bdcb68cf32", "logId": "f9dbccfc-7dee-483c-a7cf-920a3ef6402a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e9b7161-71e7-4e8b-b451-f8a3aca7d26f", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205089646700, "endTime": 32205093084300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2ea8befb-7756-4018-8a20-63baae3b0c1f", "logId": "4f56facd-a4cc-4964-b277-de21875ddea8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f23939dd-7527-4795-92ab-b0ea3d4298d6", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205093104700, "endTime": 32205161901400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2ea8befb-7756-4018-8a20-63baae3b0c1f", "logId": "1d74de2b-11d9-461c-9845-babc5aa62b73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ae0f2bd-6ef7-46c0-a7c9-9644dc1b6cfa", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205089651200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ed737ec-ece2-42e4-8c53-34d4c49faec7", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205092966900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f56facd-a4cc-4964-b277-de21875ddea8", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205089646700, "endTime": 32205093084300}, "additional": {"logType": "info", "children": [], "durationId": "2e9b7161-71e7-4e8b-b451-f8a3aca7d26f", "parent": "f9dbccfc-7dee-483c-a7cf-920a3ef6402a"}}, {"head": {"id": "8b78d99f-fc5c-4795-8bfd-3d307616c4c3", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205093111400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed78c22e-8ba3-4215-a368-45b363e55300", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205102277500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c33835f-f7bb-4ac0-ada8-9c63c054a1dc", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205102402600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ec9671f-cfaf-47a8-81c9-f527f725512a", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205102506900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f352031-923a-4f66-a8f5-71543dc157bc", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205102563300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f7fb350-be0d-4e35-8b50-e1582f2b1196", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205104093900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1985f7a-731b-47b3-ae81-efae9035ae00", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205107576800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49af8cb0-9eff-43ba-bbec-629c2faf582d", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205116593800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f19b601f-1fb5-41ad-8544-9cb3776381b3", "name": "Sdk init in 30 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205137701100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d661658-61aa-4021-a6e9-f49e274e7620", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205138121300}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 18, "minute": 2}, "markType": "other"}}, {"head": {"id": "ee314616-024c-4eaf-bc82-a34656b6bf46", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205138167700}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 18, "minute": 2}, "markType": "other"}}, {"head": {"id": "12739e29-4488-425e-af8e-45324c210bb4", "name": "Project task initialization takes 23 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205161571900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "218c8e95-861c-4211-887d-ff11a9245ada", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205161763700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "058de138-9c37-4cc3-9b09-4ef1a8fcaeca", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205161827800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80f5368b-69c7-484e-846a-c613e0bafc2c", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205161865700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d74de2b-11d9-461c-9845-babc5aa62b73", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205093104700, "endTime": 32205161901400}, "additional": {"logType": "info", "children": [], "durationId": "f23939dd-7527-4795-92ab-b0ea3d4298d6", "parent": "f9dbccfc-7dee-483c-a7cf-920a3ef6402a"}}, {"head": {"id": "f9dbccfc-7dee-483c-a7cf-920a3ef6402a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205089645600, "endTime": 32205161923600}, "additional": {"logType": "info", "children": ["4f56facd-a4cc-4964-b277-de21875ddea8", "1d74de2b-11d9-461c-9845-babc5aa62b73"], "durationId": "2ea8befb-7756-4018-8a20-63baae3b0c1f", "parent": "a57529ef-c3cf-40ed-8673-9c3530224a80"}}, {"head": {"id": "a055e05c-61bf-4905-a4ae-df2cba5d55e7", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205163005900, "endTime": 32205163030800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1bed0d11-c88f-4ad7-82f2-81bdcb68cf32", "logId": "30803e19-ba1d-42f2-a675-247d8f38bf2e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30803e19-ba1d-42f2-a675-247d8f38bf2e", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205163005900, "endTime": 32205163030800}, "additional": {"logType": "info", "children": [], "durationId": "a055e05c-61bf-4905-a4ae-df2cba5d55e7", "parent": "a57529ef-c3cf-40ed-8673-9c3530224a80"}}, {"head": {"id": "a57529ef-c3cf-40ed-8673-9c3530224a80", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205086228300, "endTime": 32205163056000}, "additional": {"logType": "info", "children": ["8d44d3f4-8252-4414-b0b1-fb35662f326a", "f9dbccfc-7dee-483c-a7cf-920a3ef6402a", "30803e19-ba1d-42f2-a675-247d8f38bf2e"], "durationId": "1bed0d11-c88f-4ad7-82f2-81bdcb68cf32", "parent": "d95f8558-f43c-4e6c-9f10-931458476a2d"}}, {"head": {"id": "6cdcbeda-0ab6-4330-a3a9-574a2461481a", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205164390400, "endTime": 32205185146900}, "additional": {"children": ["8af5e0a8-5b7b-487c-91d4-7ac7bb2138d4", "2b752ab8-b30e-4df5-9610-8a128336050a", "d83d6dc5-a248-4ef1-a9ea-ec399b203898"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a73788ae-33e9-4c66-be5b-a751477c6220", "logId": "64b7f6dd-de45-4945-bb91-832847093888"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8af5e0a8-5b7b-487c-91d4-7ac7bb2138d4", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205169724200, "endTime": 32205169741300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6cdcbeda-0ab6-4330-a3a9-574a2461481a", "logId": "e3080383-159e-4703-b8bc-bb672c858622"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3080383-159e-4703-b8bc-bb672c858622", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205169724200, "endTime": 32205169741300}, "additional": {"logType": "info", "children": [], "durationId": "8af5e0a8-5b7b-487c-91d4-7ac7bb2138d4", "parent": "64b7f6dd-de45-4945-bb91-832847093888"}}, {"head": {"id": "2b752ab8-b30e-4df5-9610-8a128336050a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205171405400, "endTime": 32205183683700}, "additional": {"children": ["3c9711d6-1098-4810-ac02-eb001ad825b9", "12283e33-5d4b-4275-a9de-f29c2b3971f8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6cdcbeda-0ab6-4330-a3a9-574a2461481a", "logId": "a16c09c7-bfec-48fa-baa7-fadb04e4faed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c9711d6-1098-4810-ac02-eb001ad825b9", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205171406500, "endTime": 32205174108900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b752ab8-b30e-4df5-9610-8a128336050a", "logId": "2c68b978-c514-4b3c-87a2-d77992f106b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12283e33-5d4b-4275-a9de-f29c2b3971f8", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205174126000, "endTime": 32205183668300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b752ab8-b30e-4df5-9610-8a128336050a", "logId": "25ba51d0-6df7-4d18-b7f0-b1b7fe27cb01"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3aa2549-6dfa-4362-a92e-7e9bf7789ff1", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205171410100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3c08073-b44a-46be-b856-07b35853c43a", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205174009600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c68b978-c514-4b3c-87a2-d77992f106b1", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205171406500, "endTime": 32205174108900}, "additional": {"logType": "info", "children": [], "durationId": "3c9711d6-1098-4810-ac02-eb001ad825b9", "parent": "a16c09c7-bfec-48fa-baa7-fadb04e4faed"}}, {"head": {"id": "61de2ba8-61b4-4114-bd01-970f182b8a7e", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205174131000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52738765-f1a8-414d-a5e6-1dbff8522a2e", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205180328000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26e8a14d-314b-4030-84d5-5d1134eed0a7", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205180429400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb9ff58f-0933-40d1-9cec-bd8d3bba4ee5", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205180599500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2b64b9c-d7f0-404f-b4f9-c6a4c99a5d79", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205180775600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "422323cd-4c99-4253-8d87-e851e2b0b41b", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205180817900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "650f93a8-7d6d-40cb-96c4-251512738e0a", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205180847500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0d18fe5-b3f8-4f3a-b7bf-f7fbd781c66b", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205180877600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ff0ad32-7d03-47d9-bfd6-28d3eb49eb71", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205183455600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da59e4df-76a1-49c3-bc04-945adfc4c273", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205183576800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af70a2fd-8711-4d57-a424-52755ff8098f", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205183610600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d95d321-f475-4055-ac0e-3de0165747ad", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205183634000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25ba51d0-6df7-4d18-b7f0-b1b7fe27cb01", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205174126000, "endTime": 32205183668300}, "additional": {"logType": "info", "children": [], "durationId": "12283e33-5d4b-4275-a9de-f29c2b3971f8", "parent": "a16c09c7-bfec-48fa-baa7-fadb04e4faed"}}, {"head": {"id": "a16c09c7-bfec-48fa-baa7-fadb04e4faed", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205171405400, "endTime": 32205183683700}, "additional": {"logType": "info", "children": ["2c68b978-c514-4b3c-87a2-d77992f106b1", "25ba51d0-6df7-4d18-b7f0-b1b7fe27cb01"], "durationId": "2b752ab8-b30e-4df5-9610-8a128336050a", "parent": "64b7f6dd-de45-4945-bb91-832847093888"}}, {"head": {"id": "d83d6dc5-a248-4ef1-a9ea-ec399b203898", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205185112400, "endTime": 32205185130200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6cdcbeda-0ab6-4330-a3a9-574a2461481a", "logId": "a73e3b23-dddf-457c-98c7-7cfc842a3257"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a73e3b23-dddf-457c-98c7-7cfc842a3257", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205185112400, "endTime": 32205185130200}, "additional": {"logType": "info", "children": [], "durationId": "d83d6dc5-a248-4ef1-a9ea-ec399b203898", "parent": "64b7f6dd-de45-4945-bb91-832847093888"}}, {"head": {"id": "64b7f6dd-de45-4945-bb91-832847093888", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205164390400, "endTime": 32205185146900}, "additional": {"logType": "info", "children": ["e3080383-159e-4703-b8bc-bb672c858622", "a16c09c7-bfec-48fa-baa7-fadb04e4faed", "a73e3b23-dddf-457c-98c7-7cfc842a3257"], "durationId": "6cdcbeda-0ab6-4330-a3a9-574a2461481a", "parent": "55087ed0-e417-4a14-b8f7-1ee77a24f91f"}}, {"head": {"id": "55087ed0-e417-4a14-b8f7-1ee77a24f91f", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205163092900, "endTime": 32205185158500}, "additional": {"logType": "info", "children": ["64b7f6dd-de45-4945-bb91-832847093888"], "durationId": "a73788ae-33e9-4c66-be5b-a751477c6220", "parent": "d95f8558-f43c-4e6c-9f10-931458476a2d"}}, {"head": {"id": "391fed47-034c-4251-a62f-21a05557c86a", "name": "watch files: [\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\hvigorfile.ts',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205197796000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46165e80-c047-42b5-937e-77fba687ddfc", "name": "hvigorfile, resolve hvigorfile dependencies in 13 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205198082700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf2a3355-d45e-4abd-8b6e-1f9d6398002c", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205185166400, "endTime": 32205198132900}, "additional": {"logType": "info", "children": [], "durationId": "6247df05-05ec-4d51-890f-3a7d427faac8", "parent": "d95f8558-f43c-4e6c-9f10-931458476a2d"}}, {"head": {"id": "e1cdff0b-2bdb-4299-900c-47557acac29c", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205199054200, "endTime": 32205199218900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c1c3de0-7706-4096-a4f0-2b8135745303", "logId": "f2097206-cc8d-4439-b58c-b135c111b10f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1ec60af-f957-4749-ae0b-178914ed795b", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205199077900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2097206-cc8d-4439-b58c-b135c111b10f", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205199054200, "endTime": 32205199218900}, "additional": {"logType": "info", "children": [], "durationId": "e1cdff0b-2bdb-4299-900c-47557acac29c", "parent": "d95f8558-f43c-4e6c-9f10-931458476a2d"}}, {"head": {"id": "6fc4d504-6c7d-49f5-9f68-a8c776bbf698", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205200434500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "066b8179-d273-48f5-8116-4f1b1623ad3e", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205206027600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1281f20e-9b9b-4822-893f-238f9a8e5d02", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205199231900, "endTime": 32205206849900}, "additional": {"logType": "info", "children": [], "durationId": "54204041-d2ae-4620-a59e-a98f9225cc90", "parent": "d95f8558-f43c-4e6c-9f10-931458476a2d"}}, {"head": {"id": "f21a6bfc-f555-43d3-b748-ddde83546d9d", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205206886600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e609e1fb-b078-471a-a1e0-fe7b4f078c9d", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205211617500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cc5829d-2adc-43d0-87d7-6b73976a8542", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205211741400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "400aeec6-c206-435b-a045-a7f5973134cd", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205211934700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b21610b-7324-4796-ba18-08d3188e3da2", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205213758900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8e90d06-05db-491a-9f80-8e14b3d8a14e", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205213856900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71877774-df48-464c-9414-f71529b46301", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205206875000, "endTime": 32205216275000}, "additional": {"logType": "info", "children": [], "durationId": "7e45c4c9-abce-4adf-b9a7-62df2a896cec", "parent": "d95f8558-f43c-4e6c-9f10-931458476a2d"}}, {"head": {"id": "897bce0e-8eca-4049-b255-4c52b5a0ed58", "name": "Configuration phase cost:134 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205216315000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1dcc4c8-4fff-4bee-98e1-487553ffb584", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205216295700, "endTime": 32205216419300}, "additional": {"logType": "info", "children": [], "durationId": "96e4c1e3-feeb-4dff-9846-daf6184fc25d", "parent": "d95f8558-f43c-4e6c-9f10-931458476a2d"}}, {"head": {"id": "d95f8558-f43c-4e6c-9f10-931458476a2d", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205077363200, "endTime": 32205216433500}, "additional": {"logType": "info", "children": ["cf2b8ea3-8cc5-4436-ba17-4771424853f5", "2ff9f480-789f-49a4-b30c-ade77a06adb7", "a57529ef-c3cf-40ed-8673-9c3530224a80", "55087ed0-e417-4a14-b8f7-1ee77a24f91f", "bf2a3355-d45e-4abd-8b6e-1f9d6398002c", "1281f20e-9b9b-4822-893f-238f9a8e5d02", "71877774-df48-464c-9414-f71529b46301", "e1dcc4c8-4fff-4bee-98e1-487553ffb584", "f2097206-cc8d-4439-b58c-b135c111b10f"], "durationId": "9c1c3de0-7706-4096-a4f0-2b8135745303", "parent": "3981d711-0a4d-423a-8254-beff3f21bd7e"}}, {"head": {"id": "7851ca4a-3e3c-40cf-92c6-c701d94839ea", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205217441300, "endTime": 32205217454500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ffed86e6-cfbc-44a8-b8fc-7e5d354efd5d", "logId": "c526fe81-ca7b-4c88-a803-f82a0ffa117e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c526fe81-ca7b-4c88-a803-f82a0ffa117e", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205217441300, "endTime": 32205217454500}, "additional": {"logType": "info", "children": [], "durationId": "7851ca4a-3e3c-40cf-92c6-c701d94839ea", "parent": "3981d711-0a4d-423a-8254-beff3f21bd7e"}}, {"head": {"id": "7b3abaaa-c8a2-4eff-8b39-5ef80b1da895", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205216454200, "endTime": 32205217463400}, "additional": {"logType": "info", "children": [], "durationId": "386b143a-13fe-4eb5-a09b-10812b28f9a0", "parent": "3981d711-0a4d-423a-8254-beff3f21bd7e"}}, {"head": {"id": "1835efce-6151-44d4-8705-e5d6aba36e49", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205217466800, "endTime": 32205217468600}, "additional": {"logType": "info", "children": [], "durationId": "c6c3ba96-460d-4c05-b7d9-a1b02068735a", "parent": "3981d711-0a4d-423a-8254-beff3f21bd7e"}}, {"head": {"id": "3981d711-0a4d-423a-8254-beff3f21bd7e", "name": "init", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205064636300, "endTime": 32205217470800}, "additional": {"logType": "info", "children": ["128b4bfe-5c28-4d96-9186-b3fe81bd2322", "d95f8558-f43c-4e6c-9f10-931458476a2d", "7b3abaaa-c8a2-4eff-8b39-5ef80b1da895", "1835efce-6151-44d4-8705-e5d6aba36e49", "3cb5d4eb-ac59-4750-957a-4f486d9292f8", "8782a0fe-81ca-41ce-afaa-7f97e7f7ade6", "c526fe81-ca7b-4c88-a803-f82a0ffa117e"], "durationId": "ffed86e6-cfbc-44a8-b8fc-7e5d354efd5d"}}, {"head": {"id": "96122233-0399-4ee1-8394-e4a60cd0c1af", "name": "Configuration task cost before running: 158 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205217585700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8366a586-fafb-451c-9334-a494c31cc36c", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205221622400, "endTime": 32205228622300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "73d92a32-feb9-43c9-8038-e3d336940800", "logId": "547d348d-5fd0-4b5f-a967-1bc2bc2a2af7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73d92a32-feb9-43c9-8038-e3d336940800", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205218839200}, "additional": {"logType": "detail", "children": [], "durationId": "8366a586-fafb-451c-9334-a494c31cc36c"}}, {"head": {"id": "a5ee3ca1-fa7d-4fb4-af58-1cc655fcd64e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205219238900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ab11715-e346-4d5b-bae7-0e9ec82252c0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205219311600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a6a90e4-3c68-41ae-8773-b7a04c8b981e", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205221632100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e210d432-7685-42f8-b82b-8b39e4d2957b", "name": "Incremental task entry:default@PreBuild pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205227936300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6efee654-622b-4a10-a30f-d572b1ef3e77", "name": "entry : default@PreBuild cost memory -1.5274200439453125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205228517500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "547d348d-5fd0-4b5f-a967-1bc2bc2a2af7", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205221622400, "endTime": 32205228622300}, "additional": {"logType": "info", "children": [], "durationId": "8366a586-fafb-451c-9334-a494c31cc36c"}}, {"head": {"id": "d1119773-76d6-46e8-8c88-7d5db5997ade", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205233043500, "endTime": 32205234601800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "0a46757c-c5a6-4c18-a5a4-12407f235a8c", "logId": "c56f4a26-368d-4edd-8139-9aa5a18ccefa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a46757c-c5a6-4c18-a5a4-12407f235a8c", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205231970300}, "additional": {"logType": "detail", "children": [], "durationId": "d1119773-76d6-46e8-8c88-7d5db5997ade"}}, {"head": {"id": "eb2ff1c4-4807-40e8-bec1-075e0450fe17", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205232426200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bdbba44-ccca-4820-a116-40ab49a90d3e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205232502300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5233ed33-75e4-4922-81aa-eaf3ff638c72", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205233051000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a342618d-16c1-4e2d-84e7-86f5b4a5dc41", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205234438000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca69cbef-d45d-40dc-9c3c-45a60d1ab21a", "name": "entry : default@MergeProfile cost memory 0.1082305908203125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205234516700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c56f4a26-368d-4edd-8139-9aa5a18ccefa", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205233043500, "endTime": 32205234601800}, "additional": {"logType": "info", "children": [], "durationId": "d1119773-76d6-46e8-8c88-7d5db5997ade"}}, {"head": {"id": "f0af1f2f-8f4b-407b-826f-1db19b4f1427", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205237011700, "endTime": 32205239053200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4d0f6774-b6b1-4c85-8f32-d08c293720b5", "logId": "40fa1d26-e510-458d-acb6-25658f63cfc3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4d0f6774-b6b1-4c85-8f32-d08c293720b5", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205235914100}, "additional": {"logType": "detail", "children": [], "durationId": "f0af1f2f-8f4b-407b-826f-1db19b4f1427"}}, {"head": {"id": "762ce515-c15c-4037-8eff-3cc54c783874", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205236347200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fbc68ca-5a75-40f2-9b2e-ac47ec2b3380", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205236410600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa3fa5ef-08d6-49b9-984d-bcaceea29174", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205237017600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c5f1ec3-adee-427f-8ea9-fe331bcd7256", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205237770900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2319634f-cc63-4b28-9ec2-c430629047b8", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205238897800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73a569a5-f4da-4bdb-b9ad-e1cf26f27fe4", "name": "entry : default@CreateBuildProfile cost memory 0.09621429443359375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205238980200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40fa1d26-e510-458d-acb6-25658f63cfc3", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205237011700, "endTime": 32205239053200}, "additional": {"logType": "info", "children": [], "durationId": "f0af1f2f-8f4b-407b-826f-1db19b4f1427"}}, {"head": {"id": "0bb8dc90-6edf-4b79-90ae-ef678e6956f5", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205241414700, "endTime": 32205241644700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "efec7b44-3d76-4502-a12b-b831b2cfb9bd", "logId": "d2499799-3023-4cb7-a26a-9476ecac65e8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "efec7b44-3d76-4502-a12b-b831b2cfb9bd", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205240360100}, "additional": {"logType": "detail", "children": [], "durationId": "0bb8dc90-6edf-4b79-90ae-ef678e6956f5"}}, {"head": {"id": "c7b872cd-e34a-4cd6-8ce6-af3674e40672", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205240780200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4ae8fd1-da3f-4d52-850c-210d609a34bc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205240846400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd57108b-fddf-4e58-9c45-8d91fa01727c", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205241421500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e9185ac-8459-4e05-a933-3715925e1b0e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205241501700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72ac3fe4-bef9-4625-9886-3538206e5228", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205241530600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b357dec-699b-413f-9c20-9fd71ea17b3f", "name": "entry : default@PreCheckSyscap cost memory 0.03680419921875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205241575500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acace828-87b8-45c7-a6c1-a1a26affc816", "name": "runTaskFromQueue task cost before running: 182 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205241618800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2499799-3023-4cb7-a26a-9476ecac65e8", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205241414700, "endTime": 32205241644700, "totalTime": 192400}, "additional": {"logType": "info", "children": [], "durationId": "0bb8dc90-6edf-4b79-90ae-ef678e6956f5"}}, {"head": {"id": "ecb03601-d9f3-4fd9-a4a4-38c48f5c1b75", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205248431300, "endTime": 32205249308100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "58e693ba-cd32-43a8-91e2-1e08e634c26c", "logId": "f9e694da-ed4a-4f24-a369-76f9c8615939"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58e693ba-cd32-43a8-91e2-1e08e634c26c", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205242868700}, "additional": {"logType": "detail", "children": [], "durationId": "ecb03601-d9f3-4fd9-a4a4-38c48f5c1b75"}}, {"head": {"id": "8db94ba9-db8f-4e5a-afca-0b09223721a8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205243262100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eea62163-6a2c-44da-9fa8-b9e48f305b89", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205243325700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ed7f3af-235b-4911-8eed-669e3547e90d", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205248444900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ffc6382-89ce-4113-95a8-c3741be4503a", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205248625800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a72b14b-d417-4f59-8208-1735cde2af3a", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205249183300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ca496be-5f09-4b4e-bd82-514f54f2faf7", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06366729736328125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205249265200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9e694da-ed4a-4f24-a369-76f9c8615939", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205248431300, "endTime": 32205249308100}, "additional": {"logType": "info", "children": [], "durationId": "ecb03601-d9f3-4fd9-a4a4-38c48f5c1b75"}}, {"head": {"id": "7a5f26a0-c462-436c-9988-433f84db2cc3", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205252156800, "endTime": 32205253101500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "3af79662-a3c2-4bc5-afa7-4ea1a88f8b2a", "logId": "857218a7-d9a7-4ae4-b2a9-d0ad6b813e04"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3af79662-a3c2-4bc5-afa7-4ea1a88f8b2a", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205250706500}, "additional": {"logType": "detail", "children": [], "durationId": "7a5f26a0-c462-436c-9988-433f84db2cc3"}}, {"head": {"id": "b4d56f19-e0d8-423f-ab86-f824ee298b33", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205251157700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ae60111-da0f-47da-9557-61d97cf9c7f5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205251221900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f97a3d74-e4a0-4a91-ac62-70089b6f4e80", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205252164000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39e4c6bb-dcb7-4bfa-b77f-e6ecea0659cc", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205252894600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8800c823-70d3-4808-8e02-3dac6ebba59e", "name": "entry : default@ProcessProfile cost memory 0.05466461181640625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205253026300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "857218a7-d9a7-4ae4-b2a9-d0ad6b813e04", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205252156800, "endTime": 32205253101500}, "additional": {"logType": "info", "children": [], "durationId": "7a5f26a0-c462-436c-9988-433f84db2cc3"}}, {"head": {"id": "bfc5e8af-abaf-4683-ac6b-a645051932de", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205257285100, "endTime": 32205262441000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6eae0f4b-87b3-41d1-b115-4b51eb4cfe16", "logId": "4ba8723f-26a1-4d0c-ad37-d29551cd8994"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6eae0f4b-87b3-41d1-b115-4b51eb4cfe16", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205255440200}, "additional": {"logType": "detail", "children": [], "durationId": "bfc5e8af-abaf-4683-ac6b-a645051932de"}}, {"head": {"id": "3827e04a-afa1-4653-b0e9-428d44f29c9b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205255862800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7fc0026-a303-4198-9df0-238e8b5ce78c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205255943700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04378a4a-4034-49e5-bdc1-3df2785acc1e", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205257292000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b38b9d24-ce7a-4ccb-b812-e24ac3c762b4", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205262260000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db0bc334-bd7a-49ae-99ca-7ef951331d6b", "name": "entry : default@ProcessRouterMap cost memory 0.19420623779296875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205262388600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ba8723f-26a1-4d0c-ad37-d29551cd8994", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205257285100, "endTime": 32205262441000}, "additional": {"logType": "info", "children": [], "durationId": "bfc5e8af-abaf-4683-ac6b-a645051932de"}}, {"head": {"id": "7926cdeb-fd30-4b1f-baed-5382075ba053", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205267948800, "endTime": 32205270292800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "c734914b-c3fe-400c-97cf-efee1d9e97a7", "logId": "93878594-9ad8-4c3b-9481-effd594cd626"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c734914b-c3fe-400c-97cf-efee1d9e97a7", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205265121000}, "additional": {"logType": "detail", "children": [], "durationId": "7926cdeb-fd30-4b1f-baed-5382075ba053"}}, {"head": {"id": "53906878-dd70-4e37-90d0-901ff2f11f04", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205265593800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25db973d-c65d-495c-bff3-1f064959cd34", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205265667400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c37f0b11-0340-44cf-a16b-b3d616cc0d56", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205266380000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d27bcf1e-8e61-4664-9efa-2666073c5157", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205268896900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13ab8924-c935-4506-9a52-e19513098032", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205269011000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef4b75f8-f084-481d-8b15-0c298dc560aa", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205269045000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9fc5436-b2af-473b-9f33-566a5ffe5a81", "name": "entry : default@PreviewProcessResource cost memory 0.0698699951171875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205269091500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "360f2c8f-43e0-4de1-b31f-8d595b6032e2", "name": "runTaskFromQueue task cost before running: 211 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205270204800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93878594-9ad8-4c3b-9481-effd594cd626", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205267948800, "endTime": 32205270292800, "totalTime": 1178900}, "additional": {"logType": "info", "children": [], "durationId": "7926cdeb-fd30-4b1f-baed-5382075ba053"}}, {"head": {"id": "93b5ce20-5952-417c-a941-333b24c985f0", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205275607500, "endTime": 32205294407000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c6324593-4a82-4f79-883e-b4d9f77e5ad5", "logId": "3c207418-3d86-415e-82c1-654461284b9c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c6324593-4a82-4f79-883e-b4d9f77e5ad5", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205272508700}, "additional": {"logType": "detail", "children": [], "durationId": "93b5ce20-5952-417c-a941-333b24c985f0"}}, {"head": {"id": "756d2bca-69e4-4e99-8b3c-0d0678358817", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205272968000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a34fcfc0-44af-4dea-909c-e8192ebff5bc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205273081400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88993281-7122-4f72-8280-b25afaa381cd", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205275617600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65bd51a7-0315-4502-82a0-a75dc762fc10", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205294215700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1a3bbf4-b529-42f0-8ad9-5c637ecf3a93", "name": "entry : default@GenerateLoaderJson cost memory -0.9741592407226562", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205294354600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c207418-3d86-415e-82c1-654461284b9c", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205275607500, "endTime": 32205294407000}, "additional": {"logType": "info", "children": [], "durationId": "93b5ce20-5952-417c-a941-333b24c985f0"}}, {"head": {"id": "ab7dbebb-7f33-493b-a803-9de193479411", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205304054700, "endTime": 32205546566300}, "additional": {"children": ["c49bdefc-8927-432f-bc14-123119137103", "d7e0f836-b5c1-46b5-984e-4f23d0346722", "d9c9345c-bce3-48c9-8b17-f974d0ea9884", "5ded5d40-5c54-479a-b66d-8e0f89f1b3a7"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "725b77d9-cf59-463e-b975-d92f1c6129e2", "logId": "de18ac57-14b1-461b-b04e-325e961579ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "725b77d9-cf59-463e-b975-d92f1c6129e2", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205300453400}, "additional": {"logType": "detail", "children": [], "durationId": "ab7dbebb-7f33-493b-a803-9de193479411"}}, {"head": {"id": "eb8963f1-8d6a-4d52-ba00-07da1e59bdd1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205301003600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5535eba0-6ddf-4ad2-b67a-4a33e8291cf1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205301090400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4d74361-1252-4d7d-82bd-922cfaf74810", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205302197800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1032532c-3983-4c33-bc2e-4aaf99d0b63c", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205304079500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f22bad17-d6d5-49c5-8e9e-e3e6da5b1f4c", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205319490100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45d554a3-77d0-4379-af0b-a1087c95074e", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 15 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205319626600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c49bdefc-8927-432f-bc14-123119137103", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205320633800, "endTime": 32205352885500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ab7dbebb-7f33-493b-a803-9de193479411", "logId": "2690fc14-9889-4899-a231-9c3b9c016aca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2690fc14-9889-4899-a231-9c3b9c016aca", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205320633800, "endTime": 32205352885500}, "additional": {"logType": "info", "children": [], "durationId": "c49bdefc-8927-432f-bc14-123119137103", "parent": "de18ac57-14b1-461b-b04e-325e961579ea"}}, {"head": {"id": "b8413aa5-0597-41d8-9656-8a1140f3ee2f", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205353167500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7e0f836-b5c1-46b5-984e-4f23d0346722", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205354058200, "endTime": 32205412122400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ab7dbebb-7f33-493b-a803-9de193479411", "logId": "b92499b8-c274-4aaa-b6ed-da19e1f75bdb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ccb0d362-9b75-4632-b0f2-4f772ef8658c", "name": "current process  memoryUsage: {\n  rss: 113012736,\n  heapTotal: 127754240,\n  heapUsed: 107559240,\n  external: 3075129,\n  arrayBuffers: 69030\n} os memoryUsage :12.46795654296875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205354881400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "894fcffb-0415-4f77-913d-0bceac64dd52", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205409574400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b92499b8-c274-4aaa-b6ed-da19e1f75bdb", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205354058200, "endTime": 32205412122400}, "additional": {"logType": "info", "children": [], "durationId": "d7e0f836-b5c1-46b5-984e-4f23d0346722", "parent": "de18ac57-14b1-461b-b04e-325e961579ea"}}, {"head": {"id": "5dc510a7-6779-461e-86cd-f1515b7e433c", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205412238600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9c9345c-bce3-48c9-8b17-f974d0ea9884", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205413221400, "endTime": 32205468859100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ab7dbebb-7f33-493b-a803-9de193479411", "logId": "6c926cf2-4492-43f1-983a-82c5ed1c35b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ad5a041-5963-4a64-a8ed-44a095f2f538", "name": "current process  memoryUsage: {\n  rss: 113012736,\n  heapTotal: 127754240,\n  heapUsed: 107823104,\n  external: 3075255,\n  arrayBuffers: 69171\n} os memoryUsage :12.479927062988281", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205414089000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3658a86c-3d0b-42f2-b054-1892ed44af1f", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205464823800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c926cf2-4492-43f1-983a-82c5ed1c35b3", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205413221400, "endTime": 32205468859100}, "additional": {"logType": "info", "children": [], "durationId": "d9c9345c-bce3-48c9-8b17-f974d0ea9884", "parent": "de18ac57-14b1-461b-b04e-325e961579ea"}}, {"head": {"id": "6bcc4957-47c1-4dba-a151-754744b15d73", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205469134800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ded5d40-5c54-479a-b66d-8e0f89f1b3a7", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205470684300, "endTime": 32205545253000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ab7dbebb-7f33-493b-a803-9de193479411", "logId": "09b35066-8fd1-4aae-8571-b64ff095a1d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61006f73-e713-4bc2-bcfe-6de207c85cd3", "name": "current process  memoryUsage: {\n  rss: 113012736,\n  heapTotal: 127754240,\n  heapUsed: 108113888,\n  external: 3075381,\n  arrayBuffers: 70181\n} os memoryUsage :12.473358154296875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205472041800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c69c5bd8-6e60-48b4-b7d3-19eaafa58103", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205541873500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09b35066-8fd1-4aae-8571-b64ff095a1d8", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205470684300, "endTime": 32205545253000}, "additional": {"logType": "info", "children": [], "durationId": "5ded5d40-5c54-479a-b66d-8e0f89f1b3a7", "parent": "de18ac57-14b1-461b-b04e-325e961579ea"}}, {"head": {"id": "1e205e46-b2e1-4054-bc17-bf787710d0f7", "name": "entry : default@PreviewCompileResource cost memory -9.926956176757812", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205546296900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0dff56a-a26e-4ec3-9a21-64296dbe55b3", "name": "runTaskFromQueue task cost before running: 487 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205546500400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de18ac57-14b1-461b-b04e-325e961579ea", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205304054700, "endTime": 32205546566300, "totalTime": 242378700}, "additional": {"logType": "info", "children": ["2690fc14-9889-4899-a231-9c3b9c016aca", "b92499b8-c274-4aaa-b6ed-da19e1f75bdb", "6c926cf2-4492-43f1-983a-82c5ed1c35b3", "09b35066-8fd1-4aae-8571-b64ff095a1d8"], "durationId": "ab7dbebb-7f33-493b-a803-9de193479411"}}, {"head": {"id": "b8f349f9-4b2a-43bd-81bc-f7d252f85eda", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205549731000, "endTime": 32205549996000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "6f7f7cf4-9072-4cf2-9303-c0cb0d0d6ae6", "logId": "ed5ffabf-4f05-4fd1-83a7-e8516d3271e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f7f7cf4-9072-4cf2-9303-c0cb0d0d6ae6", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205549043900}, "additional": {"logType": "detail", "children": [], "durationId": "b8f349f9-4b2a-43bd-81bc-f7d252f85eda"}}, {"head": {"id": "32ff2728-829d-4359-9b4b-218b6b78e4f3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205549548500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64a052ea-740f-4ef2-8632-1c65676db6e5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205549652100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "183bbf19-18d4-49bc-87cd-116d1e3ee881", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205549737800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31d99607-e0c9-48f7-8204-b9137d3bb540", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205549798400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6e18d64-9a4f-4461-89f7-48cd56d73d0d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205549820500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c4a5559-a056-4efb-bbac-fa0d56d6c85c", "name": "entry : default@PreviewHookCompileResource cost memory 0.0379180908203125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205549901200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33212e6a-be8e-4fd8-94d1-1fb7890648b5", "name": "runTaskFromQueue task cost before running: 491 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205549964100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed5ffabf-4f05-4fd1-83a7-e8516d3271e4", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205549731000, "endTime": 32205549996000, "totalTime": 217400}, "additional": {"logType": "info", "children": [], "durationId": "b8f349f9-4b2a-43bd-81bc-f7d252f85eda"}}, {"head": {"id": "5e32e248-d164-4a8d-b005-cc6827856401", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205552581100, "endTime": 32205558960600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "9efbdaec-5410-47b6-a0dc-4e00c7eb4fd7", "logId": "e642c5c2-6b8d-4509-a745-ca8f16329ca2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9efbdaec-5410-47b6-a0dc-4e00c7eb4fd7", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205551373200}, "additional": {"logType": "detail", "children": [], "durationId": "5e32e248-d164-4a8d-b005-cc6827856401"}}, {"head": {"id": "bbd27585-1bcb-414b-9f57-6f52542c6ad5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205551917300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12024051-0524-4d1d-bf95-0b1acc2bf58a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205552005200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fbab4a9-090a-433d-88c9-0fa8a43c382e", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205552590700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f166cb23-4657-4863-8f90-0e6695c6cbc0", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205553725400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bcc5ef5-96cc-4c1e-8e22-ed4a550f2ed4", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205553823900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c818ff5-8e0d-4d0b-8c87-f824e17dad9c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205553878900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3588671-e882-4750-bc57-08db9a7e2a3c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205553905900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e0c8eaf-5edf-4e67-bdb7-0c5e2fea00c7", "name": "entry : default@CopyPreviewProfile cost memory 0.2425079345703125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205558749900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9961614-5064-42bb-8918-faa535dd9028", "name": "runTaskFromQueue task cost before running: 499 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205558901700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e642c5c2-6b8d-4509-a745-ca8f16329ca2", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205552581100, "endTime": 32205558960600, "totalTime": 6308600}, "additional": {"logType": "info", "children": [], "durationId": "5e32e248-d164-4a8d-b005-cc6827856401"}}, {"head": {"id": "620abca1-2c97-4f3e-8e21-77ff5ce409b6", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205561791800, "endTime": 32205562087200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "35ad004a-5be3-4d32-b0ed-9d1b6d85449b", "logId": "f9af10aa-d7c9-4a9e-83b6-249195ca7ea4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35ad004a-5be3-4d32-b0ed-9d1b6d85449b", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205560621800}, "additional": {"logType": "detail", "children": [], "durationId": "620abca1-2c97-4f3e-8e21-77ff5ce409b6"}}, {"head": {"id": "924ea156-5035-42d0-99d5-e42cfce650ed", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205561099600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fad29018-f709-4e99-badc-1806d6329b56", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205561185600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3970a5db-21d5-482b-8ab1-8198ba2ed084", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205561799500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb53bea3-4ada-43b5-a241-42464b706842", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205561894000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a96e8280-bc22-4c88-9d80-2929c502c537", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205561934800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acccd979-ace5-401f-adab-31a87cfac306", "name": "entry : default@ReplacePreviewerPage cost memory 0.037872314453125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205562000900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a96a350-46ad-47ee-aede-a5f2a162f50f", "name": "runTaskFromQueue task cost before running: 503 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205562053400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9af10aa-d7c9-4a9e-83b6-249195ca7ea4", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205561791800, "endTime": 32205562087200, "totalTime": 249600}, "additional": {"logType": "info", "children": [], "durationId": "620abca1-2c97-4f3e-8e21-77ff5ce409b6"}}, {"head": {"id": "f040dbd6-e6e2-4bab-ba37-705321269a36", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205564155800, "endTime": 32205564352300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "d06b1d57-7945-4594-af03-0e38779ed901", "logId": "94929083-d11e-4ae8-b7f4-11214065d97e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d06b1d57-7945-4594-af03-0e38779ed901", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205564101500}, "additional": {"logType": "detail", "children": [], "durationId": "f040dbd6-e6e2-4bab-ba37-705321269a36"}}, {"head": {"id": "c2ad338a-a00a-4bee-9951-d420a46be23d", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205564162000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49ccb9d7-feb9-47b6-b356-893634511b38", "name": "entry : buildPreviewerResource cost memory 0.0116424560546875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205564267800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a25972c-6630-4a16-96e0-726e6bfddbee", "name": "runTaskFromQueue task cost before running: 505 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205564319500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94929083-d11e-4ae8-b7f4-11214065d97e", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205564155800, "endTime": 32205564352300, "totalTime": 149900}, "additional": {"logType": "info", "children": [], "durationId": "f040dbd6-e6e2-4bab-ba37-705321269a36"}}, {"head": {"id": "425761d8-4e52-437a-bfb8-c35fb44dc776", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205567089200, "endTime": 32205570830100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "da5eebd9-d769-46c2-8307-8445e3417f26", "logId": "ed5d3ef2-f059-4caa-ab5a-54977193806a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da5eebd9-d769-46c2-8307-8445e3417f26", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205565740600}, "additional": {"logType": "detail", "children": [], "durationId": "425761d8-4e52-437a-bfb8-c35fb44dc776"}}, {"head": {"id": "0f1411fd-ead3-46e3-9666-fa18d53c5707", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205566355300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa2d39c3-0a2f-4600-9fbe-e0d24c4b50ef", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205566435700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5738a35f-6df4-4040-a158-ba23710811fb", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205567097500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f19aadd-7a9d-4357-8760-e25c23327f1c", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205568880600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd6554f7-fe21-4916-9fb8-2eb04a04b511", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205568992700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "703a2c9c-bc12-4991-b3fd-18809915e489", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205569051700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4aaea35-4b12-4e0f-8e46-54ad91114207", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205569077000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4af18e20-35b2-4093-832e-f2925b9af225", "name": "entry : default@PreviewUpdateAssets cost memory 0.146331787109375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205570650700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76ea8724-8a07-493c-a14a-269a80de51c5", "name": "runTaskFromQueue task cost before running: 511 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205570785300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed5d3ef2-f059-4caa-ab5a-54977193806a", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205567089200, "endTime": 32205570830100, "totalTime": 3674500}, "additional": {"logType": "info", "children": [], "durationId": "425761d8-4e52-437a-bfb8-c35fb44dc776"}}, {"head": {"id": "3231a57d-1479-4a61-8048-0f0d8feb79b6", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205577574500, "endTime": 32214278424800}, "additional": {"children": ["b18d3be7-56e6-460b-85d0-3a702a733e96"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "57d0a507-1f42-4680-98dc-14a3c114afdf", "logId": "437f68bc-5b55-44b5-ade4-0fffbff3d73c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "57d0a507-1f42-4680-98dc-14a3c114afdf", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205572869100}, "additional": {"logType": "detail", "children": [], "durationId": "3231a57d-1479-4a61-8048-0f0d8feb79b6"}}, {"head": {"id": "318dff11-ed7b-40e9-a96e-6a35143a6805", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205573350500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ab8ea4f-2a54-4607-9df6-1f9d3f7128e1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205573432500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0fec855-dd21-4cc8-80f2-f2a7648651fb", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205577585500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb653092-3a7b-41fe-b787-5799689decac", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205587385500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9137896-020d-4a71-b6b9-5128c31765a0", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205587512800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b18d3be7-56e6-460b-85d0-3a702a733e96", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker22", "startTime": 32205598862500, "endTime": 32214275046100}, "additional": {"children": ["abf0fef5-ed85-402a-9fc8-f0be5fc04645", "95e3317a-f82e-4648-a2a6-fc59c22fb00a", "d10a96d3-7bd1-43f5-a4dd-d386826e9a0f", "fceaaaa3-ceb0-4ed4-92b5-b7b8ceeb0ef6", "cfbb403f-5ffd-4f88-8512-514dabaf74f9", "6e0408c8-552e-4332-86d4-50fc9e0d909f"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "3231a57d-1479-4a61-8048-0f0d8feb79b6", "logId": "bfba069e-a692-4d41-b41b-929cfa382515"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e3dc061-f224-426d-878e-5b26d10433e4", "name": "entry : default@PreviewArkTS cost memory -0.35216522216796875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205600825700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e93d16b-3c7e-4be8-9ea8-d8004cf2148d", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32209225700100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abf0fef5-ed85-402a-9fc8-f0be5fc04645", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker22", "startTime": 32209226727800, "endTime": 32209226745700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b18d3be7-56e6-460b-85d0-3a702a733e96", "logId": "94069159-ea79-43cd-9f8c-cec0f9a3e04e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94069159-ea79-43cd-9f8c-cec0f9a3e04e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32209226727800, "endTime": 32209226745700}, "additional": {"logType": "info", "children": [], "durationId": "abf0fef5-ed85-402a-9fc8-f0be5fc04645", "parent": "bfba069e-a692-4d41-b41b-929cfa382515"}}, {"head": {"id": "adf8c789-a8ee-4cb3-afed-27188f39a78f", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214273830100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95e3317a-f82e-4648-a2a6-fc59c22fb00a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker22", "startTime": 32214274916600, "endTime": 32214274934400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b18d3be7-56e6-460b-85d0-3a702a733e96", "logId": "2de830aa-e57e-4583-89ac-f9af78cd55fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2de830aa-e57e-4583-89ac-f9af78cd55fd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214274916600, "endTime": 32214274934400}, "additional": {"logType": "info", "children": [], "durationId": "95e3317a-f82e-4648-a2a6-fc59c22fb00a", "parent": "bfba069e-a692-4d41-b41b-929cfa382515"}}, {"head": {"id": "bfba069e-a692-4d41-b41b-929cfa382515", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Worker22", "startTime": 32205598862500, "endTime": 32214275046100}, "additional": {"logType": "info", "children": ["94069159-ea79-43cd-9f8c-cec0f9a3e04e", "2de830aa-e57e-4583-89ac-f9af78cd55fd", "7974cf9b-4c06-49db-bacc-e780cbb82283", "2a8d9922-4af6-4625-a398-76b9f22e52f8", "cea64282-37e7-4151-993e-b865643b20d0", "77ab6cc5-0d06-4b54-9499-318dcbbd9695"], "durationId": "b18d3be7-56e6-460b-85d0-3a702a733e96", "parent": "437f68bc-5b55-44b5-ade4-0fffbff3d73c"}}, {"head": {"id": "d10a96d3-7bd1-43f5-a4dd-d386826e9a0f", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker22", "startTime": 32207989345000, "endTime": 32209171589800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "b18d3be7-56e6-460b-85d0-3a702a733e96", "logId": "7974cf9b-4c06-49db-bacc-e780cbb82283"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7974cf9b-4c06-49db-bacc-e780cbb82283", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32207989345000, "endTime": 32209171589800}, "additional": {"logType": "info", "children": [], "durationId": "d10a96d3-7bd1-43f5-a4dd-d386826e9a0f", "parent": "bfba069e-a692-4d41-b41b-929cfa382515"}}, {"head": {"id": "fceaaaa3-ceb0-4ed4-92b5-b7b8ceeb0ef6", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker22", "startTime": 32209171878300, "endTime": 32209200863400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "b18d3be7-56e6-460b-85d0-3a702a733e96", "logId": "2a8d9922-4af6-4625-a398-76b9f22e52f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a8d9922-4af6-4625-a398-76b9f22e52f8", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32209171878300, "endTime": 32209200863400}, "additional": {"logType": "info", "children": [], "durationId": "fceaaaa3-ceb0-4ed4-92b5-b7b8ceeb0ef6", "parent": "bfba069e-a692-4d41-b41b-929cfa382515"}}, {"head": {"id": "cfbb403f-5ffd-4f88-8512-514dabaf74f9", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker22", "startTime": 32209200954100, "endTime": 32209201069300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "b18d3be7-56e6-460b-85d0-3a702a733e96", "logId": "cea64282-37e7-4151-993e-b865643b20d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cea64282-37e7-4151-993e-b865643b20d0", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32209200954100, "endTime": 32209201069300}, "additional": {"logType": "info", "children": [], "durationId": "cfbb403f-5ffd-4f88-8512-514dabaf74f9", "parent": "bfba069e-a692-4d41-b41b-929cfa382515"}}, {"head": {"id": "6e0408c8-552e-4332-86d4-50fc9e0d909f", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker22", "startTime": 32209201115700, "endTime": 32214273881700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "b18d3be7-56e6-460b-85d0-3a702a733e96", "logId": "77ab6cc5-0d06-4b54-9499-318dcbbd9695"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "77ab6cc5-0d06-4b54-9499-318dcbbd9695", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32209201115700, "endTime": 32214273881700}, "additional": {"logType": "info", "children": [], "durationId": "6e0408c8-552e-4332-86d4-50fc9e0d909f", "parent": "bfba069e-a692-4d41-b41b-929cfa382515"}}, {"head": {"id": "437f68bc-5b55-44b5-ade4-0fffbff3d73c", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205577574500, "endTime": 32214278424800, "totalTime": 8700836900}, "additional": {"logType": "info", "children": ["bfba069e-a692-4d41-b41b-929cfa382515"], "durationId": "3231a57d-1479-4a61-8048-0f0d8feb79b6"}}, {"head": {"id": "3e95bc17-32e5-413e-abd9-d0eed394811b", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214283567600, "endTime": 32214283889800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "8cadd71f-d051-4a65-9676-e88e3ceeffdf", "logId": "c3689c5f-dcfb-48b2-ab25-e58ac7f29865"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8cadd71f-d051-4a65-9676-e88e3ceeffdf", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214283498900}, "additional": {"logType": "detail", "children": [], "durationId": "3e95bc17-32e5-413e-abd9-d0eed394811b"}}, {"head": {"id": "b41972eb-98aa-4bed-b73f-fdcd1aa670ec", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214283578200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36c25bc0-5772-4cd7-b1fe-e5f12e98c0c8", "name": "entry : PreviewBuild cost memory 0.0115203857421875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214283769800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55bc1699-237b-4ebc-a6b3-6625c7f0f0f1", "name": "runTaskFromQueue task cost before running: 9 s 224 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214283847900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3689c5f-dcfb-48b2-ab25-e58ac7f29865", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214283567600, "endTime": 32214283889800, "totalTime": 260900}, "additional": {"logType": "info", "children": [], "durationId": "3e95bc17-32e5-413e-abd9-d0eed394811b"}}, {"head": {"id": "6c3218dd-c458-473d-8d4f-732efd2f8b1a", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214293866100, "endTime": 32214293889800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aa5589d1-ced1-4816-8f22-c8453fe420ca", "logId": "a3bf3580-270e-4017-a7f2-d02aaa9f3197"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a3bf3580-270e-4017-a7f2-d02aaa9f3197", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214293866100, "endTime": 32214293889800}, "additional": {"logType": "info", "children": [], "durationId": "6c3218dd-c458-473d-8d4f-732efd2f8b1a"}}, {"head": {"id": "e38f18fa-99a4-470e-a8d3-6ebf89f8bf93", "name": "BUILD SUCCESSFUL in 9 s 234 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214293993200}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "c53c5901-0947-4716-9039-358d4abba6f4", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32205059936900, "endTime": 32214294578300}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 18, "minute": 2}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "4c6bea21-6dad-40ac-b1bd-c6c6e37cd6ab", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214294624200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6162c88f-3a52-4de8-90d4-4a5ee2e7d07a", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214294731400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "730de044-88ce-49d8-af51-8d8040dddbf8", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214294764100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43503852-d91a-4132-a741-c45ca0b012ec", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214294798200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf82b737-c35a-4ebc-8006-a6d39d758f1a", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214294822500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af379472-0647-4e0a-930f-13078bb81593", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214294845500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "194753fc-3c0c-4412-97c4-0834537213a4", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214294880500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06ff77df-4941-4ecd-841d-bcbcd2faf3b6", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214295581300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ebc6e82-6ab5-4a62-a6c9-4843c577bc9b", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214301238200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b75dbd3-77f5-4e64-b544-3fad3b5702e4", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214301602200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c41fa410-faed-42d0-a5ba-85aab3c278a2", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214309174900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29854b53-736f-4682-be20-f2183eed7ba2", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:15 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214309833300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91c5a477-a5eb-4465-8e8f-ef6fe7790e19", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214310037600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "741ad6df-ac84-476f-b88b-f53a3ca30f86", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214311125900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9436970e-b4f2-430a-8e90-4b638b01f277", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214312929500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf2c82f8-1be5-4509-aeae-bbdc6c294933", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214313825100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cb446b3-1d30-4903-935b-2262dc5eda9a", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214314498500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c76d6e0-4d37-42d8-926b-e5c6db84e999", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214315108200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2eb03482-4e81-445a-8702-d7e1c0ed9b99", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214320386300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2c0e4cb-7621-40e8-aa1c-178c4a37145f", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214321898300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cab2c427-2c7f-4477-a690-7d15e7dad13d", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214322019100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45a805a9-b68c-4f71-8882-d959f24f3d70", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214322303200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "450eae54-b7df-46ba-960f-0323ff61f9f9", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214323455500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c7d38ef-75f9-43a3-b094-6cbc516dc747", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214333269100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e91c5ae3-0e91-4fba-816c-7841cc73948f", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214333615100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1abf0e3-2824-497c-a79d-e9d2d4be1fe8", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214333878200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd0965c7-f2fb-4180-bdad-bf00ecb70811", "name": "Update task entry:default@PreviewArkTS output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214334146200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac411322-0fc2-4b10-b393-1a38245899d0", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:20 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 32214334407700}, "additional": {"logType": "debug", "children": []}}], "workLog": []}