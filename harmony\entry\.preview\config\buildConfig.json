{"deviceType": "phone,tablet,2in1", "buildMode": "debug", "note": "false", "logLevel": "3", "isPreview": "true", "port": "29904", "checkEntry": "true", "localPropertiesPath": "D:\\vue\\daxiangmuwallet\\harmony\\local.properties", "Path": "D:\\app\\devecostudio\\DevEco Studio\\tools\\node\\", "aceProfilePath": "D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", "hapMode": "false", "img2bin": "true", "projectProfilePath": "D:\\vue\\daxiangmuwallet\\harmony\\build-profile.json5", "watchMode": "true", "appResource": "D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt", "aceBuildJson": "D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json", "aceModuleRoot": "D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets", "aceSoPath": "D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\cache\\nativeDependencies.txt", "cachePath": "D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\cache\\.default", "aceModuleBuild": "D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\assets\\default\\ets", "aceModuleJsonPath": "D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "stageRouterConfig": {"paths": ["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json"], "contents": ["{\"module\":{\"pages\":\"$profile:main_pages\",\"name\":\"entry\"}}", "{\"src\":[\"pages/Index\",\"pages/LoginPage\",\"pages/RegisterPage\",\"pages/ForgotPasswordPage\",\"pages/MainPage\",\"pages/BankCardPage\",\"pages/BankCardDetailPage\",\"pages/AddBankCardPage\",\"pages/TransactionListPage\",\"pages/TransactionDetailPage\",\"pages/WalletOperationPage\",\"pages/PaymentPage\",\"pages/SettingsPage\",\"pages/ChangePayPasswordPage\",\"pages/PayLimitSettingPage\",\"pages/BankSelectorPage\",\"pages/CardTypeSelectorPage\",\"pages/HelpCenterPage\",\"pages/AboutPage\"]}"]}}