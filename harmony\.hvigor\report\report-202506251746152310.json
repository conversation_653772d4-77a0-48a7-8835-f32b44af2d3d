{"version": "2.0", "ppid": 21780, "events": [{"head": {"id": "55ad6d22-4e36-425d-b406-1fde82b0f0d7", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989289501800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "038fa946-1c97-4142-bb66-e693e1876eb3", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989559456200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22d0515b-0637-48e7-8501-fd92c87146c7", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989559765600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64d0636a-2922-485d-816c-172ede788420", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31217976843500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a25af42f-79bd-4b74-b7b1-b48e436127f9", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31217983078000, "endTime": 31218108747900}, "additional": {"children": ["fbef247b-2972-4c97-bcba-e298121a4b71", "00d9290a-995f-43e1-a4e9-2f014e34a9ba", "44247e9f-bbc3-4b2d-8fee-3277bf9bacd1", "a722ef3b-c43f-4b2c-b4b0-c2f25027af3a", "ce33b31e-db23-43e6-aa74-e8b6625c4087", "5514b748-a433-419a-9025-da1c9e7de691", "db65790b-f0d2-460a-913f-97c9dca21829"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "c2ab3b67-305d-42f5-a29e-e40560e4fc71"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fbef247b-2972-4c97-bcba-e298121a4b71", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31217983079600, "endTime": 31217993538200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a25af42f-79bd-4b74-b7b1-b48e436127f9", "logId": "5a440ad7-b374-4d17-941c-dbdd7b21e0d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "00d9290a-995f-43e1-a4e9-2f014e34a9ba", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31217993556200, "endTime": 31218107745800}, "additional": {"children": ["4980f1f4-fa3a-4d36-877e-f121cd444f59", "f7caa663-0a18-4617-a299-b5b6195b61b4", "7d7d15ea-81b9-4d89-89a1-14564b8987f0", "8699694e-4fa9-46e2-aa9f-960489019e7c", "56e31bfe-8696-4724-a50c-89979bb59d7a", "8a8ce4c2-0101-44d4-8015-8833c5f472eb", "a9fdbe0a-432a-43fe-a63d-166905977502", "0a8abf96-b7f8-403b-afa2-acc676854644", "44f7d65b-9175-466a-a15a-c3bfc9f3c534"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a25af42f-79bd-4b74-b7b1-b48e436127f9", "logId": "584339f2-e446-4bda-93e9-057d15ffc074"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44247e9f-bbc3-4b2d-8fee-3277bf9bacd1", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218107760200, "endTime": 31218108740900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a25af42f-79bd-4b74-b7b1-b48e436127f9", "logId": "2baa6348-76c0-491f-99d9-ac530c9627f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a722ef3b-c43f-4b2c-b4b0-c2f25027af3a", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218108743900, "endTime": 31218108745400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a25af42f-79bd-4b74-b7b1-b48e436127f9", "logId": "85d2bc86-27b9-4e77-9ead-4d0de2a3e09d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce33b31e-db23-43e6-aa74-e8b6625c4087", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31217985677600, "endTime": 31217985722100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a25af42f-79bd-4b74-b7b1-b48e436127f9", "logId": "a1cfc9c0-3188-4f27-88fb-aa78a6b1382b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1cfc9c0-3188-4f27-88fb-aa78a6b1382b", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31217985677600, "endTime": 31217985722100}, "additional": {"logType": "info", "children": [], "durationId": "ce33b31e-db23-43e6-aa74-e8b6625c4087", "parent": "c2ab3b67-305d-42f5-a29e-e40560e4fc71"}}, {"head": {"id": "5514b748-a433-419a-9025-da1c9e7de691", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31217990269000, "endTime": 31217990284500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a25af42f-79bd-4b74-b7b1-b48e436127f9", "logId": "bc2117e6-c0d3-453c-be7a-4caec2e731f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc2117e6-c0d3-453c-be7a-4caec2e731f1", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31217990269000, "endTime": 31217990284500}, "additional": {"logType": "info", "children": [], "durationId": "5514b748-a433-419a-9025-da1c9e7de691", "parent": "c2ab3b67-305d-42f5-a29e-e40560e4fc71"}}, {"head": {"id": "7b3035ad-a124-4853-a9c9-97481600764e", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31217990329700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e687771e-e6f4-4180-88c5-0b3542233902", "name": "Cache service initialization finished in 3 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31217993425200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a440ad7-b374-4d17-941c-dbdd7b21e0d2", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31217983079600, "endTime": 31217993538200}, "additional": {"logType": "info", "children": [], "durationId": "fbef247b-2972-4c97-bcba-e298121a4b71", "parent": "c2ab3b67-305d-42f5-a29e-e40560e4fc71"}}, {"head": {"id": "4980f1f4-fa3a-4d36-877e-f121cd444f59", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31217998154900, "endTime": 31217998161700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "00d9290a-995f-43e1-a4e9-2f014e34a9ba", "logId": "1d40616b-0cca-42d8-be9e-b79d4c9f21a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7caa663-0a18-4617-a299-b5b6195b61b4", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31217998171600, "endTime": 31218001058200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "00d9290a-995f-43e1-a4e9-2f014e34a9ba", "logId": "658034ea-7083-4515-8029-a82fcb399821"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d7d15ea-81b9-4d89-89a1-14564b8987f0", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218001069500, "endTime": 31218061880700}, "additional": {"children": ["b9ce7801-07ba-47c1-a4df-bb96ae85adb7", "add6a86f-eba5-4a99-a66a-3e6138111fc0", "4627cd21-fb59-428c-bdb5-5e845f666dfc"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "00d9290a-995f-43e1-a4e9-2f014e34a9ba", "logId": "34a93de6-5bab-4f93-883b-57dca38e82d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8699694e-4fa9-46e2-aa9f-960489019e7c", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218061890600, "endTime": 31218077882000}, "additional": {"children": ["dfce091a-b782-40f3-881f-270f10b3b34d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "00d9290a-995f-43e1-a4e9-2f014e34a9ba", "logId": "bca393db-5871-4dbd-803b-e6bd79ddb46c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56e31bfe-8696-4724-a50c-89979bb59d7a", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218077891300, "endTime": 31218089600600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "00d9290a-995f-43e1-a4e9-2f014e34a9ba", "logId": "97e2c753-2971-415f-9090-f6dbfdc79832"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a8ce4c2-0101-44d4-8015-8833c5f472eb", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218090449500, "endTime": 31218098601500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "00d9290a-995f-43e1-a4e9-2f014e34a9ba", "logId": "432d130f-0cd0-4c31-9648-a40e34ef26c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9fdbe0a-432a-43fe-a63d-166905977502", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218098634600, "endTime": 31218107603100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "00d9290a-995f-43e1-a4e9-2f014e34a9ba", "logId": "ac2f7495-72f5-49c2-a112-f995e34712c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a8abf96-b7f8-403b-afa2-acc676854644", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218107641300, "endTime": 31218107736800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "00d9290a-995f-43e1-a4e9-2f014e34a9ba", "logId": "5fb47536-13ed-4e67-94ea-491ce359c748"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d40616b-0cca-42d8-be9e-b79d4c9f21a5", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31217998154900, "endTime": 31217998161700}, "additional": {"logType": "info", "children": [], "durationId": "4980f1f4-fa3a-4d36-877e-f121cd444f59", "parent": "584339f2-e446-4bda-93e9-057d15ffc074"}}, {"head": {"id": "658034ea-7083-4515-8029-a82fcb399821", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31217998171600, "endTime": 31218001058200}, "additional": {"logType": "info", "children": [], "durationId": "f7caa663-0a18-4617-a299-b5b6195b61b4", "parent": "584339f2-e446-4bda-93e9-057d15ffc074"}}, {"head": {"id": "b9ce7801-07ba-47c1-a4df-bb96ae85adb7", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218001552000, "endTime": 31218001570400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d7d15ea-81b9-4d89-89a1-14564b8987f0", "logId": "6989f91d-3d71-432e-b4b6-1539e30490fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6989f91d-3d71-432e-b4b6-1539e30490fb", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218001552000, "endTime": 31218001570400}, "additional": {"logType": "info", "children": [], "durationId": "b9ce7801-07ba-47c1-a4df-bb96ae85adb7", "parent": "34a93de6-5bab-4f93-883b-57dca38e82d7"}}, {"head": {"id": "add6a86f-eba5-4a99-a66a-3e6138111fc0", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218003105600, "endTime": 31218061280900}, "additional": {"children": ["1ee38af3-ca4e-49e8-96f2-92cdc9879f39", "e3bb8734-7a41-4d2c-bf52-3fe88fb08253"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d7d15ea-81b9-4d89-89a1-14564b8987f0", "logId": "20fb04d0-d9f3-4b6b-a744-05c402db0219"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ee38af3-ca4e-49e8-96f2-92cdc9879f39", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218003106700, "endTime": 31218007186200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "add6a86f-eba5-4a99-a66a-3e6138111fc0", "logId": "f457e01e-a058-43ef-b5c7-6ef22f047beb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3bb8734-7a41-4d2c-bf52-3fe88fb08253", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218007201200, "endTime": 31218061270800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "add6a86f-eba5-4a99-a66a-3e6138111fc0", "logId": "652a942e-8732-4797-a0c6-ab7955a8d629"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "594c66d7-9c9d-4a44-afa1-b40e3568ba38", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218003110200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a43a266d-8a4f-4aa7-bf49-effc812081f3", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218007082000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f457e01e-a058-43ef-b5c7-6ef22f047beb", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218003106700, "endTime": 31218007186200}, "additional": {"logType": "info", "children": [], "durationId": "1ee38af3-ca4e-49e8-96f2-92cdc9879f39", "parent": "20fb04d0-d9f3-4b6b-a744-05c402db0219"}}, {"head": {"id": "95d4bf82-7046-4da2-8f44-04dde2853d15", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218007209500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f1e5ee6-0778-47e6-a8f8-b7c3fad3e9b3", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218012449000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d692d68c-0bc4-440a-9de6-e70061c4084d", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218012539700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8edc496d-38ab-4818-89bc-72866cf44e98", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218012634200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a104e11-bedb-49d6-b074-539c4a7efdd5", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218012685300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "459a1a64-c570-4c87-a2f2-7dfd325a4e3e", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218014699800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "960aa9ca-45f7-4954-a30f-ef0786719ebe", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218018311700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18852eee-84f5-43c8-a40b-290db585a099", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218026291900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ba04caa-f644-41f6-b210-c05f71a8bea2", "name": "Sdk init in 25 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218043873300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b20b2b25-2ff7-436e-8337-ad32083855c0", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218043990300}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 46}, "markType": "other"}}, {"head": {"id": "01f3c954-c7a7-4d76-b0d7-a6dd88f90724", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218044032700}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 46}, "markType": "other"}}, {"head": {"id": "26713a63-14e3-453b-b08a-f74c3e3e94f1", "name": "Project task initialization takes 16 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218061093400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0babc7b9-8132-4513-994a-81eed9af40ea", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218061189400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e35c7e13-e5b4-4339-9e84-5a8336807aad", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218061219700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "802113bf-3b96-4053-b58f-8b42969e530a", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218061247500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "652a942e-8732-4797-a0c6-ab7955a8d629", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218007201200, "endTime": 31218061270800}, "additional": {"logType": "info", "children": [], "durationId": "e3bb8734-7a41-4d2c-bf52-3fe88fb08253", "parent": "20fb04d0-d9f3-4b6b-a744-05c402db0219"}}, {"head": {"id": "20fb04d0-d9f3-4b6b-a744-05c402db0219", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218003105600, "endTime": 31218061280900}, "additional": {"logType": "info", "children": ["f457e01e-a058-43ef-b5c7-6ef22f047beb", "652a942e-8732-4797-a0c6-ab7955a8d629"], "durationId": "add6a86f-eba5-4a99-a66a-3e6138111fc0", "parent": "34a93de6-5bab-4f93-883b-57dca38e82d7"}}, {"head": {"id": "4627cd21-fb59-428c-bdb5-5e845f666dfc", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218061857700, "endTime": 31218061870500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d7d15ea-81b9-4d89-89a1-14564b8987f0", "logId": "bbaa7afd-6a77-4a05-96d7-e2ead7787104"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bbaa7afd-6a77-4a05-96d7-e2ead7787104", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218061857700, "endTime": 31218061870500}, "additional": {"logType": "info", "children": [], "durationId": "4627cd21-fb59-428c-bdb5-5e845f666dfc", "parent": "34a93de6-5bab-4f93-883b-57dca38e82d7"}}, {"head": {"id": "34a93de6-5bab-4f93-883b-57dca38e82d7", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218001069500, "endTime": 31218061880700}, "additional": {"logType": "info", "children": ["6989f91d-3d71-432e-b4b6-1539e30490fb", "20fb04d0-d9f3-4b6b-a744-05c402db0219", "bbaa7afd-6a77-4a05-96d7-e2ead7787104"], "durationId": "7d7d15ea-81b9-4d89-89a1-14564b8987f0", "parent": "584339f2-e446-4bda-93e9-057d15ffc074"}}, {"head": {"id": "dfce091a-b782-40f3-881f-270f10b3b34d", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218062392800, "endTime": 31218077874300}, "additional": {"children": ["367d930f-bd23-4f50-b363-c4411d0b6c47", "2fa22aeb-91ae-4104-b7aa-ddf02a0aa731", "d0f216d1-fac5-4be2-8d0a-8063d599b369"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8699694e-4fa9-46e2-aa9f-960489019e7c", "logId": "a49df8c8-47f5-4df6-933a-eab61dc1c58a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "367d930f-bd23-4f50-b363-c4411d0b6c47", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218065096600, "endTime": 31218065111200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dfce091a-b782-40f3-881f-270f10b3b34d", "logId": "37bc0a18-0d13-4481-82e0-c2e011de999f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "37bc0a18-0d13-4481-82e0-c2e011de999f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218065096600, "endTime": 31218065111200}, "additional": {"logType": "info", "children": [], "durationId": "367d930f-bd23-4f50-b363-c4411d0b6c47", "parent": "a49df8c8-47f5-4df6-933a-eab61dc1c58a"}}, {"head": {"id": "2fa22aeb-91ae-4104-b7aa-ddf02a0aa731", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218066655800, "endTime": 31218076821700}, "additional": {"children": ["d715d922-3cfc-4af8-9eea-72f7e5519563", "f76ae719-4084-4b85-9714-e90d1bf4622c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dfce091a-b782-40f3-881f-270f10b3b34d", "logId": "5fa7be06-493f-4610-b190-6501d986a73b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d715d922-3cfc-4af8-9eea-72f7e5519563", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218066656800, "endTime": 31218068792400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2fa22aeb-91ae-4104-b7aa-ddf02a0aa731", "logId": "8be68264-5e39-4778-8ad7-e7914308f404"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f76ae719-4084-4b85-9714-e90d1bf4622c", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218068808200, "endTime": 31218076811600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2fa22aeb-91ae-4104-b7aa-ddf02a0aa731", "logId": "c4824378-5a5e-4253-9e5b-a65402daa320"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e8e1ce87-1478-4c77-b6ab-51bfcb90906c", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218066659500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a12992c-4f63-44f6-8ece-aa3e5c31131a", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218068681700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8be68264-5e39-4778-8ad7-e7914308f404", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218066656800, "endTime": 31218068792400}, "additional": {"logType": "info", "children": [], "durationId": "d715d922-3cfc-4af8-9eea-72f7e5519563", "parent": "5fa7be06-493f-4610-b190-6501d986a73b"}}, {"head": {"id": "48041f5c-abbb-42a1-aa25-5cb8947cfa9c", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218068815200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f53f4268-8c03-46f8-9c87-f4305a514502", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218073895400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f798ce10-99b4-45c6-ac79-04900d19e948", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218073979700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9ce5725-fc14-412d-a64a-1d12c0e8ee66", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218074110100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8460efb7-779a-4536-bb26-77dfd28e6bbb", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218074187500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "774fb878-bd7e-4809-af3c-308aaad5d7f9", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218074234000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67e07797-2e13-4c0f-9e83-f764c52fa5b7", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218074261100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39894d79-7ad7-4514-9152-2cb1dc5b3f0d", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218074303200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e53411e-b17a-4ea5-9371-99d680e089d3", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218076633300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7142a189-f300-43a7-a4e3-1cdf807d4de8", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218076723900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78f18ef8-14c2-4050-8e4d-dfb3843e852d", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218076757800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24379a18-42e2-4f77-ab12-961a9e2bfbcc", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218076784000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4824378-5a5e-4253-9e5b-a65402daa320", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218068808200, "endTime": 31218076811600}, "additional": {"logType": "info", "children": [], "durationId": "f76ae719-4084-4b85-9714-e90d1bf4622c", "parent": "5fa7be06-493f-4610-b190-6501d986a73b"}}, {"head": {"id": "5fa7be06-493f-4610-b190-6501d986a73b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218066655800, "endTime": 31218076821700}, "additional": {"logType": "info", "children": ["8be68264-5e39-4778-8ad7-e7914308f404", "c4824378-5a5e-4253-9e5b-a65402daa320"], "durationId": "2fa22aeb-91ae-4104-b7aa-ddf02a0aa731", "parent": "a49df8c8-47f5-4df6-933a-eab61dc1c58a"}}, {"head": {"id": "d0f216d1-fac5-4be2-8d0a-8063d599b369", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218077853400, "endTime": 31218077863100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dfce091a-b782-40f3-881f-270f10b3b34d", "logId": "2baf0413-4540-4a12-bced-23b1a642d68e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2baf0413-4540-4a12-bced-23b1a642d68e", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218077853400, "endTime": 31218077863100}, "additional": {"logType": "info", "children": [], "durationId": "d0f216d1-fac5-4be2-8d0a-8063d599b369", "parent": "a49df8c8-47f5-4df6-933a-eab61dc1c58a"}}, {"head": {"id": "a49df8c8-47f5-4df6-933a-eab61dc1c58a", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218062392800, "endTime": 31218077874300}, "additional": {"logType": "info", "children": ["37bc0a18-0d13-4481-82e0-c2e011de999f", "5fa7be06-493f-4610-b190-6501d986a73b", "2baf0413-4540-4a12-bced-23b1a642d68e"], "durationId": "dfce091a-b782-40f3-881f-270f10b3b34d", "parent": "bca393db-5871-4dbd-803b-e6bd79ddb46c"}}, {"head": {"id": "bca393db-5871-4dbd-803b-e6bd79ddb46c", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218061890600, "endTime": 31218077882000}, "additional": {"logType": "info", "children": ["a49df8c8-47f5-4df6-933a-eab61dc1c58a"], "durationId": "8699694e-4fa9-46e2-aa9f-960489019e7c", "parent": "584339f2-e446-4bda-93e9-057d15ffc074"}}, {"head": {"id": "ee938935-7a78-4223-be68-527fb47ee78b", "name": "watch files: [\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\hvigorfile.ts',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218089289400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33688d0c-ae56-4811-b296-57f94b052ebf", "name": "hvigorfile, resolve hvigorfile dependencies in 12 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218089545900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97e2c753-2971-415f-9090-f6dbfdc79832", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218077891300, "endTime": 31218089600600}, "additional": {"logType": "info", "children": [], "durationId": "56e31bfe-8696-4724-a50c-89979bb59d7a", "parent": "584339f2-e446-4bda-93e9-057d15ffc074"}}, {"head": {"id": "44f7d65b-9175-466a-a15a-c3bfc9f3c534", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218090283000, "endTime": 31218090438400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "00d9290a-995f-43e1-a4e9-2f014e34a9ba", "logId": "9f03020e-b520-46a7-aeac-6ed4ab3748dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f36ec873-fa2a-4aa2-86e4-610646a3eb4c", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218090300200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f03020e-b520-46a7-aeac-6ed4ab3748dc", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218090283000, "endTime": 31218090438400}, "additional": {"logType": "info", "children": [], "durationId": "44f7d65b-9175-466a-a15a-c3bfc9f3c534", "parent": "584339f2-e446-4bda-93e9-057d15ffc074"}}, {"head": {"id": "f9606c5a-c5e7-4ce8-a39a-efa80d5a3d1b", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218091626900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70c87c8e-5b00-4ce1-9a95-1873aa4b43d6", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218097869700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "432d130f-0cd0-4c31-9648-a40e34ef26c5", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218090449500, "endTime": 31218098601500}, "additional": {"logType": "info", "children": [], "durationId": "8a8ce4c2-0101-44d4-8015-8833c5f472eb", "parent": "584339f2-e446-4bda-93e9-057d15ffc074"}}, {"head": {"id": "da963df8-b786-4164-b459-e6a06889047a", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218098646400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e4e9850-07b5-4111-ad52-893a0afa8593", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218103048000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62d917ce-5319-4388-a2d8-8f092c4b7db9", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218103140200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "940e1b5a-2152-40ef-acee-7b7da4ece877", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218103307800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09c07f44-e11c-4c48-b9d1-ca8775c61d91", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218105053600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1aa36f28-5270-4e17-a7d8-dd71ac9758f0", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218105116300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac2f7495-72f5-49c2-a112-f995e34712c5", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218098634600, "endTime": 31218107603100}, "additional": {"logType": "info", "children": [], "durationId": "a9fdbe0a-432a-43fe-a63d-166905977502", "parent": "584339f2-e446-4bda-93e9-057d15ffc074"}}, {"head": {"id": "1043487b-a981-4d07-834f-10e2a829339b", "name": "Configuration phase cost:110 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218107659100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fb47536-13ed-4e67-94ea-491ce359c748", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218107641300, "endTime": 31218107736800}, "additional": {"logType": "info", "children": [], "durationId": "0a8abf96-b7f8-403b-afa2-acc676854644", "parent": "584339f2-e446-4bda-93e9-057d15ffc074"}}, {"head": {"id": "584339f2-e446-4bda-93e9-057d15ffc074", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31217993556200, "endTime": 31218107745800}, "additional": {"logType": "info", "children": ["1d40616b-0cca-42d8-be9e-b79d4c9f21a5", "658034ea-7083-4515-8029-a82fcb399821", "34a93de6-5bab-4f93-883b-57dca38e82d7", "bca393db-5871-4dbd-803b-e6bd79ddb46c", "97e2c753-2971-415f-9090-f6dbfdc79832", "432d130f-0cd0-4c31-9648-a40e34ef26c5", "ac2f7495-72f5-49c2-a112-f995e34712c5", "5fb47536-13ed-4e67-94ea-491ce359c748", "9f03020e-b520-46a7-aeac-6ed4ab3748dc"], "durationId": "00d9290a-995f-43e1-a4e9-2f014e34a9ba", "parent": "c2ab3b67-305d-42f5-a29e-e40560e4fc71"}}, {"head": {"id": "db65790b-f0d2-460a-913f-97c9dca21829", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218108721400, "endTime": 31218108732200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a25af42f-79bd-4b74-b7b1-b48e436127f9", "logId": "6109cf59-7a11-4c6b-b5f3-cc7c2c66d160"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6109cf59-7a11-4c6b-b5f3-cc7c2c66d160", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218108721400, "endTime": 31218108732200}, "additional": {"logType": "info", "children": [], "durationId": "db65790b-f0d2-460a-913f-97c9dca21829", "parent": "c2ab3b67-305d-42f5-a29e-e40560e4fc71"}}, {"head": {"id": "2baa6348-76c0-491f-99d9-ac530c9627f6", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218107760200, "endTime": 31218108740900}, "additional": {"logType": "info", "children": [], "durationId": "44247e9f-bbc3-4b2d-8fee-3277bf9bacd1", "parent": "c2ab3b67-305d-42f5-a29e-e40560e4fc71"}}, {"head": {"id": "85d2bc86-27b9-4e77-9ead-4d0de2a3e09d", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218108743900, "endTime": 31218108745400}, "additional": {"logType": "info", "children": [], "durationId": "a722ef3b-c43f-4b2c-b4b0-c2f25027af3a", "parent": "c2ab3b67-305d-42f5-a29e-e40560e4fc71"}}, {"head": {"id": "c2ab3b67-305d-42f5-a29e-e40560e4fc71", "name": "init", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31217983078000, "endTime": 31218108747900}, "additional": {"logType": "info", "children": ["5a440ad7-b374-4d17-941c-dbdd7b21e0d2", "584339f2-e446-4bda-93e9-057d15ffc074", "2baa6348-76c0-491f-99d9-ac530c9627f6", "85d2bc86-27b9-4e77-9ead-4d0de2a3e09d", "a1cfc9c0-3188-4f27-88fb-aa78a6b1382b", "bc2117e6-c0d3-453c-be7a-4caec2e731f1", "6109cf59-7a11-4c6b-b5f3-cc7c2c66d160"], "durationId": "a25af42f-79bd-4b74-b7b1-b48e436127f9"}}, {"head": {"id": "b55f851a-e8a0-4b19-b934-4a2d593fd358", "name": "Configuration task cost before running: 129 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218108960400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef1a3b94-0194-440c-a79f-9dc8e87bdb1a", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218112832100, "endTime": 31218118761500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "891f8fb1-7edb-425c-bfb6-f0021a9a1fec", "logId": "61c25b40-1acb-48ad-9e49-ba2c573f4da3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "891f8fb1-7edb-425c-bfb6-f0021a9a1fec", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218110113100}, "additional": {"logType": "detail", "children": [], "durationId": "ef1a3b94-0194-440c-a79f-9dc8e87bdb1a"}}, {"head": {"id": "5f4f9b1c-b13e-424b-bec6-87073585a701", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218110526900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c4ec901-e898-4c88-9d1e-1a164896448e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218110592600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7c63679-c8ad-4b93-9bb4-f885c60a832a", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218112840200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b729669c-ddda-46ce-90f1-8368fa1124e1", "name": "Incremental task entry:default@PreBuild pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218118641100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9cd6126-88dd-42be-837f-e2a993065b14", "name": "entry : default@PreBuild cost memory 0.27890777587890625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218118721100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61c25b40-1acb-48ad-9e49-ba2c573f4da3", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218112832100, "endTime": 31218118761500}, "additional": {"logType": "info", "children": [], "durationId": "ef1a3b94-0194-440c-a79f-9dc8e87bdb1a"}}, {"head": {"id": "d7dd4fae-f002-46a8-a27a-9fe1f8d14463", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218122967900, "endTime": 31218124442900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "da9e9002-82da-4c2f-8e49-dd066a80f54a", "logId": "bded0585-7e79-4aab-a101-1a72a48b12be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da9e9002-82da-4c2f-8e49-dd066a80f54a", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218121940600}, "additional": {"logType": "detail", "children": [], "durationId": "d7dd4fae-f002-46a8-a27a-9fe1f8d14463"}}, {"head": {"id": "dcd775f5-bc5c-4d4b-9fdd-4c7dea9c74da", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218122328700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1d0c2fc-010f-4579-b8c6-0bc8c54a75ce", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218122391200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be621b54-dc17-4e0d-b667-02607af79b8b", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218122973400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d21caa8-9db6-464a-a3cc-2d82bb4acce0", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218124323500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8d3e7f2-bcaf-4e85-b44b-5b4f17446006", "name": "entry : default@MergeProfile cost memory 0.1077423095703125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218124401200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bded0585-7e79-4aab-a101-1a72a48b12be", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218122967900, "endTime": 31218124442900}, "additional": {"logType": "info", "children": [], "durationId": "d7dd4fae-f002-46a8-a27a-9fe1f8d14463"}}, {"head": {"id": "eedc981d-1669-4d0c-b1b7-dbc4f11ef73c", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218126795300, "endTime": 31218128551000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a6fa1e13-0e06-4dc9-896f-3d5606878f7d", "logId": "84208aeb-a3ac-4636-8de2-dad5762265d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6fa1e13-0e06-4dc9-896f-3d5606878f7d", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218125740000}, "additional": {"logType": "detail", "children": [], "durationId": "eedc981d-1669-4d0c-b1b7-dbc4f11ef73c"}}, {"head": {"id": "cb89b009-b7ca-482d-bf78-0ed964f0e870", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218126138500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d3c7cbb-cf6a-44d7-8864-c6e695c72f16", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218126210300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c82181ba-42a7-4610-84ab-1d5b2f4fc8ef", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218126801700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68b23dfa-ff2e-42fc-ac51-2b33f33acecd", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218127455900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "404e1e73-66cf-4a83-bea4-dacb59cc1d93", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218128427900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "695f9cbb-b3b4-4cab-be83-680f562f1842", "name": "entry : default@CreateBuildProfile cost memory 0.0951385498046875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218128505400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84208aeb-a3ac-4636-8de2-dad5762265d7", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218126795300, "endTime": 31218128551000}, "additional": {"logType": "info", "children": [], "durationId": "eedc981d-1669-4d0c-b1b7-dbc4f11ef73c"}}, {"head": {"id": "8e890933-fd47-41a5-a4ba-710dd1ca4c07", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218130808900, "endTime": 31218131056500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "c3e4b3ae-e6e8-4412-9fa4-c46c5a12c853", "logId": "e59f78f5-4234-4c81-bec1-7c1a555c74df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3e4b3ae-e6e8-4412-9fa4-c46c5a12c853", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218129775900}, "additional": {"logType": "detail", "children": [], "durationId": "8e890933-fd47-41a5-a4ba-710dd1ca4c07"}}, {"head": {"id": "4df0a2c2-74be-426c-b309-a899e6dfdab9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218130174600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46e78f24-a5b1-4be3-8997-5d97ad278394", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218130240600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ab92294-ba98-4b88-9380-03067dff4e9b", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218130815600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "850a4a18-9ae1-4f54-b164-379e606c9085", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218130901200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e939c464-59c9-4c44-b7ce-555ee0d5e54b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218130933200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b342938-5893-4fdf-a8d8-bcbaaf0dfcb0", "name": "entry : default@PreCheckSyscap cost memory 0.03680419921875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218130982500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f18a29e6-abfc-4534-afa8-ec7198741655", "name": "runTaskFromQueue task cost before running: 151 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218131027900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e59f78f5-4234-4c81-bec1-7c1a555c74df", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218130808900, "endTime": 31218131056500, "totalTime": 207600}, "additional": {"logType": "info", "children": [], "durationId": "8e890933-fd47-41a5-a4ba-710dd1ca4c07"}}, {"head": {"id": "58e61c87-4ad0-4f48-a66a-1481f8ac9efe", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218137587700, "endTime": 31218138347000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6b4b39ef-1e7f-4adb-b2a0-436bff79dc61", "logId": "84798371-1261-413f-954e-25abcb57439e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b4b39ef-1e7f-4adb-b2a0-436bff79dc61", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218132227300}, "additional": {"logType": "detail", "children": [], "durationId": "58e61c87-4ad0-4f48-a66a-1481f8ac9efe"}}, {"head": {"id": "726cbca2-bf80-4c4d-8893-6abc422e4da5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218132648100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e87c409-37c4-4fc2-9d26-2cda530a5db9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218132715900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39c147af-8ec6-41d2-af19-89dcdb62d6e1", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218137597600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77f942e6-f157-4b65-842a-6b5c735f08dd", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218137735500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f0107e4-8993-4675-b395-15ffbb272012", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218138235700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93756341-fa21-48df-b3bd-3baa9f0e0d8e", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06746673583984375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218138305200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84798371-1261-413f-954e-25abcb57439e", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218137587700, "endTime": 31218138347000}, "additional": {"logType": "info", "children": [], "durationId": "58e61c87-4ad0-4f48-a66a-1481f8ac9efe"}}, {"head": {"id": "00fe75b4-ef44-4700-a03a-a8d4471894eb", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218141326300, "endTime": 31218142245100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "9fa3a33b-afda-4b96-8796-1460416cb432", "logId": "9024160b-4b71-48e8-8358-b41d115f17d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9fa3a33b-afda-4b96-8796-1460416cb432", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218139862000}, "additional": {"logType": "detail", "children": [], "durationId": "00fe75b4-ef44-4700-a03a-a8d4471894eb"}}, {"head": {"id": "4a824519-22ba-47f3-a455-e5776d13b830", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218140313800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02231441-3668-4801-aa93-c3ab315cf89d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218140384700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5559df6b-ff96-4c0a-8311-c22bdcf5e528", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218141331900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6b982f0-d339-4d97-9995-9c5fbb7ec744", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218142127500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d0f5059-b002-4131-9854-5a7012944af2", "name": "entry : default@ProcessProfile cost memory 0.05466461181640625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218142201600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9024160b-4b71-48e8-8358-b41d115f17d5", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218141326300, "endTime": 31218142245100}, "additional": {"logType": "info", "children": [], "durationId": "00fe75b4-ef44-4700-a03a-a8d4471894eb"}}, {"head": {"id": "2f6a41dc-5c46-4e57-9167-7fe9500ca44c", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218145908500, "endTime": 31218150389500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a88b3385-f0f2-4876-aa58-988fed4b9ac7", "logId": "7a14cad1-46cd-4409-8776-b29b3a605948"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a88b3385-f0f2-4876-aa58-988fed4b9ac7", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218144122500}, "additional": {"logType": "detail", "children": [], "durationId": "2f6a41dc-5c46-4e57-9167-7fe9500ca44c"}}, {"head": {"id": "c3eb3853-bcc5-4553-a2f1-caf5868aa2b2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218144547400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e37296b2-6fdf-43ce-9d48-bede29bee9e1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218144613800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76d0f944-b6dd-40cc-9bea-cec0d9e4f16d", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218145914000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5074fc6-fd93-424d-8415-54792054465a", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218150266400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "491ea759-9ffb-4198-b471-f0e4295df5d0", "name": "entry : default@ProcessRouterMap cost memory 0.194183349609375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218150344500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a14cad1-46cd-4409-8776-b29b3a605948", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218145908500, "endTime": 31218150389500}, "additional": {"logType": "info", "children": [], "durationId": "2f6a41dc-5c46-4e57-9167-7fe9500ca44c"}}, {"head": {"id": "ebf68f37-ffcb-4124-8fa4-9e90c854f244", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218155229100, "endTime": 31218157130600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "424d1e05-c3c7-4194-a9ae-ffb7b0c60c74", "logId": "a9849220-31d2-4e66-9925-4c024506fe40"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "424d1e05-c3c7-4194-a9ae-ffb7b0c60c74", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218152600300}, "additional": {"logType": "detail", "children": [], "durationId": "ebf68f37-ffcb-4124-8fa4-9e90c854f244"}}, {"head": {"id": "6a2d5e50-1980-41e5-9310-dc4c378898ac", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218153002300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac7c7c65-6219-4a33-8d9a-6204238fe295", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218153066700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8b213a4-d6fe-4d4b-8bbc-46c808f17b32", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218153755000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c6f584a-eec9-425b-8e30-81ba006a3512", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218156027800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e5d8ef5-9a6d-40c6-9aaa-30b6f91cb82b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218156128400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e26805be-c300-4708-ba89-3092844f7476", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218156162600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "953af047-6102-4e83-8824-9f4c34411d93", "name": "entry : default@PreviewProcessResource cost memory 0.07001495361328125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218156211300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01870921-2a0f-4100-a417-af3c5736eedc", "name": "runTaskFromQueue task cost before running: 177 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218157061200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9849220-31d2-4e66-9925-4c024506fe40", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218155229100, "endTime": 31218157130600, "totalTime": 1018700}, "additional": {"logType": "info", "children": [], "durationId": "ebf68f37-ffcb-4124-8fa4-9e90c854f244"}}, {"head": {"id": "6c1f9d38-b89c-4211-b16d-e998d2fba2b3", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218162190800, "endTime": 31218176911900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "612b7dae-b458-42f8-8cad-064942ad0cdb", "logId": "d97508f0-01ae-4173-a579-e94b041b810c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "612b7dae-b458-42f8-8cad-064942ad0cdb", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218159222200}, "additional": {"logType": "detail", "children": [], "durationId": "6c1f9d38-b89c-4211-b16d-e998d2fba2b3"}}, {"head": {"id": "da09559c-fb9d-4054-99f8-6b1eed3277b0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218159710500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "011d481c-45ea-49e6-86c5-87ff7ff136f1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218159791600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e0aee58-3e02-4ffc-9344-3c3b6541fd73", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218162201500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fd09ac6-3b21-4cf6-8a28-a4749a3b44ed", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218176759200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "892118a3-20bf-4490-9edc-f86d4abd0a5e", "name": "entry : default@GenerateLoaderJson cost memory -0.962646484375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218176864700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d97508f0-01ae-4173-a579-e94b041b810c", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218162190800, "endTime": 31218176911900}, "additional": {"logType": "info", "children": [], "durationId": "6c1f9d38-b89c-4211-b16d-e998d2fba2b3"}}, {"head": {"id": "7b0671f5-c59a-4d5a-8718-8cfcc90d060c", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218185322400, "endTime": 31218366153600}, "additional": {"children": ["3e6645c3-89e1-47e4-ba92-a5a03f3f0c4c", "d69340f5-204e-406b-901f-4e14269e00d3", "3a36e795-c2a2-4246-9152-e594a5da1974", "42e3c721-e848-43c1-ba8b-d2c0fe90349c"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "c9fc053f-da7c-4a04-8b21-940e4509b76c", "logId": "8293eba3-d291-4a74-9f18-d431c3388f9f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9fc053f-da7c-4a04-8b21-940e4509b76c", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218182286800}, "additional": {"logType": "detail", "children": [], "durationId": "7b0671f5-c59a-4d5a-8718-8cfcc90d060c"}}, {"head": {"id": "06efb4d4-33a8-4cd1-b216-d2aaba235e1e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218182697800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64df1e29-4e92-4087-b0ad-7f7683ea2576", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218182766700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5055fb9-79ef-4087-94f2-0089ad1ed33c", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218183667000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49e0f452-3170-43fd-9968-a63dfa3edcab", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218185343300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef49753a-1f1f-4fe6-a10e-8517f776ecd4", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218201246500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9220391e-f68c-43c0-b499-d481e743ab67", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 16 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218201459200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e6645c3-89e1-47e4-ba92-a5a03f3f0c4c", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218202720700, "endTime": 31218225849000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b0671f5-c59a-4d5a-8718-8cfcc90d060c", "logId": "df94a555-0d6a-4d35-b5d4-b0cb146d6770"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df94a555-0d6a-4d35-b5d4-b0cb146d6770", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218202720700, "endTime": 31218225849000}, "additional": {"logType": "info", "children": [], "durationId": "3e6645c3-89e1-47e4-ba92-a5a03f3f0c4c", "parent": "8293eba3-d291-4a74-9f18-d431c3388f9f"}}, {"head": {"id": "5a5ec38d-795b-4279-ad15-c8d6f8a4df51", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218226136700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d69340f5-204e-406b-901f-4e14269e00d3", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218226886200, "endTime": 31218259957100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b0671f5-c59a-4d5a-8718-8cfcc90d060c", "logId": "9adf7cd9-bea2-4123-9dee-9b10723b842f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed7c7938-a1cd-4368-940c-ca821110085c", "name": "current process  memoryUsage: {\n  rss: 114450432,\n  heapTotal: 127229952,\n  heapUsed: 110733352,\n  external: 3108407,\n  arrayBuffers: 102308\n} os memoryUsage :13.512767791748047", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218227617000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3e6c26c-7792-4402-9a5a-9213cefcdef7", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218257519000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9adf7cd9-bea2-4123-9dee-9b10723b842f", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218226886200, "endTime": 31218259957100}, "additional": {"logType": "info", "children": [], "durationId": "d69340f5-204e-406b-901f-4e14269e00d3", "parent": "8293eba3-d291-4a74-9f18-d431c3388f9f"}}, {"head": {"id": "98cf9bea-aff6-4565-a805-9bc214eb8a8c", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218260061600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a36e795-c2a2-4246-9152-e594a5da1974", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218260918500, "endTime": 31218300060800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b0671f5-c59a-4d5a-8718-8cfcc90d060c", "logId": "9eab91e1-f1f2-44df-91d5-b98dcde5512d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a50a9cd5-65b2-467d-870f-caab0b03ec6c", "name": "current process  memoryUsage: {\n  rss: 114450432,\n  heapTotal: 127229952,\n  heapUsed: 110996712,\n  external: 3108533,\n  arrayBuffers: 102449\n} os memoryUsage :13.504779815673828", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218261717100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef7b6351-62f5-4945-a988-61ab33e41a93", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218298024200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9eab91e1-f1f2-44df-91d5-b98dcde5512d", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218260918500, "endTime": 31218300060800}, "additional": {"logType": "info", "children": [], "durationId": "3a36e795-c2a2-4246-9152-e594a5da1974", "parent": "8293eba3-d291-4a74-9f18-d431c3388f9f"}}, {"head": {"id": "e8238a9e-aecf-4ece-b228-f7e32e89a86e", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218300310300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42e3c721-e848-43c1-ba8b-d2c0fe90349c", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218301637300, "endTime": 31218365055700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b0671f5-c59a-4d5a-8718-8cfcc90d060c", "logId": "83e6ff91-1b79-45a3-b69b-53cdd167023f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c74cdeb-7941-48d9-8042-ff06f9dc3862", "name": "current process  memoryUsage: {\n  rss: 114458624,\n  heapTotal: 127229952,\n  heapUsed: 111290600,\n  external: 3108659,\n  arrayBuffers: 103459\n} os memoryUsage :13.517677307128906", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218302561500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d3064ad-43e7-4244-8520-15efa8c7213a", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218362129000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83e6ff91-1b79-45a3-b69b-53cdd167023f", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218301637300, "endTime": 31218365055700}, "additional": {"logType": "info", "children": [], "durationId": "42e3c721-e848-43c1-ba8b-d2c0fe90349c", "parent": "8293eba3-d291-4a74-9f18-d431c3388f9f"}}, {"head": {"id": "24a5c6f6-f8ae-47bb-9720-075be3688438", "name": "entry : default@PreviewCompileResource cost memory 0.28882598876953125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218365952300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f88c2590-079f-4ba7-9f8e-36b2e41d678c", "name": "runTaskFromQueue task cost before running: 386 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218366108100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8293eba3-d291-4a74-9f18-d431c3388f9f", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218185322400, "endTime": 31218366153600, "totalTime": 180738700}, "additional": {"logType": "info", "children": ["df94a555-0d6a-4d35-b5d4-b0cb146d6770", "9adf7cd9-bea2-4123-9dee-9b10723b842f", "9eab91e1-f1f2-44df-91d5-b98dcde5512d", "83e6ff91-1b79-45a3-b69b-53cdd167023f"], "durationId": "7b0671f5-c59a-4d5a-8718-8cfcc90d060c"}}, {"head": {"id": "bb1a49ab-d298-47c5-9a09-7cd106d44c2b", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218368964100, "endTime": 31218369164700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "6b5cfd94-7d8d-48a9-9aaf-2275262eeba3", "logId": "213f45e9-ccab-4e38-b8aa-07038daa055e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b5cfd94-7d8d-48a9-9aaf-2275262eeba3", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218368338100}, "additional": {"logType": "detail", "children": [], "durationId": "bb1a49ab-d298-47c5-9a09-7cd106d44c2b"}}, {"head": {"id": "4256645e-f1c0-4e40-80f6-d1c6c1c99197", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218368795800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ba92225-1520-411c-a7e3-f41bde8284a0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218368883700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afdd1f4a-e88f-47c5-ac28-fc21bc507504", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218368969700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec100bc5-dc3d-44b3-81d2-9cb76df3a852", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218369029300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4ed218a-0c97-4c35-80c3-78bd769d5be1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218369051900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fad507aa-d683-4425-8ed8-06fed0a7e805", "name": "entry : default@PreviewHookCompileResource cost memory 0.03792572021484375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218369091200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f9bd4ff-9188-4064-8146-2c5dfcbaeb4f", "name": "runTaskFromQueue task cost before running: 389 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218369138800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "213f45e9-ccab-4e38-b8aa-07038daa055e", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218368964100, "endTime": 31218369164700, "totalTime": 163400}, "additional": {"logType": "info", "children": [], "durationId": "bb1a49ab-d298-47c5-9a09-7cd106d44c2b"}}, {"head": {"id": "8f89ce88-4617-45f6-a934-e3bac05dd406", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218371324100, "endTime": 31218377154100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "c8b533e0-dfd2-4f83-8cc3-813d2cd6a0e5", "logId": "1ec516e5-ee90-478c-bb49-190aeac2473b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8b533e0-dfd2-4f83-8cc3-813d2cd6a0e5", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218370355600}, "additional": {"logType": "detail", "children": [], "durationId": "8f89ce88-4617-45f6-a934-e3bac05dd406"}}, {"head": {"id": "0ebbf416-2628-4c05-9ab7-5a55318fbeed", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218370767500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfae4d93-7842-495f-ac74-75371e7391b8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218370840300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91302d7c-cc4b-42e9-aa43-495ca77b8556", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218371331400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5be5b99a-f6f2-4c7c-ab2b-a6d1f1bd16c4", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218372300500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44e4ef01-64ef-4850-a98a-ccdfaa536b54", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218372389100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83ed5e1a-573d-49c1-a0d2-f19b466b1381", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218372437200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96b1a1bc-1507-420d-8791-2b6de64a87e6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218372462300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12663cc1-90dc-4c40-846d-b820b9b8b47b", "name": "entry : default@CopyPreviewProfile cost memory 0.22303009033203125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218376954500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5eb69bc8-0cf9-4b38-9a03-f8e26e75f90f", "name": "runTaskFromQueue task cost before running: 397 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218377110200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ec516e5-ee90-478c-bb49-190aeac2473b", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218371324100, "endTime": 31218377154100, "totalTime": 5761400}, "additional": {"logType": "info", "children": [], "durationId": "8f89ce88-4617-45f6-a934-e3bac05dd406"}}, {"head": {"id": "e4745e5d-d19a-4ea7-b9b5-107de2688e63", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218379738000, "endTime": 31218380423400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "a7abffc6-62ce-4e40-93e2-49c06e397afe", "logId": "1f7beb92-7841-4c51-91ea-8793f71b24b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7abffc6-62ce-4e40-93e2-49c06e397afe", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218378699700}, "additional": {"logType": "detail", "children": [], "durationId": "e4745e5d-d19a-4ea7-b9b5-107de2688e63"}}, {"head": {"id": "466ab591-1d85-4650-8f2d-194e3085d6d7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218379124100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06078d70-a74b-4afc-8a3b-e1f7f525d5f0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218379204800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bc6574a-9ecc-41fc-b154-522c2be465a7", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218379744700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cc2d4f1-4e3a-4def-b089-adb954f7124d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218379831100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db28de9c-a1a7-47d2-90bc-af3802a72328", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218379871000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6c12558-05f1-4753-bfa6-09346583f5f7", "name": "entry : default@ReplacePreviewerPage cost memory -1.7189788818359375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218380305400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83a79bd6-c428-4abe-a8e2-a0a86ec16bd6", "name": "runTaskFromQueue task cost before running: 401 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218380391900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f7beb92-7841-4c51-91ea-8793f71b24b4", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218379738000, "endTime": 31218380423400, "totalTime": 621000}, "additional": {"logType": "info", "children": [], "durationId": "e4745e5d-d19a-4ea7-b9b5-107de2688e63"}}, {"head": {"id": "9bf7ef93-1311-4cdc-8f05-5119249924cd", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218381674200, "endTime": 31218381844900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "12be919a-699f-4d6d-a58b-f967cd41e192", "logId": "e66789f7-f574-4ebb-a517-6edf7e122cf0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12be919a-699f-4d6d-a58b-f967cd41e192", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218381646700}, "additional": {"logType": "detail", "children": [], "durationId": "9bf7ef93-1311-4cdc-8f05-5119249924cd"}}, {"head": {"id": "26815561-5e49-4870-b964-f8a11ac46e46", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218381678700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fb2be07-0b0f-40ab-bff8-0e19c0b45ed2", "name": "entry : buildPreviewerResource cost memory 0.0116424560546875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218381758800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb53bc7c-e995-402c-af9a-ae86dc873e89", "name": "runTaskFromQueue task cost before running: 402 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218381816200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e66789f7-f574-4ebb-a517-6edf7e122cf0", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218381674200, "endTime": 31218381844900, "totalTime": 131000}, "additional": {"logType": "info", "children": [], "durationId": "9bf7ef93-1311-4cdc-8f05-5119249924cd"}}, {"head": {"id": "5f39802e-9ca3-4baa-ac2c-4e013a7c4973", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218384027100, "endTime": 31218387607300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "7855443c-1445-4fc9-8cf0-7c50417a6cdc", "logId": "c4f51a68-8b25-4fa3-a600-ac4a9b4c1083"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7855443c-1445-4fc9-8cf0-7c50417a6cdc", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218382957600}, "additional": {"logType": "detail", "children": [], "durationId": "5f39802e-9ca3-4baa-ac2c-4e013a7c4973"}}, {"head": {"id": "d142225c-bebd-4192-8392-e76c2e3ad0e6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218383378600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3ee6b0b-b30d-4f12-95a8-8d50ff297c41", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218383457800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f56262b9-6044-416f-b405-e60933b5d140", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218384033700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e13af56e-f2ce-443f-ad77-f89634a0461f", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218385736000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e39a6610-c28e-4441-a282-08ee9d5b87d6", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218385822700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8391f63-e133-48bf-afcb-f2c9fa789c8d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218385888200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d22528c4-924f-4a5e-bf12-460bd06dc58d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218385914900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97b3a655-7c8f-4c1f-9693-c3e28015ef7e", "name": "entry : default@PreviewUpdateAssets cost memory 0.1499786376953125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218387474300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f75963a5-9c84-4e19-a86e-2ddf5b889631", "name": "runTaskFromQueue task cost before running: 408 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218387567800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4f51a68-8b25-4fa3-a600-ac4a9b4c1083", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218384027100, "endTime": 31218387607300, "totalTime": 3524400}, "additional": {"logType": "info", "children": [], "durationId": "5f39802e-9ca3-4baa-ac2c-4e013a7c4973"}}, {"head": {"id": "f82a7a72-5b52-456c-aaab-99e1100e0226", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218393685900, "endTime": 31229399708600}, "additional": {"children": ["9e3a3e27-b62d-4140-92d4-d1e5dd4918c7"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1bcc22aa-292d-417d-bed1-3d0c2026d957", "logId": "433e4a10-8a7a-4f47-aac6-e101e00be559"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1bcc22aa-292d-417d-bed1-3d0c2026d957", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218389470100}, "additional": {"logType": "detail", "children": [], "durationId": "f82a7a72-5b52-456c-aaab-99e1100e0226"}}, {"head": {"id": "2bdfe117-f4a1-44df-be68-70738cc61e42", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218389874600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35c6aba7-5292-48fa-9cdf-85dd67f6ee4a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218389951000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "870190ba-6e2d-4a3b-863e-71d2da72933b", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218393693400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e3a3e27-b62d-4140-92d4-d1e5dd4918c7", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker17", "startTime": 31218409405500, "endTime": 31229398929400}, "additional": {"children": ["f3067a9f-056d-4035-a89e-63e6e0ded952", "edc8cd7e-339a-43bd-996e-4d54058094d8", "68fbd1a5-c438-472e-b6ed-0cfff01cfa83", "26aabba8-0e6d-405f-a00d-f2fe521226cd"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "f82a7a72-5b52-456c-aaab-99e1100e0226", "logId": "be71878a-1466-400b-9dad-8e85b0a6a0c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e6c0abc-502f-4662-a99f-bf3e65a44cb9", "name": "entry : default@PreviewArkTS cost memory -0.53057861328125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218411333900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "276a4216-e5d9-4b43-8cd2-e04437ecf771", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31221867516000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3067a9f-056d-4035-a89e-63e6e0ded952", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31221868570300, "endTime": 31221868586100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9e3a3e27-b62d-4140-92d4-d1e5dd4918c7", "logId": "264ab80d-67c0-45dc-83a3-351ea50f94a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "264ab80d-67c0-45dc-83a3-351ea50f94a7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31221868570300, "endTime": 31221868586100}, "additional": {"logType": "info", "children": [], "durationId": "f3067a9f-056d-4035-a89e-63e6e0ded952", "parent": "be71878a-1466-400b-9dad-8e85b0a6a0c0"}}, {"head": {"id": "2eba6bf4-44c4-41f4-be18-2c9589d06d72", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31226182704700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edc8cd7e-339a-43bd-996e-4d54058094d8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31226185091100, "endTime": 31226185114100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9e3a3e27-b62d-4140-92d4-d1e5dd4918c7", "logId": "90dcb6f3-486c-4dde-adf7-0159a7c3d6ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "90dcb6f3-486c-4dde-adf7-0159a7c3d6ce", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31226185091100, "endTime": 31226185114100}, "additional": {"logType": "info", "children": [], "durationId": "edc8cd7e-339a-43bd-996e-4d54058094d8", "parent": "be71878a-1466-400b-9dad-8e85b0a6a0c0"}}, {"head": {"id": "145af294-b3fe-4c31-b985-1937aab3638a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31226185225700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68fbd1a5-c438-472e-b6ed-0cfff01cfa83", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31226186580600, "endTime": 31226186606000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9e3a3e27-b62d-4140-92d4-d1e5dd4918c7", "logId": "f2bd8395-d1b9-4bad-837f-65c234eda013"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f2bd8395-d1b9-4bad-837f-65c234eda013", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31226186580600, "endTime": 31226186606000}, "additional": {"logType": "info", "children": [], "durationId": "68fbd1a5-c438-472e-b6ed-0cfff01cfa83", "parent": "be71878a-1466-400b-9dad-8e85b0a6a0c0"}}, {"head": {"id": "e4cc8ef2-eb2a-43b2-aaa2-c85349c8be4f", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229397186500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26aabba8-0e6d-405f-a00d-f2fe521226cd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229398585100, "endTime": 31229398600600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9e3a3e27-b62d-4140-92d4-d1e5dd4918c7", "logId": "a2f1766f-106c-409a-baba-70d06e560767"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a2f1766f-106c-409a-baba-70d06e560767", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229398585100, "endTime": 31229398600600}, "additional": {"logType": "info", "children": [], "durationId": "26aabba8-0e6d-405f-a00d-f2fe521226cd", "parent": "be71878a-1466-400b-9dad-8e85b0a6a0c0"}}, {"head": {"id": "be71878a-1466-400b-9dad-8e85b0a6a0c0", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Worker17", "startTime": 31218409405500, "endTime": 31229398929400}, "additional": {"logType": "error", "children": ["264ab80d-67c0-45dc-83a3-351ea50f94a7", "90dcb6f3-486c-4dde-adf7-0159a7c3d6ce", "f2bd8395-d1b9-4bad-837f-65c234eda013", "a2f1766f-106c-409a-baba-70d06e560767"], "durationId": "9e3a3e27-b62d-4140-92d4-d1e5dd4918c7", "parent": "433e4a10-8a7a-4f47-aac6-e101e00be559"}}, {"head": {"id": "2efe26f0-2475-46c6-9d2f-1b1f6c733b1d", "name": "default@PreviewArkTS watch work[17] failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229398969100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "433e4a10-8a7a-4f47-aac6-e101e00be559", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31218393685900, "endTime": 31229399708600}, "additional": {"logType": "error", "children": ["be71878a-1466-400b-9dad-8e85b0a6a0c0"], "durationId": "f82a7a72-5b52-456c-aaab-99e1100e0226"}}, {"head": {"id": "08669810-cb78-4793-b455-629770d4cddc", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229399999000}, "additional": {"logType": "debug", "children": [], "durationId": "f82a7a72-5b52-456c-aaab-99e1100e0226"}}, {"head": {"id": "e5b9e6b6-c580-4e6c-be85-54dfcad0d372", "name": "ERROR: stacktrace = Error: \u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:5:10\n Duplicate identifier 'storageManager'.\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:15:10\n Duplicate identifier 'storageManager'.\n\n    at handleResponse (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229401011500}, "additional": {"logType": "debug", "children": [], "durationId": "f82a7a72-5b52-456c-aaab-99e1100e0226"}}, {"head": {"id": "066fbcc9-be47-4fb3-be70-cf608bca461f", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229410009000, "endTime": 31229410055500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b082ba5b-25f2-499c-84b8-d72d5f12fb87", "logId": "adcc1432-d84d-44b7-960d-adfd22f15465"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "adcc1432-d84d-44b7-960d-adfd22f15465", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229410009000, "endTime": 31229410055500}, "additional": {"logType": "info", "children": [], "durationId": "066fbcc9-be47-4fb3-be70-cf608bca461f"}}, {"head": {"id": "4f8eeaf6-d65c-4a5e-b573-e2c745e0094e", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31217980318500, "endTime": 31229410148100}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 46}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "edd76a07-c198-45e7-8c70-45745553fbdd", "name": "BUILD FAILED in 11 s 430 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229410169800}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "1eee6c1f-a30e-420c-bd7f-9ed49806b5e7", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229410288800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b25864b4-0f50-40d3-84f8-fe1ea5677f58", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229410323600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f4fcb87-5ac4-45b0-b14f-cbf064d72657", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229410345500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46a2a187-dd29-463c-b567-f07771ab6d6f", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229410377500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a8c112c-f7e0-4802-959c-78729b45d836", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229410399800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "638f6d81-633f-42db-927b-47fa1add86cc", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229410420800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6de07931-38df-4ac2-be9c-f764a151deff", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229410444000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be7bf0d5-a793-4fd4-9a16-0959e7cb7fde", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229411331700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a52ebf1-904a-482d-aac6-c4791038f4e4", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229417560900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96b14790-004c-41bf-8afb-5567105bc637", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229417999900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e39653d6-b794-43d9-bb60-38c89099fd40", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229425667200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8938ccd8-f874-44d2-b577-4cdf8b746595", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:16 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229426464100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5f21bb9-59f3-49ff-b825-80a3c9a7603b", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229426666600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "877a6138-fc0f-4da7-b4f4-88863540d984", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229428072500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "402a0b4c-e989-46f4-b0ab-a6ecadb71ceb", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229428728300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1f2cc65-0714-4d5b-8927-a3dbae752393", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229429109000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbb74491-270b-4722-9c47-890512144ca6", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229429357300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7aebe411-76e1-4fdf-b7cf-3e1a20955a7d", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229429589300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "546cb99a-4226-4fa3-804b-0df9db5a1944", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229431833400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59b82d04-abb7-4c56-9df6-02e5751c9d65", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229432414100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb2b97b7-9f5d-4f8f-af6d-7a74b6948ca3", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229432622400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecd26c55-aefc-46fa-b054-49ee1af1c6e6", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229432820800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc99ccfd-9899-4d59-a9a9-c5e67506c921", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229433375600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d3b08f3-5603-42ad-ab54-ae7eae6b7a74", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229442083300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b55da82b-a471-483d-aeca-c44be4da0ffe", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229442360200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60bba8c9-f6dd-41d7-a0fa-ddc2a992fd8a", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229442679900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "708db876-2aae-488b-9bf8-2b9b35b170f4", "name": "Update task entry:default@PreviewArkTS output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229443450000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adae96de-ef25-40cb-a1f0-cd11b9391a28", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:15 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229443710500}, "additional": {"logType": "debug", "children": []}}], "workLog": []}