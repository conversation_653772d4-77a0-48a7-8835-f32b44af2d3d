if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface LoginPage_Params {
    phone?: string;
    password?: string;
    code?: string;
    isLoading?: boolean;
    currentTab?: number;
    countdown?: number;
    canSendCode?: boolean;
    showNetworkSettings?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import type { SendCodeRequest, SpringBootPasswordLoginRequest, SpringBootSmsLoginRequest, SpringBootUserResponse } from '../common/types/index';
import { storageManager } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
import type { LocalUserInfo } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
import { httpClient } from "@normalized:N&&&entry/src/main/ets/common/http/HttpClient&";
import type common from "@ohos:app.ability.common";
class LoginPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__phone = new ObservedPropertySimplePU('', this, "phone");
        this.__password = new ObservedPropertySimplePU('', this, "password");
        this.__code = new ObservedPropertySimplePU('', this, "code");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__currentTab = new ObservedPropertySimplePU(0, this, "currentTab");
        this.__countdown = new ObservedPropertySimplePU(0, this, "countdown");
        this.__canSendCode = new ObservedPropertySimplePU(true, this, "canSendCode");
        this.__showNetworkSettings = new ObservedPropertySimplePU(false, this, "showNetworkSettings");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: LoginPage_Params) {
        if (params.phone !== undefined) {
            this.phone = params.phone;
        }
        if (params.password !== undefined) {
            this.password = params.password;
        }
        if (params.code !== undefined) {
            this.code = params.code;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.currentTab !== undefined) {
            this.currentTab = params.currentTab;
        }
        if (params.countdown !== undefined) {
            this.countdown = params.countdown;
        }
        if (params.canSendCode !== undefined) {
            this.canSendCode = params.canSendCode;
        }
        if (params.showNetworkSettings !== undefined) {
            this.showNetworkSettings = params.showNetworkSettings;
        }
    }
    updateStateVars(params: LoginPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__phone.purgeDependencyOnElmtId(rmElmtId);
        this.__password.purgeDependencyOnElmtId(rmElmtId);
        this.__code.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__currentTab.purgeDependencyOnElmtId(rmElmtId);
        this.__countdown.purgeDependencyOnElmtId(rmElmtId);
        this.__canSendCode.purgeDependencyOnElmtId(rmElmtId);
        this.__showNetworkSettings.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__phone.aboutToBeDeleted();
        this.__password.aboutToBeDeleted();
        this.__code.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__currentTab.aboutToBeDeleted();
        this.__countdown.aboutToBeDeleted();
        this.__canSendCode.aboutToBeDeleted();
        this.__showNetworkSettings.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __phone: ObservedPropertySimplePU<string>;
    get phone() {
        return this.__phone.get();
    }
    set phone(newValue: string) {
        this.__phone.set(newValue);
    }
    private __password: ObservedPropertySimplePU<string>;
    get password() {
        return this.__password.get();
    }
    set password(newValue: string) {
        this.__password.set(newValue);
    }
    private __code: ObservedPropertySimplePU<string>;
    get code() {
        return this.__code.get();
    }
    set code(newValue: string) {
        this.__code.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __currentTab: ObservedPropertySimplePU<number>; // 0: 密码登录, 1: 验证码登录
    get currentTab() {
        return this.__currentTab.get();
    }
    set currentTab(newValue: number) {
        this.__currentTab.set(newValue);
    }
    private __countdown: ObservedPropertySimplePU<number>; // 验证码倒计时
    get countdown() {
        return this.__countdown.get();
    }
    set countdown(newValue: number) {
        this.__countdown.set(newValue);
    }
    private __canSendCode: ObservedPropertySimplePU<boolean>; // 是否可以发送验证码
    get canSendCode() {
        return this.__canSendCode.get();
    }
    set canSendCode(newValue: boolean) {
        this.__canSendCode.set(newValue);
    }
    private __showNetworkSettings: ObservedPropertySimplePU<boolean>; // 显示网络设置
    get showNetworkSettings() {
        return this.__showNetworkSettings.get();
    }
    set showNetworkSettings(newValue: boolean) {
        this.__showNetworkSettings.set(newValue);
    }
    async aboutToAppear() {
        // 页面即将出现时的初始化逻辑
        console.log('LoginPage aboutToAppear');
        // 确保StorageManager已初始化
        await this.ensureStorageInitialized();
    }
    /**
     * 确保StorageManager已初始化
     */
    private async ensureStorageInitialized() {
        try {
            // 尝试获取context
            const context = getContext(this) as common.UIAbilityContext;
            // 初始化StorageManager（如果还没有初始化）
            await storageManager.init(context);
            console.log('StorageManager 初始化成功');
        }
        catch (error) {
            console.error('StorageManager 初始化失败:', error);
            // 如果初始化失败，可以显示提示但不阻止用户使用
            promptAction.showToast({ message: '存储初始化失败，部分功能可能受影响' });
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(53:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#FFFFFF');
            Column.bindSheet({ value: this.showNetworkSettings, changeEvent: newValue => { this.showNetworkSettings = newValue; } }, { builder: () => {
                    this.NetworkSettingsSheet.call(this);
                } }, {
                height: 400,
                showClose: true,
                dragBar: true,
                onDisappear: () => {
                    this.showNetworkSettings = false;
                }
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部网络设置按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/LoginPage.ets(55:7)", "entry");
            // 顶部网络设置按钮
            Row.width('100%');
            // 顶部网络设置按钮
            Row.padding({ left: 16, right: 16, top: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/LoginPage.ets(56:9)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('网络设置');
            Button.debugLine("entry/src/main/ets/pages/LoginPage.ets(57:9)", "entry");
            Button.fontSize(14);
            Button.fontColor('#1976D2');
            Button.backgroundColor(Color.Transparent);
            Button.border({ width: 1, color: '#1976D2', radius: 16 });
            Button.height(32);
            Button.padding({ left: 12, right: 12 });
            Button.onClick(() => {
                this.showNetworkSettings = true;
            });
        }, Button);
        Button.pop();
        // 顶部网络设置按钮
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部Logo区域
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(72:7)", "entry");
            // 顶部Logo区域
            Column.margin({ top: 40, bottom: 60 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777239, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/LoginPage.ets(73:9)", "entry");
            Image.width(80);
            Image.height(80);
            Image.margin({ bottom: 16 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('E-Wallet');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(78:9)", "entry");
            Text.fontSize(28);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1976D2');
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('欢迎使用电子钱包');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(84:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#666666');
        }, Text);
        Text.pop();
        // 顶部Logo区域
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 登录表单
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(91:7)", "entry");
            // 登录表单
            Column.width('100%');
            // 登录表单
            Column.padding({ left: 24, right: 24 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // Tab切换
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/LoginPage.ets(93:9)", "entry");
            // Tab切换
            Row.width('100%');
            // Tab切换
            Row.margin({ bottom: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('密码登录');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(94:11)", "entry");
            Text.fontSize(16);
            Text.fontColor(this.currentTab === 0 ? '#1976D2' : '#666666');
            Text.fontWeight(this.currentTab === 0 ? FontWeight.Bold : FontWeight.Normal);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
            Text.padding({ top: 12, bottom: 12 });
            Text.onClick(() => {
                this.currentTab = 0;
            });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('验证码登录');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(105:11)", "entry");
            Text.fontSize(16);
            Text.fontColor(this.currentTab === 1 ? '#1976D2' : '#666666');
            Text.fontWeight(this.currentTab === 1 ? FontWeight.Bold : FontWeight.Normal);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
            Text.padding({ top: 12, bottom: 12 });
            Text.onClick(() => {
                this.currentTab = 1;
            });
        }, Text);
        Text.pop();
        // Tab切换
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // Tab指示器
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/LoginPage.ets(120:9)", "entry");
            // Tab指示器
            Row.width('100%');
            // Tab指示器
            Row.margin({ bottom: 30 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/LoginPage.ets(121:11)", "entry");
            Divider.width('50%');
            Divider.height(2);
            Divider.color(this.currentTab === 0 ? '#1976D2' : '#E0E0E0');
        }, Divider);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/LoginPage.ets(126:11)", "entry");
            Divider.width('50%');
            Divider.height(2);
            Divider.color(this.currentTab === 1 ? '#1976D2' : '#E0E0E0');
        }, Divider);
        // Tab指示器
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 手机号输入框（两种登录方式都需要）
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(135:9)", "entry");
            // 手机号输入框（两种登录方式都需要）
            Column.alignItems(HorizontalAlign.Start);
            // 手机号输入框（两种登录方式都需要）
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('手机号');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(136:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入手机号', text: this.phone });
            TextInput.debugLine("entry/src/main/ets/pages/LoginPage.ets(142:11)", "entry");
            TextInput.type(InputType.PhoneNumber);
            TextInput.maxLength(11);
            TextInput.fontSize(16);
            TextInput.height(48);
            TextInput.borderRadius(8);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E0E0E0' });
            TextInput.onChange((value: string) => {
                this.phone = value;
            });
        }, TextInput);
        // 手机号输入框（两种登录方式都需要）
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 密码输入框（仅密码登录时显示）
            if (this.currentTab === 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(159:11)", "entry");
                        Column.alignItems(HorizontalAlign.Start);
                        Column.margin({ bottom: 30 });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('密码');
                        Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(160:13)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#333333');
                        Text.alignSelf(ItemAlign.Start);
                        Text.margin({ bottom: 8 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        TextInput.create({ placeholder: '请输入密码', text: this.password });
                        TextInput.debugLine("entry/src/main/ets/pages/LoginPage.ets(166:13)", "entry");
                        TextInput.type(InputType.Password);
                        TextInput.fontSize(16);
                        TextInput.height(48);
                        TextInput.borderRadius(8);
                        TextInput.backgroundColor('#F8F9FA');
                        TextInput.border({ width: 1, color: '#E0E0E0' });
                        TextInput.showPasswordIcon(true);
                        TextInput.onChange((value: string) => {
                            this.password = value;
                        });
                    }, TextInput);
                    Column.pop();
                });
            }
            // 验证码输入框（仅验证码登录时显示）
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 验证码输入框（仅验证码登录时显示）
            if (this.currentTab === 1) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(184:11)", "entry");
                        Column.alignItems(HorizontalAlign.Start);
                        Column.margin({ bottom: 30 });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('验证码');
                        Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(185:13)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#333333');
                        Text.alignSelf(ItemAlign.Start);
                        Text.margin({ bottom: 8 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/LoginPage.ets(191:13)", "entry");
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        TextInput.create({ placeholder: '请输入验证码', text: this.code });
                        TextInput.debugLine("entry/src/main/ets/pages/LoginPage.ets(192:15)", "entry");
                        TextInput.type(InputType.Number);
                        TextInput.maxLength(6);
                        TextInput.fontSize(16);
                        TextInput.height(48);
                        TextInput.borderRadius(8);
                        TextInput.backgroundColor('#F8F9FA');
                        TextInput.border({ width: 1, color: '#E0E0E0' });
                        TextInput.layoutWeight(1);
                        TextInput.onChange((value: string) => {
                            this.code = value;
                        });
                    }, TextInput);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel(this.countdown > 0 ? `${this.countdown}s` : '获取验证码');
                        Button.debugLine("entry/src/main/ets/pages/LoginPage.ets(205:15)", "entry");
                        Button.fontSize(12);
                        Button.fontColor(this.canSendCode ? '#1976D2' : '#999999');
                        Button.backgroundColor(this.canSendCode ? '#E3F2FD' : '#F5F5F5');
                        Button.width(100);
                        Button.height(48);
                        Button.enabled(this.canSendCode && this.phone.length === 11);
                        Button.onClick(() => {
                            this.sendSmsCode();
                        });
                    }, Button);
                    Button.pop();
                    Row.pop();
                    Column.pop();
                });
            }
            // 登录按钮
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 登录按钮
            Button.createWithLabel(this.currentTab === 0 ? '密码登录' : '验证码登录');
            Button.debugLine("entry/src/main/ets/pages/LoginPage.ets(222:9)", "entry");
            // 登录按钮
            Button.width('100%');
            // 登录按钮
            Button.height(48);
            // 登录按钮
            Button.fontSize(16);
            // 登录按钮
            Button.fontColor(Color.White);
            // 登录按钮
            Button.backgroundColor('#1976D2');
            // 登录按钮
            Button.borderRadius(8);
            // 登录按钮
            Button.enabled(this.isLoginEnabled());
            // 登录按钮
            Button.opacity(this.isLoginEnabled() ? 1 : 0.5);
            // 登录按钮
            Button.onClick(() => {
                this.handleLogin();
            });
        }, Button);
        // 登录按钮
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 忘记密码链接（仅密码登录时显示）
            if (this.currentTab === 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/LoginPage.ets(237:11)", "entry");
                        Row.width('100%');
                        Row.justifyContent(FlexAlign.End);
                        Row.margin({ top: 16 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('忘记密码？');
                        Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(238:13)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#1976D2');
                        Text.onClick(() => {
                            router.pushUrl({
                                url: 'pages/ForgotPasswordPage'
                            }).catch((error: Error) => {
                                console.error('跳转忘记密码页面失败:', error);
                                promptAction.showToast({ message: `页面跳转失败: ${error.message}` });
                            });
                        });
                    }, Text);
                    Text.pop();
                    Row.pop();
                });
            }
            // 注册链接
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 注册链接
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/LoginPage.ets(256:9)", "entry");
            // 注册链接
            Row.margin({ top: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('还没有账号？');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(257:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('立即注册');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(261:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#1976D2');
            Text.onClick(() => {
                router.pushUrl({
                    url: 'pages/RegisterPage'
                }).catch((error: Error) => {
                    console.error('跳转注册页面失败:', error);
                    promptAction.showToast({ message: `页面跳转失败: ${error.message}` });
                });
            });
        }, Text);
        Text.pop();
        // 注册链接
        Row.pop();
        // 登录表单
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/LoginPage.ets(278:7)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 底部信息
            Text.create('E-Wallet v1.0.0');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(281:7)", "entry");
            // 底部信息
            Text.fontSize(12);
            // 底部信息
            Text.fontColor('#999999');
            // 底部信息
            Text.margin({ bottom: 20 });
        }, Text);
        // 底部信息
        Text.pop();
        Column.pop();
    }
    /**
     * 判断是否可以登录
     */
    isLoginEnabled(): boolean {
        if (!this.phone || this.phone.length !== 11 || this.isLoading) {
            return false;
        }
        if (this.currentTab === 0) {
            // 密码登录：需要密码
            return !!(this.password && this.password.length >= 6);
        }
        else {
            // 验证码登录：需要验证码
            return !!(this.code && this.code.length === 6);
        }
    }
    /**
     * 发送短信验证码
     */
    async sendSmsCode() {
        if (!this.canSendCode || this.phone.length !== 11) {
            return;
        }
        try {
            // 根据SpringBoot3后端接口格式调整参数
            const requestData: SendCodeRequest = {
                phone: this.phone,
                type: 1 // 1-登录验证码，2-操作验证码
            };
            const response = await httpClient.post<string>('/user/send-code', requestData);
            // 后端直接返回验证码，显示给用户（开发环境）
            if (response.data) {
                promptAction.showToast({ message: `验证码: ${response.data}` });
            }
            else {
                promptAction.showToast({ message: '验证码发送成功' });
            }
            // 开始倒计时
            this.startCountdown();
        }
        catch (error) {
            console.error('发送验证码失败:', error);
            let errorMessage = '发送验证码失败';
            if (error instanceof Error) {
                errorMessage = `发送失败: ${error.message}`;
                console.error('详细错误信息:', error);
            }
            promptAction.showToast({ message: errorMessage });
        }
    }
    /**
     * 开始倒计时
     */
    startCountdown() {
        this.canSendCode = false;
        this.countdown = 60;
        const timer = setInterval(() => {
            this.countdown--;
            if (this.countdown <= 0) {
                clearInterval(timer);
                this.canSendCode = true;
                this.countdown = 0;
            }
        }, 1000);
    }
    /**
     * 处理登录
     */
    async handleLogin() {
        if (this.isLoading)
            return;
        // 表单验证
        if (this.phone.length !== 11) {
            promptAction.showToast({ message: '请输入正确的手机号' });
            return;
        }
        if (this.currentTab === 0) {
            // 密码登录验证
            if (this.password.length < 6) {
                promptAction.showToast({ message: '密码长度不能少于6位' });
                return;
            }
        }
        else {
            // 验证码登录验证
            if (this.code.length !== 6) {
                promptAction.showToast({ message: '请输入6位验证码' });
                return;
            }
        }
        this.isLoading = true;
        try {
            let loginResponse: SpringBootUserResponse;
            if (this.currentTab === 0) {
                // 密码登录 - 调整为SpringBoot3后端格式
                const loginData: SpringBootPasswordLoginRequest = {
                    phone: this.phone,
                    password: this.password,
                    loginType: 'password'
                };
                const apiResponse = await httpClient.post<SpringBootUserResponse>('/user/login', loginData);
                loginResponse = apiResponse.data;
            }
            else {
                // 验证码登录 - 调整为SpringBoot3后端格式
                const smsLoginData: SpringBootSmsLoginRequest = {
                    phone: this.phone,
                    verificationCode: this.code,
                    loginType: 'sms'
                };
                const apiResponse = await httpClient.post<SpringBootUserResponse>('/user/login', smsLoginData);
                loginResponse = apiResponse.data;
            }
            // SpringBoot3后端返回的是User对象，需要适配
            // 生成一个简单的token（实际项目中应该由后端提供）
            const token = `token_${loginResponse.userId}_${Date.now()}`;
            // 保存token和用户信息到本地存储
            await storageManager.saveUserToken(token);
            const localUserInfo = this.convertToLocalUserInfo(loginResponse);
            await storageManager.saveUserInfo(localUserInfo);
            await storageManager.saveLoginPhone(this.phone);
            // 设置HTTP客户端的token
            httpClient.setAuthToken(token);
            promptAction.showToast({ message: '登录成功' });
            // 跳转到主页
            router.replaceUrl({
                url: 'pages/MainPage'
            }).catch((error: Error) => {
                console.error('跳转主页失败:', error);
                promptAction.showToast({ message: `页面跳转失败: ${error.message}` });
            });
        }
        catch (error) {
            console.error('登录失败:', error);
            let errorMessage = this.currentTab === 0 ? '登录失败，请检查账号密码' : '登录失败，请检查验证码';
            if (error instanceof Error) {
                errorMessage = `登录失败: ${error.message}`;
                console.error('详细错误信息:', error);
            }
            promptAction.showToast({ message: errorMessage });
        }
        finally {
            this.isLoading = false;
        }
    }
    private convertToLocalUserInfo(userInfo: SpringBootUserResponse): LocalUserInfo {
        return {
            userId: userInfo.userId,
            phone: userInfo.phone,
            realName: userInfo.realName || '',
            idCard: userInfo.idCard || '',
            walletNo: '',
            balance: 0,
            payLimit: userInfo.payLimit || 1000,
            status: userInfo.status,
            createTime: userInfo.createdAt || userInfo.createTime || '',
            updateTime: userInfo.updatedAt || userInfo.updateTime || ''
        };
    }
    NetworkSettingsSheet(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(475:5)", "entry");
            Column.width('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('网络连接设置');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(476:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(481:7)", "entry");
            Column.padding(20);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('服务器地址');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(482:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入服务器地址', text: 'http://localhost:8096' });
            TextInput.debugLine("entry/src/main/ets/pages/LoginPage.ets(488:9)", "entry");
            TextInput.fontSize(16);
            TextInput.height(48);
            TextInput.borderRadius(8);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E0E0E0' });
            TextInput.onChange((value: string) => {
                // 更新服务器地址
                httpClient.setBaseUrl(value);
            });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('默认: http://localhost:8096');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(499:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ top: 4, bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/LoginPage.ets(505:9)", "entry");
            Row.width('100%');
            Row.margin({ bottom: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('重置默认');
            Button.debugLine("entry/src/main/ets/pages/LoginPage.ets(506:11)", "entry");
            Button.fontSize(14);
            Button.fontColor('#666666');
            Button.backgroundColor('#F5F5F5');
            Button.borderRadius(8);
            Button.layoutWeight(1);
            Button.margin({ right: 8 });
            Button.onClick(() => {
                httpClient.setBaseUrl('http://localhost:8096');
                promptAction.showToast({ message: '已重置为默认地址' });
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('测试连接');
            Button.debugLine("entry/src/main/ets/pages/LoginPage.ets(518:11)", "entry");
            Button.fontSize(14);
            Button.fontColor('#FFFFFF');
            Button.backgroundColor('#1976D2');
            Button.borderRadius(8);
            Button.layoutWeight(1);
            Button.margin({ left: 8 });
            Button.onClick(() => {
                this.testConnection();
            });
        }, Button);
        Button.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('保存设置');
            Button.debugLine("entry/src/main/ets/pages/LoginPage.ets(532:9)", "entry");
            Button.width('100%');
            Button.height(48);
            Button.fontSize(16);
            Button.fontColor('#FFFFFF');
            Button.backgroundColor('#1976D2');
            Button.borderRadius(8);
            Button.onClick(() => {
                this.showNetworkSettings = false;
                promptAction.showToast({ message: '网络设置已保存' });
            });
        }, Button);
        Button.pop();
        Column.pop();
        Column.pop();
    }
    /**
     * 测试网络连接
     */
    async testConnection() {
        try {
            const response = await httpClient.get<string>('/test/ping');
            promptAction.showToast({ message: '连接成功' });
        }
        catch (error) {
            console.error('连接测试失败:', error);
            promptAction.showToast({ message: '连接失败，请检查网络设置' });
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "LoginPage";
    }
}
registerNamedRoute(() => new LoginPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/LoginPage", pageFullPath: "entry/src/main/ets/pages/LoginPage", integratedHsp: "false", moduleType: "followWithHap" });
