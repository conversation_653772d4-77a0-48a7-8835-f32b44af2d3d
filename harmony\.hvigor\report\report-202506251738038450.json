{"version": "2.0", "ppid": 21780, "events": [{"head": {"id": "7c831ccf-85b2-49b1-bb79-bb395ac758ea", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703998015400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f04ee386-8126-48ed-ace0-9e4ac3e28b73", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30725912744300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cff3abbe-cdea-4a49-a661-d4e1ca1c035f", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30725913606100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4395b3b-e54b-462c-be3e-a01709df8870", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726588697600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f262e8c8-f900-4d40-843c-f8934d4f10c8", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726597089500, "endTime": 30726754147500}, "additional": {"children": ["d9fc69d0-7bea-4c85-8747-c2f396b55b8b", "62287127-834a-43ff-a8f3-eaf2861c1fd0", "6457a47b-0ba4-4d04-a837-0bcbee92b003", "b5308e35-59dc-4ffe-a275-292aa7f608cf", "73278641-60f9-4387-b327-9c3771301db9", "9bca943a-f208-4635-96b7-e4d1bed6af49", "b176b723-110e-40ce-a758-a804a2f79681"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "166a1bed-5786-4ab0-9217-7628071941ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d9fc69d0-7bea-4c85-8747-c2f396b55b8b", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726597091100, "endTime": 30726611189500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f262e8c8-f900-4d40-843c-f8934d4f10c8", "logId": "fb2a9605-33e1-48ac-aa47-4562726fd756"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62287127-834a-43ff-a8f3-eaf2861c1fd0", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726611218400, "endTime": 30726753100600}, "additional": {"children": ["d700401a-eb11-48e8-8b9d-aba8fed32458", "26c996c7-f3f2-4f5a-b025-65c39e7d1111", "474b9f93-44e8-4716-8e39-cc19e5027946", "a8071841-6c75-4683-ac64-49ec768c9b5b", "6b6c44ea-aba5-4a65-9833-b042d252ef37", "71aabdb6-a49e-4e36-8fd5-98fb6ea1c275", "6a23d335-5571-46a8-ad69-d98b82929309", "cf79cc00-beee-4d86-9d28-06555ff02679", "d8cd91e3-464e-4866-9f47-fc6edeffb7cd"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f262e8c8-f900-4d40-843c-f8934d4f10c8", "logId": "dfa49ad0-2631-4a4f-b438-87e930f9a06d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6457a47b-0ba4-4d04-a837-0bcbee92b003", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726753116900, "endTime": 30726754140600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f262e8c8-f900-4d40-843c-f8934d4f10c8", "logId": "1886b995-027f-432c-a93b-48c7fdf1d63f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b5308e35-59dc-4ffe-a275-292aa7f608cf", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726754143700, "endTime": 30726754145400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f262e8c8-f900-4d40-843c-f8934d4f10c8", "logId": "73580b5d-6f2d-4f67-94fc-cbceb1cfc12b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73278641-60f9-4387-b327-9c3771301db9", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726602981400, "endTime": 30726603019300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f262e8c8-f900-4d40-843c-f8934d4f10c8", "logId": "b2331ecb-7ce0-49ed-9fed-74fe827fef67"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b2331ecb-7ce0-49ed-9fed-74fe827fef67", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726602981400, "endTime": 30726603019300}, "additional": {"logType": "info", "children": [], "durationId": "73278641-60f9-4387-b327-9c3771301db9", "parent": "166a1bed-5786-4ab0-9217-7628071941ef"}}, {"head": {"id": "9bca943a-f208-4635-96b7-e4d1bed6af49", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726607326200, "endTime": 30726607346200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f262e8c8-f900-4d40-843c-f8934d4f10c8", "logId": "b8d3180f-6727-445b-8fcf-220692a6b979"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8d3180f-6727-445b-8fcf-220692a6b979", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726607326200, "endTime": 30726607346200}, "additional": {"logType": "info", "children": [], "durationId": "9bca943a-f208-4635-96b7-e4d1bed6af49", "parent": "166a1bed-5786-4ab0-9217-7628071941ef"}}, {"head": {"id": "e246a3b3-2657-4f34-9c89-3ae2db65d650", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726607391600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5084e7bc-b2c9-4696-a598-bb2657b998b3", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726610529800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb2a9605-33e1-48ac-aa47-4562726fd756", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726597091100, "endTime": 30726611189500}, "additional": {"logType": "info", "children": [], "durationId": "d9fc69d0-7bea-4c85-8747-c2f396b55b8b", "parent": "166a1bed-5786-4ab0-9217-7628071941ef"}}, {"head": {"id": "d700401a-eb11-48e8-8b9d-aba8fed32458", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726616012300, "endTime": 30726616019800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "62287127-834a-43ff-a8f3-eaf2861c1fd0", "logId": "59929246-15fc-4707-afcd-8a961b18a579"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "26c996c7-f3f2-4f5a-b025-65c39e7d1111", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726616033900, "endTime": 30726619417900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "62287127-834a-43ff-a8f3-eaf2861c1fd0", "logId": "2b0b8325-1147-4666-9665-acb008d51390"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "474b9f93-44e8-4716-8e39-cc19e5027946", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726619432400, "endTime": 30726697881800}, "additional": {"children": ["751b116a-b938-4da6-b608-57199074fbcd", "e931b34d-3476-46b5-991c-f9b186a265e8", "79d837a9-76b7-402e-8bb8-948551d33811"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "62287127-834a-43ff-a8f3-eaf2861c1fd0", "logId": "b9f56eb5-6b54-4edd-a6fb-307f94682c6e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8071841-6c75-4683-ac64-49ec768c9b5b", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726697892700, "endTime": 30726719081700}, "additional": {"children": ["fa3630f5-0658-4dde-b27c-869d751e0302"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "62287127-834a-43ff-a8f3-eaf2861c1fd0", "logId": "902e5f84-ed9f-406b-9125-c65eb7d57aef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b6c44ea-aba5-4a65-9833-b042d252ef37", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726719087300, "endTime": 30726733284600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "62287127-834a-43ff-a8f3-eaf2861c1fd0", "logId": "afd83a0b-06e7-444d-9bbc-a4e007e85aae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "71aabdb6-a49e-4e36-8fd5-98fb6ea1c275", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726734257000, "endTime": 30726743312700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "62287127-834a-43ff-a8f3-eaf2861c1fd0", "logId": "a4437b86-27df-485b-8d7e-fb89f0b968a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a23d335-5571-46a8-ad69-d98b82929309", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726743339800, "endTime": 30726752963800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "62287127-834a-43ff-a8f3-eaf2861c1fd0", "logId": "d5780d1b-eb71-4d0a-8876-60a962219c28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf79cc00-beee-4d86-9d28-06555ff02679", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726752996400, "endTime": 30726753090200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "62287127-834a-43ff-a8f3-eaf2861c1fd0", "logId": "d957cb16-5ae1-406b-84cb-8c551f897768"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59929246-15fc-4707-afcd-8a961b18a579", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726616012300, "endTime": 30726616019800}, "additional": {"logType": "info", "children": [], "durationId": "d700401a-eb11-48e8-8b9d-aba8fed32458", "parent": "dfa49ad0-2631-4a4f-b438-87e930f9a06d"}}, {"head": {"id": "2b0b8325-1147-4666-9665-acb008d51390", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726616033900, "endTime": 30726619417900}, "additional": {"logType": "info", "children": [], "durationId": "26c996c7-f3f2-4f5a-b025-65c39e7d1111", "parent": "dfa49ad0-2631-4a4f-b438-87e930f9a06d"}}, {"head": {"id": "751b116a-b938-4da6-b608-57199074fbcd", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726620532900, "endTime": 30726620555800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "474b9f93-44e8-4716-8e39-cc19e5027946", "logId": "4e1b2002-e9b9-4b6b-a5a0-0424b1eedecc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e1b2002-e9b9-4b6b-a5a0-0424b1eedecc", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726620532900, "endTime": 30726620555800}, "additional": {"logType": "info", "children": [], "durationId": "751b116a-b938-4da6-b608-57199074fbcd", "parent": "b9f56eb5-6b54-4edd-a6fb-307f94682c6e"}}, {"head": {"id": "e931b34d-3476-46b5-991c-f9b186a265e8", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726622804600, "endTime": 30726697051300}, "additional": {"children": ["2767eabd-7f6e-4e11-a093-28ff54c47294", "c64732ce-e262-404d-b017-0811a98c06b6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "474b9f93-44e8-4716-8e39-cc19e5027946", "logId": "0daedd15-cfe4-4e28-9a13-cf91ab4ba0ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2767eabd-7f6e-4e11-a093-28ff54c47294", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726622806100, "endTime": 30726626454800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e931b34d-3476-46b5-991c-f9b186a265e8", "logId": "97ad637f-4365-490c-8bbc-2cd40676d4e5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c64732ce-e262-404d-b017-0811a98c06b6", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726626476800, "endTime": 30726697039800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e931b34d-3476-46b5-991c-f9b186a265e8", "logId": "01551f5b-0d53-4d4c-8a6b-6e1e1058ff79"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db8a26b4-a191-47a1-b518-13aaed8a97d6", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726622811900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a757bef-79a5-4470-9cf0-74c656e5b280", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726626317200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97ad637f-4365-490c-8bbc-2cd40676d4e5", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726622806100, "endTime": 30726626454800}, "additional": {"logType": "info", "children": [], "durationId": "2767eabd-7f6e-4e11-a093-28ff54c47294", "parent": "0daedd15-cfe4-4e28-9a13-cf91ab4ba0ff"}}, {"head": {"id": "df9cb754-b201-49a9-83c3-5e6364c11301", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726626486400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15dcd655-70c2-4d8c-bdd7-5bd72f94b0ea", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726634242400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f94f92c-2f06-465e-8b3e-ee8ebd0970cd", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726634367600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2900fa0a-a8bd-4af7-8bea-03d14e759c8a", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726634530000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e18f9c5b-7dee-4411-8ccb-7c9bae1d4efa", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726634602700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af6f6d35-77e7-4b9f-b144-29cdd3afc3ed", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726636467800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67e727eb-d43c-49e2-8877-1fd32fb46929", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726639972700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86d361b4-30fa-4624-be1e-05fb0d66004e", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726654059600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b20b632c-ce3d-46aa-98b4-e29eab0dabef", "name": "Sdk init in 35 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726675913900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a73d8b14-a21b-4e19-bc4c-ca8f46bfcf75", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726676063800}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 38}, "markType": "other"}}, {"head": {"id": "97fce7b5-96f3-4cc4-9c39-45fc8615a8a0", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726676078300}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 38}, "markType": "other"}}, {"head": {"id": "a7df8f02-5f6b-4930-a435-74391dd9bc16", "name": "Project task initialization takes 20 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726696854600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0690be5-da1e-4fd5-a4fc-15c175c54cc8", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726696962800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00b5f624-6351-497f-b5b8-a3afa13cb269", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726696993800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7deac3d5-a286-48ce-bd30-139752e0aabb", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726697016200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01551f5b-0d53-4d4c-8a6b-6e1e1058ff79", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726626476800, "endTime": 30726697039800}, "additional": {"logType": "info", "children": [], "durationId": "c64732ce-e262-404d-b017-0811a98c06b6", "parent": "0daedd15-cfe4-4e28-9a13-cf91ab4ba0ff"}}, {"head": {"id": "0daedd15-cfe4-4e28-9a13-cf91ab4ba0ff", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726622804600, "endTime": 30726697051300}, "additional": {"logType": "info", "children": ["97ad637f-4365-490c-8bbc-2cd40676d4e5", "01551f5b-0d53-4d4c-8a6b-6e1e1058ff79"], "durationId": "e931b34d-3476-46b5-991c-f9b186a265e8", "parent": "b9f56eb5-6b54-4edd-a6fb-307f94682c6e"}}, {"head": {"id": "79d837a9-76b7-402e-8bb8-948551d33811", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726697854100, "endTime": 30726697869000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "474b9f93-44e8-4716-8e39-cc19e5027946", "logId": "0dfffc04-b591-4d6b-8321-f8a4d4fd81c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0dfffc04-b591-4d6b-8321-f8a4d4fd81c2", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726697854100, "endTime": 30726697869000}, "additional": {"logType": "info", "children": [], "durationId": "79d837a9-76b7-402e-8bb8-948551d33811", "parent": "b9f56eb5-6b54-4edd-a6fb-307f94682c6e"}}, {"head": {"id": "b9f56eb5-6b54-4edd-a6fb-307f94682c6e", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726619432400, "endTime": 30726697881800}, "additional": {"logType": "info", "children": ["4e1b2002-e9b9-4b6b-a5a0-0424b1eedecc", "0daedd15-cfe4-4e28-9a13-cf91ab4ba0ff", "0dfffc04-b591-4d6b-8321-f8a4d4fd81c2"], "durationId": "474b9f93-44e8-4716-8e39-cc19e5027946", "parent": "dfa49ad0-2631-4a4f-b438-87e930f9a06d"}}, {"head": {"id": "fa3630f5-0658-4dde-b27c-869d751e0302", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726698433600, "endTime": 30726719072100}, "additional": {"children": ["0b708bbe-c114-4393-9e6a-c6f948e87045", "e18339a3-b791-4f2d-ba73-1e5c89108a54", "5a4bf458-4f84-4a12-97c6-d4822c85392f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a8071841-6c75-4683-ac64-49ec768c9b5b", "logId": "e49e656b-d03f-4a71-a86e-8eb87dbcb8dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b708bbe-c114-4393-9e6a-c6f948e87045", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726702225700, "endTime": 30726702242900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa3630f5-0658-4dde-b27c-869d751e0302", "logId": "b6d6c144-f9b7-43a1-a7c1-3493cef1a2cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6d6c144-f9b7-43a1-a7c1-3493cef1a2cb", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726702225700, "endTime": 30726702242900}, "additional": {"logType": "info", "children": [], "durationId": "0b708bbe-c114-4393-9e6a-c6f948e87045", "parent": "e49e656b-d03f-4a71-a86e-8eb87dbcb8dd"}}, {"head": {"id": "e18339a3-b791-4f2d-ba73-1e5c89108a54", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726703889300, "endTime": 30726717761200}, "additional": {"children": ["261676da-f0a2-43f3-830d-68b6e1c3a69b", "87543071-edb9-44e7-98e3-915a5ef6ef87"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa3630f5-0658-4dde-b27c-869d751e0302", "logId": "06221d92-6bdb-45d9-baba-809a773b3c8f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "261676da-f0a2-43f3-830d-68b6e1c3a69b", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726703890400, "endTime": 30726706608400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e18339a3-b791-4f2d-ba73-1e5c89108a54", "logId": "1016719c-8ebc-4e68-8a86-3247b7049109"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87543071-edb9-44e7-98e3-915a5ef6ef87", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726706625700, "endTime": 30726717747200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e18339a3-b791-4f2d-ba73-1e5c89108a54", "logId": "a1d8a66a-8ceb-4194-9315-d5ca19453c1b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ca35585-a690-4a87-8370-f4b51fedb699", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726703893800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc6712ed-d61b-4fd8-b1ed-adbe1f26aff9", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726706503100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1016719c-8ebc-4e68-8a86-3247b7049109", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726703890400, "endTime": 30726706608400}, "additional": {"logType": "info", "children": [], "durationId": "261676da-f0a2-43f3-830d-68b6e1c3a69b", "parent": "06221d92-6bdb-45d9-baba-809a773b3c8f"}}, {"head": {"id": "6723414b-3ab8-4e21-9bb2-3f2030284c11", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726706632600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ad377f7-6720-465a-9c9e-ee2bef5f6b17", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726713571500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb4db150-fa4c-4f2c-8849-47b7c278d00c", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726713711800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9c25ea4-b513-4d7c-983f-6279b8771a25", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726713902600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cece668e-74be-4af5-b208-38d0754dea5b", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726714051500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9c4120e-d6ae-4a7c-808c-f5a43062aeec", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726714109300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33da68ea-8517-4427-bdf3-8e8cf94ceddf", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726714151800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb2d8c40-bb87-49db-8091-68f8b2772064", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726714223700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c09e326a-c613-4b40-9729-9f5f65f0e9cc", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726717535300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5c3ae92-96d9-4397-95b3-1254a0381873", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726717655000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18c1f437-0624-4c7e-8f60-faa2662c6252", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726717691400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1b73489-43e6-4fef-bd21-469906f2d907", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726717718200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1d8a66a-8ceb-4194-9315-d5ca19453c1b", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726706625700, "endTime": 30726717747200}, "additional": {"logType": "info", "children": [], "durationId": "87543071-edb9-44e7-98e3-915a5ef6ef87", "parent": "06221d92-6bdb-45d9-baba-809a773b3c8f"}}, {"head": {"id": "06221d92-6bdb-45d9-baba-809a773b3c8f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726703889300, "endTime": 30726717761200}, "additional": {"logType": "info", "children": ["1016719c-8ebc-4e68-8a86-3247b7049109", "a1d8a66a-8ceb-4194-9315-d5ca19453c1b"], "durationId": "e18339a3-b791-4f2d-ba73-1e5c89108a54", "parent": "e49e656b-d03f-4a71-a86e-8eb87dbcb8dd"}}, {"head": {"id": "5a4bf458-4f84-4a12-97c6-d4822c85392f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726719037000, "endTime": 30726719050300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa3630f5-0658-4dde-b27c-869d751e0302", "logId": "176f4c62-9a83-464e-a8d5-c9b7485f1ac8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "176f4c62-9a83-464e-a8d5-c9b7485f1ac8", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726719037000, "endTime": 30726719050300}, "additional": {"logType": "info", "children": [], "durationId": "5a4bf458-4f84-4a12-97c6-d4822c85392f", "parent": "e49e656b-d03f-4a71-a86e-8eb87dbcb8dd"}}, {"head": {"id": "e49e656b-d03f-4a71-a86e-8eb87dbcb8dd", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726698433600, "endTime": 30726719072100}, "additional": {"logType": "info", "children": ["b6d6c144-f9b7-43a1-a7c1-3493cef1a2cb", "06221d92-6bdb-45d9-baba-809a773b3c8f", "176f4c62-9a83-464e-a8d5-c9b7485f1ac8"], "durationId": "fa3630f5-0658-4dde-b27c-869d751e0302", "parent": "902e5f84-ed9f-406b-9125-c65eb7d57aef"}}, {"head": {"id": "902e5f84-ed9f-406b-9125-c65eb7d57aef", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726697892700, "endTime": 30726719081700}, "additional": {"logType": "info", "children": ["e49e656b-d03f-4a71-a86e-8eb87dbcb8dd"], "durationId": "a8071841-6c75-4683-ac64-49ec768c9b5b", "parent": "dfa49ad0-2631-4a4f-b438-87e930f9a06d"}}, {"head": {"id": "66774555-8315-4c3e-9c7f-23b87e9d6bf6", "name": "watch files: [\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\hvigorfile.ts',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726732980800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67818076-1f58-472c-ad17-138c46767eeb", "name": "hvigorfile, resolve hvigorfile dependencies in 15 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726733234400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afd83a0b-06e7-444d-9bbc-a4e007e85aae", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726719087300, "endTime": 30726733284600}, "additional": {"logType": "info", "children": [], "durationId": "6b6c44ea-aba5-4a65-9833-b042d252ef37", "parent": "dfa49ad0-2631-4a4f-b438-87e930f9a06d"}}, {"head": {"id": "d8cd91e3-464e-4866-9f47-fc6edeffb7cd", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726734074500, "endTime": 30726734245000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "62287127-834a-43ff-a8f3-eaf2861c1fd0", "logId": "65759140-69b1-4aca-bb6d-a0d7b26dbf63"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17c38723-fdcf-4904-b2ac-f8c9cf72d863", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726734094200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65759140-69b1-4aca-bb6d-a0d7b26dbf63", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726734074500, "endTime": 30726734245000}, "additional": {"logType": "info", "children": [], "durationId": "d8cd91e3-464e-4866-9f47-fc6edeffb7cd", "parent": "dfa49ad0-2631-4a4f-b438-87e930f9a06d"}}, {"head": {"id": "c44e10d9-1916-4cde-b4c4-de9380c9bc95", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726735730900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52dd8441-50c5-4e24-9262-488e07f6578b", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726741968400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4437b86-27df-485b-8d7e-fb89f0b968a4", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726734257000, "endTime": 30726743312700}, "additional": {"logType": "info", "children": [], "durationId": "71aabdb6-a49e-4e36-8fd5-98fb6ea1c275", "parent": "dfa49ad0-2631-4a4f-b438-87e930f9a06d"}}, {"head": {"id": "c501628a-c990-4e70-b2a9-5d0165022677", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726743353400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bd64e41-df44-4cbc-abc3-224a3f43a4f8", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726748259900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54f57c1a-a37a-472d-9a66-7257d1188d12", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726748381600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8f258eb-9841-4b92-b197-5c20e4199e21", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726748664000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c9115c0-3f5b-4329-b530-53a71a1d1a2d", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726750518100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a7ef15a-47aa-4180-aa55-38081f19064c", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726750594800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5780d1b-eb71-4d0a-8876-60a962219c28", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726743339800, "endTime": 30726752963800}, "additional": {"logType": "info", "children": [], "durationId": "6a23d335-5571-46a8-ad69-d98b82929309", "parent": "dfa49ad0-2631-4a4f-b438-87e930f9a06d"}}, {"head": {"id": "833a0dbf-0c38-48fa-b466-1a559a30da4a", "name": "Configuration phase cost:137 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726753015300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d957cb16-5ae1-406b-84cb-8c551f897768", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726752996400, "endTime": 30726753090200}, "additional": {"logType": "info", "children": [], "durationId": "cf79cc00-beee-4d86-9d28-06555ff02679", "parent": "dfa49ad0-2631-4a4f-b438-87e930f9a06d"}}, {"head": {"id": "dfa49ad0-2631-4a4f-b438-87e930f9a06d", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726611218400, "endTime": 30726753100600}, "additional": {"logType": "info", "children": ["59929246-15fc-4707-afcd-8a961b18a579", "2b0b8325-1147-4666-9665-acb008d51390", "b9f56eb5-6b54-4edd-a6fb-307f94682c6e", "902e5f84-ed9f-406b-9125-c65eb7d57aef", "afd83a0b-06e7-444d-9bbc-a4e007e85aae", "a4437b86-27df-485b-8d7e-fb89f0b968a4", "d5780d1b-eb71-4d0a-8876-60a962219c28", "d957cb16-5ae1-406b-84cb-8c551f897768", "65759140-69b1-4aca-bb6d-a0d7b26dbf63"], "durationId": "62287127-834a-43ff-a8f3-eaf2861c1fd0", "parent": "166a1bed-5786-4ab0-9217-7628071941ef"}}, {"head": {"id": "b176b723-110e-40ce-a758-a804a2f79681", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726754117200, "endTime": 30726754130500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f262e8c8-f900-4d40-843c-f8934d4f10c8", "logId": "2b5fe9ec-af93-4831-8f60-f98966d224e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b5fe9ec-af93-4831-8f60-f98966d224e3", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726754117200, "endTime": 30726754130500}, "additional": {"logType": "info", "children": [], "durationId": "b176b723-110e-40ce-a758-a804a2f79681", "parent": "166a1bed-5786-4ab0-9217-7628071941ef"}}, {"head": {"id": "1886b995-027f-432c-a93b-48c7fdf1d63f", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726753116900, "endTime": 30726754140600}, "additional": {"logType": "info", "children": [], "durationId": "6457a47b-0ba4-4d04-a837-0bcbee92b003", "parent": "166a1bed-5786-4ab0-9217-7628071941ef"}}, {"head": {"id": "73580b5d-6f2d-4f67-94fc-cbceb1cfc12b", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726754143700, "endTime": 30726754145400}, "additional": {"logType": "info", "children": [], "durationId": "b5308e35-59dc-4ffe-a275-292aa7f608cf", "parent": "166a1bed-5786-4ab0-9217-7628071941ef"}}, {"head": {"id": "166a1bed-5786-4ab0-9217-7628071941ef", "name": "init", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726597089500, "endTime": 30726754147500}, "additional": {"logType": "info", "children": ["fb2a9605-33e1-48ac-aa47-4562726fd756", "dfa49ad0-2631-4a4f-b438-87e930f9a06d", "1886b995-027f-432c-a93b-48c7fdf1d63f", "73580b5d-6f2d-4f67-94fc-cbceb1cfc12b", "b2331ecb-7ce0-49ed-9fed-74fe827fef67", "b8d3180f-6727-445b-8fcf-220692a6b979", "2b5fe9ec-af93-4831-8f60-f98966d224e3"], "durationId": "f262e8c8-f900-4d40-843c-f8934d4f10c8"}}, {"head": {"id": "3b2d2d35-7421-4505-b39f-0f4fa4dd8376", "name": "Configuration task cost before running: 161 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726754240700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06191049-a180-44b7-b83a-2d6e888d0cae", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726758811900, "endTime": 30726766366800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "8f759ce2-7c7a-4682-9c60-e517e58df3b4", "logId": "f46f2459-59e6-4f0a-96a7-9d218aecff4a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f759ce2-7c7a-4682-9c60-e517e58df3b4", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726755668600}, "additional": {"logType": "detail", "children": [], "durationId": "06191049-a180-44b7-b83a-2d6e888d0cae"}}, {"head": {"id": "55d92b57-b66c-45ff-b8ed-9119c46d2648", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726756176600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "005eed9a-0244-4cdd-b319-0c8dc7b5dce4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726756256100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "241be500-cbc3-460c-b333-3d4e8d42ab35", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726758822700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55dc2f9d-66c4-41cd-b8e8-23bbd0d69dad", "name": "Incremental task entry:default@PreBuild pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726766191500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0e5d0d5-2a5f-4a91-ba09-6b10d888cb62", "name": "entry : default@PreBuild cost memory -1.4893035888671875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726766316300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f46f2459-59e6-4f0a-96a7-9d218aecff4a", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726758811900, "endTime": 30726766366800}, "additional": {"logType": "info", "children": [], "durationId": "06191049-a180-44b7-b83a-2d6e888d0cae"}}, {"head": {"id": "57924f5d-7343-4b8f-a127-96ad7968f150", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726770505000, "endTime": 30726772113400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "873b56ea-8025-48c7-8a28-7275bbe4b87f", "logId": "eac307ef-abf7-46cb-8365-dced91f7ca03"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "873b56ea-8025-48c7-8a28-7275bbe4b87f", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726769398400}, "additional": {"logType": "detail", "children": [], "durationId": "57924f5d-7343-4b8f-a127-96ad7968f150"}}, {"head": {"id": "a2c51d7c-011a-4515-98c5-5cbcb5207c5b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726769857200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b0be03f-06c4-47d5-82d0-657cde7a81e8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726769929400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "354d29f7-f14f-4a81-85d4-f8f6faaebda7", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726770511100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "160d47c6-7694-4620-81dd-ac6958d33a64", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726771986000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95d406b4-14a1-48f0-87db-4f55e174e22e", "name": "entry : default@MergeProfile cost memory 0.11151885986328125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726772071100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eac307ef-abf7-46cb-8365-dced91f7ca03", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726770505000, "endTime": 30726772113400}, "additional": {"logType": "info", "children": [], "durationId": "57924f5d-7343-4b8f-a127-96ad7968f150"}}, {"head": {"id": "3b611878-19e1-4262-bfc6-6fc8ca4456af", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726774529600, "endTime": 30726776347100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4eeb031f-77c5-4ec9-8299-0604f73ff36f", "logId": "18fb6962-b4b3-4c14-88e9-4b1d29357932"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4eeb031f-77c5-4ec9-8299-0604f73ff36f", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726773471000}, "additional": {"logType": "detail", "children": [], "durationId": "3b611878-19e1-4262-bfc6-6fc8ca4456af"}}, {"head": {"id": "ab7ff118-4f55-467a-b989-70df7d051772", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726773880200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e11cbc10-5a25-4281-9874-47d7ca915d51", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726773952600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "628f686c-fa47-4c87-be95-6c3a0789fa8c", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726774536800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9330a86c-91e2-4b94-957b-234d3b7e47f2", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726775259300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e7d40ba-b8cd-4ced-939f-f48151d6d00d", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726776229700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c96e14f5-36ce-4150-8a8d-aa9aea5f8224", "name": "entry : default@CreateBuildProfile cost memory 0.09683990478515625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726776302500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18fb6962-b4b3-4c14-88e9-4b1d29357932", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726774529600, "endTime": 30726776347100}, "additional": {"logType": "info", "children": [], "durationId": "3b611878-19e1-4262-bfc6-6fc8ca4456af"}}, {"head": {"id": "39fd3b4d-84c9-4adc-96e9-b636e578eb56", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726778666900, "endTime": 30726778910000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "2a0fff31-18d9-4740-b1f5-5217d2c0f20c", "logId": "7e631b53-de6b-481d-a089-65d3d9c24eab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a0fff31-18d9-4740-b1f5-5217d2c0f20c", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726777535600}, "additional": {"logType": "detail", "children": [], "durationId": "39fd3b4d-84c9-4adc-96e9-b636e578eb56"}}, {"head": {"id": "bfb12a23-fa5b-4992-b812-d36fe9fb1631", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726777976500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "302b2264-58c6-44e1-967e-597d8cf8adeb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726778054000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14409bbd-323e-4f7d-8086-19d02d32c058", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726778673600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94681804-07de-40d4-929f-17f3075415ba", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726778753900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9b6a304-ad0a-421f-a45f-b50dda969e16", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726778785700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d6ea5d6-f894-479d-8893-09e70b2296c8", "name": "entry : default@PreCheckSyscap cost memory 0.03680419921875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726778832900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd9b496c-7087-4e74-8cfb-cf73bf45df42", "name": "runTaskFromQueue task cost before running: 185 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726778877800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e631b53-de6b-481d-a089-65d3d9c24eab", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726778666900, "endTime": 30726778910000, "totalTime": 198900}, "additional": {"logType": "info", "children": [], "durationId": "39fd3b4d-84c9-4adc-96e9-b636e578eb56"}}, {"head": {"id": "890bff2b-e9bc-4985-aafa-56c7b0050746", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726785307800, "endTime": 30726786108300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b3a2ccf9-64f5-4a38-afa2-09605b629b7b", "logId": "a43231b5-7607-4aff-b265-0588c4f2997b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3a2ccf9-64f5-4a38-afa2-09605b629b7b", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726780092800}, "additional": {"logType": "detail", "children": [], "durationId": "890bff2b-e9bc-4985-aafa-56c7b0050746"}}, {"head": {"id": "3767c73f-b762-4388-8b2b-c1f0dd8e0c04", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726780488100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bd06aae-db27-45b8-bd08-b92e876e3677", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726780558200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5489dc20-6110-402a-a12e-3892cf24422e", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726785316000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d68a8c7b-4590-4864-a6f2-29acecf06143", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726785472600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac3a2d4f-d49a-425e-94a6-77d0b025afb4", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726785992100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9099d222-897a-4fcd-b8f6-e8de0a45ce7d", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0652923583984375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726786065700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a43231b5-7607-4aff-b265-0588c4f2997b", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726785307800, "endTime": 30726786108300}, "additional": {"logType": "info", "children": [], "durationId": "890bff2b-e9bc-4985-aafa-56c7b0050746"}}, {"head": {"id": "d8c8458f-2bdb-4b82-b08c-1c3fde7b1680", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726790852300, "endTime": 30726791969700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "d086dfaf-f290-4ffc-b348-07a8f3c9ead6", "logId": "fa2503ef-0681-45ec-b708-4981afddf433"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d086dfaf-f290-4ffc-b348-07a8f3c9ead6", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726787826600}, "additional": {"logType": "detail", "children": [], "durationId": "d8c8458f-2bdb-4b82-b08c-1c3fde7b1680"}}, {"head": {"id": "de6acd1b-129a-437f-8672-20db2bccb0f7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726789209100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "390cdc2f-04d8-40b2-be23-88a7dfacb27a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726789380500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f09febf4-b6c0-4bcb-9243-6e4307a01b50", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726790859800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1ca3025-d490-4f07-8217-e578f482d6cf", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726791790800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbc5da16-d9ab-43cb-a2e0-ec354e2b9c36", "name": "entry : default@ProcessProfile cost memory 0.06365203857421875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726791878300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa2503ef-0681-45ec-b708-4981afddf433", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726790852300, "endTime": 30726791969700}, "additional": {"logType": "info", "children": [], "durationId": "d8c8458f-2bdb-4b82-b08c-1c3fde7b1680"}}, {"head": {"id": "129f6fb7-aee2-4a4f-a4e9-90ccaaff2225", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726795683700, "endTime": 30726800881500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7326d7f7-0226-45f1-810d-aef99c92c94d", "logId": "98d5fee4-8efd-4f31-b4d5-eabec5743f21"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7326d7f7-0226-45f1-810d-aef99c92c94d", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726793325800}, "additional": {"logType": "detail", "children": [], "durationId": "129f6fb7-aee2-4a4f-a4e9-90ccaaff2225"}}, {"head": {"id": "5d919972-4488-452b-a89b-96c5a1667f25", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726793804200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e37dcb62-0d4a-4a3a-a5f8-bcaf3ce37af8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726793886500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b77f524-665a-4583-95a2-38c8668ed2b2", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726795692400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d64b3694-97c7-49ac-8e7a-a1a9f4c0ac7c", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726800734700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d774c5d4-e7d1-4e01-9761-d899d15f83ef", "name": "entry : default@ProcessRouterMap cost memory 0.1957244873046875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726800836900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98d5fee4-8efd-4f31-b4d5-eabec5743f21", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726795683700, "endTime": 30726800881500}, "additional": {"logType": "info", "children": [], "durationId": "129f6fb7-aee2-4a4f-a4e9-90ccaaff2225"}}, {"head": {"id": "b08a0ca3-d3a8-41f3-b89a-071157a1b8e3", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726807005000, "endTime": 30726809443400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "3c080e59-002c-4d67-aa72-8478b6750fe7", "logId": "a92cb671-262d-4eee-8777-8cf3e8d6eb38"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c080e59-002c-4d67-aa72-8478b6750fe7", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726803680200}, "additional": {"logType": "detail", "children": [], "durationId": "b08a0ca3-d3a8-41f3-b89a-071157a1b8e3"}}, {"head": {"id": "905e973f-daf1-484b-89eb-e577aad4ae8f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726804178200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f68e53c-dc58-4042-a1ea-30b451670bbf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726804268000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d0a7c1e-0bdc-469c-9bea-2a24206fdde8", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726805053800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0196f98-4bca-44d6-bd76-51ce303398f2", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726807983900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b43bc706-6211-4283-9108-32f5a7c22aa4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726808126200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84347f47-c887-41fb-a760-12af66cdf6b1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726808168600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3681296-3fa2-4677-b905-45163440c8e9", "name": "entry : default@PreviewProcessResource cost memory 0.0698699951171875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726808224200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e27ad9f6-ffaf-4f48-967d-f38338d65d34", "name": "runTaskFromQueue task cost before running: 216 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726809355600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a92cb671-262d-4eee-8777-8cf3e8d6eb38", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726807005000, "endTime": 30726809443400, "totalTime": 1263300}, "additional": {"logType": "info", "children": [], "durationId": "b08a0ca3-d3a8-41f3-b89a-071157a1b8e3"}}, {"head": {"id": "ef662cde-d3f1-4f21-8d86-71d131f75e9e", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726815453400, "endTime": 30726834125500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5fe5a4e0-99fe-4a0c-a4f8-1cd3889300c9", "logId": "49c3b451-1463-4148-83ff-b51a3a6330c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5fe5a4e0-99fe-4a0c-a4f8-1cd3889300c9", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726811816100}, "additional": {"logType": "detail", "children": [], "durationId": "ef662cde-d3f1-4f21-8d86-71d131f75e9e"}}, {"head": {"id": "f9fe3b2b-eaf7-4db0-8aec-506211c802c9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726812320200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2751949a-53c7-46d0-ba6a-dab9e7fe9e52", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726812402000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edc94358-2a6e-49ce-a2d1-ff1d9f670aea", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726815471400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "467a5be3-c5bd-4882-b9c4-525d19e4f7e4", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726833960300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b8bb43e-22ae-4cf1-87c3-7dfb733009a3", "name": "entry : default@GenerateLoaderJson cost memory -0.9239501953125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726834083000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49c3b451-1463-4148-83ff-b51a3a6330c3", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726815453400, "endTime": 30726834125500}, "additional": {"logType": "info", "children": [], "durationId": "ef662cde-d3f1-4f21-8d86-71d131f75e9e"}}, {"head": {"id": "3e1abba7-cde2-46a8-b8db-c0cb4d0eb08e", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726843341800, "endTime": 30727033616000}, "additional": {"children": ["cc233dd4-2d4d-4ec6-849a-3021e9b8789b", "f5eb52e7-38e9-47e9-88c5-e25a82de9fac", "2f8f12b9-00a4-4bd6-ba66-2caa58e3e4a6", "508acd71-ceb0-4c44-a915-3e47467c294f"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "8e78870e-90d8-4c7f-abfd-e1339fdfcdae", "logId": "8ae03abd-4161-4d09-8801-2dda0e9b49a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e78870e-90d8-4c7f-abfd-e1339fdfcdae", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726840205700}, "additional": {"logType": "detail", "children": [], "durationId": "3e1abba7-cde2-46a8-b8db-c0cb4d0eb08e"}}, {"head": {"id": "fb027dae-8a12-44b0-9c63-9151066909ca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726840682300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32960c85-3d3c-40e4-99eb-c21d3a001e95", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726840758600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccfa9888-790e-4a20-a7dd-9a5ca1e01323", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726841534300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "787ae8e7-81c9-461e-a9a4-a11222fa0c0a", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726843365900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5454596f-3c1c-4a1e-b9cf-968dc0b2703f", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726859550400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07f9289b-ff14-46d9-8959-3ec3732646f1", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 16 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726859691600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc233dd4-2d4d-4ec6-849a-3021e9b8789b", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726860833900, "endTime": 30726888561100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3e1abba7-cde2-46a8-b8db-c0cb4d0eb08e", "logId": "c1016339-e0e5-42f7-8349-5aabfdfd3669"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1016339-e0e5-42f7-8349-5aabfdfd3669", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726860833900, "endTime": 30726888561100}, "additional": {"logType": "info", "children": [], "durationId": "cc233dd4-2d4d-4ec6-849a-3021e9b8789b", "parent": "8ae03abd-4161-4d09-8801-2dda0e9b49a4"}}, {"head": {"id": "c42e5ea1-6566-47b8-986a-0b8c11f5ce85", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726888833200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5eb52e7-38e9-47e9-88c5-e25a82de9fac", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726889836100, "endTime": 30726921755700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3e1abba7-cde2-46a8-b8db-c0cb4d0eb08e", "logId": "2c1e0aad-88eb-443c-a407-d949208cd671"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "789b044f-52ad-4001-b47d-73fed4cc77bf", "name": "current process  memoryUsage: {\n  rss: 117772288,\n  heapTotal: 128540672,\n  heapUsed: 115058256,\n  external: 3117218,\n  arrayBuffers: 111119\n} os memoryUsage :13.159526824951172", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726890816800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "415f3e95-fcaf-4d0c-8635-3246e2da6dca", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726919424600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c1e0aad-88eb-443c-a407-d949208cd671", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726889836100, "endTime": 30726921755700}, "additional": {"logType": "info", "children": [], "durationId": "f5eb52e7-38e9-47e9-88c5-e25a82de9fac", "parent": "8ae03abd-4161-4d09-8801-2dda0e9b49a4"}}, {"head": {"id": "02e9d9e2-bc35-42dc-906e-8c3224060062", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726921862700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f8f12b9-00a4-4bd6-ba66-2caa58e3e4a6", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726922773000, "endTime": 30726962251200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3e1abba7-cde2-46a8-b8db-c0cb4d0eb08e", "logId": "8974b308-57b0-4e6f-b7d4-6ddd1c5e5dd3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "701dd3e7-f34c-490f-9d93-2be78b71891c", "name": "current process  memoryUsage: {\n  rss: 117776384,\n  heapTotal: 128540672,\n  heapUsed: 115320064,\n  external: 3117344,\n  arrayBuffers: 111260\n} os memoryUsage :13.15488052368164", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726923581600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c9f83d6-0372-4f4f-a9aa-677a12690ea8", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726959901800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8974b308-57b0-4e6f-b7d4-6ddd1c5e5dd3", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726922773000, "endTime": 30726962251200}, "additional": {"logType": "info", "children": [], "durationId": "2f8f12b9-00a4-4bd6-ba66-2caa58e3e4a6", "parent": "8ae03abd-4161-4d09-8801-2dda0e9b49a4"}}, {"head": {"id": "4c205f2c-790d-4b15-8881-29ea01e76eb4", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726962434200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "508acd71-ceb0-4c44-a915-3e47467c294f", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726963384100, "endTime": 30727032305000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3e1abba7-cde2-46a8-b8db-c0cb4d0eb08e", "logId": "ba198d82-389c-4e00-9827-0aa081d94051"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e2955d3-f938-461a-8b9f-6453d530887a", "name": "current process  memoryUsage: {\n  rss: 117776384,\n  heapTotal: 128540672,\n  heapUsed: 115609664,\n  external: 3117470,\n  arrayBuffers: 112270\n} os memoryUsage :13.160404205322266", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726964166900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fc50fae-2f3d-44cd-9daa-272855afe2c4", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727029112800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba198d82-389c-4e00-9827-0aa081d94051", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726963384100, "endTime": 30727032305000}, "additional": {"logType": "info", "children": [], "durationId": "508acd71-ceb0-4c44-a915-3e47467c294f", "parent": "8ae03abd-4161-4d09-8801-2dda0e9b49a4"}}, {"head": {"id": "953090a1-5529-465f-b81a-f8f0c299bf92", "name": "entry : default@PreviewCompileResource cost memory 0.1383819580078125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727033428000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14fce8ff-09c0-4b40-bd84-a5999aaab355", "name": "runTaskFromQueue task cost before running: 440 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727033572400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ae03abd-4161-4d09-8801-2dda0e9b49a4", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726843341800, "endTime": 30727033616000, "totalTime": 190196100}, "additional": {"logType": "info", "children": ["c1016339-e0e5-42f7-8349-5aabfdfd3669", "2c1e0aad-88eb-443c-a407-d949208cd671", "8974b308-57b0-4e6f-b7d4-6ddd1c5e5dd3", "ba198d82-389c-4e00-9827-0aa081d94051"], "durationId": "3e1abba7-cde2-46a8-b8db-c0cb4d0eb08e"}}, {"head": {"id": "166dd460-313f-4090-869c-841812a4cd66", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727036156300, "endTime": 30727036328000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "a9d8e038-ffbd-4020-b47f-6b13969618c4", "logId": "d6bf211c-1a2f-4d91-941d-acee166be852"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9d8e038-ffbd-4020-b47f-6b13969618c4", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727035647700}, "additional": {"logType": "detail", "children": [], "durationId": "166dd460-313f-4090-869c-841812a4cd66"}}, {"head": {"id": "52d05f13-c617-4c10-8a89-3e56887a0b78", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727036036800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f59407ce-52c6-4a01-a1ea-0ccd59c3acee", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727036094200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80d73157-740b-4e0f-9c1f-01cb3582115c", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727036160600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c20679f3-266c-42a4-b3c7-6d29a4ff8ee7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727036206200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83f1635d-834c-4bb2-a1b6-d6aa89954cb3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727036226000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf4d68f9-794a-4239-a53c-bc98191740d9", "name": "entry : default@PreviewHookCompileResource cost memory 0.0379180908203125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727036263300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3039c6c-8319-4170-89b5-9165703aa4fe", "name": "runTaskFromQueue task cost before running: 443 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727036304100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6bf211c-1a2f-4d91-941d-acee166be852", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727036156300, "endTime": 30727036328000, "totalTime": 138000}, "additional": {"logType": "info", "children": [], "durationId": "166dd460-313f-4090-869c-841812a4cd66"}}, {"head": {"id": "74734654-6f30-4a99-b23b-55ecf1bb358a", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727038512500, "endTime": 30727044393600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "120d4fde-2fd5-433c-94b0-1417b85f6686", "logId": "87f4a15f-f6a1-4e69-b1a2-a7847a5f9be6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "120d4fde-2fd5-433c-94b0-1417b85f6686", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727037512600}, "additional": {"logType": "detail", "children": [], "durationId": "74734654-6f30-4a99-b23b-55ecf1bb358a"}}, {"head": {"id": "8fbd33d7-08f4-4787-928a-4a71e4ceef72", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727037949600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3e9fe5f-b946-42d0-8a4b-77bc94ad7861", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727038015100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b941715-ea40-4c47-b255-71debdd6c7d1", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727038518400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67f43c73-89e9-42ed-96be-24dc9b5c3c49", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727039503100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6e15412-ac78-4765-bd6a-a6121ae941c1", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727039573700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaa99682-71c2-42fc-9ca5-1df85a25a7c4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727039622000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4a2da9f-42f4-430d-96f3-35ae0c165246", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727039644700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f531edd-0ef5-4262-a1a0-f987ee8b5b5f", "name": "entry : default@CopyPreviewProfile cost memory 0.21860504150390625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727044201000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5874a6d4-e36f-4405-9701-dcde394f5020", "name": "runTaskFromQueue task cost before running: 451 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727044346000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87f4a15f-f6a1-4e69-b1a2-a7847a5f9be6", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727038512500, "endTime": 30727044393600, "totalTime": 5808900}, "additional": {"logType": "info", "children": [], "durationId": "74734654-6f30-4a99-b23b-55ecf1bb358a"}}, {"head": {"id": "31d5a992-529d-47b9-ac14-1d4f9cb1a4fa", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727047010700, "endTime": 30727047789400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "3828e382-9433-4d0e-92f3-a379afb40120", "logId": "b161d944-ab8c-4da1-a9c6-a84e42d33f16"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3828e382-9433-4d0e-92f3-a379afb40120", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727045949100}, "additional": {"logType": "detail", "children": [], "durationId": "31d5a992-529d-47b9-ac14-1d4f9cb1a4fa"}}, {"head": {"id": "add71b81-cad9-4e9f-b40a-e1dcc31185dc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727046373700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9043f298-5cc0-472b-9df1-5f53c22fb4be", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727046439800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c96766f5-cc2b-48de-aaec-ea846b7e1c59", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727047019900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af91e2ee-0773-41a8-9b0b-c3a0ca72bcf8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727047100400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5902060-1397-4381-95cf-f3345287fd78", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727047613300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c3aac5a-a370-45e2-a23d-3e286150cf6d", "name": "entry : default@ReplacePreviewerPage cost memory -1.723663330078125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727047710200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9aa7d3e1-3a72-4ace-9a6b-be37f22732cc", "name": "runTaskFromQueue task cost before running: 454 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727047762100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b161d944-ab8c-4da1-a9c6-a84e42d33f16", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727047010700, "endTime": 30727047789400, "totalTime": 736400}, "additional": {"logType": "info", "children": [], "durationId": "31d5a992-529d-47b9-ac14-1d4f9cb1a4fa"}}, {"head": {"id": "c20fc7e9-9629-453c-a6d6-1ae6810a5181", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727049180900, "endTime": 30727049363200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "d0879b8c-3544-4f6f-8ff9-82b3b27642f6", "logId": "8b43a14e-6f4a-4f3e-8af6-3cd6cbadd314"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0879b8c-3544-4f6f-8ff9-82b3b27642f6", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727049142900}, "additional": {"logType": "detail", "children": [], "durationId": "c20fc7e9-9629-453c-a6d6-1ae6810a5181"}}, {"head": {"id": "249f5451-4873-45bb-821b-4ec997672207", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727049186100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef9ec2ba-a3ee-43fa-8163-8af6781411ed", "name": "entry : buildPreviewerResource cost memory 0.0116424560546875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727049284500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "851c4975-4f6b-4b7e-b947-fed46b735ff9", "name": "runTaskFromQueue task cost before running: 456 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727049335100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b43a14e-6f4a-4f3e-8af6-3cd6cbadd314", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727049180900, "endTime": 30727049363200, "totalTime": 140700}, "additional": {"logType": "info", "children": [], "durationId": "c20fc7e9-9629-453c-a6d6-1ae6810a5181"}}, {"head": {"id": "7a448803-1daa-4f82-a445-debd6e8df18d", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727051522700, "endTime": 30727054842100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "db97687a-3a9c-45c9-a090-a472cfd1d734", "logId": "0f3d2467-c61a-4bd3-95a5-230706ce6ea8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db97687a-3a9c-45c9-a090-a472cfd1d734", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727050517900}, "additional": {"logType": "detail", "children": [], "durationId": "7a448803-1daa-4f82-a445-debd6e8df18d"}}, {"head": {"id": "3a82e739-9940-45a0-9cdc-4f3434f67116", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727050919100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "195aebae-f526-4591-ac59-f119b7389bea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727050987900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab0b10f5-f62f-42c3-868e-90a83d680db8", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727051527900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80d1ece8-eee8-4d98-ad95-978b506931ca", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727052919800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ddeaa70-2a6d-481d-963c-e750b67057f9", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727052994400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1479ff6b-267b-4df1-bbd4-04223975f2b2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727053046200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4534411-5b67-404b-9007-0e83245d4716", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727053070900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b117ac6c-f1d4-4bf2-8b70-aa60a6a2f650", "name": "entry : default@PreviewUpdateAssets cost memory 0.139739990234375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727054683500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c05fb809-91d5-40cd-8f52-185681b05804", "name": "runTaskFromQueue task cost before running: 461 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727054793000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f3d2467-c61a-4bd3-95a5-230706ce6ea8", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727051522700, "endTime": 30727054842100, "totalTime": 3252400}, "additional": {"logType": "info", "children": [], "durationId": "7a448803-1daa-4f82-a445-debd6e8df18d"}}, {"head": {"id": "ada558db-85b0-42fa-ac3e-034fac8672d8", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727061578500, "endTime": 30736030361400}, "additional": {"children": ["ea517078-943b-47b3-9f83-03ab44719960"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "ebf9939e-0479-4eb6-957b-0362c19aeb9f", "logId": "a018b96d-263a-4779-a325-2b5566c13747"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ebf9939e-0479-4eb6-957b-0362c19aeb9f", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727056858900}, "additional": {"logType": "detail", "children": [], "durationId": "ada558db-85b0-42fa-ac3e-034fac8672d8"}}, {"head": {"id": "9f1584d4-8221-4ac3-9f04-3deb4304c32d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727057271700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3354f0a7-4d66-4c35-aaae-575c399c2603", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727057352600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08d4b129-0e07-4d18-988a-c6ed189113c6", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727061586400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1238691-815b-4559-824a-caf21e76ada6", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727070633300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b27fcacd-b00a-4ac9-88b4-ae998b1ee4d8", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727070743900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea517078-943b-47b3-9f83-03ab44719960", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker15", "startTime": 30727081319000, "endTime": 30736027592700}, "additional": {"children": ["8a2bf2f5-8a59-4a04-8826-fab835bdbaa8", "4da0d9ca-e5a0-4b6a-8c75-748d409ae04e", "8ccf51e8-7eb8-40c4-bec5-967c360e3138", "0a8d0204-c6d1-41a9-89cc-5bc816139bd8", "2aeae7ed-9456-4984-bdfa-ed04bb7059a9", "47f101a6-4db2-424b-abca-22f7d42a0a88"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "ada558db-85b0-42fa-ac3e-034fac8672d8", "logId": "299564e8-47b1-4235-afdc-08c4418a4199"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b30f55f-c207-4bfe-8087-856370c724cd", "name": "entry : default@PreviewArkTS cost memory -0.5166702270507812", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727083103900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a419d6a-1a10-4daa-8bd9-34483feae16e", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30730893853600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a2bf2f5-8a59-4a04-8826-fab835bdbaa8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker15", "startTime": 30730895223400, "endTime": 30730895240700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea517078-943b-47b3-9f83-03ab44719960", "logId": "5f5c785d-6430-46f8-876e-e6a567eb923b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f5c785d-6430-46f8-876e-e6a567eb923b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30730895223400, "endTime": 30730895240700}, "additional": {"logType": "info", "children": [], "durationId": "8a2bf2f5-8a59-4a04-8826-fab835bdbaa8", "parent": "299564e8-47b1-4235-afdc-08c4418a4199"}}, {"head": {"id": "66bf4280-a2b1-46f3-a555-a5bf142be51f", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736026446500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4da0d9ca-e5a0-4b6a-8c75-748d409ae04e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker15", "startTime": 30736027482900, "endTime": 30736027498100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea517078-943b-47b3-9f83-03ab44719960", "logId": "41734409-67d6-4711-93ee-44e69c65df89"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41734409-67d6-4711-93ee-44e69c65df89", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736027482900, "endTime": 30736027498100}, "additional": {"logType": "info", "children": [], "durationId": "4da0d9ca-e5a0-4b6a-8c75-748d409ae04e", "parent": "299564e8-47b1-4235-afdc-08c4418a4199"}}, {"head": {"id": "299564e8-47b1-4235-afdc-08c4418a4199", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Worker15", "startTime": 30727081319000, "endTime": 30736027592700}, "additional": {"logType": "info", "children": ["5f5c785d-6430-46f8-876e-e6a567eb923b", "41734409-67d6-4711-93ee-44e69c65df89", "9107ec8d-d13c-42fd-bd97-a862feb70322", "55551390-8c5b-46ea-8cec-3e366dbead1a", "570ac287-c9f6-4b8e-b61d-680251ce4750", "6ac79b10-70b9-470a-be37-5d92e7bfd00d"], "durationId": "ea517078-943b-47b3-9f83-03ab44719960", "parent": "a018b96d-263a-4779-a325-2b5566c13747"}}, {"head": {"id": "8ccf51e8-7eb8-40c4-bec5-967c360e3138", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker15", "startTime": 30729508019700, "endTime": 30730827718100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ea517078-943b-47b3-9f83-03ab44719960", "logId": "9107ec8d-d13c-42fd-bd97-a862feb70322"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9107ec8d-d13c-42fd-bd97-a862feb70322", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30729508019700, "endTime": 30730827718100}, "additional": {"logType": "info", "children": [], "durationId": "8ccf51e8-7eb8-40c4-bec5-967c360e3138", "parent": "299564e8-47b1-4235-afdc-08c4418a4199"}}, {"head": {"id": "0a8d0204-c6d1-41a9-89cc-5bc816139bd8", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker15", "startTime": 30730827954800, "endTime": 30730862597400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ea517078-943b-47b3-9f83-03ab44719960", "logId": "55551390-8c5b-46ea-8cec-3e366dbead1a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "55551390-8c5b-46ea-8cec-3e366dbead1a", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30730827954800, "endTime": 30730862597400}, "additional": {"logType": "info", "children": [], "durationId": "0a8d0204-c6d1-41a9-89cc-5bc816139bd8", "parent": "299564e8-47b1-4235-afdc-08c4418a4199"}}, {"head": {"id": "2aeae7ed-9456-4984-bdfa-ed04bb7059a9", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker15", "startTime": 30730862690300, "endTime": 30730862813000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ea517078-943b-47b3-9f83-03ab44719960", "logId": "570ac287-c9f6-4b8e-b61d-680251ce4750"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "570ac287-c9f6-4b8e-b61d-680251ce4750", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30730862690300, "endTime": 30730862813000}, "additional": {"logType": "info", "children": [], "durationId": "2aeae7ed-9456-4984-bdfa-ed04bb7059a9", "parent": "299564e8-47b1-4235-afdc-08c4418a4199"}}, {"head": {"id": "47f101a6-4db2-424b-abca-22f7d42a0a88", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker15", "startTime": 30730862858800, "endTime": 30736026460800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ea517078-943b-47b3-9f83-03ab44719960", "logId": "6ac79b10-70b9-470a-be37-5d92e7bfd00d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ac79b10-70b9-470a-be37-5d92e7bfd00d", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30730862858800, "endTime": 30736026460800}, "additional": {"logType": "info", "children": [], "durationId": "47f101a6-4db2-424b-abca-22f7d42a0a88", "parent": "299564e8-47b1-4235-afdc-08c4418a4199"}}, {"head": {"id": "a018b96d-263a-4779-a325-2b5566c13747", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30727061578500, "endTime": 30736030361400, "totalTime": 8968771600}, "additional": {"logType": "info", "children": ["299564e8-47b1-4235-afdc-08c4418a4199"], "durationId": "ada558db-85b0-42fa-ac3e-034fac8672d8"}}, {"head": {"id": "1110948c-5c72-41fa-b23d-3758379e501d", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736034046400, "endTime": 30736034285300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "29f00410-f5d1-4628-bb74-a1fcd97c9f76", "logId": "27fb254a-35f1-4d23-9d08-f932f4fbc221"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29f00410-f5d1-4628-bb74-a1fcd97c9f76", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736034015900}, "additional": {"logType": "detail", "children": [], "durationId": "1110948c-5c72-41fa-b23d-3758379e501d"}}, {"head": {"id": "2255a68d-cd43-4f21-8151-2b954717d15a", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736034054000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8250aea-51c1-4bba-84d5-e091e1da9941", "name": "entry : PreviewBuild cost memory 0.0115203857421875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736034168200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4028eabf-071f-4a16-85cd-ef66e772ffb3", "name": "runTaskFromQueue task cost before running: 9 s 441 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736034254300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27fb254a-35f1-4d23-9d08-f932f4fbc221", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736034046400, "endTime": 30736034285300, "totalTime": 192600}, "additional": {"logType": "info", "children": [], "durationId": "1110948c-5c72-41fa-b23d-3758379e501d"}}, {"head": {"id": "00156fca-0759-43e3-a978-de1cf86f1852", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736042796700, "endTime": 30736042819000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "85e894a6-dd6d-443f-939e-f134979a3533", "logId": "8ebcbe8d-119b-4120-b34c-de5c7f5c094c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ebcbe8d-119b-4120-b34c-de5c7f5c094c", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736042796700, "endTime": 30736042819000}, "additional": {"logType": "info", "children": [], "durationId": "00156fca-0759-43e3-a978-de1cf86f1852"}}, {"head": {"id": "e280a972-d086-485b-9181-cc5f8c3b808c", "name": "BUILD SUCCESSFUL in 9 s 449 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736042860500}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "20561630-00f1-48d2-b527-3173288af73b", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30726593985200, "endTime": 30736043029900}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 38}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "3549c6eb-b008-4ad8-ac21-464dae8398ae", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736043044400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b70399a-6a6f-4408-ae5f-55cca5d5a470", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736043074800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef40af40-3f91-48b2-87de-b489c295eb88", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736043095700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e97be44d-39f0-4e54-b1b0-241cdd13bca0", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736043118200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "658ec1e8-d876-4acb-8b37-cbc97c729b94", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736043137400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "822e6e7e-4d6b-4f66-942f-f67efaee7a13", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736043168200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88118c41-de63-4d43-a27d-eb55f28dd6c4", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736043185600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc500cba-18fd-47bf-bfb7-8da75caa5866", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736044021500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "431af358-949b-40f6-b942-e03107db6633", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736049902300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcb3af2a-c9e5-460d-b929-405dbd462e97", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736050160700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0565c900-95bd-4dca-ac2d-f4c37389960a", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736056937100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55622d42-f9e0-412b-bfca-fa97a037944b", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:15 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736057412000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "543a1f62-6e64-4fc9-9ce3-78e14b21e410", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736057569900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49a7813f-030d-411f-85f2-1a8ae0b9ad49", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736058129500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "630e036e-7862-4c45-a7ed-495b429e6789", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736058731600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0f673e7-8928-4940-82f1-13dc898a5c89", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736059062900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "596c25f5-d8cc-429f-b773-3e9f4d2aa087", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736059337900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98c79ad4-4319-4db3-b7e5-cc1d7f7d1024", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736059571700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61fb8306-df8f-40f6-9dfb-bc553db73be8", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736062271500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ce73d72-2101-4cee-95c5-53b64adaa664", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736062927700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f58f9011-b7ba-4914-b4c2-9c1aba31fc40", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736062991100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cc33923-c5db-4fbb-8660-98151825fb43", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736063180100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d281dcf1-8ed1-4066-8855-170219173da9", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736063758800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a80fdfea-6ecf-4d35-850d-51353c2789d3", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736072133800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97ec9342-3c42-402e-8307-927281896e88", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736072445100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b012ac5-aeb4-4c53-b833-5167a44f21ce", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736072714900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d4d4495-cb31-4cd5-a625-13b863e7bea5", "name": "Update task entry:default@PreviewArkTS output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736073049800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d378452-5362-4ca1-95cb-440135514fc3", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:14 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736073349800}, "additional": {"logType": "debug", "children": []}}], "workLog": []}