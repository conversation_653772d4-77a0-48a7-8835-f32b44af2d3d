{"version": "2.0", "ppid": 21780, "events": [{"head": {"id": "c8333d8b-0ded-4d90-991e-37d86cb9972d", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30736091030800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6de11cc-7495-4df9-803c-8f2d6fadbb75", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30913332119100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93323bf4-154a-417a-baff-b9e6cedf7340", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30913335957000, "endTime": 30913335985900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea517078-943b-47b3-9f83-03ab44719960", "logId": "eafbb16d-bf9e-4064-87df-21905584ee8f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eafbb16d-bf9e-4064-87df-21905584ee8f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30913335957000, "endTime": 30913335985900}, "additional": {"logType": "info", "children": [], "durationId": "93323bf4-154a-417a-baff-b9e6cedf7340"}}, {"head": {"id": "2ada4290-b935-4081-a773-5b53f25aa74d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30916645725600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac084839-c0e5-43f2-a166-ea0c38535a59", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30916647942000, "endTime": 30916647988800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea517078-943b-47b3-9f83-03ab44719960", "logId": "fbaf7e83-ac56-4bd6-8885-117c23030d46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fbaf7e83-ac56-4bd6-8885-117c23030d46", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30916647942000, "endTime": 30916647988800}, "additional": {"logType": "info", "children": [], "durationId": "ac084839-c0e5-43f2-a166-ea0c38535a59"}}, {"head": {"id": "6a7dfed1-0496-4955-b7c2-fe825bd76286", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30916648096300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ec93291-6af3-4b3c-9548-fa5b21701eeb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30916651140400, "endTime": 30916651187500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea517078-943b-47b3-9f83-03ab44719960", "logId": "d28a11fc-ae9d-4614-b837-e4410d71eb59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d28a11fc-ae9d-4614-b837-e4410d71eb59", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30916651140400, "endTime": 30916651187500}, "additional": {"logType": "info", "children": [], "durationId": "4ec93291-6af3-4b3c-9548-fa5b21701eeb"}}, {"head": {"id": "8ceef13a-9613-41a0-b704-95aed7b3aaac", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30917056111500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0786646-b85a-496a-b901-180bee178b45", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30917059215800, "endTime": 30917059271200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea517078-943b-47b3-9f83-03ab44719960", "logId": "af47c3ae-3d72-448b-bcfb-fbe8a149462c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af47c3ae-3d72-448b-bcfb-fbe8a149462c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30917059215800, "endTime": 30917059271200}, "additional": {"logType": "info", "children": [], "durationId": "f0786646-b85a-496a-b901-180bee178b45"}}, {"head": {"id": "da566db1-9f6c-402c-b0f4-061982defa8b", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30917072414700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8edc21f7-cf84-46fe-b627-542eb4a0c5dd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30917076865400, "endTime": 30917076923800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea517078-943b-47b3-9f83-03ab44719960", "logId": "0533e32e-4da5-41dd-979d-831f4f7d5f29"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0533e32e-4da5-41dd-979d-831f4f7d5f29", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30917076865400, "endTime": 30917076923800}, "additional": {"logType": "info", "children": [], "durationId": "8edc21f7-cf84-46fe-b627-542eb4a0c5dd"}}, {"head": {"id": "e8ed67fb-058f-4d34-b219-ab7f3474c6b5", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30931447133000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "220256a5-66c8-40c4-bad8-b63a1e6b8909", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30931449483500, "endTime": 30931449518400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea517078-943b-47b3-9f83-03ab44719960", "logId": "2d92995a-2d90-4ba9-bf7a-35d26b48e929"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d92995a-2d90-4ba9-bf7a-35d26b48e929", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30931449483500, "endTime": 30931449518400}, "additional": {"logType": "info", "children": [], "durationId": "220256a5-66c8-40c4-bad8-b63a1e6b8909"}}, {"head": {"id": "abf8b327-0771-4dfa-a07c-b8d5769d6e53", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30935653882800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47d766b2-8a59-4265-9ad5-be78140aa02a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30935656820900, "endTime": 30935656864100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea517078-943b-47b3-9f83-03ab44719960", "logId": "a49ec5c3-25d1-4c7b-a270-df0310d511c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a49ec5c3-25d1-4c7b-a270-df0310d511c3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30935656820900, "endTime": 30935656864100}, "additional": {"logType": "info", "children": [], "durationId": "47d766b2-8a59-4265-9ad5-be78140aa02a"}}, {"head": {"id": "4913e8bf-13a7-4056-92a5-490390010831", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30935657063400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "746539aa-17b2-45f7-9a78-7eb7daddf117", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30935659361400, "endTime": 30935659413900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea517078-943b-47b3-9f83-03ab44719960", "logId": "2f3dfb4e-3ed7-4d0b-a187-995cd7d8860f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f3dfb4e-3ed7-4d0b-a187-995cd7d8860f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30935659361400, "endTime": 30935659413900}, "additional": {"logType": "info", "children": [], "durationId": "746539aa-17b2-45f7-9a78-7eb7daddf117"}}, {"head": {"id": "3c7f2a3b-6ecf-4494-a647-0fd4cfd23c91", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30935659581800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3da879d-42cc-4bf3-9ed9-ae66ad718077", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30935665197300, "endTime": 30935665252600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea517078-943b-47b3-9f83-03ab44719960", "logId": "d965e262-9b23-48a3-aa71-02dc51063dc5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d965e262-9b23-48a3-aa71-02dc51063dc5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30935665197300, "endTime": 30935665252600}, "additional": {"logType": "info", "children": [], "durationId": "e3da879d-42cc-4bf3-9ed9-ae66ad718077"}}, {"head": {"id": "5c697ed2-2cab-4961-b019-f51f0d667ee5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30935665508000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78070d97-123d-454b-9e5a-3bbe0eb8af55", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30935669708600, "endTime": 30935669765300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea517078-943b-47b3-9f83-03ab44719960", "logId": "68f8d768-9a33-4db7-a28c-2e15253debea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68f8d768-9a33-4db7-a28c-2e15253debea", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30935669708600, "endTime": 30935669765300}, "additional": {"logType": "info", "children": [], "durationId": "78070d97-123d-454b-9e5a-3bbe0eb8af55"}}, {"head": {"id": "23eef054-e208-4851-9544-fb9ed3d1f27c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30935921735700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8257a9f2-055b-48a7-a5bb-5e23508fc2d9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30935923897000, "endTime": 30935923928300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea517078-943b-47b3-9f83-03ab44719960", "logId": "5f85d9fc-8d0f-4373-9e27-7b59d5e6eb33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f85d9fc-8d0f-4373-9e27-7b59d5e6eb33", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30935923897000, "endTime": 30935923928300}, "additional": {"logType": "info", "children": [], "durationId": "8257a9f2-055b-48a7-a5bb-5e23508fc2d9"}}, {"head": {"id": "de54b68c-749d-45e1-9e88-fccbcfdeb77f", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30935926053700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fe44857-67e8-4636-bf41-87a572812a7a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30935927922300, "endTime": 30935927950400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea517078-943b-47b3-9f83-03ab44719960", "logId": "c85bc290-56ec-4343-9d6d-42c0dc7cb561"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c85bc290-56ec-4343-9d6d-42c0dc7cb561", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30935927922300, "endTime": 30935927950400}, "additional": {"logType": "info", "children": [], "durationId": "3fe44857-67e8-4636-bf41-87a572812a7a"}}, {"head": {"id": "685c3fd1-f8f6-4795-8771-ca152c23d285", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978000009400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cd0e0fa-f304-4d65-9bf9-4b4d0ccfc76c", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978000327200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b008005-428b-4919-a2df-eef3584a0a02", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978780809300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2958654c-b63b-4248-a30c-303a7c7240c5", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978793920900, "endTime": 30979094234900}, "additional": {"children": ["b318d77f-70d8-4b7a-8da2-7cbdbdcf25cd", "928907ca-bb19-4206-b26c-998bca6e448a", "ee39c97b-a754-4b19-ac37-9945f3f7c81e", "b992ab42-0d04-42b8-88dc-e6817d3df3fa", "f64cd525-3334-45c2-b862-f23b808204a3", "df4a77c7-11ca-4ab8-8ba2-f6acbd5e5e40", "a68d13ae-0a41-4382-a0cd-3804598715d9"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "f6afb3f3-5cab-4281-a8bf-58252d11ad23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b318d77f-70d8-4b7a-8da2-7cbdbdcf25cd", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978793922700, "endTime": 30978815273600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2958654c-b63b-4248-a30c-303a7c7240c5", "logId": "242d9e4b-1ac3-4539-9bcc-c9eb4f35ef88"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "928907ca-bb19-4206-b26c-998bca6e448a", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978815297500, "endTime": 30979092477800}, "additional": {"children": ["2d5e13f1-85c4-4789-b347-c8171f96331a", "12940c0a-c38e-4f57-9a95-86cdcf14ce25", "0dd6443f-0967-4f37-af88-62b2c381e163", "f7a3301c-cd9a-4845-973c-97d03a44b6cb", "26064e37-1dd3-44b8-b844-ddcb7c62725a", "b252b9da-1dff-4b94-a742-2767062ddea4", "011eb5c7-9314-401b-8ffa-b8cd76043abc", "cf587a23-96bf-4625-b379-aa311785d0ea", "c51a3222-02bf-4e45-9904-baad2d92b715"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2958654c-b63b-4248-a30c-303a7c7240c5", "logId": "98cb46f8-e47d-4c2d-b5f4-e69bde0ed80d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee39c97b-a754-4b19-ac37-9945f3f7c81e", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979092506500, "endTime": 30979094220800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2958654c-b63b-4248-a30c-303a7c7240c5", "logId": "b708e911-a11d-4c7c-b6c2-70b006b64c8e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b992ab42-0d04-42b8-88dc-e6817d3df3fa", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979094227700, "endTime": 30979094229500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2958654c-b63b-4248-a30c-303a7c7240c5", "logId": "e0f973ee-92c7-4ec2-ba15-4e64f776dad0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f64cd525-3334-45c2-b862-f23b808204a3", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978800098200, "endTime": 30978800136100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2958654c-b63b-4248-a30c-303a7c7240c5", "logId": "719567c2-9c67-4f93-9fca-61227d549dd6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "719567c2-9c67-4f93-9fca-61227d549dd6", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978800098200, "endTime": 30978800136100}, "additional": {"logType": "info", "children": [], "durationId": "f64cd525-3334-45c2-b862-f23b808204a3", "parent": "f6afb3f3-5cab-4281-a8bf-58252d11ad23"}}, {"head": {"id": "df4a77c7-11ca-4ab8-8ba2-f6acbd5e5e40", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978808683300, "endTime": 30978808700800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2958654c-b63b-4248-a30c-303a7c7240c5", "logId": "145a9ff2-75d5-4ba2-8e52-9d38bf9f7bf7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "145a9ff2-75d5-4ba2-8e52-9d38bf9f7bf7", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978808683300, "endTime": 30978808700800}, "additional": {"logType": "info", "children": [], "durationId": "df4a77c7-11ca-4ab8-8ba2-f6acbd5e5e40", "parent": "f6afb3f3-5cab-4281-a8bf-58252d11ad23"}}, {"head": {"id": "8af31201-0a9e-4ba7-aef3-70e112910f4e", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978808756100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a76593ec-e640-4c8f-85d0-1d1815a08023", "name": "Cache service initialization finished in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978815115600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "242d9e4b-1ac3-4539-9bcc-c9eb4f35ef88", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978793922700, "endTime": 30978815273600}, "additional": {"logType": "info", "children": [], "durationId": "b318d77f-70d8-4b7a-8da2-7cbdbdcf25cd", "parent": "f6afb3f3-5cab-4281-a8bf-58252d11ad23"}}, {"head": {"id": "2d5e13f1-85c4-4789-b347-c8171f96331a", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978824695200, "endTime": 30978824708400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "928907ca-bb19-4206-b26c-998bca6e448a", "logId": "0b4c167e-9800-463e-87de-3fd85270e71b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12940c0a-c38e-4f57-9a95-86cdcf14ce25", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978824727700, "endTime": 30978832058500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "928907ca-bb19-4206-b26c-998bca6e448a", "logId": "d8b4f2f0-43e9-4b17-9985-22d416e74330"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0dd6443f-0967-4f37-af88-62b2c381e163", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978832078300, "endTime": 30978970685300}, "additional": {"children": ["8ca3cdad-af9b-4fce-bb81-493f87c5b6dd", "42333b30-0796-467f-8453-1c10354f18dd", "57a52d6b-4d9d-489f-a7be-745dece5828a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "928907ca-bb19-4206-b26c-998bca6e448a", "logId": "9d85c2a0-0c80-4245-bb5c-84aba3510664"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7a3301c-cd9a-4845-973c-97d03a44b6cb", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978970703100, "endTime": 30979024741100}, "additional": {"children": ["541e371a-81fa-4b3e-9fe3-ff2942da97c6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "928907ca-bb19-4206-b26c-998bca6e448a", "logId": "aa54294b-2d77-4a20-8ed6-33ae108de09f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "26064e37-1dd3-44b8-b844-ddcb7c62725a", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979024754300, "endTime": 30979052296300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "928907ca-bb19-4206-b26c-998bca6e448a", "logId": "cc2fb15e-728b-4c5d-a3dc-a0db5901d66a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b252b9da-1dff-4b94-a742-2767062ddea4", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979054321300, "endTime": 30979071951700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "928907ca-bb19-4206-b26c-998bca6e448a", "logId": "75207ac9-e46b-4f37-bf14-c07a4250b879"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "011eb5c7-9314-401b-8ffa-b8cd76043abc", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979071983300, "endTime": 30979092259600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "928907ca-bb19-4206-b26c-998bca6e448a", "logId": "1f64aab7-c076-4b58-b43d-82d3c5b5f33d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf587a23-96bf-4625-b379-aa311785d0ea", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979092287400, "endTime": 30979092461900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "928907ca-bb19-4206-b26c-998bca6e448a", "logId": "d65265db-f015-4ab0-b488-3fbd9efe3564"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b4c167e-9800-463e-87de-3fd85270e71b", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978824695200, "endTime": 30978824708400}, "additional": {"logType": "info", "children": [], "durationId": "2d5e13f1-85c4-4789-b347-c8171f96331a", "parent": "98cb46f8-e47d-4c2d-b5f4-e69bde0ed80d"}}, {"head": {"id": "d8b4f2f0-43e9-4b17-9985-22d416e74330", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978824727700, "endTime": 30978832058500}, "additional": {"logType": "info", "children": [], "durationId": "12940c0a-c38e-4f57-9a95-86cdcf14ce25", "parent": "98cb46f8-e47d-4c2d-b5f4-e69bde0ed80d"}}, {"head": {"id": "8ca3cdad-af9b-4fce-bb81-493f87c5b6dd", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978834187800, "endTime": 30978834215700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0dd6443f-0967-4f37-af88-62b2c381e163", "logId": "0471b8eb-778c-4a25-aa5d-4a9577d7eec2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0471b8eb-778c-4a25-aa5d-4a9577d7eec2", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978834187800, "endTime": 30978834215700}, "additional": {"logType": "info", "children": [], "durationId": "8ca3cdad-af9b-4fce-bb81-493f87c5b6dd", "parent": "9d85c2a0-0c80-4245-bb5c-84aba3510664"}}, {"head": {"id": "42333b30-0796-467f-8453-1c10354f18dd", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978838678200, "endTime": 30978969445500}, "additional": {"children": ["b5c7cfe0-f0c2-4805-8cd0-7e4fec5a2cec", "294d2f9e-9dd1-4ea4-9c26-eb5298d49b46"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0dd6443f-0967-4f37-af88-62b2c381e163", "logId": "8c1d66dc-2285-4646-af0a-2fafac0671b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b5c7cfe0-f0c2-4805-8cd0-7e4fec5a2cec", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978838680900, "endTime": 30978842496400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "42333b30-0796-467f-8453-1c10354f18dd", "logId": "d080ef94-61cf-458b-9fa9-b3d4474705c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "294d2f9e-9dd1-4ea4-9c26-eb5298d49b46", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978842523300, "endTime": 30978969425500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "42333b30-0796-467f-8453-1c10354f18dd", "logId": "674a8012-dc34-481a-b961-7bcb0cbe1217"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e9f759e-09e7-45ac-84b8-adaccb6c97ad", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978838686500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fae71248-d405-4605-b0b3-85cfb1d70231", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978842336500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d080ef94-61cf-458b-9fa9-b3d4474705c2", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978838680900, "endTime": 30978842496400}, "additional": {"logType": "info", "children": [], "durationId": "b5c7cfe0-f0c2-4805-8cd0-7e4fec5a2cec", "parent": "8c1d66dc-2285-4646-af0a-2fafac0671b8"}}, {"head": {"id": "e4725624-f52a-4032-afb3-49d1be5ed444", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978842537500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81ae4bc5-6697-4794-8246-0aa85c5edb76", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978855997500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37fa0e60-5a07-4529-93fd-24214ffbd2f3", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978856134500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b12c5639-409b-4712-9fc9-16485b01f5d7", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978856298200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1f98799-c8eb-4007-9da9-4f2f216b7dd4", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978856442400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68913123-5a23-4f41-988d-1dfa5e999db0", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978859362900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fd1ee21-c702-49bb-8971-29a8a97b5864", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978865398000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39900e1d-2427-407d-beee-b9665a36b70d", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978882667400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2204c9d7-5444-4cb1-9c40-adaa90636329", "name": "Sdk init in 54 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978920909200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "132acecb-a0b1-4ee4-a474-386c033cfd5d", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978921087800}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 42}, "markType": "other"}}, {"head": {"id": "ac86cb36-417e-454f-9751-f1fd9ff5a775", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978921105300}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 42}, "markType": "other"}}, {"head": {"id": "3c916144-3346-40a8-8da4-3063a1e02ac8", "name": "Project task initialization takes 46 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978969064600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04960e07-5b57-403c-a856-4ca1cbcc817c", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978969216900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53932ba2-f6e7-4e9d-872b-7bac3ef4842e", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978969290400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad726c90-4d5f-4aae-a4eb-8f86872b4915", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978969366600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "674a8012-dc34-481a-b961-7bcb0cbe1217", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978842523300, "endTime": 30978969425500}, "additional": {"logType": "info", "children": [], "durationId": "294d2f9e-9dd1-4ea4-9c26-eb5298d49b46", "parent": "8c1d66dc-2285-4646-af0a-2fafac0671b8"}}, {"head": {"id": "8c1d66dc-2285-4646-af0a-2fafac0671b8", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978838678200, "endTime": 30978969445500}, "additional": {"logType": "info", "children": ["d080ef94-61cf-458b-9fa9-b3d4474705c2", "674a8012-dc34-481a-b961-7bcb0cbe1217"], "durationId": "42333b30-0796-467f-8453-1c10354f18dd", "parent": "9d85c2a0-0c80-4245-bb5c-84aba3510664"}}, {"head": {"id": "57a52d6b-4d9d-489f-a7be-745dece5828a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978970649300, "endTime": 30978970670100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0dd6443f-0967-4f37-af88-62b2c381e163", "logId": "d8d8ffbf-95b0-42aa-9eb0-9fa9613bdda6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8d8ffbf-95b0-42aa-9eb0-9fa9613bdda6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978970649300, "endTime": 30978970670100}, "additional": {"logType": "info", "children": [], "durationId": "57a52d6b-4d9d-489f-a7be-745dece5828a", "parent": "9d85c2a0-0c80-4245-bb5c-84aba3510664"}}, {"head": {"id": "9d85c2a0-0c80-4245-bb5c-84aba3510664", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978832078300, "endTime": 30978970685300}, "additional": {"logType": "info", "children": ["0471b8eb-778c-4a25-aa5d-4a9577d7eec2", "8c1d66dc-2285-4646-af0a-2fafac0671b8", "d8d8ffbf-95b0-42aa-9eb0-9fa9613bdda6"], "durationId": "0dd6443f-0967-4f37-af88-62b2c381e163", "parent": "98cb46f8-e47d-4c2d-b5f4-e69bde0ed80d"}}, {"head": {"id": "541e371a-81fa-4b3e-9fe3-ff2942da97c6", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978971859200, "endTime": 30979024723400}, "additional": {"children": ["b3eced1a-b112-4654-ae50-cfd75445a039", "97149dbf-781c-44e8-af9b-3c2d3ea84a1a", "57bbdca9-e570-41c2-972c-1199822e245f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f7a3301c-cd9a-4845-973c-97d03a44b6cb", "logId": "81870ad4-eb75-4475-9a04-d5e7b550a131"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3eced1a-b112-4654-ae50-cfd75445a039", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978979596300, "endTime": 30978979624900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "541e371a-81fa-4b3e-9fe3-ff2942da97c6", "logId": "de2146ca-e926-45c1-9de1-eb1677e7f246"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de2146ca-e926-45c1-9de1-eb1677e7f246", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978979596300, "endTime": 30978979624900}, "additional": {"logType": "info", "children": [], "durationId": "b3eced1a-b112-4654-ae50-cfd75445a039", "parent": "81870ad4-eb75-4475-9a04-d5e7b550a131"}}, {"head": {"id": "97149dbf-781c-44e8-af9b-3c2d3ea84a1a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978987343800, "endTime": 30979021613200}, "additional": {"children": ["1fa26e65-381e-4f58-8acb-6a8eb545921d", "b65bd98b-440a-44a5-8053-0e56d7a53e0e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "541e371a-81fa-4b3e-9fe3-ff2942da97c6", "logId": "617a255f-c889-4a0d-bcb2-d08eb71b0d23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1fa26e65-381e-4f58-8acb-6a8eb545921d", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978987346900, "endTime": 30978993352700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "97149dbf-781c-44e8-af9b-3c2d3ea84a1a", "logId": "4451c3cc-8fe6-407a-9da5-46e32af5c2a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b65bd98b-440a-44a5-8053-0e56d7a53e0e", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978993492300, "endTime": 30979021574200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "97149dbf-781c-44e8-af9b-3c2d3ea84a1a", "logId": "7f4083cf-f5cb-4d5a-bbaa-16e18b7fa793"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bceac707-8590-487f-9f63-afbcad62dad8", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978987354400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "578825f5-6ca6-4df6-b827-7fe6dce1518d", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978993188500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4451c3cc-8fe6-407a-9da5-46e32af5c2a8", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978987346900, "endTime": 30978993352700}, "additional": {"logType": "info", "children": [], "durationId": "1fa26e65-381e-4f58-8acb-6a8eb545921d", "parent": "617a255f-c889-4a0d-bcb2-d08eb71b0d23"}}, {"head": {"id": "e018147c-068a-4be6-aa5a-15d8e02aebaa", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978993517300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d352bcea-e27a-438d-83f3-73d7faa60d96", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979013414400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e47f54b-25d1-404f-85fe-6aa5ab658ac3", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979013595000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6cc4ea7-2e7e-454b-927b-e103ecb3e2d8", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979013898200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "455306d8-ec86-43c2-a19e-f24a63279df1", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979014125400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b60bee1e-c9b0-42cf-a5d8-ac76ac3b1a6f", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979014235400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f501aca8-e7ce-423a-b68c-8e52ec228c75", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979014300100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2cfe2d9-4cb6-4162-86a2-3e01324f6ec7", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979014365800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffdb84ec-dd9c-49ad-bd61-98c960e8be64", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979021185200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8822cf59-7ab5-40ba-a630-6276bc299ec5", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979021384300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61e90196-6a89-4b2f-a0e9-a71a26c6bd32", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979021464200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2d3831e-56d7-4186-b227-547a1fd9a327", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979021521200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f4083cf-f5cb-4d5a-bbaa-16e18b7fa793", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978993492300, "endTime": 30979021574200}, "additional": {"logType": "info", "children": [], "durationId": "b65bd98b-440a-44a5-8053-0e56d7a53e0e", "parent": "617a255f-c889-4a0d-bcb2-d08eb71b0d23"}}, {"head": {"id": "617a255f-c889-4a0d-bcb2-d08eb71b0d23", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978987343800, "endTime": 30979021613200}, "additional": {"logType": "info", "children": ["4451c3cc-8fe6-407a-9da5-46e32af5c2a8", "7f4083cf-f5cb-4d5a-bbaa-16e18b7fa793"], "durationId": "97149dbf-781c-44e8-af9b-3c2d3ea84a1a", "parent": "81870ad4-eb75-4475-9a04-d5e7b550a131"}}, {"head": {"id": "57bbdca9-e570-41c2-972c-1199822e245f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979024673200, "endTime": 30979024699100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "541e371a-81fa-4b3e-9fe3-ff2942da97c6", "logId": "246c2fe9-dc7a-40c5-9b50-90bde5715401"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "246c2fe9-dc7a-40c5-9b50-90bde5715401", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979024673200, "endTime": 30979024699100}, "additional": {"logType": "info", "children": [], "durationId": "57bbdca9-e570-41c2-972c-1199822e245f", "parent": "81870ad4-eb75-4475-9a04-d5e7b550a131"}}, {"head": {"id": "81870ad4-eb75-4475-9a04-d5e7b550a131", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978971859200, "endTime": 30979024723400}, "additional": {"logType": "info", "children": ["de2146ca-e926-45c1-9de1-eb1677e7f246", "617a255f-c889-4a0d-bcb2-d08eb71b0d23", "246c2fe9-dc7a-40c5-9b50-90bde5715401"], "durationId": "541e371a-81fa-4b3e-9fe3-ff2942da97c6", "parent": "aa54294b-2d77-4a20-8ed6-33ae108de09f"}}, {"head": {"id": "aa54294b-2d77-4a20-8ed6-33ae108de09f", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978970703100, "endTime": 30979024741100}, "additional": {"logType": "info", "children": ["81870ad4-eb75-4475-9a04-d5e7b550a131"], "durationId": "f7a3301c-cd9a-4845-973c-97d03a44b6cb", "parent": "98cb46f8-e47d-4c2d-b5f4-e69bde0ed80d"}}, {"head": {"id": "0ff1db39-1337-4181-a611-c70e0a11afb3", "name": "watch files: [\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\hvigorfile.ts',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979051782900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cad86cc-44e5-4336-b6ce-1e6ab528130b", "name": "hvigorfile, resolve hvigorfile dependencies in 28 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979052203500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc2fb15e-728b-4c5d-a3dc-a0db5901d66a", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979024754300, "endTime": 30979052296300}, "additional": {"logType": "info", "children": [], "durationId": "26064e37-1dd3-44b8-b844-ddcb7c62725a", "parent": "98cb46f8-e47d-4c2d-b5f4-e69bde0ed80d"}}, {"head": {"id": "c51a3222-02bf-4e45-9904-baad2d92b715", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979054028500, "endTime": 30979054300700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "928907ca-bb19-4206-b26c-998bca6e448a", "logId": "c2e876f8-e4d3-4900-bebb-0c9e452e0317"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c42c0f3a-571c-4ead-9e7a-5dd459b75dca", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979054063200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2e876f8-e4d3-4900-bebb-0c9e452e0317", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979054028500, "endTime": 30979054300700}, "additional": {"logType": "info", "children": [], "durationId": "c51a3222-02bf-4e45-9904-baad2d92b715", "parent": "98cb46f8-e47d-4c2d-b5f4-e69bde0ed80d"}}, {"head": {"id": "43dc8f8d-0244-4af2-99be-79c059b364ce", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979058069800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21217ff9-6634-4242-bb8f-5f49fb51958c", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979070313800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75207ac9-e46b-4f37-bf14-c07a4250b879", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979054321300, "endTime": 30979071951700}, "additional": {"logType": "info", "children": [], "durationId": "b252b9da-1dff-4b94-a742-2767062ddea4", "parent": "98cb46f8-e47d-4c2d-b5f4-e69bde0ed80d"}}, {"head": {"id": "60ab7cf8-92bd-4356-bb58-3b07260feca0", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979072000100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8919a7a5-f6c0-44aa-be7e-1bcc426718e2", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979082794700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9659c10b-6e4d-41f8-96d3-3e0779372d60", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979082950100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea21ed8c-daa6-41da-b564-f8efca53cb20", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979083237100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "404b316e-1852-41e3-b3c0-8a49d6d0d999", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979087391800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0991bb19-4174-4123-a600-26f1739b19a2", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979087531600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f64aab7-c076-4b58-b43d-82d3c5b5f33d", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979071983300, "endTime": 30979092259600}, "additional": {"logType": "info", "children": [], "durationId": "011eb5c7-9314-401b-8ffa-b8cd76043abc", "parent": "98cb46f8-e47d-4c2d-b5f4-e69bde0ed80d"}}, {"head": {"id": "82abc5b6-d1de-4c29-b317-98f25c67c8a2", "name": "Configuration phase cost:268 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979092330000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d65265db-f015-4ab0-b488-3fbd9efe3564", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979092287400, "endTime": 30979092461900}, "additional": {"logType": "info", "children": [], "durationId": "cf587a23-96bf-4625-b379-aa311785d0ea", "parent": "98cb46f8-e47d-4c2d-b5f4-e69bde0ed80d"}}, {"head": {"id": "98cb46f8-e47d-4c2d-b5f4-e69bde0ed80d", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978815297500, "endTime": 30979092477800}, "additional": {"logType": "info", "children": ["0b4c167e-9800-463e-87de-3fd85270e71b", "d8b4f2f0-43e9-4b17-9985-22d416e74330", "9d85c2a0-0c80-4245-bb5c-84aba3510664", "aa54294b-2d77-4a20-8ed6-33ae108de09f", "cc2fb15e-728b-4c5d-a3dc-a0db5901d66a", "75207ac9-e46b-4f37-bf14-c07a4250b879", "1f64aab7-c076-4b58-b43d-82d3c5b5f33d", "d65265db-f015-4ab0-b488-3fbd9efe3564", "c2e876f8-e4d3-4900-bebb-0c9e452e0317"], "durationId": "928907ca-bb19-4206-b26c-998bca6e448a", "parent": "f6afb3f3-5cab-4281-a8bf-58252d11ad23"}}, {"head": {"id": "a68d13ae-0a41-4382-a0cd-3804598715d9", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979094187600, "endTime": 30979094207400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2958654c-b63b-4248-a30c-303a7c7240c5", "logId": "888cfbcd-8bb8-4fec-8470-a5d4735842df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "888cfbcd-8bb8-4fec-8470-a5d4735842df", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979094187600, "endTime": 30979094207400}, "additional": {"logType": "info", "children": [], "durationId": "a68d13ae-0a41-4382-a0cd-3804598715d9", "parent": "f6afb3f3-5cab-4281-a8bf-58252d11ad23"}}, {"head": {"id": "b708e911-a11d-4c7c-b6c2-70b006b64c8e", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979092506500, "endTime": 30979094220800}, "additional": {"logType": "info", "children": [], "durationId": "ee39c97b-a754-4b19-ac37-9945f3f7c81e", "parent": "f6afb3f3-5cab-4281-a8bf-58252d11ad23"}}, {"head": {"id": "e0f973ee-92c7-4ec2-ba15-4e64f776dad0", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979094227700, "endTime": 30979094229500}, "additional": {"logType": "info", "children": [], "durationId": "b992ab42-0d04-42b8-88dc-e6817d3df3fa", "parent": "f6afb3f3-5cab-4281-a8bf-58252d11ad23"}}, {"head": {"id": "f6afb3f3-5cab-4281-a8bf-58252d11ad23", "name": "init", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978793920900, "endTime": 30979094234900}, "additional": {"logType": "info", "children": ["242d9e4b-1ac3-4539-9bcc-c9eb4f35ef88", "98cb46f8-e47d-4c2d-b5f4-e69bde0ed80d", "b708e911-a11d-4c7c-b6c2-70b006b64c8e", "e0f973ee-92c7-4ec2-ba15-4e64f776dad0", "719567c2-9c67-4f93-9fca-61227d549dd6", "145a9ff2-75d5-4ba2-8e52-9d38bf9f7bf7", "888cfbcd-8bb8-4fec-8470-a5d4735842df"], "durationId": "2958654c-b63b-4248-a30c-303a7c7240c5"}}, {"head": {"id": "cefdce63-ba5c-45f4-9ecf-fa2db8a4a564", "name": "Configuration task cost before running: 307 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979094389900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b5b23ad-1dcf-4846-87e1-d4860a9e92f0", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979102911500, "endTime": 30979118980600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "d9ffd0db-b3bc-4484-b818-ccfcf817a512", "logId": "f9122912-531b-46aa-9827-eccf06cde3ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d9ffd0db-b3bc-4484-b818-ccfcf817a512", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979096682700}, "additional": {"logType": "detail", "children": [], "durationId": "9b5b23ad-1dcf-4846-87e1-d4860a9e92f0"}}, {"head": {"id": "5e9fc4bc-7d03-49db-bc29-3222d244841b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979097627200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20d5b403-245c-4513-a88d-c2a65a57698d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979097758900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b657ddb-3da4-4e4a-94dd-1842cfa94263", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979102934300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "950447ed-1c01-4978-be73-02c14405f378", "name": "Incremental task entry:default@PreBuild pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979118719400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9a334cc-076a-4b68-9e45-a4ca6124a087", "name": "entry : default@PreBuild cost memory 0.27819061279296875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979118881300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9122912-531b-46aa-9827-eccf06cde3ac", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979102911500, "endTime": 30979118980600}, "additional": {"logType": "info", "children": [], "durationId": "9b5b23ad-1dcf-4846-87e1-d4860a9e92f0"}}, {"head": {"id": "066b1306-2f78-4c4c-a046-72acd1df086a", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979127827700, "endTime": 30979130898000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "e2b4f9b5-000a-443e-b9d0-b9732b2903b4", "logId": "73e0b7ac-9b4c-4ee5-9de8-471c87ea5c42"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2b4f9b5-000a-443e-b9d0-b9732b2903b4", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979125836100}, "additional": {"logType": "detail", "children": [], "durationId": "066b1306-2f78-4c4c-a046-72acd1df086a"}}, {"head": {"id": "05d25c80-c879-4718-b2cb-e5e44c0de436", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979126701200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39ec1102-81ac-4049-a0ad-b9ea02d8fda9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979126834200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68b5d682-97f4-4c96-8965-4cc614108d76", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979127838600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d15ccb3-3bd5-4cc3-8f6b-af089c33d0bd", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979130709700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c92f0174-2818-4928-9ce6-7ba95542b861", "name": "entry : default@MergeProfile cost memory 0.10788726806640625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979130820900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73e0b7ac-9b4c-4ee5-9de8-471c87ea5c42", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979127827700, "endTime": 30979130898000}, "additional": {"logType": "info", "children": [], "durationId": "066b1306-2f78-4c4c-a046-72acd1df086a"}}, {"head": {"id": "e693c0c0-b452-43ed-99f2-4ea6c8d3f0f7", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979135641000, "endTime": 30979139438900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "283eac36-85ef-482e-ae65-a7beca5240c2", "logId": "a695a39d-e719-4420-8b4a-077dcdcfd6cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "283eac36-85ef-482e-ae65-a7beca5240c2", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979133341700}, "additional": {"logType": "detail", "children": [], "durationId": "e693c0c0-b452-43ed-99f2-4ea6c8d3f0f7"}}, {"head": {"id": "5b7b4245-0a2b-4142-a0ed-3eca1e8f63e6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979134255000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74408dde-4754-49a8-bdaa-87ad3908b1dd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979134378800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36827441-0811-472e-aabf-7f9296deeea8", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979135650800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bc632a7-ea93-413f-a115-68485fcc51a5", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979137206000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba0e2af6-49ef-4ee0-8a27-ed314b0f06fc", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979139227200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "102145f9-103d-4cce-895e-4ea1eac766b6", "name": "entry : default@CreateBuildProfile cost memory 0.096435546875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979139354500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a695a39d-e719-4420-8b4a-077dcdcfd6cd", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979135641000, "endTime": 30979139438900}, "additional": {"logType": "info", "children": [], "durationId": "e693c0c0-b452-43ed-99f2-4ea6c8d3f0f7"}}, {"head": {"id": "7ec0331a-3269-4744-ab1f-c9c3335bd496", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979144385500, "endTime": 30979144834600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "3aec3430-c129-49f3-b36f-979e93548e2d", "logId": "1809c4b9-b407-4018-899c-e7a4a43ada81"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3aec3430-c129-49f3-b36f-979e93548e2d", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979142348100}, "additional": {"logType": "detail", "children": [], "durationId": "7ec0331a-3269-4744-ab1f-c9c3335bd496"}}, {"head": {"id": "0c7a46a7-c8bc-41ef-ada2-703736cd9e27", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979143170100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50a4b6a2-8868-4020-a9f0-cff6c6e1e6ef", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979143289200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b48ac4e-3834-4541-b13c-4116be65f0de", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979144393800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0db3a554-53a5-4fa0-9f6c-3674f8a3e7c2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979144513700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b905b96-669e-4fb5-b982-473e3959f52c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979144590000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ab499b4-4387-4566-8f90-33e9b9a599c7", "name": "entry : default@PreCheckSyscap cost memory 0.03680419921875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979144682700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f92f7fd-3599-4fec-afeb-ef3cf51e12b7", "name": "runTaskFromQueue task cost before running: 358 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979144777000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1809c4b9-b407-4018-899c-e7a4a43ada81", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979144385500, "endTime": 30979144834600, "totalTime": 370000}, "additional": {"logType": "info", "children": [], "durationId": "7ec0331a-3269-4744-ab1f-c9c3335bd496"}}, {"head": {"id": "3383f901-6b59-48b4-9b8e-9a721c29e293", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979159225600, "endTime": 30979160619200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "199745be-653f-43c8-af2e-684ebfb18755", "logId": "1380ef00-fcd7-4cad-85ca-0b2a89904b20"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "199745be-653f-43c8-af2e-684ebfb18755", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979147194100}, "additional": {"logType": "detail", "children": [], "durationId": "3383f901-6b59-48b4-9b8e-9a721c29e293"}}, {"head": {"id": "b04d433b-3474-4d8d-b7c7-9e83312f9f10", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979148020200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b498b084-5eef-403c-9096-8d5742ed862a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979148128800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38b234cf-808c-430c-8906-3ff58813e9ce", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979159242700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9749490d-1252-4e40-bf10-4b7f309ae1ac", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979159496900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3346865-1665-42a3-b028-7d3900c31b0e", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979160413300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21bbe7ca-c217-43da-b941-ea86e0a03259", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06369781494140625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979160525600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1380ef00-fcd7-4cad-85ca-0b2a89904b20", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979159225600, "endTime": 30979160619200}, "additional": {"logType": "info", "children": [], "durationId": "3383f901-6b59-48b4-9b8e-9a721c29e293"}}, {"head": {"id": "0bdee661-177f-433e-94b1-18b25825193b", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979165550600, "endTime": 30979167501000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "4190210b-2e94-45ef-8a1e-7eaf5272db0a", "logId": "b8770baf-0e6e-45ca-9f5b-35d4f4b6d06e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4190210b-2e94-45ef-8a1e-7eaf5272db0a", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979162943900}, "additional": {"logType": "detail", "children": [], "durationId": "0bdee661-177f-433e-94b1-18b25825193b"}}, {"head": {"id": "4ff91941-133a-44d9-87b4-717efb308dea", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979163729800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e2becff-7f75-4bd8-b859-1b32116d283a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979163838400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f45db2c1-810b-41c4-9974-aaa7a3ddf014", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979165560400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68da0677-26c5-4628-a04e-611abc32d1f0", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979167210800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e3469ab-1710-46db-b1b2-453fc07d2ac0", "name": "entry : default@ProcessProfile cost memory 0.055450439453125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979167388600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8770baf-0e6e-45ca-9f5b-35d4f4b6d06e", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979165550600, "endTime": 30979167501000}, "additional": {"logType": "info", "children": [], "durationId": "0bdee661-177f-433e-94b1-18b25825193b"}}, {"head": {"id": "d35cec52-463c-4c9b-8b07-3ebc299ae41f", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979175590700, "endTime": 30979188082500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e58a02ef-4d05-4106-be66-89b9b9adc198", "logId": "474cfaae-3f5c-45b0-9052-1fe0eff0b5f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e58a02ef-4d05-4106-be66-89b9b9adc198", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979170865900}, "additional": {"logType": "detail", "children": [], "durationId": "d35cec52-463c-4c9b-8b07-3ebc299ae41f"}}, {"head": {"id": "fc7bfe6d-07ab-4c38-af25-4fae257e8fcc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979171963000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6894dd44-690a-4023-9ca7-b7dc14c06353", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979172106700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58228c1a-3bca-4db9-961e-8763804bbb83", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979175610200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc9a7525-2b89-46bd-8849-871177eb5a30", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979187494100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08754c7d-b4b3-4d10-80c1-97d359cdb4b6", "name": "entry : default@ProcessRouterMap cost memory 0.19411468505859375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979187874100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "474cfaae-3f5c-45b0-9052-1fe0eff0b5f6", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979175590700, "endTime": 30979188082500}, "additional": {"logType": "info", "children": [], "durationId": "d35cec52-463c-4c9b-8b07-3ebc299ae41f"}}, {"head": {"id": "6bc9912b-e917-4a36-bc7b-40f5df85d578", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979213734700, "endTime": 30979222178400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "cc5defde-83ba-48a3-8064-38799de7797f", "logId": "e81f4dba-f026-402b-bc69-4399bae73b7c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc5defde-83ba-48a3-8064-38799de7797f", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979198828700}, "additional": {"logType": "detail", "children": [], "durationId": "6bc9912b-e917-4a36-bc7b-40f5df85d578"}}, {"head": {"id": "baab6010-7dd3-488a-8f9a-a29bbb0c201c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979200988900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46969c3f-fb48-4f8e-8096-5f7d851e3994", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979201185100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f13ee27-b45a-4883-83e7-e419bdc9ea3d", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979204301800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "507a2f52-64be-423c-956e-81c4f1c19432", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979217633200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34cfcf77-15b1-45de-abc4-eb3d7efd5cf2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979217992700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1f755f3-e443-4116-8846-4edfe9778497", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979218071500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7eac9dea-fca8-4368-836a-6c54bc8705af", "name": "entry : default@PreviewProcessResource cost memory 0.0698699951171875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979218174900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "598cd062-037f-4b11-88b3-b08562b25560", "name": "runTaskFromQueue task cost before running: 435 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979221914800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e81f4dba-f026-402b-bc69-4399bae73b7c", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979213734700, "endTime": 30979222178400, "totalTime": 4519200}, "additional": {"logType": "info", "children": [], "durationId": "6bc9912b-e917-4a36-bc7b-40f5df85d578"}}, {"head": {"id": "3de0bc35-ab09-4b6a-8784-3cb18c444ea5", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979237853900, "endTime": 30979298296200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0c4dd482-e340-452b-a0b8-c094c1f94346", "logId": "0b3c2716-7c0d-4a40-831c-491d52a18e8a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c4dd482-e340-452b-a0b8-c094c1f94346", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979228167300}, "additional": {"logType": "detail", "children": [], "durationId": "3de0bc35-ab09-4b6a-8784-3cb18c444ea5"}}, {"head": {"id": "f298d662-9b5e-4421-8db3-4929245c995f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979229896400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3022aa4e-f700-446f-8772-6a6be578af25", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979230034800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e616ba8e-2101-4ebf-aff4-75ef154f64e1", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979237877100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e5c41fd-036e-4596-afe4-131ae12c4574", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 33 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979298012100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7748f54d-999b-4270-b7a6-e95f70f036d8", "name": "entry : default@GenerateLoaderJson cost memory 0.7570724487304688", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979298176300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b3c2716-7c0d-4a40-831c-491d52a18e8a", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979237853900, "endTime": 30979298296200}, "additional": {"logType": "info", "children": [], "durationId": "3de0bc35-ab09-4b6a-8784-3cb18c444ea5"}}, {"head": {"id": "8427b7eb-f006-43e5-9510-90829f39d135", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979328894900, "endTime": 30979401811000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "2e35528c-ecd6-48f9-8f56-72e9b0eded1a", "logId": "3c186e54-f83d-45da-a7cf-03d4eb6cffee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e35528c-ecd6-48f9-8f56-72e9b0eded1a", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979320343400}, "additional": {"logType": "detail", "children": [], "durationId": "8427b7eb-f006-43e5-9510-90829f39d135"}}, {"head": {"id": "4864bebf-8c42-498d-9eed-b71bc17846db", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979321354000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "caa8e241-5b2a-42e1-842b-fee56136bd7a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979321506100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b008455-ab54-435c-a199-baef00ab8167", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979323796600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47fd6b65-761c-4b4b-a09e-4338533045fe", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979328928400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "733a5867-3d2e-4005-a52c-0b3928f586e3", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 71 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979401258200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e141fbd6-c32a-44c0-9bcb-c7401ed1c49c", "name": "entry : default@PreviewCompileResource cost memory -1.0503158569335938", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979401559300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c186e54-f83d-45da-a7cf-03d4eb6cffee", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979328894900, "endTime": 30979401811000}, "additional": {"logType": "info", "children": [], "durationId": "8427b7eb-f006-43e5-9510-90829f39d135"}}, {"head": {"id": "044faea9-2037-4c6f-8599-8a1930440bae", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979410596500, "endTime": 30979411072800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "28ac87fc-dd30-4f9e-9fcb-0fae387e0215", "logId": "902a48b6-5e7c-4f10-aaa8-2e73754376c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28ac87fc-dd30-4f9e-9fcb-0fae387e0215", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979408786000}, "additional": {"logType": "detail", "children": [], "durationId": "044faea9-2037-4c6f-8599-8a1930440bae"}}, {"head": {"id": "4e65ebed-aa97-4d37-89d9-7313403de165", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979410228400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42ee8798-a6d1-4662-b18e-44d7247b3f54", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979410462600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "340d161d-a9f7-442a-b3ac-91bc8e8ae9f7", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979410627000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73ad40c8-0dfb-4b9d-ad96-746c6050597d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979410761600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a7d1fc7-bacb-4265-ab9e-58a7c586f238", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979410820300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04301eeb-b44a-4cfc-8e21-cdb51548cca3", "name": "entry : default@PreviewHookCompileResource cost memory 0.03826141357421875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979410921200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "621138a8-e9b2-41af-97e5-62f831d61e23", "name": "runTaskFromQueue task cost before running: 624 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979411015900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "902a48b6-5e7c-4f10-aaa8-2e73754376c6", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979410596500, "endTime": 30979411072800, "totalTime": 401900}, "additional": {"logType": "info", "children": [], "durationId": "044faea9-2037-4c6f-8599-8a1930440bae"}}, {"head": {"id": "2612064c-30f7-46eb-a80b-8f8b9577a458", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979417703300, "endTime": 30979425798800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "0f43f4a0-1a52-43f9-a3be-c365c7856899", "logId": "0796b4da-74c3-4cde-9098-4e5d0f713de8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f43f4a0-1a52-43f9-a3be-c365c7856899", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979414072800}, "additional": {"logType": "detail", "children": [], "durationId": "2612064c-30f7-46eb-a80b-8f8b9577a458"}}, {"head": {"id": "b867c1b2-33f3-4bf0-b0d6-e5d3572b81ca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979415448200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3eac971c-837d-4342-ad8f-ff259df4a978", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979415679700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d08a946-6598-43b9-a65e-1ea621bd3014", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979417724500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f04d509b-fee4-4397-84ae-315d447c40bc", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979425521900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88269429-4b61-4e72-bdfd-26644f3056f9", "name": "entry : default@CopyPreviewProfile cost memory 0.096893310546875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979425704500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0796b4da-74c3-4cde-9098-4e5d0f713de8", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979417703300, "endTime": 30979425798800}, "additional": {"logType": "info", "children": [], "durationId": "2612064c-30f7-46eb-a80b-8f8b9577a458"}}, {"head": {"id": "178464d0-8cc0-4b8e-8178-9832877bbf83", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979433387000, "endTime": 30979434442200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "b42ce424-6d65-4e2f-91ac-8c571bfd3c79", "logId": "e021642e-f138-41d1-ac10-a4a0872a029e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b42ce424-6d65-4e2f-91ac-8c571bfd3c79", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979429356600}, "additional": {"logType": "detail", "children": [], "durationId": "178464d0-8cc0-4b8e-8178-9832877bbf83"}}, {"head": {"id": "a3dabfbf-4f49-4cf6-b1f8-a748a8761b4a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979430388100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d1941cc-8366-4b75-99cc-38f3e5fcc2bc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979430617000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de235ec0-f95a-4a0b-9bf8-be7f4a8b68fb", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979433411200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9aa01b75-b8d9-4768-b3ee-0965f5508faf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979433706600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0fcc735-e5f4-4c54-9ba6-3a46ca563cb1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979433856000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc90afc0-a651-42ec-afc8-a9dee95d02d8", "name": "entry : default@ReplacePreviewerPage cost memory 0.037872314453125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979434118100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea504c3c-bc5a-42bf-87ce-049c393698d9", "name": "runTaskFromQueue task cost before running: 647 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979434308800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e021642e-f138-41d1-ac10-a4a0872a029e", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979433387000, "endTime": 30979434442200, "totalTime": 884700}, "additional": {"logType": "info", "children": [], "durationId": "178464d0-8cc0-4b8e-8178-9832877bbf83"}}, {"head": {"id": "6db7a145-bae3-44a4-985f-eee5ed31100d", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979437814300, "endTime": 30979438456800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "a6ce9cb0-36a7-4a67-a1a7-8a53ce9fe790", "logId": "0ac0e601-e1bb-40b8-8fef-28ff59d90975"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6ce9cb0-36a7-4a67-a1a7-8a53ce9fe790", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979437691000}, "additional": {"logType": "detail", "children": [], "durationId": "6db7a145-bae3-44a4-985f-eee5ed31100d"}}, {"head": {"id": "61090d49-6e1d-49da-aba1-414ebb281258", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979437828500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de770d82-c868-4ef3-9057-143a4a547674", "name": "entry : buildPreviewerResource cost memory 0.0116424560546875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979438108600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03c63a9c-a981-4f6f-bbe8-2c55e6667ea8", "name": "runTaskFromQueue task cost before running: 651 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979438314100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ac0e601-e1bb-40b8-8fef-28ff59d90975", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979437814300, "endTime": 30979438456800, "totalTime": 459600}, "additional": {"logType": "info", "children": [], "durationId": "6db7a145-bae3-44a4-985f-eee5ed31100d"}}, {"head": {"id": "f9ed18d9-239c-462a-8d2b-6fa53d77d638", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979447201200, "endTime": 30979452600200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "81aebfc7-57a4-4099-905c-c38162c84641", "logId": "13eee18d-d298-4ff5-aa70-a95e147f058d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "81aebfc7-57a4-4099-905c-c38162c84641", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979442093600}, "additional": {"logType": "detail", "children": [], "durationId": "f9ed18d9-239c-462a-8d2b-6fa53d77d638"}}, {"head": {"id": "857234d0-11f3-47e2-8f5e-44301c64c820", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979443935800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec8d48c4-5075-4026-97de-9878a2cb83ef", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979444159000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "403c0401-403d-46d8-8d92-6b2f379046c9", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979447215400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c25b47b0-0652-49c9-b06e-a5c108664ba7", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979452370200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b768d2f9-9ccc-44dd-88c5-6901c8080fb8", "name": "entry : default@PreviewUpdateAssets cost memory 0.11354827880859375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979452522300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13eee18d-d298-4ff5-aa70-a95e147f058d", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979447201200, "endTime": 30979452600200}, "additional": {"logType": "info", "children": [], "durationId": "f9ed18d9-239c-462a-8d2b-6fa53d77d638"}}, {"head": {"id": "29ec4370-dff1-4b62-9923-c9e30ffd4535", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979472996400, "endTime": 30989252661400}, "additional": {"children": ["03987e5a-5301-447d-ba31-474f4f9a0e06"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets' has been changed."], "detailId": "324a5fef-e5b2-416a-b867-d34f255e031f", "logId": "6d9e3ed1-9aad-44fb-b8ba-d7af2302653f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "324a5fef-e5b2-416a-b867-d34f255e031f", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979458055200}, "additional": {"logType": "detail", "children": [], "durationId": "29ec4370-dff1-4b62-9923-c9e30ffd4535"}}, {"head": {"id": "a300626d-a68f-4afd-a4ca-d581ce51d0f3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979459359400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b118e39c-4efa-41a4-98df-1168d6a055a9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979459509300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a9f6af3-20c5-4492-b43e-e26037794a34", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979473010800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3cf8019-c3be-4d01-9f57-fc62ddd21ab4", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979537264300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a893a474-e0ca-43aa-8b17-3b4be7db0180", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 54 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979537438900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03987e5a-5301-447d-ba31-474f4f9a0e06", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker16", "startTime": 30979576316000, "endTime": 30989252296700}, "additional": {"children": ["46e21601-dc5c-4db0-b8fe-de4425836037", "1c16910a-e69d-4a4c-95ed-11e036d5b94a", "4dd99bda-63b6-4e8a-8d90-de5540ecc35d", "13f57eda-7fef-44fa-90ed-92c9bf5b784a", "41a77f19-7a2b-4871-83de-d1ffffac8b45", "f8a52ffa-e31e-48b0-93db-c77285e6cb00", "e4066895-a30f-4d3c-ae06-467788f6b4bc"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "29ec4370-dff1-4b62-9923-c9e30ffd4535", "logId": "4f9a59ec-6266-4bc0-a6a9-70000865ddcc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11d9f04d-bc1b-40c2-86e5-a425ebe9e8af", "name": "entry : default@PreviewArkTS cost memory -0.13312530517578125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979583573400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "606e5e85-0bb0-4459-b899-7829a18390ec", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30984584609600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46e21601-dc5c-4db0-b8fe-de4425836037", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30984585635100, "endTime": 30984585649600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03987e5a-5301-447d-ba31-474f4f9a0e06", "logId": "ea5e31d2-216e-4274-bfcd-81c340b6cb4c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea5e31d2-216e-4274-bfcd-81c340b6cb4c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30984585635100, "endTime": 30984585649600}, "additional": {"logType": "info", "children": [], "durationId": "46e21601-dc5c-4db0-b8fe-de4425836037", "parent": "4f9a59ec-6266-4bc0-a6a9-70000865ddcc"}}, {"head": {"id": "91e90deb-0056-408f-bc47-70ea0f8d6686", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30988218630500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c16910a-e69d-4a4c-95ed-11e036d5b94a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30988219592500, "endTime": 30988219606700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03987e5a-5301-447d-ba31-474f4f9a0e06", "logId": "48942c3f-d34f-42ef-8bcd-3e20c89ce22c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48942c3f-d34f-42ef-8bcd-3e20c89ce22c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30988219592500, "endTime": 30988219606700}, "additional": {"logType": "info", "children": [], "durationId": "1c16910a-e69d-4a4c-95ed-11e036d5b94a", "parent": "4f9a59ec-6266-4bc0-a6a9-70000865ddcc"}}, {"head": {"id": "4c12558a-b4c7-4e47-aea6-145b58ece06f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30988219673200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dd99bda-63b6-4e8a-8d90-de5540ecc35d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30988220385200, "endTime": 30988220396100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03987e5a-5301-447d-ba31-474f4f9a0e06", "logId": "448ed837-3c1b-4fd1-bd2f-226236ba178b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "448ed837-3c1b-4fd1-bd2f-226236ba178b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30988220385200, "endTime": 30988220396100}, "additional": {"logType": "info", "children": [], "durationId": "4dd99bda-63b6-4e8a-8d90-de5540ecc35d", "parent": "4f9a59ec-6266-4bc0-a6a9-70000865ddcc"}}, {"head": {"id": "d7843cec-85db-4989-beea-0bccd6e2c39c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30988220444300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13f57eda-7fef-44fa-90ed-92c9bf5b784a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30988220982300, "endTime": 30988220992600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03987e5a-5301-447d-ba31-474f4f9a0e06", "logId": "2f474cbf-9087-4b01-9ab3-b21e01f768c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f474cbf-9087-4b01-9ab3-b21e01f768c2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30988220982300, "endTime": 30988220992600}, "additional": {"logType": "info", "children": [], "durationId": "13f57eda-7fef-44fa-90ed-92c9bf5b784a", "parent": "4f9a59ec-6266-4bc0-a6a9-70000865ddcc"}}, {"head": {"id": "418716d4-0573-4e52-b14a-691d19ffc36c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30988221037600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41a77f19-7a2b-4871-83de-d1ffffac8b45", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30988221798500, "endTime": 30988221833200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03987e5a-5301-447d-ba31-474f4f9a0e06", "logId": "4507a5ac-aef1-41aa-8cb8-a5ad1d811842"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4507a5ac-aef1-41aa-8cb8-a5ad1d811842", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30988221798500, "endTime": 30988221833200}, "additional": {"logType": "info", "children": [], "durationId": "41a77f19-7a2b-4871-83de-d1ffffac8b45", "parent": "4f9a59ec-6266-4bc0-a6a9-70000865ddcc"}}, {"head": {"id": "6726d943-6dc5-4bb7-86e2-c9ce7e8a2bd1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989245914900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8a52ffa-e31e-48b0-93db-c77285e6cb00", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989247003600, "endTime": 30989247018100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03987e5a-5301-447d-ba31-474f4f9a0e06", "logId": "5e85d2cc-2884-4b91-abf9-5c29a371f653"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5e85d2cc-2884-4b91-abf9-5c29a371f653", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989247003600, "endTime": 30989247018100}, "additional": {"logType": "info", "children": [], "durationId": "f8a52ffa-e31e-48b0-93db-c77285e6cb00", "parent": "4f9a59ec-6266-4bc0-a6a9-70000865ddcc"}}, {"head": {"id": "c3e4b8e9-7ec7-4e82-bbe6-786a5faa231d", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989251090000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4066895-a30f-4d3c-ae06-467788f6b4bc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989252106500, "endTime": 30989252128600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03987e5a-5301-447d-ba31-474f4f9a0e06", "logId": "0eae5962-5786-40b1-a5da-487854d94d36"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0eae5962-5786-40b1-a5da-487854d94d36", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989252106500, "endTime": 30989252128600}, "additional": {"logType": "info", "children": [], "durationId": "e4066895-a30f-4d3c-ae06-467788f6b4bc", "parent": "4f9a59ec-6266-4bc0-a6a9-70000865ddcc"}}, {"head": {"id": "4f9a59ec-6266-4bc0-a6a9-70000865ddcc", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Worker16", "startTime": 30979576316000, "endTime": 30989252296700}, "additional": {"logType": "error", "children": ["ea5e31d2-216e-4274-bfcd-81c340b6cb4c", "48942c3f-d34f-42ef-8bcd-3e20c89ce22c", "448ed837-3c1b-4fd1-bd2f-226236ba178b", "2f474cbf-9087-4b01-9ab3-b21e01f768c2", "4507a5ac-aef1-41aa-8cb8-a5ad1d811842", "5e85d2cc-2884-4b91-abf9-5c29a371f653", "0eae5962-5786-40b1-a5da-487854d94d36"], "durationId": "03987e5a-5301-447d-ba31-474f4f9a0e06", "parent": "6d9e3ed1-9aad-44fb-b8ba-d7af2302653f"}}, {"head": {"id": "3221be7f-9d20-4ca6-b3ae-c1c1f60435a6", "name": "default@PreviewArkTS watch work[16] failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989252366100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d9e3ed1-9aad-44fb-b8ba-d7af2302653f", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30979472996400, "endTime": 30989252661400}, "additional": {"logType": "error", "children": ["4f9a59ec-6266-4bc0-a6a9-70000865ddcc"], "durationId": "29ec4370-dff1-4b62-9923-c9e30ffd4535"}}, {"head": {"id": "05ffd087-c021-4e7d-a91e-574ba149d198", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989252881400}, "additional": {"logType": "debug", "children": [], "durationId": "29ec4370-dff1-4b62-9923-c9e30ffd4535"}}, {"head": {"id": "7cb61cde-342e-4cd3-af4d-ad7bb176272f", "name": "ERROR: stacktrace = Error: Compilation failed\n    at handleResponse (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989253766200}, "additional": {"logType": "debug", "children": [], "durationId": "29ec4370-dff1-4b62-9923-c9e30ffd4535"}}, {"head": {"id": "43ff28de-8995-4044-8147-f33a0a8da24f", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989263068700, "endTime": 30989263127500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "606ab694-578f-4c67-bd02-c8d7ad280c3e", "logId": "a4bb0d29-975a-4c64-8ed0-1db64cc14106"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4bb0d29-975a-4c64-8ed0-1db64cc14106", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989263068700, "endTime": 30989263127500}, "additional": {"logType": "info", "children": [], "durationId": "43ff28de-8995-4044-8147-f33a0a8da24f"}}, {"head": {"id": "74d7e29f-ba7c-4d5a-a7a6-a016de0e68ca", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30978787592800, "endTime": 30989263245200}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 42}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "5f71f0ed-c18b-4320-9cf8-4a9fc2b3be6c", "name": "BUILD FAILED in 10 s 476 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989263266300}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "bb462132-8212-4c8b-90d5-0fdca3381e34", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989263384000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "336d8613-dade-495e-b923-c59564777801", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989263417200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfeb7e3e-6a69-4df5-8a9c-ff30041b526b", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989263443300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a556bb72-34fe-453d-ac2a-d42adbdcf4e8", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989263467600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16c05e22-ef90-427c-bd5a-4b2a814dd897", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989263490400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7dc7130-8b06-4995-857c-00ab064613a6", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989263524100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f169978-43c6-402a-8a18-a06b09e49d77", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989263549400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1443d341-22f9-4849-9698-633c3c7b2c96", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989263572700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ad5d925-08b2-4e15-9965-067916a3eee4", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989263596500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9576f81f-0a86-4dc7-8eb3-198bb169b1a7", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989263618200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0520fce8-fb57-4856-b653-2c9b1dc20bff", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989266455100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2ce1294-ce58-4525-b479-665fec81f80a", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989267218900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c47e2f1-5ce2-4049-a78b-c457cb52c7a1", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989267468300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b2aef8a-d70a-4137-ba74-a931c9aeb0ad", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989267675700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2d201e3-9b6f-41ca-831b-595f7e8aed64", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989268262300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea977d0c-b85a-4893-91d8-2ccbbbbfb71f", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989268315900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15f1cc17-20fa-493a-be9f-cea90ed3d487", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989268504700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04a05823-e3b5-4c63-9e9c-e0397572b3ac", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989268689300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbbd2493-6fbd-4f8f-aca8-86af3766fd77", "name": "Update task entry:default@PreviewArkTS output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989268917900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4fe33bf-f50d-4966-a244-a7134656ab2e", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30989269148900}, "additional": {"logType": "debug", "children": []}}], "workLog": []}