{"version": "2.0", "ppid": 21780, "events": [{"head": {"id": "36c01e37-398c-448c-9b7c-0ebc9618c825", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229461265500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ea5248f-7d2f-4120-a71d-60fb3fc97c55", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229471938100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80546984-d4f0-492a-8561-eaefcdda78d9", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31229472242500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77bff5e6-c5d9-4712-aedf-9b43d649e58b", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284637383100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b40c2c8-efc0-45f1-a7b1-d10c80189a30", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284644910800, "endTime": 31284778665800}, "additional": {"children": ["506f291c-6447-4860-89c1-78693099c30b", "a6a3d7f4-f212-48c5-acf8-f409cca0ca02", "de3528e4-2fb4-454c-8980-401b458e5078", "ad6ee0dc-e918-48e0-a13e-6ac47f7917df", "3acaa7fa-21b5-4715-bd7e-2ac0cd5c422d", "19fd7a54-e1c5-4f14-8da9-783daa013fd6", "25b2b841-6173-4296-ac0e-11e608cb9a63"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "4a4319bd-8288-49c3-ba82-bb96133ae64b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "506f291c-6447-4860-89c1-78693099c30b", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284644912700, "endTime": 31284654652200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b40c2c8-efc0-45f1-a7b1-d10c80189a30", "logId": "0d44da33-6e2e-4014-a54e-1facdadc0d0e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6a3d7f4-f212-48c5-acf8-f409cca0ca02", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284654669900, "endTime": 31284777604000}, "additional": {"children": ["862b5846-e439-4e00-8b17-b55e24d9ba31", "38a60a0f-a3fc-4266-8f03-cad868a414e6", "1ebfe3d1-693d-4507-b1ef-d03435c5b994", "24a53f2e-7515-48fe-8e41-753d83a841d4", "dc6ad7af-1381-4f1c-853a-62fb18e2357c", "7f18b396-63e2-4ca7-a74b-06d37722f92b", "8a4b8fe2-11aa-4b40-9b78-4b70674887ae", "4014a9aa-859c-4833-a003-4960b22fb34e", "b993694e-49b9-4c3e-bdc4-b235d14aa5e8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b40c2c8-efc0-45f1-a7b1-d10c80189a30", "logId": "a2c9ff66-d296-477e-83de-5cdbef8156f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de3528e4-2fb4-454c-8980-401b458e5078", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284777619200, "endTime": 31284778659500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b40c2c8-efc0-45f1-a7b1-d10c80189a30", "logId": "75b5a63c-f76c-4444-8c1d-c421bdb70199"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad6ee0dc-e918-48e0-a13e-6ac47f7917df", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284778662500, "endTime": 31284778663600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b40c2c8-efc0-45f1-a7b1-d10c80189a30", "logId": "885abeef-d6f7-4f81-af38-a3119056885a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3acaa7fa-21b5-4715-bd7e-2ac0cd5c422d", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284647701000, "endTime": 31284647732300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b40c2c8-efc0-45f1-a7b1-d10c80189a30", "logId": "ba3ae199-51b0-4be1-b559-fb1db5b6220a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ba3ae199-51b0-4be1-b559-fb1db5b6220a", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284647701000, "endTime": 31284647732300}, "additional": {"logType": "info", "children": [], "durationId": "3acaa7fa-21b5-4715-bd7e-2ac0cd5c422d", "parent": "4a4319bd-8288-49c3-ba82-bb96133ae64b"}}, {"head": {"id": "19fd7a54-e1c5-4f14-8da9-783daa013fd6", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284651149500, "endTime": 31284651165200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b40c2c8-efc0-45f1-a7b1-d10c80189a30", "logId": "d457aa3a-fca4-41f4-b431-7da06afb55ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d457aa3a-fca4-41f4-b431-7da06afb55ea", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284651149500, "endTime": 31284651165200}, "additional": {"logType": "info", "children": [], "durationId": "19fd7a54-e1c5-4f14-8da9-783daa013fd6", "parent": "4a4319bd-8288-49c3-ba82-bb96133ae64b"}}, {"head": {"id": "4302664b-6624-482f-a07d-bed54b12a582", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284651209000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9903491-ac57-424a-be0d-c0560f25032e", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284654555200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d44da33-6e2e-4014-a54e-1facdadc0d0e", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284644912700, "endTime": 31284654652200}, "additional": {"logType": "info", "children": [], "durationId": "506f291c-6447-4860-89c1-78693099c30b", "parent": "4a4319bd-8288-49c3-ba82-bb96133ae64b"}}, {"head": {"id": "862b5846-e439-4e00-8b17-b55e24d9ba31", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284658419400, "endTime": 31284658425600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a6a3d7f4-f212-48c5-acf8-f409cca0ca02", "logId": "ca3cf35d-2789-4406-89c3-5df0f7b6867d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "38a60a0f-a3fc-4266-8f03-cad868a414e6", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284658436100, "endTime": 31284661824400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a6a3d7f4-f212-48c5-acf8-f409cca0ca02", "logId": "eb2906c5-be97-4c52-9208-bebf2e5f0ebd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ebfe3d1-693d-4507-b1ef-d03435c5b994", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284661836100, "endTime": 31284728616700}, "additional": {"children": ["75531a74-0c89-4a14-918e-8070ee6b0b91", "7c147777-6712-469b-aae5-08a28e6b5c3e", "cc7406b2-991a-4726-aa4c-b45e909efdf7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a6a3d7f4-f212-48c5-acf8-f409cca0ca02", "logId": "a7ff2325-cdc4-4120-b1b9-f8b973cba481"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24a53f2e-7515-48fe-8e41-753d83a841d4", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284728650300, "endTime": 31284747840700}, "additional": {"children": ["c84a36c4-2371-412e-92fe-b3ed0d3a6da1"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a6a3d7f4-f212-48c5-acf8-f409cca0ca02", "logId": "8429ebd4-6c4c-4e1d-b38b-8f795708f812"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc6ad7af-1381-4f1c-853a-62fb18e2357c", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284747848000, "endTime": 31284759707300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a6a3d7f4-f212-48c5-acf8-f409cca0ca02", "logId": "c93c476a-c2d3-4c0b-aaa9-597f4ab72f9e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f18b396-63e2-4ca7-a74b-06d37722f92b", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284760752700, "endTime": 31284768571100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a6a3d7f4-f212-48c5-acf8-f409cca0ca02", "logId": "ce2e81e9-908e-403e-98f9-2ef8bb585133"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a4b8fe2-11aa-4b40-9b78-4b70674887ae", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284768595200, "endTime": 31284777482800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a6a3d7f4-f212-48c5-acf8-f409cca0ca02", "logId": "2d215b90-0ba9-4c78-9929-0f11726ffc94"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4014a9aa-859c-4833-a003-4960b22fb34e", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284777511500, "endTime": 31284777596000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a6a3d7f4-f212-48c5-acf8-f409cca0ca02", "logId": "995560a0-3181-4305-9c54-07b4550ce5c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca3cf35d-2789-4406-89c3-5df0f7b6867d", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284658419400, "endTime": 31284658425600}, "additional": {"logType": "info", "children": [], "durationId": "862b5846-e439-4e00-8b17-b55e24d9ba31", "parent": "a2c9ff66-d296-477e-83de-5cdbef8156f9"}}, {"head": {"id": "eb2906c5-be97-4c52-9208-bebf2e5f0ebd", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284658436100, "endTime": 31284661824400}, "additional": {"logType": "info", "children": [], "durationId": "38a60a0f-a3fc-4266-8f03-cad868a414e6", "parent": "a2c9ff66-d296-477e-83de-5cdbef8156f9"}}, {"head": {"id": "75531a74-0c89-4a14-918e-8070ee6b0b91", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284662337200, "endTime": 31284662353100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1ebfe3d1-693d-4507-b1ef-d03435c5b994", "logId": "1e0ff0f4-f24a-4531-9680-0aeea8714b31"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e0ff0f4-f24a-4531-9680-0aeea8714b31", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284662337200, "endTime": 31284662353100}, "additional": {"logType": "info", "children": [], "durationId": "75531a74-0c89-4a14-918e-8070ee6b0b91", "parent": "a7ff2325-cdc4-4120-b1b9-f8b973cba481"}}, {"head": {"id": "7c147777-6712-469b-aae5-08a28e6b5c3e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284663846900, "endTime": 31284728079500}, "additional": {"children": ["d1442e64-224d-401b-8fb7-3274cf6672ed", "3a01f876-902c-4b90-8137-527189a43825"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1ebfe3d1-693d-4507-b1ef-d03435c5b994", "logId": "8bf2d4fd-dab2-47bb-b1f1-1333ec3f55dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1442e64-224d-401b-8fb7-3274cf6672ed", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284663847900, "endTime": 31284666309000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7c147777-6712-469b-aae5-08a28e6b5c3e", "logId": "6a1b7b57-76aa-4f19-9070-e0a5a9285363"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a01f876-902c-4b90-8137-527189a43825", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284666322000, "endTime": 31284728069500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7c147777-6712-469b-aae5-08a28e6b5c3e", "logId": "f7768895-92d1-42fe-972f-a24524792dba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "00644b85-7423-475a-96b5-8bdf2a2cec67", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284663851300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b987544-f477-4af2-9f27-0f58256ea22b", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284666226400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a1b7b57-76aa-4f19-9070-e0a5a9285363", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284663847900, "endTime": 31284666309000}, "additional": {"logType": "info", "children": [], "durationId": "d1442e64-224d-401b-8fb7-3274cf6672ed", "parent": "8bf2d4fd-dab2-47bb-b1f1-1333ec3f55dd"}}, {"head": {"id": "fa942948-db25-4607-914a-ec2c36bb1e58", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284666329100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56512c77-709f-4401-9b56-31306e42c0ef", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284675982400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e910d119-07fb-4880-afbe-333dd333f948", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284676075300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10f97c9d-9603-4805-8851-87ea6fb74233", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284676163100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e8f6113-020c-4d8b-a489-49708ec41f1e", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284676209500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64371d6d-cd3d-4e10-af1f-8f18db9ceb29", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284678554600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "078ea8fe-3b83-4f94-9581-7ee3ac62d171", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284684199600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7605e73-659f-41e7-bbc5-6a6b9684be5d", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284693996400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81e8abc0-1eb9-42c4-b33a-06516c7076e7", "name": "Sdk init in 26 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284712046700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b60ed3cf-29d6-4bba-a33a-90ccc5abcd00", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284712156100}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 47}, "markType": "other"}}, {"head": {"id": "a6588199-1dd3-49f0-a3b1-d3523d0b638b", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284712196500}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 47}, "markType": "other"}}, {"head": {"id": "0485f89a-02b5-4b8f-8753-fc3a8c1901ed", "name": "Project task initialization takes 15 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284727900100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "226583ae-34a7-4cc5-a1cb-15b2fb162f5f", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284727993800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a015b741-d903-4936-bf4b-db02b2aaa12b", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284728023400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dd1d483-c459-4479-b0df-7db3b017286d", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284728047000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7768895-92d1-42fe-972f-a24524792dba", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284666322000, "endTime": 31284728069500}, "additional": {"logType": "info", "children": [], "durationId": "3a01f876-902c-4b90-8137-527189a43825", "parent": "8bf2d4fd-dab2-47bb-b1f1-1333ec3f55dd"}}, {"head": {"id": "8bf2d4fd-dab2-47bb-b1f1-1333ec3f55dd", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284663846900, "endTime": 31284728079500}, "additional": {"logType": "info", "children": ["6a1b7b57-76aa-4f19-9070-e0a5a9285363", "f7768895-92d1-42fe-972f-a24524792dba"], "durationId": "7c147777-6712-469b-aae5-08a28e6b5c3e", "parent": "a7ff2325-cdc4-4120-b1b9-f8b973cba481"}}, {"head": {"id": "cc7406b2-991a-4726-aa4c-b45e909efdf7", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284728593000, "endTime": 31284728606000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1ebfe3d1-693d-4507-b1ef-d03435c5b994", "logId": "a7371070-78a1-40a2-9080-412887b0cc77"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7371070-78a1-40a2-9080-412887b0cc77", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284728593000, "endTime": 31284728606000}, "additional": {"logType": "info", "children": [], "durationId": "cc7406b2-991a-4726-aa4c-b45e909efdf7", "parent": "a7ff2325-cdc4-4120-b1b9-f8b973cba481"}}, {"head": {"id": "a7ff2325-cdc4-4120-b1b9-f8b973cba481", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284661836100, "endTime": 31284728616700}, "additional": {"logType": "info", "children": ["1e0ff0f4-f24a-4531-9680-0aeea8714b31", "8bf2d4fd-dab2-47bb-b1f1-1333ec3f55dd", "a7371070-78a1-40a2-9080-412887b0cc77"], "durationId": "1ebfe3d1-693d-4507-b1ef-d03435c5b994", "parent": "a2c9ff66-d296-477e-83de-5cdbef8156f9"}}, {"head": {"id": "c84a36c4-2371-412e-92fe-b3ed0d3a6da1", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284729129500, "endTime": 31284747830000}, "additional": {"children": ["34409fbe-379a-4d2c-8fa3-e48fc24e9d1a", "de8111a8-b38f-4fad-89bf-d17a3cf87a9c", "08863cc7-47ae-4f73-9e61-5a16589a4f28"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "24a53f2e-7515-48fe-8e41-753d83a841d4", "logId": "582763ca-940d-4644-8320-95540fd96278"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34409fbe-379a-4d2c-8fa3-e48fc24e9d1a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284731999600, "endTime": 31284732012500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c84a36c4-2371-412e-92fe-b3ed0d3a6da1", "logId": "8a2ce4a8-274c-4eff-984d-572f8560f721"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a2ce4a8-274c-4eff-984d-572f8560f721", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284731999600, "endTime": 31284732012500}, "additional": {"logType": "info", "children": [], "durationId": "34409fbe-379a-4d2c-8fa3-e48fc24e9d1a", "parent": "582763ca-940d-4644-8320-95540fd96278"}}, {"head": {"id": "de8111a8-b38f-4fad-89bf-d17a3cf87a9c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284734362800, "endTime": 31284746332100}, "additional": {"children": ["4c91b69c-d04c-4d63-a17f-5a30ccd928db", "2c701c67-4e99-4b53-a8f1-ecbc83db5ddd"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c84a36c4-2371-412e-92fe-b3ed0d3a6da1", "logId": "571792fb-db0c-4ff7-8c89-d8b18812b06c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c91b69c-d04c-4d63-a17f-5a30ccd928db", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284734364000, "endTime": 31284737035500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de8111a8-b38f-4fad-89bf-d17a3cf87a9c", "logId": "0f406e28-ea66-4110-89c6-184dc59e4960"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c701c67-4e99-4b53-a8f1-ecbc83db5ddd", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284737048300, "endTime": 31284746316300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de8111a8-b38f-4fad-89bf-d17a3cf87a9c", "logId": "14bd460b-7eed-4323-8cf4-d98fd9761889"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a42ec34e-b768-4018-857d-eae1ed65359c", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284734366900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e7f7f23-b966-4f0c-bfb5-e4c7cf9d3e70", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284736955600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f406e28-ea66-4110-89c6-184dc59e4960", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284734364000, "endTime": 31284737035500}, "additional": {"logType": "info", "children": [], "durationId": "4c91b69c-d04c-4d63-a17f-5a30ccd928db", "parent": "571792fb-db0c-4ff7-8c89-d8b18812b06c"}}, {"head": {"id": "4312a032-874c-481f-b88b-2794d7634f0b", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284737054400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b044b9e-d5c2-49f9-97da-af223ba40c1c", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284742305500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7ded6ed-c6a6-4b4c-b192-9e5e71212b24", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284742409900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea3c1fdc-25eb-4b9a-a8b5-7b001fb48549", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284742528700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c28a1ba-6d89-4103-8ebc-835768d83b1e", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284742591600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78119ef3-da16-4c02-ba63-a7673cc721db", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284742616800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e82c454-57c8-4cca-85f8-8613900573ad", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284742648500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "761515fb-5761-4fbe-b4a2-62795f19c81b", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284742673900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93aa7132-b228-4469-be93-f306e73c1ef1", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284746082200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f042ba09-a930-4c7e-b82b-1e8de559c921", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284746230400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c75e281b-fd6a-4724-8840-bdd67e678837", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284746270000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed0e3205-df10-4b17-9a31-f5f7b3c625bb", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284746292800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14bd460b-7eed-4323-8cf4-d98fd9761889", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284737048300, "endTime": 31284746316300}, "additional": {"logType": "info", "children": [], "durationId": "2c701c67-4e99-4b53-a8f1-ecbc83db5ddd", "parent": "571792fb-db0c-4ff7-8c89-d8b18812b06c"}}, {"head": {"id": "571792fb-db0c-4ff7-8c89-d8b18812b06c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284734362800, "endTime": 31284746332100}, "additional": {"logType": "info", "children": ["0f406e28-ea66-4110-89c6-184dc59e4960", "14bd460b-7eed-4323-8cf4-d98fd9761889"], "durationId": "de8111a8-b38f-4fad-89bf-d17a3cf87a9c", "parent": "582763ca-940d-4644-8320-95540fd96278"}}, {"head": {"id": "08863cc7-47ae-4f73-9e61-5a16589a4f28", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284747801200, "endTime": 31284747816500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c84a36c4-2371-412e-92fe-b3ed0d3a6da1", "logId": "b8a55b71-c841-419e-b332-9ee1938468ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8a55b71-c841-419e-b332-9ee1938468ac", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284747801200, "endTime": 31284747816500}, "additional": {"logType": "info", "children": [], "durationId": "08863cc7-47ae-4f73-9e61-5a16589a4f28", "parent": "582763ca-940d-4644-8320-95540fd96278"}}, {"head": {"id": "582763ca-940d-4644-8320-95540fd96278", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284729129500, "endTime": 31284747830000}, "additional": {"logType": "info", "children": ["8a2ce4a8-274c-4eff-984d-572f8560f721", "571792fb-db0c-4ff7-8c89-d8b18812b06c", "b8a55b71-c841-419e-b332-9ee1938468ac"], "durationId": "c84a36c4-2371-412e-92fe-b3ed0d3a6da1", "parent": "8429ebd4-6c4c-4e1d-b38b-8f795708f812"}}, {"head": {"id": "8429ebd4-6c4c-4e1d-b38b-8f795708f812", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284728650300, "endTime": 31284747840700}, "additional": {"logType": "info", "children": ["582763ca-940d-4644-8320-95540fd96278"], "durationId": "24a53f2e-7515-48fe-8e41-753d83a841d4", "parent": "a2c9ff66-d296-477e-83de-5cdbef8156f9"}}, {"head": {"id": "5c32ad5d-543f-4e58-aaa1-be8998150e64", "name": "watch files: [\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\hvigorfile.ts',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284759177300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c4b885c-b644-46eb-9f49-0db722771321", "name": "hvigorfile, resolve hvigorfile dependencies in 12 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284759551000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c93c476a-c2d3-4c0b-aaa9-597f4ab72f9e", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284747848000, "endTime": 31284759707300}, "additional": {"logType": "info", "children": [], "durationId": "dc6ad7af-1381-4f1c-853a-62fb18e2357c", "parent": "a2c9ff66-d296-477e-83de-5cdbef8156f9"}}, {"head": {"id": "b993694e-49b9-4c3e-bdc4-b235d14aa5e8", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284760569200, "endTime": 31284760739600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a6a3d7f4-f212-48c5-acf8-f409cca0ca02", "logId": "8b21d2db-2f9c-40df-923f-64ebbc4ffcf3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7cc5f292-06f8-4b40-bb31-1c5505b5b2cb", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284760585800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b21d2db-2f9c-40df-923f-64ebbc4ffcf3", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284760569200, "endTime": 31284760739600}, "additional": {"logType": "info", "children": [], "durationId": "b993694e-49b9-4c3e-bdc4-b235d14aa5e8", "parent": "a2c9ff66-d296-477e-83de-5cdbef8156f9"}}, {"head": {"id": "ace7dd78-1651-4b55-b228-c1aae2b56c11", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284762084100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eda1a08a-2559-497e-b5ba-ba6cdda89511", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284767820400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce2e81e9-908e-403e-98f9-2ef8bb585133", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284760752700, "endTime": 31284768571100}, "additional": {"logType": "info", "children": [], "durationId": "7f18b396-63e2-4ca7-a74b-06d37722f92b", "parent": "a2c9ff66-d296-477e-83de-5cdbef8156f9"}}, {"head": {"id": "0ff45b04-c4ef-4c67-9b1e-f1f86953d8c4", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284768606800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98d3a388-f1dc-49c7-9327-033ff3245ea8", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284773442600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97b853fd-93a5-42e7-a0ba-fb0348ab373c", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284773548800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ee7a747-1820-4c4c-8067-e5a32913e13d", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284773825500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36d61630-ddf2-4877-8302-b3a564d7bd95", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284775374100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a924e644-8199-4055-84ec-53fe68b7dfe4", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284775440400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d215b90-0ba9-4c78-9929-0f11726ffc94", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284768595200, "endTime": 31284777482800}, "additional": {"logType": "info", "children": [], "durationId": "8a4b8fe2-11aa-4b40-9b78-4b70674887ae", "parent": "a2c9ff66-d296-477e-83de-5cdbef8156f9"}}, {"head": {"id": "e92900e5-4e22-457e-bb48-24f63649e38b", "name": "Configuration phase cost:120 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284777531000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "995560a0-3181-4305-9c54-07b4550ce5c6", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284777511500, "endTime": 31284777596000}, "additional": {"logType": "info", "children": [], "durationId": "4014a9aa-859c-4833-a003-4960b22fb34e", "parent": "a2c9ff66-d296-477e-83de-5cdbef8156f9"}}, {"head": {"id": "a2c9ff66-d296-477e-83de-5cdbef8156f9", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284654669900, "endTime": 31284777604000}, "additional": {"logType": "info", "children": ["ca3cf35d-2789-4406-89c3-5df0f7b6867d", "eb2906c5-be97-4c52-9208-bebf2e5f0ebd", "a7ff2325-cdc4-4120-b1b9-f8b973cba481", "8429ebd4-6c4c-4e1d-b38b-8f795708f812", "c93c476a-c2d3-4c0b-aaa9-597f4ab72f9e", "ce2e81e9-908e-403e-98f9-2ef8bb585133", "2d215b90-0ba9-4c78-9929-0f11726ffc94", "995560a0-3181-4305-9c54-07b4550ce5c6", "8b21d2db-2f9c-40df-923f-64ebbc4ffcf3"], "durationId": "a6a3d7f4-f212-48c5-acf8-f409cca0ca02", "parent": "4a4319bd-8288-49c3-ba82-bb96133ae64b"}}, {"head": {"id": "25b2b841-6173-4296-ac0e-11e608cb9a63", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284778637800, "endTime": 31284778650200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b40c2c8-efc0-45f1-a7b1-d10c80189a30", "logId": "2ad86305-2b90-43f0-92bc-4f3a4dd7bb2c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ad86305-2b90-43f0-92bc-4f3a4dd7bb2c", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284778637800, "endTime": 31284778650200}, "additional": {"logType": "info", "children": [], "durationId": "25b2b841-6173-4296-ac0e-11e608cb9a63", "parent": "4a4319bd-8288-49c3-ba82-bb96133ae64b"}}, {"head": {"id": "75b5a63c-f76c-4444-8c1d-c421bdb70199", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284777619200, "endTime": 31284778659500}, "additional": {"logType": "info", "children": [], "durationId": "de3528e4-2fb4-454c-8980-401b458e5078", "parent": "4a4319bd-8288-49c3-ba82-bb96133ae64b"}}, {"head": {"id": "885abeef-d6f7-4f81-af38-a3119056885a", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284778662500, "endTime": 31284778663600}, "additional": {"logType": "info", "children": [], "durationId": "ad6ee0dc-e918-48e0-a13e-6ac47f7917df", "parent": "4a4319bd-8288-49c3-ba82-bb96133ae64b"}}, {"head": {"id": "4a4319bd-8288-49c3-ba82-bb96133ae64b", "name": "init", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284644910800, "endTime": 31284778665800}, "additional": {"logType": "info", "children": ["0d44da33-6e2e-4014-a54e-1facdadc0d0e", "a2c9ff66-d296-477e-83de-5cdbef8156f9", "75b5a63c-f76c-4444-8c1d-c421bdb70199", "885abeef-d6f7-4f81-af38-a3119056885a", "ba3ae199-51b0-4be1-b559-fb1db5b6220a", "d457aa3a-fca4-41f4-b431-7da06afb55ea", "2ad86305-2b90-43f0-92bc-4f3a4dd7bb2c"], "durationId": "9b40c2c8-efc0-45f1-a7b1-d10c80189a30"}}, {"head": {"id": "a7e244a1-6323-464d-8249-e041ddb39b63", "name": "Configuration task cost before running: 138 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284778750600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3efb6a4-1419-4f7a-adaf-dc4213f3c302", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284782580900, "endTime": 31284789189700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "6a4322a5-8288-422a-9ec7-0daced937a22", "logId": "6beffb2b-d497-4e37-b117-bace3de271f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a4322a5-8288-422a-9ec7-0daced937a22", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284779975800}, "additional": {"logType": "detail", "children": [], "durationId": "c3efb6a4-1419-4f7a-adaf-dc4213f3c302"}}, {"head": {"id": "54d45649-acbd-4d17-962a-1c9d5d2fc9d5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284780402400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b534f12-68d1-4253-b97a-133793d99891", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284780474600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e88a7f8-b59b-4a3f-9524-c91a8ddb2427", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284782589600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7110cc6f-5ee2-426a-ab1e-4d2521e99464", "name": "Incremental task entry:default@PreBuild pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284789020100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a247b68-f062-4b64-af13-3c199b6db37c", "name": "entry : default@PreBuild cost memory 0.28430938720703125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284789143600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6beffb2b-d497-4e37-b117-bace3de271f2", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284782580900, "endTime": 31284789189700}, "additional": {"logType": "info", "children": [], "durationId": "c3efb6a4-1419-4f7a-adaf-dc4213f3c302"}}, {"head": {"id": "34b630af-c066-404c-ad73-e0a3ad33c67d", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284793220900, "endTime": 31284794691000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "e49c1683-3d96-4a6e-bb7f-280491530528", "logId": "ababdd8f-79be-4db0-a1d6-37af9f5d15b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e49c1683-3d96-4a6e-bb7f-280491530528", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284792132100}, "additional": {"logType": "detail", "children": [], "durationId": "34b630af-c066-404c-ad73-e0a3ad33c67d"}}, {"head": {"id": "97a48677-0e15-400f-92fb-38f48f6b51d7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284792572000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cce99d26-0637-40da-83f4-3540852fbe20", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284792657200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "037d760d-ccfd-4064-9361-23b8ef5c1e24", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284793228600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "757076a2-72e2-4336-92c6-594943a963ba", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284794565600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83dc099e-36ed-450e-8b6e-976fd568bedb", "name": "entry : default@MergeProfile cost memory 0.10770416259765625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284794649600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ababdd8f-79be-4db0-a1d6-37af9f5d15b2", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284793220900, "endTime": 31284794691000}, "additional": {"logType": "info", "children": [], "durationId": "34b630af-c066-404c-ad73-e0a3ad33c67d"}}, {"head": {"id": "e80f6945-b5db-4b62-9e44-f017c6dde129", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284796908600, "endTime": 31284798975300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ee751f72-d6b1-45eb-9139-ef5cf726d575", "logId": "6a5ba1b3-bc7c-4ff0-8bfa-6a871b66005c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee751f72-d6b1-45eb-9139-ef5cf726d575", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284795892600}, "additional": {"logType": "detail", "children": [], "durationId": "e80f6945-b5db-4b62-9e44-f017c6dde129"}}, {"head": {"id": "8f7877c3-f232-4555-8509-b3c4642440a3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284796272700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b697cac2-d6cf-445f-9126-4a8dfdcf8a07", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284796339300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4324c52a-5932-4faf-a834-a0856b1057ec", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284796916000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "758b790f-0287-44b9-9f9a-2ee32c77d88f", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284797596800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6db87f31-fa25-4706-b129-20858b56e603", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284798453600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7720c4dc-7d01-4441-a98b-5fb12d5ce2e2", "name": "entry : default@CreateBuildProfile cost memory 0.09539794921875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284798523900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a5ba1b3-bc7c-4ff0-8bfa-6a871b66005c", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284796908600, "endTime": 31284798975300}, "additional": {"logType": "info", "children": [], "durationId": "e80f6945-b5db-4b62-9e44-f017c6dde129"}}, {"head": {"id": "0683928f-e7a1-4408-8da1-b48e5181e97c", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284801178900, "endTime": 31284801445100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "e568ecb4-ba31-42a8-82c7-fc61dfa32643", "logId": "d52db4d3-a874-4e78-b517-c4df26357d82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e568ecb4-ba31-42a8-82c7-fc61dfa32643", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284800166300}, "additional": {"logType": "detail", "children": [], "durationId": "0683928f-e7a1-4408-8da1-b48e5181e97c"}}, {"head": {"id": "86d5ca00-5add-46dd-99f1-66c4f335c44a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284800559000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "641d703b-520d-433f-a389-db26ea8ed443", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284800639200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a4589a8-bee7-489d-bcfa-2d0af7de9c14", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284801184500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c76c6b0c-fb2b-4999-bb9a-bafa8a0017ec", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284801293900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f179971-37eb-4786-bae7-3ef6f447cc4b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284801324300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21a274b8-534a-402f-9231-9e6329abe790", "name": "entry : default@PreCheckSyscap cost memory 0.03688812255859375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284801372000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c045a80c-eaff-4ea7-a00f-b8f9f0ff7fd9", "name": "runTaskFromQueue task cost before running: 161 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284801420400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d52db4d3-a874-4e78-b517-c4df26357d82", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284801178900, "endTime": 31284801445100, "totalTime": 228600}, "additional": {"logType": "info", "children": [], "durationId": "0683928f-e7a1-4408-8da1-b48e5181e97c"}}, {"head": {"id": "5d4438b5-9469-4f93-8f20-55a959fe99f1", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284807723700, "endTime": 31284808618800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1e974a97-fd7a-4ab8-9b8d-331f29a530e2", "logId": "a566b1d0-33fe-4ddb-9b35-2e43ed71357a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e974a97-fd7a-4ab8-9b8d-331f29a530e2", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284802574100}, "additional": {"logType": "detail", "children": [], "durationId": "5d4438b5-9469-4f93-8f20-55a959fe99f1"}}, {"head": {"id": "a739f014-900a-4cfb-a15a-fa2b5223a013", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284803040100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bb2761a-c942-45aa-af9b-679bce9ea5e4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284803138200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5ca238c-775d-4158-9cce-9aac5baf5426", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284807733200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f2d8926-b309-4970-b8b5-7a3108c7c191", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284807900900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12a28eeb-7727-4588-b2c2-f63a8ffb4c49", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284808480900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "706a47db-a7da-43a8-8c1a-068830e8db05", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06453704833984375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284808574700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a566b1d0-33fe-4ddb-9b35-2e43ed71357a", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284807723700, "endTime": 31284808618800}, "additional": {"logType": "info", "children": [], "durationId": "5d4438b5-9469-4f93-8f20-55a959fe99f1"}}, {"head": {"id": "f41008d8-adbc-4e64-94b6-d0a614be2312", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284811171900, "endTime": 31284812058600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "22e49e4d-39e6-478f-9d61-505a9818157e", "logId": "accddfdd-bb71-4cc3-991a-677b171b42b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22e49e4d-39e6-478f-9d61-505a9818157e", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284809891700}, "additional": {"logType": "detail", "children": [], "durationId": "f41008d8-adbc-4e64-94b6-d0a614be2312"}}, {"head": {"id": "ce6c0bfc-832f-46d5-bb8b-265028936a70", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284810294600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa02fcd2-bbc7-46ff-ad77-3f381566dcea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284810360200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "284ea04d-8151-42f9-afc2-d0256a710ed8", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284811181200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b25183d6-b018-419e-8863-c519c5f79c88", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284811946800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63530a75-bba8-44fb-960c-435aec342cd7", "name": "entry : default@ProcessProfile cost memory 0.05682373046875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284812018600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "accddfdd-bb71-4cc3-991a-677b171b42b2", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284811171900, "endTime": 31284812058600}, "additional": {"logType": "info", "children": [], "durationId": "f41008d8-adbc-4e64-94b6-d0a614be2312"}}, {"head": {"id": "ae1a8b2a-2474-4c5d-8427-f5aec9057b4f", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284815373200, "endTime": 31284819790300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "64e4533c-bf31-47a5-9d49-b876b97d0d06", "logId": "cd88a169-5429-41fe-8bae-8a59b23f97e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "64e4533c-bf31-47a5-9d49-b876b97d0d06", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284813610800}, "additional": {"logType": "detail", "children": [], "durationId": "ae1a8b2a-2474-4c5d-8427-f5aec9057b4f"}}, {"head": {"id": "245d9de9-a0e7-414d-b167-3a9b7b7c5a13", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284814001900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44b6e09d-ba4c-41c9-86f8-ac2c931ec1ee", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284814073900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad57ea03-9acb-4cf9-a14b-f16ff021a28b", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284815380100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47987fb1-17d7-4f30-8226-611d9eeb9d83", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284819651300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a04e3a7a-fc47-4034-9e77-e3799daaa34d", "name": "entry : default@ProcessRouterMap cost memory 0.19719696044921875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284819749400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd88a169-5429-41fe-8bae-8a59b23f97e4", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284815373200, "endTime": 31284819790300}, "additional": {"logType": "info", "children": [], "durationId": "ae1a8b2a-2474-4c5d-8427-f5aec9057b4f"}}, {"head": {"id": "7dda306c-36ad-49b9-9e55-6927f8f6c484", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284825388600, "endTime": 31284827438000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "c619ddf1-b0ca-4d49-8ab4-c676d377e28d", "logId": "76a2d5ea-b590-4637-809a-f2bba29f1780"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c619ddf1-b0ca-4d49-8ab4-c676d377e28d", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284822106600}, "additional": {"logType": "detail", "children": [], "durationId": "7dda306c-36ad-49b9-9e55-6927f8f6c484"}}, {"head": {"id": "4a9e695e-c778-4655-ab4f-cb987c334dc4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284822500100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ec82f14-6393-4e03-87d0-30a4f836a1d9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284822568400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba725e4d-d891-4c4a-abe3-cb001b0dc1fa", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284823873500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f425b016-5273-4c52-bb07-6875cb3f39a3", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284826237700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a130125-3055-48cb-8fcc-5b3ace51eb4c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284826337000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "651b3b96-1784-447f-acc9-8ce4bfac038f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284826370000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67ba8304-4a02-4c08-b4cc-e6c4d34a8d12", "name": "entry : default@PreviewProcessResource cost memory 0.0698699951171875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284826420700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f3a1fac-6b92-4e9d-8e63-cb6aa5f7d793", "name": "runTaskFromQueue task cost before running: 187 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284827362500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76a2d5ea-b590-4637-809a-f2bba29f1780", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284825388600, "endTime": 31284827438000, "totalTime": 1075000}, "additional": {"logType": "info", "children": [], "durationId": "7dda306c-36ad-49b9-9e55-6927f8f6c484"}}, {"head": {"id": "e34646aa-a1ba-4ed1-8cfe-8d2debd4fcc2", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284832923400, "endTime": 31284851772300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "61d17520-8229-4b8c-a6b8-0a728157e7c9", "logId": "73d04708-8c63-4a16-8df7-26ce476d1a62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61d17520-8229-4b8c-a6b8-0a728157e7c9", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284829656900}, "additional": {"logType": "detail", "children": [], "durationId": "e34646aa-a1ba-4ed1-8cfe-8d2debd4fcc2"}}, {"head": {"id": "f94190cd-a083-4975-aa48-bf172895489d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284830132400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23becd15-d58d-4383-949e-917cee3e67c3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284830203500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec122f22-0ebc-47ca-8731-a06c946c8fad", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284832934200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9837c5bb-8024-4d3d-b724-ad10adead136", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284851496900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab319320-15e8-49bb-9a61-5fe4b645e064", "name": "entry : default@GenerateLoaderJson cost memory 0.746826171875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284851681100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73d04708-8c63-4a16-8df7-26ce476d1a62", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284832923400, "endTime": 31284851772300}, "additional": {"logType": "info", "children": [], "durationId": "e34646aa-a1ba-4ed1-8cfe-8d2debd4fcc2"}}, {"head": {"id": "130326d4-07d2-4a2c-a821-cb189b667893", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284865978100, "endTime": 31284881695800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "76ae970f-9e8d-4789-98c9-0c010b39fb79", "logId": "6c2e987d-ef60-4262-9386-989057da56d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "76ae970f-9e8d-4789-98c9-0c010b39fb79", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284861021000}, "additional": {"logType": "detail", "children": [], "durationId": "130326d4-07d2-4a2c-a821-cb189b667893"}}, {"head": {"id": "9f49b90c-17dd-4d2e-b5be-719e937f6094", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284861643700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71ca7988-7c2f-44c3-b223-2095b486dbac", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284861740700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "219c9b25-a73f-4d4b-8386-eb2e0f1a7abf", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284862664500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cfa3e91-516f-40a6-a84f-4cd4390851ab", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284866001800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88c33b74-ccf3-4bb3-9031-bbd080107afe", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 15 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284881497200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74f6cb76-8c26-421e-9877-d10547eb86d9", "name": "entry : default@PreviewCompileResource cost memory 0.7189712524414062", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284881629400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c2e987d-ef60-4262-9386-989057da56d0", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284865978100, "endTime": 31284881695800}, "additional": {"logType": "info", "children": [], "durationId": "130326d4-07d2-4a2c-a821-cb189b667893"}}, {"head": {"id": "49ddff28-a60f-4847-9f68-e38dc7732a3a", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284884210300, "endTime": 31284884466100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "193ed5ca-1156-4975-b21a-faef15dd6039", "logId": "57627f7c-7bce-4c60-9156-3b7e1235cd70"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "193ed5ca-1156-4975-b21a-faef15dd6039", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284883646600}, "additional": {"logType": "detail", "children": [], "durationId": "49ddff28-a60f-4847-9f68-e38dc7732a3a"}}, {"head": {"id": "635798eb-7c35-40a7-beae-789509d2a2f9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284884066100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0043060-c0e8-446b-8131-dcf4cb2ca9d1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284884146300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdbc6d9c-d11a-4b31-846f-a3fbe22e984e", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284884216000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d0a5619-ae18-43e1-a901-1e658da8681b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284884265000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09809f76-20ae-4d6b-8962-f5a34a1ccdb0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284884290500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f9aa67b-100a-4fe7-a784-1753260b5f4a", "name": "entry : default@PreviewHookCompileResource cost memory 0.0379180908203125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284884327800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dbbfcad-5803-421d-b778-96563e63b5ee", "name": "runTaskFromQueue task cost before running: 244 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284884372300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57627f7c-7bce-4c60-9156-3b7e1235cd70", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284884210300, "endTime": 31284884466100, "totalTime": 150500}, "additional": {"logType": "info", "children": [], "durationId": "49ddff28-a60f-4847-9f68-e38dc7732a3a"}}, {"head": {"id": "db846046-a5a4-452a-b0c6-a6ae66807e20", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284886630200, "endTime": 31284888498500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "12f450bc-6848-4a07-bd75-9344cec8893a", "logId": "110ce15d-1e06-4a00-b3f0-150b3e74e68a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12f450bc-6848-4a07-bd75-9344cec8893a", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284885652400}, "additional": {"logType": "detail", "children": [], "durationId": "db846046-a5a4-452a-b0c6-a6ae66807e20"}}, {"head": {"id": "801bb59d-3b37-4567-876d-00669bb31f26", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284886020400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc0b1554-d4ee-421e-9459-c6b10106a7a0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284886083200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7b5a60b-8598-479b-a4a2-c85af75f749d", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284886635200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c37dae72-f95a-4b04-ad68-bd217f8ea05d", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284888379600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80fb52fc-e617-4445-835e-6f0509a78b9d", "name": "entry : default@CopyPreviewProfile cost memory 0.09670257568359375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284888459700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "110ce15d-1e06-4a00-b3f0-150b3e74e68a", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284886630200, "endTime": 31284888498500}, "additional": {"logType": "info", "children": [], "durationId": "db846046-a5a4-452a-b0c6-a6ae66807e20"}}, {"head": {"id": "5cb02f01-a0d5-4c6c-9643-d8207a1ef9bb", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284890670900, "endTime": 31284890906000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "8d297865-a6d0-4e90-bf2d-4082fcf76582", "logId": "c405bc03-26ab-4683-840f-c3d2a5ca7954"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d297865-a6d0-4e90-bf2d-4082fcf76582", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284889691000}, "additional": {"logType": "detail", "children": [], "durationId": "5cb02f01-a0d5-4c6c-9643-d8207a1ef9bb"}}, {"head": {"id": "1c03bf8e-e2e6-430c-a531-73de3168a403", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284890064700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "716fe212-d523-4440-bd22-f7b08157cbce", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284890127600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d05163af-fd1f-4d80-9c9b-11d398aa9325", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284890676500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c8b21d2-663c-4df2-8837-c7b11b039771", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284890750900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0594319-15cd-4c3e-86c6-bb2f009c433d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284890778200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab55fdc1-49ea-4d8e-b0da-0407e028b91b", "name": "entry : default@ReplacePreviewerPage cost memory 0.037872314453125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284890837700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0244e197-f238-4438-9478-df4e42208626", "name": "runTaskFromQueue task cost before running: 251 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284890882200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c405bc03-26ab-4683-840f-c3d2a5ca7954", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284890670900, "endTime": 31284890906000, "totalTime": 198100}, "additional": {"logType": "info", "children": [], "durationId": "5cb02f01-a0d5-4c6c-9643-d8207a1ef9bb"}}, {"head": {"id": "59adbc3c-70d5-47f0-a597-3451d4d19a00", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284892127400, "endTime": 31284892381800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "e3b71c80-f22c-43cb-a690-c0cb72f09900", "logId": "0f53a1a3-97df-4027-8357-3454dc4fcf98"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3b71c80-f22c-43cb-a690-c0cb72f09900", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284892095900}, "additional": {"logType": "detail", "children": [], "durationId": "59adbc3c-70d5-47f0-a597-3451d4d19a00"}}, {"head": {"id": "b8ae0abf-ee73-4412-8673-7f64fd0684c2", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284892132400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79373c9a-1263-47a4-aa5c-4a997d5a716e", "name": "entry : buildPreviewerResource cost memory 0.0356903076171875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284892240300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20a30b4c-1e68-4aab-aec8-b51efaace7cf", "name": "runTaskFromQueue task cost before running: 252 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284892288700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f53a1a3-97df-4027-8357-3454dc4fcf98", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284892127400, "endTime": 31284892381800, "totalTime": 148800}, "additional": {"logType": "info", "children": [], "durationId": "59adbc3c-70d5-47f0-a597-3451d4d19a00"}}, {"head": {"id": "3ef3eab0-8ab4-434a-924b-80f96db8558a", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284895182900, "endTime": 31284897112600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "d10c5f97-7a7a-4b08-badf-1c808b11e4aa", "logId": "766ff100-0ea1-430e-b183-d148c81a2bcd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d10c5f97-7a7a-4b08-badf-1c808b11e4aa", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284894046400}, "additional": {"logType": "detail", "children": [], "durationId": "3ef3eab0-8ab4-434a-924b-80f96db8558a"}}, {"head": {"id": "07be50f3-2d84-4d00-8bda-95361657bd83", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284894516400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29d79253-eb0b-426a-9009-683db7de87cb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284894589900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83b837f9-47c5-4798-b8b3-c4650c447b6f", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284895188900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bf374d5-268d-4121-83d7-a90c6291cffd", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284896990700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95c78f53-b6cf-448e-80a4-303d99cd8e8e", "name": "entry : default@PreviewUpdateAssets cost memory 0.1135101318359375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284897074400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "766ff100-0ea1-430e-b183-d148c81a2bcd", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284895182900, "endTime": 31284897112600}, "additional": {"logType": "info", "children": [], "durationId": "3ef3eab0-8ab4-434a-924b-80f96db8558a"}}, {"head": {"id": "17f02457-9e77-4477-bb85-4aa82e28649f", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284902866500}, "additional": {"children": ["5a9ac56e-53f4-4f98-83e2-644716b3e3b5"], "state": "running", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "56a3bd3a-11d7-44fd-a7fe-cce67dfc884b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56a3bd3a-11d7-44fd-a7fe-cce67dfc884b", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284898729100}, "additional": {"logType": "detail", "children": [], "durationId": "17f02457-9e77-4477-bb85-4aa82e28649f"}}, {"head": {"id": "a48264b5-010a-4d3e-a22c-ca32e3e699c4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284899095700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dee70e6-93ca-41a4-884e-f4d22fd07a2d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284899201600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a551c30-5a95-4043-a4c6-def156e4f76b", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284902874200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a9ac56e-53f4-4f98-83e2-644716b3e3b5", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker18", "startTime": 31284918229300}, "additional": {"children": ["8fa008e3-f135-4090-a5b0-fa966b9d3abc"], "state": "running", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "17f02457-9e77-4477-bb85-4aa82e28649f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f3c5f565-8b96-459e-b510-c3da924d5f6f", "name": "entry : default@PreviewArkTS cost memory 0.9893951416015625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284920162200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da6ae537-9037-487d-ada0-e333f7772a74", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31288531774700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fa008e3-f135-4090-a5b0-fa966b9d3abc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31288532698500, "endTime": 31288532714000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5a9ac56e-53f4-4f98-83e2-644716b3e3b5", "logId": "fa0618f4-e813-4c00-adfc-c8f513a608f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fa0618f4-e813-4c00-adfc-c8f513a608f2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31288532698500, "endTime": 31288532714000}, "additional": {"logType": "info", "children": [], "durationId": "8fa008e3-f135-4090-a5b0-fa966b9d3abc"}}, {"head": {"id": "9346d321-ed4f-46a0-b3a9-526cea2326c6", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289124933300, "endTime": 31289124964100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "389a1264-1a3a-401a-aa74-ffeba6dad6ad", "logId": "8dd4a865-5d19-4efb-8301-28f7256dc2c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8dd4a865-5d19-4efb-8301-28f7256dc2c5", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289124933300, "endTime": 31289124964100}, "additional": {"logType": "info", "children": [], "durationId": "9346d321-ed4f-46a0-b3a9-526cea2326c6"}}, {"head": {"id": "189a7793-062f-4564-bb67-d79557d6d51d", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31284640768400, "endTime": 31289125055500}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 47}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "c7108d01-b1d3-4909-a25d-8eedf41b9240", "name": "BUILD FAILED in 4 s 485 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289125079000}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "fa2572cf-6bd7-48ea-bcdb-7a8aeea1a48e", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289125232300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "beec95ae-d94f-484e-89dd-11c660c4025a", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289125262200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f95f390e-fc71-4b41-8870-7682a6bd1209", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289125285300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8da3d672-9be5-41df-85ba-aafe720b5d1e", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289125306100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1d3346a-ffdf-430a-88af-8e0bd784c9d1", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289125331200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ae85f23-1944-4c58-ac1c-145e53bdb21b", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289125354900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f868dac-9e64-4910-8091-6e185eb6931c", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289125375100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c78b7bf1-3736-43da-aaf0-9a01e5fa200e", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289125396100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5aa21a6-a453-4640-b945-0f1e9076654a", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289125417400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b940ed4-252b-4b44-a7b9-6d0a49b03ad7", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289125436900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c27c767b-94e8-48ee-b801-ffd70cb8e391", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289128177200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3b86c27-0ec4-460a-b10f-ffdbf66e5e23", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289128969300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50915bfc-7be1-4cb9-8706-6fccb6cea587", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289129279300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07d7dfa0-39c2-433e-8949-668dba6c8167", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289129500700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71878c82-9b77-42c3-b6ff-1255b8fe428a", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289130202100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cd96420-c6aa-427b-9f75-f36ce2291af2", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289138550300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c7ac336-1e8e-48c5-8027-1f7ac6c4c08f", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289138866200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eec6cb47-2a95-4a58-b284-4aa5858582f9", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289139101200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "231b36a3-0df1-43ba-983f-ceb8705ce066", "name": "Update task entry:default@PreviewArkTS output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289139360000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9113b8ef-8d66-4989-a703-f510a627feed", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:15 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289139609100}, "additional": {"logType": "debug", "children": []}}], "workLog": []}