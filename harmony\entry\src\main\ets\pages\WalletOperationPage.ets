import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { WalletApi } from '../api/WalletApi';
import { BankCardApi } from '../api/BankCardApi';
import { storageManager } from '../common/storage/StorageManager';
import { globalStateManager, RefreshTypes } from '../common/utils/EventManager';
import {
  WalletRechargeRequest,
  WalletWithdrawRequest,
  WalletTransferRequest,
  BankCard,
  WalletOperationParams,
  WalletInfo
} from '../common/types/index';


@Entry
@Component
struct WalletOperationPage {
  @State operationType: string = 'recharge'; // 'recharge' | 'withdraw' | 'transfer' | 'receive'
  @State amount: string = '';
  @State payPassword: string = '';
  @State selectedCardId: number = -1;
  @State bankCards: BankCard[] = [];
  @State walletBalance: number = 0;
  @State payLimit: number = 0;
  @State isLoading: boolean = false;
  
  // 转账相关
  @State toPhone: string = '';
  @State description: string = '';
  
  // 收钱相关
  @State receiveAmount: string = '';
  @State receiveDescription: string = '';

  aboutToAppear() {
    // 检查路由参数，确定操作类型
    const params = router.getParams() as WalletOperationParams;
    if (params?.operationType) {
      this.operationType = params.operationType;
    }
    
    this.loadData();
  }

  async loadData() {
    try {
      this.isLoading = true;

      // 获取当前用户信息
      const userInfo = await storageManager.getUserInfo();
      if (!userInfo) {
        promptAction.showToast({ message: '请先登录' });
        router.back();
        return;
      }

      // 加载银行卡列表
      console.log('WalletOperationPage - 开始加载银行卡列表，用户ID:', userInfo.userId);
      this.bankCards = await BankCardApi.getCardList(userInfo.userId);
      console.log('WalletOperationPage - 银行卡列表加载完成，数量:', this.bankCards.length);

      // 加载钱包余额
      const walletInfo: WalletInfo = await WalletApi.getBalance();
      this.walletBalance = walletInfo?.balance || 0;

      // 加载用户支付限额
      this.payLimit = userInfo?.payLimit || 0;
      
    } catch (error) {
      console.error('加载数据失败:', error);
      promptAction.showToast({ message: '加载数据失败' });
    } finally {
      this.isLoading = false;
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      this.TopNavigationView()
      
      // 操作类型切换标签
      this.OperationTypeTabsView()
      
      if (this.isLoading) {
        this.LoadingView()
      } else {
        // 根据操作类型显示不同内容
        this.OperationContentView()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  TopNavigationView() {
    Row() {
      Button() {
        Image($r('app.media.ic_back'))
          .width(24)
          .height(24)
          .fillColor('#333333')
      }
      .width(40)
      .height(40)
      .backgroundColor('transparent')
      .onClick(() => {
        router.back();
      })

      Text(this.getPageTitle())
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .layoutWeight(1)
        .textAlign(TextAlign.Center)
        .fontColor('#333333')

      // 占位，保持标题居中
      Row().width(40).height(40)
    }
    .width('100%')
    .height(56)
    .padding({ left: 16, right: 16 })
    .backgroundColor('#FFFFFF')
  }

  @Builder
  OperationTypeTabsView() {
    Row() {
      ForEach(['recharge', 'withdraw', 'transfer', 'receive'], (type: string) => {
        Button(this.getOperationTypeText(type))
          .fontSize(14)
          .fontColor(this.operationType === type ? '#FFFFFF' : '#666666')
          .backgroundColor(this.operationType === type ? '#1976D2' : '#F8F9FA')
          .borderRadius(20)
          .height(36)
          .padding({ left: 16, right: 16 })
          .margin({ right: 8 })
          .onClick(() => {
            this.operationType = type;
            this.resetForm();
          })
      })
    }
    .width('100%')
    .padding({ left: 16, right: 16, top: 12, bottom: 12 })
    .backgroundColor('#FFFFFF')
  }

  @Builder
  LoadingView() {
    Column() {
      LoadingProgress()
        .width(40)
        .height(40)
        .color('#1976D2')
      
      Text('加载中...')
        .fontSize(14)
        .fontColor('#666666')
        .margin({ top: 12 })
    }
    .width('100%')
    .height(200)
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  @Builder
  OperationContentView() {
    Column() {
      if (this.operationType === 'recharge') {
        this.RechargeContentView()
      } else if (this.operationType === 'withdraw') {
        this.WithdrawContentView()
      } else if (this.operationType === 'transfer') {
        this.TransferContentView()
      } else if (this.operationType === 'receive') {
        this.ReceiveContentView()
      }
    }
    .layoutWeight(1)
    .padding(16)
  }

  /**
   * 获取页面标题
   */
  getPageTitle(): string {
    switch (this.operationType) {
      case 'recharge': return '钱包充值';
      case 'withdraw': return '钱包提现';
      case 'transfer': return '钱包转账';
      case 'receive': return '收钱码';
      default: return '钱包操作';
    }
  }

  /**
   * 获取操作类型文本
   */
  getOperationTypeText(type: string): string {
    switch (type) {
      case 'recharge': return '充值';
      case 'withdraw': return '提现';
      case 'transfer': return '转账';
      case 'receive': return '收钱';
      default: return type;
    }
  }

  /**
   * 重置表单
   */
  resetForm() {
    this.amount = '';
    this.payPassword = '';
    this.selectedCardId = -1;
    this.toPhone = '';
    this.description = '';
    this.receiveAmount = '';
    this.receiveDescription = '';
  }

  @Builder
  RechargeContentView() {
    Column() {
      // 钱包余额显示
      this.WalletBalanceView()

      // 充值金额输入
      this.AmountInputView('请输入充值金额')

      // 银行卡选择
      this.BankCardSelectorView()

      // 支付密码输入
      this.PayPasswordInputView()

      // 充值按钮
      Button('立即充值')
        .width('100%')
        .height(48)
        .fontSize(16)
        .fontColor('#FFFFFF')
        .backgroundColor('#1976D2')
        .borderRadius(8)
        .margin({ top: 24 })
        .enabled(!this.isLoading && !!this.amount && this.selectedCardId > 0 && !!this.payPassword)
        .onClick(() => {
          this.performRecharge();
        })
    }
    .width('100%')
    .alignItems(HorizontalAlign.Start)
  }

  @Builder
  WithdrawContentView() {
    Column() {
      // 钱包余额显示
      this.WalletBalanceView()

      // 提现金额输入
      this.AmountInputView('请输入提现金额')

      // 银行卡选择
      this.BankCardSelectorView()

      // 支付密码输入
      this.PayPasswordInputView()

      // 提现按钮
      Button('立即提现')
        .width('100%')
        .height(48)
        .fontSize(16)
        .fontColor('#FFFFFF')
        .backgroundColor('#FF9800')
        .borderRadius(8)
        .margin({ top: 24 })
        .enabled(!this.isLoading && !!this.amount && this.selectedCardId > 0 && !!this.payPassword)
        .onClick(() => {
          this.performWithdraw();
        })
    }
    .width('100%')
    .alignItems(HorizontalAlign.Start)
  }

  @Builder
  TransferContentView() {
    Column() {
      // 钱包余额显示
      this.WalletBalanceView()

      // 收款人手机号输入
      Column() {
        Text('收款人手机号')
          .fontSize(14)
          .fontColor('#333333')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 8 })

        TextInput({ placeholder: '请输入收款人手机号', text: this.toPhone })
          .fontSize(16)
          .borderRadius(8)
          .backgroundColor('#F8F9FA')
          .onChange((value: string) => {
            this.toPhone = value;
          })
      }
      .width('100%')
      .margin({ bottom: 16 })

      // 转账金额输入
      this.AmountInputView('请输入转账金额')

      // 转账说明
      Column() {
        Text('转账说明（可选）')
          .fontSize(14)
          .fontColor('#333333')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 8 })

        TextInput({ placeholder: '请输入转账说明', text: this.description })
          .fontSize(16)
          .borderRadius(8)
          .backgroundColor('#F8F9FA')
          .onChange((value: string) => {
            this.description = value;
          })
      }
      .width('100%')
      .margin({ bottom: 16 })

      // 支付密码输入
      this.PayPasswordInputView()

      // 转账按钮
      Button('立即转账')
        .width('100%')
        .height(48)
        .fontSize(16)
        .fontColor('#FFFFFF')
        .backgroundColor('#4CAF50')
        .borderRadius(8)
        .margin({ top: 24 })
        .enabled(!this.isLoading && !!this.amount && !!this.toPhone && !!this.payPassword)
        .onClick(() => {
          this.performTransfer();
        })
    }
    .width('100%')
    .alignItems(HorizontalAlign.Start)
  }

  @Builder
  ReceiveContentView() {
    Column() {
      // 收钱金额输入
      Column() {
        Text('收钱金额')
          .fontSize(14)
          .fontColor('#333333')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 8 })

        TextInput({ placeholder: '请输入收钱金额', text: this.receiveAmount })
          .fontSize(16)
          .borderRadius(8)
          .backgroundColor('#F8F9FA')
          .onChange((value: string) => {
            this.receiveAmount = value;
          })
      }
      .width('100%')
      .margin({ bottom: 16 })

      // 收钱说明
      Column() {
        Text('收钱说明（可选）')
          .fontSize(14)
          .fontColor('#333333')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 8 })

        TextInput({ placeholder: '请输入收钱说明', text: this.receiveDescription })
          .fontSize(16)
          .borderRadius(8)
          .backgroundColor('#F8F9FA')
          .onChange((value: string) => {
            this.receiveDescription = value;
          })
      }
      .width('100%')
      .margin({ bottom: 24 })

      // 收钱码显示区域
      Column() {
        Text('💰')
          .fontSize(80)
          .fontColor('#4CAF50')
          .margin({ bottom: 16 })

        Text('向我付钱')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .fontColor('#333333')
          .margin({ bottom: 8 })

        if (this.receiveAmount) {
          Text(`¥${this.receiveAmount}`)
            .fontSize(24)
            .fontWeight(FontWeight.Bold)
            .fontColor('#4CAF50')
            .margin({ bottom: 8 })
        }

        if (this.receiveDescription) {
          Text(this.receiveDescription)
            .fontSize(14)
            .fontColor('#666666')
        }
      }
      .width('100%')
      .height(200)
      .justifyContent(FlexAlign.Center)
      .alignItems(HorizontalAlign.Center)
      .backgroundColor('#FFFFFF')
      .borderRadius(12)
      .border({ width: 2, color: '#4CAF50', style: BorderStyle.Dashed })
    }
    .width('100%')
    .alignItems(HorizontalAlign.Start)
  }

  @Builder
  WalletBalanceView() {
    Column() {
      Text('钱包余额')
        .fontSize(14)
        .fontColor('#666666')
        .margin({ bottom: 8 })

      Text(`¥${this.walletBalance.toFixed(2)}`)
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .fontColor('#1976D2')
    }
    .width('100%')
    .padding(16)
    .backgroundColor('#FFFFFF')
    .borderRadius(12)
    .margin({ bottom: 16 })
    .alignItems(HorizontalAlign.Start)
  }

  @Builder
  AmountInputView(placeholder: string) {
    Column() {
      Text('金额')
        .fontSize(14)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 8 })

      TextInput({ placeholder: placeholder, text: this.amount })
        .fontSize(16)
        .borderRadius(8)
        .backgroundColor('#F8F9FA')
        .type(InputType.Number)
        .onChange((value: string) => {
          this.amount = value;
        })
    }
    .width('100%')
    .margin({ bottom: 16 })
  }

  @Builder
  BankCardSelectorView() {
    Column() {
      Text('选择银行卡')
        .fontSize(14)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 8 })

      if (this.bankCards.length === 0) {
        Button('添加银行卡')
          .width('100%')
          .height(48)
          .fontSize(14)
          .fontColor('#1976D2')
          .backgroundColor('#E3F2FD')
          .borderRadius(8)
          .onClick(() => {
            router.pushUrl({ url: 'pages/AddBankCardPage' });
          })
      } else {
        Column() {
          ForEach(this.bankCards, (card: BankCard) => {
            Row() {
              Radio({ value: card.cardId.toString(), group: 'bankCardGroup' })
                .checked(this.selectedCardId === card.cardId)
                .onChange((isChecked: boolean) => {
                  if (isChecked) {
                    this.selectedCardId = card.cardId;
                  }
                })
                .margin({ right: 12 })

              Column() {
                Text(card.bankName)
                  .fontSize(16)
                  .fontColor('#333333')
                  .fontWeight(FontWeight.Medium)
                  .alignSelf(ItemAlign.Start)

                Text(`**** **** **** ${card.cardNo.slice(-4)}`)
                  .fontSize(14)
                  .fontColor('#666666')
                  .alignSelf(ItemAlign.Start)
                  .margin({ top: 4 })
              }
              .layoutWeight(1)
              .alignItems(HorizontalAlign.Start)
            }
            .width('100%')
            .padding(12)
            .backgroundColor('#FFFFFF')
            .borderRadius(8)
            .margin({ bottom: 8 })
            .onClick(() => {
              this.selectedCardId = card.cardId;
            })
          })
        }
      }
    }
    .width('100%')
    .margin({ bottom: 16 })
  }

  @Builder
  PayPasswordInputView() {
    Column() {
      Text('支付密码')
        .fontSize(14)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 8 })

      TextInput({ placeholder: '请输入支付密码', text: this.payPassword })
        .fontSize(16)
        .borderRadius(8)
        .backgroundColor('#F8F9FA')
        .type(InputType.Password)
        .onChange((value: string) => {
          this.payPassword = value;
        })
    }
    .width('100%')
    .margin({ bottom: 16 })
  }

  /**
   * 执行充值操作
   */
  async performRecharge() {
    try {
      this.isLoading = true;

      const amountNum = parseFloat(this.amount);
      if (isNaN(amountNum) || amountNum <= 0) {
        promptAction.showToast({ message: '请输入有效的充值金额' });
        return;
      }

      if (amountNum > this.payLimit) {
        promptAction.showToast({ message: `充值金额不能超过支付限额 ¥${this.payLimit}` });
        return;
      }

      // 获取用户信息
      const userInfo = await storageManager.getUserInfo();
      if (!userInfo) {
        promptAction.showToast({ message: "用户信息获取失败" });
        return;
      }

      const request: WalletRechargeRequest = {
        userId: userInfo.userId,
        amount: amountNum,
        cardId: this.selectedCardId,
        payPassword: this.payPassword
      };

      await WalletApi.recharge(request);

      promptAction.showToast({ message: '充值成功' });

      // 刷新钱包余额
      globalStateManager.notifyRefresh(RefreshTypes.WALLET_BALANCE);

      // 返回上一页
      router.back();

    } catch (error) {
      console.error('充值失败:', error);
      promptAction.showToast({ message: '充值失败，请重试' });
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 执行提现操作
   */
  async performWithdraw() {
    try {
      this.isLoading = true;

      const amountNum = parseFloat(this.amount);
      if (isNaN(amountNum) || amountNum <= 0) {
        promptAction.showToast({ message: '请输入有效的提现金额' });
        return;
      }

      if (amountNum > this.walletBalance) {
        promptAction.showToast({ message: '提现金额不能超过钱包余额' });
        return;
      }

      if (amountNum > this.payLimit) {
        promptAction.showToast({ message: `提现金额不能超过支付限额 ¥${this.payLimit}` });
        return;
      }

      // 获取用户信息
      const userInfo = await storageManager.getUserInfo();
      if (!userInfo) {
        promptAction.showToast({ message: "用户信息获取失败" });
        return;
      }

      const request: WalletWithdrawRequest = {
        userId: userInfo.userId,
        amount: amountNum,
        cardId: this.selectedCardId,
        payPassword: this.payPassword
      };

      await WalletApi.withdraw(request);

      promptAction.showToast({ message: '提现成功' });

      // 刷新钱包余额
      globalStateManager.notifyRefresh(RefreshTypes.WALLET_BALANCE);

      // 返回上一页
      router.back();

    } catch (error) {
      console.error('提现失败:', error);
      promptAction.showToast({ message: '提现失败，请重试' });
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 执行转账操作
   */
  async performTransfer() {
    try {
      this.isLoading = true;

      const amountNum = parseFloat(this.amount);
      if (isNaN(amountNum) || amountNum <= 0) {
        promptAction.showToast({ message: '请输入有效的转账金额' });
        return;
      }

      if (amountNum > this.walletBalance) {
        promptAction.showToast({ message: '转账金额不能超过钱包余额' });
        return;
      }

      if (amountNum > this.payLimit) {
        promptAction.showToast({ message: `转账金额不能超过支付限额 ¥${this.payLimit}` });
        return;
      }

      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(this.toPhone)) {
        promptAction.showToast({ message: '请输入有效的手机号' });
        return;
      }

      // 获取用户信息
      const userInfo = await storageManager.getUserInfo();
      if (!userInfo) {
        promptAction.showToast({ message: "用户信息获取失败" });
        return;
      }

      const request: WalletTransferRequest = {
        userId: userInfo.userId,
        toPhone: this.toPhone,
        amount: amountNum,
        payPassword: this.payPassword,
        description: this.description
      };

      await WalletApi.transfer(request);

      promptAction.showToast({ message: '转账成功' });

      // 刷新钱包余额
      globalStateManager.notifyRefresh(RefreshTypes.WALLET_BALANCE);

      // 返回上一页
      router.back();

    } catch (error) {
      console.error('转账失败:', error);
      promptAction.showToast({ message: '转账失败，请重试' });
    } finally {
      this.isLoading = false;
    }
  }
}
