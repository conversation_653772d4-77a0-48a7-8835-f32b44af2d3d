{"version": "2.0", "ppid": 21780, "events": [{"head": {"id": "eeb88308-0f5c-4054-82e3-e37868613c7d", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29537000748900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4afe02d5-7705-4165-a5a2-53b313acf30d", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29537011189800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a289b1b3-ec5d-4cdd-a806-b0ad6b4e7595", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29537011529600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "378031fb-61a8-4b48-b7d6-4e5ba23f1504", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600472549700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "532ceb60-726e-4423-ad7b-704f52640ad0", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600484446700, "endTime": 29600654447700}, "additional": {"children": ["04690d18-5ba8-47c4-aea8-d8bdb222407b", "2837edf1-8287-4cb5-93da-53d7bc774541", "44c638b6-25ec-4c42-bae9-770c0d9e7537", "c3b95fb8-582e-45a9-a146-d89c636a48ce", "f2d65c63-4fca-4bbb-b78f-590a457f9bf9", "b2b4adcd-fe84-4f16-aefe-7f5221c2e669", "32ec4350-2f40-40eb-b709-843f369bc15e"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "bac007e6-bf3f-4f4f-8ae3-5fc3d315d25d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04690d18-5ba8-47c4-aea8-d8bdb222407b", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600484448000, "endTime": 29600503296800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "532ceb60-726e-4423-ad7b-704f52640ad0", "logId": "bca1aaf7-4e08-4e2d-8782-c5848a13f03e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2837edf1-8287-4cb5-93da-53d7bc774541", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600503313100, "endTime": 29600652324800}, "additional": {"children": ["55aa58d7-a91d-4464-939d-5b8ccbfa499d", "3521bd24-5592-4906-a4b3-24f96341bc29", "b2fb32ed-2584-47c4-9438-2454f1014921", "f510382f-0cec-4b55-a995-1824a3b5a00f", "ba0fdd5f-28c5-4f33-b9e2-8878a9c8fc62", "53360567-98fe-482c-b179-d530a9b91931", "4836b20c-aac5-4a1e-a5c1-a0e5ea799757", "e8f7bcbb-20b2-4965-9dae-360b307001f1", "fd5195d3-6eb6-4005-b61d-e0cecd767a81"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "532ceb60-726e-4423-ad7b-704f52640ad0", "logId": "e154cc35-39bd-4911-8efb-b122cea85e12"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44c638b6-25ec-4c42-bae9-770c0d9e7537", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600652342900, "endTime": 29600654438800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "532ceb60-726e-4423-ad7b-704f52640ad0", "logId": "5d5e3fb3-bc6e-4c8e-8cb1-c66a7b00a8e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3b95fb8-582e-45a9-a146-d89c636a48ce", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600654444700, "endTime": 29600654446000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "532ceb60-726e-4423-ad7b-704f52640ad0", "logId": "46835d59-fd7f-45ce-8ee6-f787b5816a36"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f2d65c63-4fca-4bbb-b78f-590a457f9bf9", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600489401700, "endTime": 29600489440500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "532ceb60-726e-4423-ad7b-704f52640ad0", "logId": "4af06137-3594-4731-a0f0-50e020b059f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4af06137-3594-4731-a0f0-50e020b059f1", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600489401700, "endTime": 29600489440500}, "additional": {"logType": "info", "children": [], "durationId": "f2d65c63-4fca-4bbb-b78f-590a457f9bf9", "parent": "bac007e6-bf3f-4f4f-8ae3-5fc3d315d25d"}}, {"head": {"id": "b2b4adcd-fe84-4f16-aefe-7f5221c2e669", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600496852400, "endTime": 29600496873400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "532ceb60-726e-4423-ad7b-704f52640ad0", "logId": "e4ff1918-985c-490b-9c67-645fb2df6bd2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e4ff1918-985c-490b-9c67-645fb2df6bd2", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600496852400, "endTime": 29600496873400}, "additional": {"logType": "info", "children": [], "durationId": "b2b4adcd-fe84-4f16-aefe-7f5221c2e669", "parent": "bac007e6-bf3f-4f4f-8ae3-5fc3d315d25d"}}, {"head": {"id": "f71c0092-356a-4df3-98cd-53f924589232", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600496926400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b57d572-ade6-40a2-9df5-ead748cdfdfc", "name": "Cache service initialization finished in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600503124100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bca1aaf7-4e08-4e2d-8782-c5848a13f03e", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600484448000, "endTime": 29600503296800}, "additional": {"logType": "info", "children": [], "durationId": "04690d18-5ba8-47c4-aea8-d8bdb222407b", "parent": "bac007e6-bf3f-4f4f-8ae3-5fc3d315d25d"}}, {"head": {"id": "55aa58d7-a91d-4464-939d-5b8ccbfa499d", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600512317900, "endTime": 29600512327400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2837edf1-8287-4cb5-93da-53d7bc774541", "logId": "579c1312-af02-4cbc-9af0-cc6f327e748a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3521bd24-5592-4906-a4b3-24f96341bc29", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600512341200, "endTime": 29600519203100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2837edf1-8287-4cb5-93da-53d7bc774541", "logId": "927429b9-fe3f-4dc0-bdb6-50de13121bb6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b2fb32ed-2584-47c4-9438-2454f1014921", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600519218100, "endTime": 29600604438400}, "additional": {"children": ["8344f7f7-9a43-43aa-9900-b4737b86da6c", "11521311-1f17-4c99-8bb6-a4f2a6ec8e5c", "c6a42cab-ee84-476b-90d0-42d7482860fa"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2837edf1-8287-4cb5-93da-53d7bc774541", "logId": "ed7e4f81-53df-482d-88b7-56d503b4aae3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f510382f-0cec-4b55-a995-1824a3b5a00f", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600604446800, "endTime": 29600622053700}, "additional": {"children": ["3ceba040-5093-4902-a978-cedee2354f84"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2837edf1-8287-4cb5-93da-53d7bc774541", "logId": "bf827c4e-a39a-44d4-8359-3a242e16a473"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ba0fdd5f-28c5-4f33-b9e2-8878a9c8fc62", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600622057700, "endTime": 29600632527100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2837edf1-8287-4cb5-93da-53d7bc774541", "logId": "db3fc91c-7215-4b5f-9251-14850b15f4fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53360567-98fe-482c-b179-d530a9b91931", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600633366400, "endTime": 29600640515700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2837edf1-8287-4cb5-93da-53d7bc774541", "logId": "fe319114-b522-4863-8ea8-c323fa7c4810"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4836b20c-aac5-4a1e-a5c1-a0e5ea799757", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600640532800, "endTime": 29600652144000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2837edf1-8287-4cb5-93da-53d7bc774541", "logId": "0336e860-9350-4363-a9d2-1a2dccfc2bf4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e8f7bcbb-20b2-4965-9dae-360b307001f1", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600652173200, "endTime": 29600652312900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2837edf1-8287-4cb5-93da-53d7bc774541", "logId": "03f2bb88-0c1e-4a0a-93d7-e1c00e1d319f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "579c1312-af02-4cbc-9af0-cc6f327e748a", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600512317900, "endTime": 29600512327400}, "additional": {"logType": "info", "children": [], "durationId": "55aa58d7-a91d-4464-939d-5b8ccbfa499d", "parent": "e154cc35-39bd-4911-8efb-b122cea85e12"}}, {"head": {"id": "927429b9-fe3f-4dc0-bdb6-50de13121bb6", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600512341200, "endTime": 29600519203100}, "additional": {"logType": "info", "children": [], "durationId": "3521bd24-5592-4906-a4b3-24f96341bc29", "parent": "e154cc35-39bd-4911-8efb-b122cea85e12"}}, {"head": {"id": "8344f7f7-9a43-43aa-9900-b4737b86da6c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600520194000, "endTime": 29600520211800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b2fb32ed-2584-47c4-9438-2454f1014921", "logId": "459a7bea-8681-4c65-8251-084660938be0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "459a7bea-8681-4c65-8251-084660938be0", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600520194000, "endTime": 29600520211800}, "additional": {"logType": "info", "children": [], "durationId": "8344f7f7-9a43-43aa-9900-b4737b86da6c", "parent": "ed7e4f81-53df-482d-88b7-56d503b4aae3"}}, {"head": {"id": "11521311-1f17-4c99-8bb6-a4f2a6ec8e5c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600524819000, "endTime": 29600603909600}, "additional": {"children": ["0147f5e2-77f7-483a-8ef3-805cd0185ccb", "eb44f70b-cf62-47b9-b094-5ab96836e88a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b2fb32ed-2584-47c4-9438-2454f1014921", "logId": "00a28083-3761-4d82-9439-cec39f6adb17"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0147f5e2-77f7-483a-8ef3-805cd0185ccb", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600524820200, "endTime": 29600531284900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "11521311-1f17-4c99-8bb6-a4f2a6ec8e5c", "logId": "fab18241-2e49-44cf-b5d6-109de4210e72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb44f70b-cf62-47b9-b094-5ab96836e88a", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600531300300, "endTime": 29600603899400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "11521311-1f17-4c99-8bb6-a4f2a6ec8e5c", "logId": "c0c2978a-2599-4a61-bc2e-44929aa0e1d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7e5d4d3-3c42-4ba2-8058-0cf947778a96", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600524856400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41f970a2-42d5-4a96-acb4-a09bbe9d3558", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600531129200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fab18241-2e49-44cf-b5d6-109de4210e72", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600524820200, "endTime": 29600531284900}, "additional": {"logType": "info", "children": [], "durationId": "0147f5e2-77f7-483a-8ef3-805cd0185ccb", "parent": "00a28083-3761-4d82-9439-cec39f6adb17"}}, {"head": {"id": "e2e47b31-fd42-46af-af67-dcdc17347f29", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600531313100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af637ad8-f5a2-4ad5-b2bf-c50497a98a06", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600542340800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ace19ed8-c61c-4e48-b5ee-acf6eab06839", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600542479800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62cd6b92-1cf0-414b-8ed6-9c8aac381b17", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600542685500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49b6475a-cc2e-4516-a483-a983d6ad6750", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600542821600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "417e1238-bd1d-457c-876f-269d38df858f", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600545305100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de2d1ff3-9365-44ec-bab8-8939d144c387", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600551980100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffd285e8-2a3d-476f-b330-23c5a491ecb1", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600565114700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2ff90da-ac69-47db-aeef-10f066f5690c", "name": "Sdk init in 35 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600587082400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3606354-e6b0-420d-8a49-b2aa3c61a787", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600587213300}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 19}, "markType": "other"}}, {"head": {"id": "8a089616-7191-4c8f-9ac9-b1a37406236b", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600587225800}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 19}, "markType": "other"}}, {"head": {"id": "5cd013f1-baef-4934-9d44-6a5cba1756cc", "name": "Project task initialization takes 16 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600603579100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d5dcf14-eee9-4db4-85ae-1618d4d0e6aa", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600603709000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31a6badd-21b8-4e55-9c1f-3b02ffef7c17", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600603778700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc6e394c-270e-4389-9558-e2b810b7a665", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600603842500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0c2978a-2599-4a61-bc2e-44929aa0e1d1", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600531300300, "endTime": 29600603899400}, "additional": {"logType": "info", "children": [], "durationId": "eb44f70b-cf62-47b9-b094-5ab96836e88a", "parent": "00a28083-3761-4d82-9439-cec39f6adb17"}}, {"head": {"id": "00a28083-3761-4d82-9439-cec39f6adb17", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600524819000, "endTime": 29600603909600}, "additional": {"logType": "info", "children": ["fab18241-2e49-44cf-b5d6-109de4210e72", "c0c2978a-2599-4a61-bc2e-44929aa0e1d1"], "durationId": "11521311-1f17-4c99-8bb6-a4f2a6ec8e5c", "parent": "ed7e4f81-53df-482d-88b7-56d503b4aae3"}}, {"head": {"id": "c6a42cab-ee84-476b-90d0-42d7482860fa", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600604420800, "endTime": 29600604432800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b2fb32ed-2584-47c4-9438-2454f1014921", "logId": "0a726ace-63c8-4785-8cde-6ec3c6cb50a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a726ace-63c8-4785-8cde-6ec3c6cb50a5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600604420800, "endTime": 29600604432800}, "additional": {"logType": "info", "children": [], "durationId": "c6a42cab-ee84-476b-90d0-42d7482860fa", "parent": "ed7e4f81-53df-482d-88b7-56d503b4aae3"}}, {"head": {"id": "ed7e4f81-53df-482d-88b7-56d503b4aae3", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600519218100, "endTime": 29600604438400}, "additional": {"logType": "info", "children": ["459a7bea-8681-4c65-8251-084660938be0", "00a28083-3761-4d82-9439-cec39f6adb17", "0a726ace-63c8-4785-8cde-6ec3c6cb50a5"], "durationId": "b2fb32ed-2584-47c4-9438-2454f1014921", "parent": "e154cc35-39bd-4911-8efb-b122cea85e12"}}, {"head": {"id": "3ceba040-5093-4902-a978-cedee2354f84", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600604917700, "endTime": 29600622045500}, "additional": {"children": ["e1e0314a-4f46-4592-9149-3bd428798d39", "01dfd0e7-47a7-4797-abcb-764f4540fbfe", "d612d19e-ea92-4b9f-8232-04e138a1a8bb"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f510382f-0cec-4b55-a995-1824a3b5a00f", "logId": "61c3cb11-2298-494d-b5c2-e4e389ba73c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1e0314a-4f46-4592-9149-3bd428798d39", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600607206700, "endTime": 29600607216100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3ceba040-5093-4902-a978-cedee2354f84", "logId": "54b3d2a2-c224-4d0e-8768-11713a77c754"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54b3d2a2-c224-4d0e-8768-11713a77c754", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600607206700, "endTime": 29600607216100}, "additional": {"logType": "info", "children": [], "durationId": "e1e0314a-4f46-4592-9149-3bd428798d39", "parent": "61c3cb11-2298-494d-b5c2-e4e389ba73c6"}}, {"head": {"id": "01dfd0e7-47a7-4797-abcb-764f4540fbfe", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600608576700, "endTime": 29600620995300}, "additional": {"children": ["bc792056-54f2-4b9a-b4b8-9b076f7a4bc7", "97bbda63-1dca-43d3-a58e-dcc02f0ead72"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3ceba040-5093-4902-a978-cedee2354f84", "logId": "c50a0977-ad88-449e-ad42-a1c2a71e3ca0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc792056-54f2-4b9a-b4b8-9b076f7a4bc7", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600608577400, "endTime": 29600610790900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "01dfd0e7-47a7-4797-abcb-764f4540fbfe", "logId": "2cf6f9a2-02bd-4c40-91cb-2e661802db72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "97bbda63-1dca-43d3-a58e-dcc02f0ead72", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600610797000, "endTime": 29600620989300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "01dfd0e7-47a7-4797-abcb-764f4540fbfe", "logId": "282d1b80-af7d-4a13-b4e6-335cf8f85cc8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c83f7d19-677c-4ea6-8cb5-da86e9b82da2", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600608579800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7bdec33-c349-40d2-9953-88fbc3571700", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600610709600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cf6f9a2-02bd-4c40-91cb-2e661802db72", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600608577400, "endTime": 29600610790900}, "additional": {"logType": "info", "children": [], "durationId": "bc792056-54f2-4b9a-b4b8-9b076f7a4bc7", "parent": "c50a0977-ad88-449e-ad42-a1c2a71e3ca0"}}, {"head": {"id": "9af18e06-a782-4019-b27b-2876a0cc51be", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600610805500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94aab705-6ec8-47d2-a10c-8386298a02cd", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600616384600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5786670c-073b-4ec8-a699-86dd8964ffbc", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600616479500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "253253bb-b7d8-4e3f-b9da-5f2b96bef36f", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600616778900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16831db4-7607-41e6-a32a-dd26ea6615ea", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600617610700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c940ecc-ac1d-4ebb-977d-c7c728fd5f56", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600617708300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3161e405-1579-4912-946f-f7afb805ce79", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600617776300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ad0b003-55c4-4f4c-a37c-fa19fe017b91", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600617840300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6910892c-5773-4b0b-bc70-8f275e7c3f36", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600620784400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47443981-a65e-4a9e-8452-bb501ba6a650", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600620891000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80219748-7a9d-4d17-8961-52e5fa3f17d3", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600620942100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ebcb564-6ac7-4213-9231-9feb3b9fcfcb", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600620968500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "282d1b80-af7d-4a13-b4e6-335cf8f85cc8", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600610797000, "endTime": 29600620989300}, "additional": {"logType": "info", "children": [], "durationId": "97bbda63-1dca-43d3-a58e-dcc02f0ead72", "parent": "c50a0977-ad88-449e-ad42-a1c2a71e3ca0"}}, {"head": {"id": "c50a0977-ad88-449e-ad42-a1c2a71e3ca0", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600608576700, "endTime": 29600620995300}, "additional": {"logType": "info", "children": ["2cf6f9a2-02bd-4c40-91cb-2e661802db72", "282d1b80-af7d-4a13-b4e6-335cf8f85cc8"], "durationId": "01dfd0e7-47a7-4797-abcb-764f4540fbfe", "parent": "61c3cb11-2298-494d-b5c2-e4e389ba73c6"}}, {"head": {"id": "d612d19e-ea92-4b9f-8232-04e138a1a8bb", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600622028300, "endTime": 29600622039100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3ceba040-5093-4902-a978-cedee2354f84", "logId": "393a0d3c-addf-4bc6-a5e6-4a0c64060e05"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "393a0d3c-addf-4bc6-a5e6-4a0c64060e05", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600622028300, "endTime": 29600622039100}, "additional": {"logType": "info", "children": [], "durationId": "d612d19e-ea92-4b9f-8232-04e138a1a8bb", "parent": "61c3cb11-2298-494d-b5c2-e4e389ba73c6"}}, {"head": {"id": "61c3cb11-2298-494d-b5c2-e4e389ba73c6", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600604917700, "endTime": 29600622045500}, "additional": {"logType": "info", "children": ["54b3d2a2-c224-4d0e-8768-11713a77c754", "c50a0977-ad88-449e-ad42-a1c2a71e3ca0", "393a0d3c-addf-4bc6-a5e6-4a0c64060e05"], "durationId": "3ceba040-5093-4902-a978-cedee2354f84", "parent": "bf827c4e-a39a-44d4-8359-3a242e16a473"}}, {"head": {"id": "bf827c4e-a39a-44d4-8359-3a242e16a473", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600604446800, "endTime": 29600622053700}, "additional": {"logType": "info", "children": ["61c3cb11-2298-494d-b5c2-e4e389ba73c6"], "durationId": "f510382f-0cec-4b55-a995-1824a3b5a00f", "parent": "e154cc35-39bd-4911-8efb-b122cea85e12"}}, {"head": {"id": "e5fdeae9-7c51-48cf-b9fe-de0fbe578403", "name": "watch files: [\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\hvigorfile.ts',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600632248500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f72d413-2612-4f52-8df4-0acb94ccb54d", "name": "hvigorfile, resolve hvigorfile dependencies in 11 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600632480100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db3fc91c-7215-4b5f-9251-14850b15f4fb", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600622057700, "endTime": 29600632527100}, "additional": {"logType": "info", "children": [], "durationId": "ba0fdd5f-28c5-4f33-b9e2-8878a9c8fc62", "parent": "e154cc35-39bd-4911-8efb-b122cea85e12"}}, {"head": {"id": "fd5195d3-6eb6-4005-b61d-e0cecd767a81", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600633208200, "endTime": 29600633361000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2837edf1-8287-4cb5-93da-53d7bc774541", "logId": "aeebc8e0-331a-44aa-8d9b-401e2c0645e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ac25198-8d6b-4a96-b68f-c126aa16adc7", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600633225400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aeebc8e0-331a-44aa-8d9b-401e2c0645e1", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600633208200, "endTime": 29600633361000}, "additional": {"logType": "info", "children": [], "durationId": "fd5195d3-6eb6-4005-b61d-e0cecd767a81", "parent": "e154cc35-39bd-4911-8efb-b122cea85e12"}}, {"head": {"id": "dc4cbde0-3a5c-4573-8f13-fc8607501eb2", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600634428600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8dc6a39-3815-430d-9627-585f8635a72a", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600639557000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe319114-b522-4863-8ea8-c323fa7c4810", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600633366400, "endTime": 29600640515700}, "additional": {"logType": "info", "children": [], "durationId": "53360567-98fe-482c-b179-d530a9b91931", "parent": "e154cc35-39bd-4911-8efb-b122cea85e12"}}, {"head": {"id": "56d3c9e6-df84-422f-b6f4-8adfc801cb75", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600640544000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbfe3a03-2c81-4e77-aee8-5f07b570dc03", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600644448500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f242916e-d89e-4a9a-a628-3e4294e0586b", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600644524300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66248ead-b8aa-4b11-9f1e-e49be0a35ab3", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600644684400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8440909-b6e4-41db-8f4c-a5b9f3c8c1db", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600647572400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07247960-c415-4411-a58a-d39d727024bc", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600647689300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0336e860-9350-4363-a9d2-1a2dccfc2bf4", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600640532800, "endTime": 29600652144000}, "additional": {"logType": "info", "children": [], "durationId": "4836b20c-aac5-4a1e-a5c1-a0e5ea799757", "parent": "e154cc35-39bd-4911-8efb-b122cea85e12"}}, {"head": {"id": "f674e91b-8e30-43b1-bc5b-9db2726c1f3b", "name": "Configuration phase cost:140 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600652199000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03f2bb88-0c1e-4a0a-93d7-e1c00e1d319f", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600652173200, "endTime": 29600652312900}, "additional": {"logType": "info", "children": [], "durationId": "e8f7bcbb-20b2-4965-9dae-360b307001f1", "parent": "e154cc35-39bd-4911-8efb-b122cea85e12"}}, {"head": {"id": "e154cc35-39bd-4911-8efb-b122cea85e12", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600503313100, "endTime": 29600652324800}, "additional": {"logType": "info", "children": ["579c1312-af02-4cbc-9af0-cc6f327e748a", "927429b9-fe3f-4dc0-bdb6-50de13121bb6", "ed7e4f81-53df-482d-88b7-56d503b4aae3", "bf827c4e-a39a-44d4-8359-3a242e16a473", "db3fc91c-7215-4b5f-9251-14850b15f4fb", "fe319114-b522-4863-8ea8-c323fa7c4810", "0336e860-9350-4363-a9d2-1a2dccfc2bf4", "03f2bb88-0c1e-4a0a-93d7-e1c00e1d319f", "aeebc8e0-331a-44aa-8d9b-401e2c0645e1"], "durationId": "2837edf1-8287-4cb5-93da-53d7bc774541", "parent": "bac007e6-bf3f-4f4f-8ae3-5fc3d315d25d"}}, {"head": {"id": "32ec4350-2f40-40eb-b709-843f369bc15e", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600654418200, "endTime": 29600654432200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "532ceb60-726e-4423-ad7b-704f52640ad0", "logId": "a949e33c-8d44-4519-a60c-ce571487823c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a949e33c-8d44-4519-a60c-ce571487823c", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600654418200, "endTime": 29600654432200}, "additional": {"logType": "info", "children": [], "durationId": "32ec4350-2f40-40eb-b709-843f369bc15e", "parent": "bac007e6-bf3f-4f4f-8ae3-5fc3d315d25d"}}, {"head": {"id": "5d5e3fb3-bc6e-4c8e-8cb1-c66a7b00a8e3", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600652342900, "endTime": 29600654438800}, "additional": {"logType": "info", "children": [], "durationId": "44c638b6-25ec-4c42-bae9-770c0d9e7537", "parent": "bac007e6-bf3f-4f4f-8ae3-5fc3d315d25d"}}, {"head": {"id": "46835d59-fd7f-45ce-8ee6-f787b5816a36", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600654444700, "endTime": 29600654446000}, "additional": {"logType": "info", "children": [], "durationId": "c3b95fb8-582e-45a9-a146-d89c636a48ce", "parent": "bac007e6-bf3f-4f4f-8ae3-5fc3d315d25d"}}, {"head": {"id": "bac007e6-bf3f-4f4f-8ae3-5fc3d315d25d", "name": "init", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600484446700, "endTime": 29600654447700}, "additional": {"logType": "info", "children": ["bca1aaf7-4e08-4e2d-8782-c5848a13f03e", "e154cc35-39bd-4911-8efb-b122cea85e12", "5d5e3fb3-bc6e-4c8e-8cb1-c66a7b00a8e3", "46835d59-fd7f-45ce-8ee6-f787b5816a36", "4af06137-3594-4731-a0f0-50e020b059f1", "e4ff1918-985c-490b-9c67-645fb2df6bd2", "a949e33c-8d44-4519-a60c-ce571487823c"], "durationId": "532ceb60-726e-4423-ad7b-704f52640ad0"}}, {"head": {"id": "a6cdb10e-774f-46d8-afd8-02bca5a91395", "name": "Configuration task cost before running: 177 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600654533200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e254a6ff-500d-48f2-924f-1cfb57b511ad", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600658521000, "endTime": 29600665363800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "3e34e79a-1c76-4b31-b21a-dbed3a85963e", "logId": "8036aaa5-b1ed-40cc-8619-0bab0f13d076"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e34e79a-1c76-4b31-b21a-dbed3a85963e", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600655897200}, "additional": {"logType": "detail", "children": [], "durationId": "e254a6ff-500d-48f2-924f-1cfb57b511ad"}}, {"head": {"id": "cd0699d2-3846-418c-a8e4-415954b7432b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600656326300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "131fc5a0-43d1-466e-8705-92aa12dbf7fd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600656394300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca1ffcd3-4ce2-43db-9099-9b64135b569b", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600658528200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5f9f0bb-a08c-41a5-a47b-2f9122c8fdc9", "name": "Incremental task entry:default@PreBuild pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600665205900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b52051aa-c26c-4727-9a01-6446cb96aedf", "name": "entry : default@PreBuild cost memory 0.2925872802734375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600665313100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8036aaa5-b1ed-40cc-8619-0bab0f13d076", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600658521000, "endTime": 29600665363800}, "additional": {"logType": "info", "children": [], "durationId": "e254a6ff-500d-48f2-924f-1cfb57b511ad"}}, {"head": {"id": "076408eb-5cd7-4015-9585-b42d8b4d2880", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600671386400, "endTime": 29600673269500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "e9d23d30-0096-47a9-9b8f-5f3768c4b54c", "logId": "89facac2-4135-4cfe-bd56-5d97b4f806a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9d23d30-0096-47a9-9b8f-5f3768c4b54c", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600668563200}, "additional": {"logType": "detail", "children": [], "durationId": "076408eb-5cd7-4015-9585-b42d8b4d2880"}}, {"head": {"id": "8ac4fdcc-3152-44a0-9a8e-f809d148db3c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600670276900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d66c29ef-7356-4f71-ba04-e5e5ee12413c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600670408400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "671fe9a1-47d2-48ad-867a-e72367921b5e", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600671394000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "860d88ad-73fb-4a16-b829-e6f0570713be", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600673111200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e30f189f-5579-4ce1-87bb-034b34f01c50", "name": "entry : default@MergeProfile cost memory 0.1161041259765625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600673220700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89facac2-4135-4cfe-bd56-5d97b4f806a3", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600671386400, "endTime": 29600673269500}, "additional": {"logType": "info", "children": [], "durationId": "076408eb-5cd7-4015-9585-b42d8b4d2880"}}, {"head": {"id": "20fbab3f-26f6-4a4d-bf48-7a121f1629b6", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600678362600, "endTime": 29600680618000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "de5fc161-5abf-4614-838e-abbedd34090a", "logId": "a8a55868-15c1-4dfb-91f7-e25a9b58673b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de5fc161-5abf-4614-838e-abbedd34090a", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600675741800}, "additional": {"logType": "detail", "children": [], "durationId": "20fbab3f-26f6-4a4d-bf48-7a121f1629b6"}}, {"head": {"id": "132d380a-0950-4f5d-9924-bd0f6c2ad02e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600676795900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32b4380f-bc2a-42df-b451-e1981e16bd3a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600676934200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "363f0002-91e2-4643-a7b0-84dfc9f08bd1", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600678371300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50ece84a-ee8a-4d89-9288-6af1b55dc009", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600679255300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "377c62de-d620-41c2-9981-3844e98d78f7", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600680472900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9244b1bd-48c1-4423-a4b3-607155b3c84b", "name": "entry : default@CreateBuildProfile cost memory 0.09986114501953125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600680567200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8a55868-15c1-4dfb-91f7-e25a9b58673b", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600678362600, "endTime": 29600680618000}, "additional": {"logType": "info", "children": [], "durationId": "20fbab3f-26f6-4a4d-bf48-7a121f1629b6"}}, {"head": {"id": "21f12883-2814-47ba-8ac3-1f235a8611b6", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600686892200, "endTime": 29600687529300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "570d5dcb-0c44-4be0-a011-e776c6b8cce1", "logId": "dbf8f112-3847-45ae-96f3-b7e637a15e3f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "570d5dcb-0c44-4be0-a011-e776c6b8cce1", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600683059700}, "additional": {"logType": "detail", "children": [], "durationId": "21f12883-2814-47ba-8ac3-1f235a8611b6"}}, {"head": {"id": "c270cdc9-a4b3-4767-94cf-e8720dd49a2a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600685391800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfa16024-4101-457b-b7f3-96dfba7115c6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600685528600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d41fe69-aea7-4e5a-9254-104403edfd98", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600686903700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25b1f7a5-17bd-45bd-94a1-9dd40d2a06ff", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600687035400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfa92cae-d77a-42d0-b91b-0ce68f31daea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600687106000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0d8b981-3867-46bd-9575-5bee6777a2ed", "name": "entry : default@PreCheckSyscap cost memory 0.039031982421875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600687341500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edc2e90b-c394-4f44-b81b-283177843ce6", "name": "runTaskFromQueue task cost before running: 210 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600687455400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbf8f112-3847-45ae-96f3-b7e637a15e3f", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600686892200, "endTime": 29600687529300, "totalTime": 536600}, "additional": {"logType": "info", "children": [], "durationId": "21f12883-2814-47ba-8ac3-1f235a8611b6"}}, {"head": {"id": "4fddfab7-cdd9-4d3e-a3a8-2caa40db67ec", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600702435600, "endTime": 29600703416500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "bb29c25f-ddd6-4716-9fde-256578e53ee6", "logId": "240d776d-c77e-4810-b663-1343d40ba859"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb29c25f-ddd6-4716-9fde-256578e53ee6", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600690477800}, "additional": {"logType": "detail", "children": [], "durationId": "4fddfab7-cdd9-4d3e-a3a8-2caa40db67ec"}}, {"head": {"id": "fc5f3e87-113a-4b6b-8dde-4d58769ee9ea", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600691408000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba62c797-9291-48d0-8a54-4ca2494db2c0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600691526000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25bdbcf1-e32e-49a8-bc54-3c02965ce377", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600702448000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "caea6bb0-8bd1-426c-a8c5-76293a0ec80b", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600702642600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "376b7922-c834-4b7d-9781-2d4a534bf6c4", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600703252100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "401be195-e283-4b5d-a927-2115a0f53a7d", "name": "entry : default@GeneratePkgContextInfo cost memory 0.065521240234375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600703369600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "240d776d-c77e-4810-b663-1343d40ba859", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600702435600, "endTime": 29600703416500}, "additional": {"logType": "info", "children": [], "durationId": "4fddfab7-cdd9-4d3e-a3a8-2caa40db67ec"}}, {"head": {"id": "cd1eaf61-5072-4d5a-a079-4a8f2d06d7aa", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600708347300, "endTime": 29600709768300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "de917496-0ffd-48f2-be4c-0093f09e1757", "logId": "335ec24d-4811-4603-b8fd-fb644553e984"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de917496-0ffd-48f2-be4c-0093f09e1757", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600705502300}, "additional": {"logType": "detail", "children": [], "durationId": "cd1eaf61-5072-4d5a-a079-4a8f2d06d7aa"}}, {"head": {"id": "27d32404-4ca4-4091-9651-7fd6f0ad55bf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600706371000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3aa128d0-5b3b-4678-bdb0-41011705d4e7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600706491100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "775d8bfa-411e-4eaa-a67b-ed3aa049c22b", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600708357300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6340e45f-a59f-4d47-ab48-eeb4f6c772d6", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600709650600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "415ed88b-a81a-4363-8f51-b2ac252a03e4", "name": "entry : default@ProcessProfile cost memory 0.0572662353515625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600709725700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "335ec24d-4811-4603-b8fd-fb644553e984", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600708347300, "endTime": 29600709768300}, "additional": {"logType": "info", "children": [], "durationId": "cd1eaf61-5072-4d5a-a079-4a8f2d06d7aa"}}, {"head": {"id": "4996ca8a-e1a9-40cb-a844-d57f626a956e", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600714559800, "endTime": 29600723810600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6e95b280-6f3e-436d-a0b1-e84c90e275db", "logId": "8a61fe48-0172-43af-a458-d4d6cb565897"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e95b280-6f3e-436d-a0b1-e84c90e275db", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600710916800}, "additional": {"logType": "detail", "children": [], "durationId": "4996ca8a-e1a9-40cb-a844-d57f626a956e"}}, {"head": {"id": "21d05cf4-c143-47ae-8e05-0d59b1832ab9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600711393500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75c0b3ee-22a0-4028-98a8-d04bf52988ef", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600711559800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d315c0a6-d75d-45da-b508-d56af761f4f4", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600714574100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdea5ec1-035d-46ba-8dc6-ded788cb9e86", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600723617100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1029d10c-8780-4d46-b9cd-3dc590ba7e2f", "name": "entry : default@ProcessRouterMap cost memory 0.196319580078125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600723760400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a61fe48-0172-43af-a458-d4d6cb565897", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600714559800, "endTime": 29600723810600}, "additional": {"logType": "info", "children": [], "durationId": "4996ca8a-e1a9-40cb-a844-d57f626a956e"}}, {"head": {"id": "a27e51e8-7329-4a29-a5e7-ed23bc0fdab3", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600731008200, "endTime": 29600733027400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "3d11824d-d75a-423b-a5a9-b19498e0fde0", "logId": "bdb36e3a-b59b-441a-8a90-d0daebac1d74"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d11824d-d75a-423b-a5a9-b19498e0fde0", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600727297600}, "additional": {"logType": "detail", "children": [], "durationId": "a27e51e8-7329-4a29-a5e7-ed23bc0fdab3"}}, {"head": {"id": "2312fcb3-55ca-41eb-a3ed-937244160318", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600727779400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cb97791-f35f-4b0f-9640-772a8c95de33", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600727862800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e30d4552-cc75-4c10-96a2-37aa5f16c509", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600728806300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d3d4e99-f91a-4bca-976e-edb4280aedb6", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600731856400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3562a25-1024-4030-ab32-166947ed651c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600731979300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aeb4086a-2566-4783-aabf-869c28846510", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600732016200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8784417b-bc9b-41fa-90b8-3be7b3d01ef5", "name": "entry : default@PreviewProcessResource cost memory 0.06989288330078125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600732070300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a8c6451-f75b-4952-aa3d-d882e47eb872", "name": "runTaskFromQueue task cost before running: 255 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600732930700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdb36e3a-b59b-441a-8a90-d0daebac1d74", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600731008200, "endTime": 29600733027400, "totalTime": 1098800}, "additional": {"logType": "info", "children": [], "durationId": "a27e51e8-7329-4a29-a5e7-ed23bc0fdab3"}}, {"head": {"id": "b8ac91f5-c406-4a52-8014-1e3e2465956e", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600740958400, "endTime": 29600758465400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9c1b0a1f-3902-4ebe-881f-ea52f2074dc5", "logId": "ccbc74cf-0c1b-48e1-bbea-f4f877ad14d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c1b0a1f-3902-4ebe-881f-ea52f2074dc5", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600735212700}, "additional": {"logType": "detail", "children": [], "durationId": "b8ac91f5-c406-4a52-8014-1e3e2465956e"}}, {"head": {"id": "009edc70-2c77-4b01-b602-975e8120771e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600735623600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce9dcb3d-3aa7-43ba-a0b8-7ee0fe69c5ac", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600735699800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9704ce2-df15-4d9f-987b-fd21b31e85ae", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600740976300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cc14279-1237-48eb-b4d4-3ba5ea3675bb", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600758281700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69e3b1fb-d580-48a7-8f8b-09424d312c4e", "name": "entry : default@GenerateLoaderJson cost memory 0.75103759765625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600758412200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccbc74cf-0c1b-48e1-bbea-f4f877ad14d5", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600740958400, "endTime": 29600758465400}, "additional": {"logType": "info", "children": [], "durationId": "b8ac91f5-c406-4a52-8014-1e3e2465956e"}}, {"head": {"id": "2f204ed7-2527-4b22-b7fb-09bab1ef45ed", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600770158700, "endTime": 29600796066100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "bdbb84a8-2bdd-43e3-87a4-dc1ea43a5d23", "logId": "931b44ce-8810-4e56-b8ad-b455d168aef2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bdbb84a8-2bdd-43e3-87a4-dc1ea43a5d23", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600766877900}, "additional": {"logType": "detail", "children": [], "durationId": "2f204ed7-2527-4b22-b7fb-09bab1ef45ed"}}, {"head": {"id": "3d145665-9677-42d5-aab2-b8037308b5b8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600767350500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b92cd8cc-c321-499a-a574-82240735b54f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600767435600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a831186-722e-42b3-94fa-502642653bb8", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600768297000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebe3ba1c-8c81-433e-8acc-d08e3110368e", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600770203100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12cf5b75-89bc-4d2b-83ca-544b6b796b3b", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 23 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600795740800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45477b54-4a6a-484c-8508-fa0f1c3da5e4", "name": "entry : default@PreviewCompileResource cost memory -1.0214462280273438", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600795950900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "931b44ce-8810-4e56-b8ad-b455d168aef2", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600770158700, "endTime": 29600796066100}, "additional": {"logType": "info", "children": [], "durationId": "2f204ed7-2527-4b22-b7fb-09bab1ef45ed"}}, {"head": {"id": "b21642cf-e429-4ec3-9882-7a63525ae939", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600799893500, "endTime": 29600800082000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "63e3385c-caf4-4180-8800-655ac57f8eab", "logId": "2eb1a49c-c362-4dd2-afb6-ca4e1e55a69d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "63e3385c-caf4-4180-8800-655ac57f8eab", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600799320400}, "additional": {"logType": "detail", "children": [], "durationId": "b21642cf-e429-4ec3-9882-7a63525ae939"}}, {"head": {"id": "bb278cce-5641-4952-894a-c7041b7ab307", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600799741200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad974804-7618-4dc1-b27b-949e10395a02", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600799824800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01b8567c-6c6c-453b-a913-8ee2cb078ddd", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600799897900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c5a156d-2718-412a-b8d9-151568b00d23", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600799949400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb3d3e6c-5b28-4ed7-8bcf-7e5aa6fd75c7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600799972100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f0f89f8-8806-431b-a925-91540db3d90e", "name": "entry : default@PreviewHookCompileResource cost memory 0.03794097900390625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600800009800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bc8b823-cbfa-4363-a82b-c00787afbb53", "name": "runTaskFromQueue task cost before running: 323 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600800056600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2eb1a49c-c362-4dd2-afb6-ca4e1e55a69d", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600799893500, "endTime": 29600800082000, "totalTime": 147900}, "additional": {"logType": "info", "children": [], "durationId": "b21642cf-e429-4ec3-9882-7a63525ae939"}}, {"head": {"id": "a54ad184-58f5-4a0a-ad9f-79863c616bf4", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600802459600, "endTime": 29600805112000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "b2ce483b-5ada-40ad-9a6b-1600ca942def", "logId": "f8ccbdb8-4045-4d54-97fc-90c6f9a626ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b2ce483b-5ada-40ad-9a6b-1600ca942def", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600801246300}, "additional": {"logType": "detail", "children": [], "durationId": "a54ad184-58f5-4a0a-ad9f-79863c616bf4"}}, {"head": {"id": "2bb32bab-73ec-47d5-9969-a3bf75fb080a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600801620300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1c7d5b5-b22b-4d02-8f7a-6f9a6df83f67", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600801691800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c786926-a552-4025-8ae6-d66bf499c7b1", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600802471800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2723de9e-2450-4821-81a9-36379c878e36", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600804957600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac5152e1-db75-4ccf-aa0a-9cd724af05f4", "name": "entry : default@CopyPreviewProfile cost memory 0.10214996337890625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600805064900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8ccbdb8-4045-4d54-97fc-90c6f9a626ac", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600802459600, "endTime": 29600805112000}, "additional": {"logType": "info", "children": [], "durationId": "a54ad184-58f5-4a0a-ad9f-79863c616bf4"}}, {"head": {"id": "5235159b-6ea8-4334-9d9d-81a1bed07203", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600808776500, "endTime": 29600809127500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "9099a21b-0a92-4beb-ac66-7aa823e2279b", "logId": "cea70765-e130-43e1-8fb1-42b8579a5a73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9099a21b-0a92-4beb-ac66-7aa823e2279b", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600807386000}, "additional": {"logType": "detail", "children": [], "durationId": "5235159b-6ea8-4334-9d9d-81a1bed07203"}}, {"head": {"id": "01b011db-51a4-40f8-a38e-88cb8d1ab67e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600808096200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f0a9c66-3a8d-4a38-ba19-5e4c60fe6604", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600808182200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94346aa2-3a86-46d2-b651-bdb7a77b54e9", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600808784100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c3b08ac-6496-4dd5-8a96-b6ebc338f4a1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600808872200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08750104-b20a-4c27-b61a-566f0833b30d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600808904600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca6aba19-13f7-4717-875f-8de7578d5d60", "name": "entry : default@ReplacePreviewerPage cost memory 0.04006195068359375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600808965300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3149e7e0-98dd-4107-9929-3a5b334a5518", "name": "runTaskFromQueue task cost before running: 332 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600809092100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cea70765-e130-43e1-8fb1-42b8579a5a73", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600808776500, "endTime": 29600809127500, "totalTime": 223200}, "additional": {"logType": "info", "children": [], "durationId": "5235159b-6ea8-4334-9d9d-81a1bed07203"}}, {"head": {"id": "feca25bb-7c3f-4230-b84c-06ba17feecd4", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600811278500, "endTime": 29600811503100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "64c632b1-aca5-4317-aedd-2b79cc9dd9ae", "logId": "2f0a71c0-82e0-4222-8e88-e64e9fdf05dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "64c632b1-aca5-4317-aedd-2b79cc9dd9ae", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600811230900}, "additional": {"logType": "detail", "children": [], "durationId": "feca25bb-7c3f-4230-b84c-06ba17feecd4"}}, {"head": {"id": "3cf77946-40a8-474e-82fb-143bca0f7c3a", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600811283700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5b0f740-844c-453b-9bc7-a00acef1d526", "name": "entry : buildPreviewerResource cost memory 0.02582550048828125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600811412500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6898d15e-966a-4047-bd9d-0187e573dfe9", "name": "runTaskFromQueue task cost before running: 334 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600811474500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f0a71c0-82e0-4222-8e88-e64e9fdf05dc", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600811278500, "endTime": 29600811503100, "totalTime": 178900}, "additional": {"logType": "info", "children": [], "durationId": "feca25bb-7c3f-4230-b84c-06ba17feecd4"}}, {"head": {"id": "fa5e2a31-a4eb-42d7-8e40-bd2f0094dce4", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600813785800, "endTime": 29600817314100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "a1a114da-ca29-4643-8570-6cb2afd184f4", "logId": "5e1089db-dcd6-47c5-9986-d60fa5a90949"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1a114da-ca29-4643-8570-6cb2afd184f4", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600812742700}, "additional": {"logType": "detail", "children": [], "durationId": "fa5e2a31-a4eb-42d7-8e40-bd2f0094dce4"}}, {"head": {"id": "363e69be-8239-486f-b42a-821d56e9d97f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600813134000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76902436-594c-474b-8a09-5e194bcdc12f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600813258900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f48d1cc5-4b00-432d-91d7-e08573304d3c", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600813790700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0655dfb-ac3a-46f5-b037-644cd4495ff4", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600817175100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97521daa-a37f-4160-802f-60e3f51ccdee", "name": "entry : default@PreviewUpdateAssets cost memory 0.11956024169921875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600817270200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e1089db-dcd6-47c5-9986-d60fa5a90949", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600813785800, "endTime": 29600817314100}, "additional": {"logType": "info", "children": [], "durationId": "fa5e2a31-a4eb-42d7-8e40-bd2f0094dce4"}}, {"head": {"id": "3fcf1c78-4899-4a11-a07a-113e303a2246", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600825365400, "endTime": 29611953700800}, "additional": {"children": ["eae4abfd-e0d4-4dcb-8c46-a059d45e0800"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8a61f3bc-c3ae-4425-8442-215f277b477a", "logId": "6d2c95f0-503f-430c-8145-d97b5990b9ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a61f3bc-c3ae-4425-8442-215f277b477a", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600819703500}, "additional": {"logType": "detail", "children": [], "durationId": "3fcf1c78-4899-4a11-a07a-113e303a2246"}}, {"head": {"id": "9420a977-5f91-4088-9548-69175bb26478", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600820265100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fd33e44-b457-4490-9487-869ad4279f93", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600820348800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d9aafdc-6f25-450d-b9e3-8812fba51ebf", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600825374900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eae4abfd-e0d4-4dcb-8c46-a059d45e0800", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker11", "startTime": 29600846241300, "endTime": 29611950867000}, "additional": {"children": ["bcb7f5ae-3b03-4873-98e3-e0521529f867", "a5e70a1c-bf65-4f0b-9969-5c3c4c6aff04", "3e53351f-0ddd-4894-adec-2811b898637d", "4d62060a-4b78-4236-9457-ed81a1f3e968", "26895a99-2950-43ad-ba5a-06930e54001b"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "3fcf1c78-4899-4a11-a07a-113e303a2246", "logId": "0aa5778d-6df2-421b-a21a-24a87ea28605"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b1f70ca1-34eb-4416-82f4-8b1e49a2f1a2", "name": "entry : default@PreviewArkTS cost memory 0.9935836791992188", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600850846900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53d274e8-8968-4791-ac0b-04a72981cb85", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29605083987000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcb7f5ae-3b03-4873-98e3-e0521529f867", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker11", "startTime": 29605085048600, "endTime": 29605085068900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "eae4abfd-e0d4-4dcb-8c46-a059d45e0800", "logId": "c150b544-0081-4796-8fbd-4413aeabdc98"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c150b544-0081-4796-8fbd-4413aeabdc98", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29605085048600, "endTime": 29605085068900}, "additional": {"logType": "info", "children": [], "durationId": "bcb7f5ae-3b03-4873-98e3-e0521529f867", "parent": "0aa5778d-6df2-421b-a21a-24a87ea28605"}}, {"head": {"id": "3bf25bbb-aad5-4f5b-8d1e-5d006ea2318f", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611948521200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5e70a1c-bf65-4f0b-9969-5c3c4c6aff04", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker11", "startTime": 29611950713900, "endTime": 29611950731100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "eae4abfd-e0d4-4dcb-8c46-a059d45e0800", "logId": "4de3d87d-4b98-41c0-9b1e-f876093c52ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4de3d87d-4b98-41c0-9b1e-f876093c52ca", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611950713900, "endTime": 29611950731100}, "additional": {"logType": "info", "children": [], "durationId": "a5e70a1c-bf65-4f0b-9969-5c3c4c6aff04", "parent": "0aa5778d-6df2-421b-a21a-24a87ea28605"}}, {"head": {"id": "0aa5778d-6df2-421b-a21a-24a87ea28605", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Worker11", "startTime": 29600846241300, "endTime": 29611950867000}, "additional": {"logType": "info", "children": ["c150b544-0081-4796-8fbd-4413aeabdc98", "4de3d87d-4b98-41c0-9b1e-f876093c52ca", "2c384cb8-e2bc-48d6-ab5b-64de06fc94c5", "11aebca8-7f8e-4577-b35f-5caff1c633d3", "53b025e6-3e6a-42a4-ac48-4d38931c5314"], "durationId": "eae4abfd-e0d4-4dcb-8c46-a059d45e0800", "parent": "6d2c95f0-503f-430c-8145-d97b5990b9ff"}}, {"head": {"id": "3e53351f-0ddd-4894-adec-2811b898637d", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker11", "startTime": 29603899003900, "endTime": 29605044305900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "eae4abfd-e0d4-4dcb-8c46-a059d45e0800", "logId": "2c384cb8-e2bc-48d6-ab5b-64de06fc94c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c384cb8-e2bc-48d6-ab5b-64de06fc94c5", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29603899003900, "endTime": 29605044305900}, "additional": {"logType": "info", "children": [], "durationId": "3e53351f-0ddd-4894-adec-2811b898637d", "parent": "0aa5778d-6df2-421b-a21a-24a87ea28605"}}, {"head": {"id": "4d62060a-4b78-4236-9457-ed81a1f3e968", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker11", "startTime": 29605044666900, "endTime": 29605044867600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "eae4abfd-e0d4-4dcb-8c46-a059d45e0800", "logId": "11aebca8-7f8e-4577-b35f-5caff1c633d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11aebca8-7f8e-4577-b35f-5caff1c633d3", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29605044666900, "endTime": 29605044867600}, "additional": {"logType": "info", "children": [], "durationId": "4d62060a-4b78-4236-9457-ed81a1f3e968", "parent": "0aa5778d-6df2-421b-a21a-24a87ea28605"}}, {"head": {"id": "26895a99-2950-43ad-ba5a-06930e54001b", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker11", "startTime": 29605045010800, "endTime": 29611948396900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "eae4abfd-e0d4-4dcb-8c46-a059d45e0800", "logId": "53b025e6-3e6a-42a4-ac48-4d38931c5314"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53b025e6-3e6a-42a4-ac48-4d38931c5314", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29605045010800, "endTime": 29611948396900}, "additional": {"logType": "info", "children": [], "durationId": "26895a99-2950-43ad-ba5a-06930e54001b", "parent": "0aa5778d-6df2-421b-a21a-24a87ea28605"}}, {"head": {"id": "6d2c95f0-503f-430c-8145-d97b5990b9ff", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600825365400, "endTime": 29611953700800, "totalTime": 11128292200}, "additional": {"logType": "info", "children": ["0aa5778d-6df2-421b-a21a-24a87ea28605"], "durationId": "3fcf1c78-4899-4a11-a07a-113e303a2246"}}, {"head": {"id": "52c6626b-a95a-4be5-9250-6db02edf13bc", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611958593300, "endTime": 29611958801800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "0ea35364-1ae7-4300-b9b7-3e0d24008477", "logId": "9a43f95e-8394-4c47-b87f-6b7bf45b67da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ea35364-1ae7-4300-b9b7-3e0d24008477", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611958545100}, "additional": {"logType": "detail", "children": [], "durationId": "52c6626b-a95a-4be5-9250-6db02edf13bc"}}, {"head": {"id": "a3db2607-9d49-4001-a123-5f8f8737a991", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611958602000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5f66e94-6213-4da4-a648-5cbcbd4f5d38", "name": "entry : PreviewBuild cost memory 0.013641357421875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611958714900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0691f56e-31b2-443b-b200-c44adf2f9b7a", "name": "runTaskFromQueue task cost before running: 11 s 481 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611958770700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a43f95e-8394-4c47-b87f-6b7bf45b67da", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611958593300, "endTime": 29611958801800, "totalTime": 162500}, "additional": {"logType": "info", "children": [], "durationId": "52c6626b-a95a-4be5-9250-6db02edf13bc"}}, {"head": {"id": "3c22dfd8-a5ed-471b-8c9d-4fbaf2587cb8", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611967465600, "endTime": 29611967487200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5d1f5a12-3229-4a70-bdd3-d5a3fa635ead", "logId": "7564ae6b-d5a4-4d9b-bc12-6e15f1658acf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7564ae6b-d5a4-4d9b-bc12-6e15f1658acf", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611967465600, "endTime": 29611967487200}, "additional": {"logType": "info", "children": [], "durationId": "3c22dfd8-a5ed-471b-8c9d-4fbaf2587cb8"}}, {"head": {"id": "89757aad-f94a-45a3-ae2d-68c2e43934ae", "name": "BUILD SUCCESSFUL in 11 s 490 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611967594200}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "416a5e8a-1df0-4230-8259-6fa808e8c504", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29600478028500, "endTime": 29611967794300}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 19}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "09229b4e-c48f-4375-a639-a0992324d8c6", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611967813600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f02a163a-e8fe-4fc1-8abf-95939d2cff74", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611967856800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "426db7bc-b35b-43c3-b3df-4a49a637c458", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611967883900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d303cb32-f43e-4276-bfa3-baf51178b8b0", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611967907500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ab2a47f-95ef-4049-baf7-343d065a97ed", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611967931000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1410569-75f9-4bc7-98d1-2ec641d499d8", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611967950900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8aa4752-f3fc-4acb-a5a7-918bb43a01a4", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611967969500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "421d8b64-70f2-4641-a8d6-697977691ca1", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611967989200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a367d60-83b8-4d0a-bf0d-62fe6e6612b8", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611968009500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "506670da-0826-4742-83b7-54d2e5ce5fe1", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611968033200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "363b1bcf-84f2-4b24-b700-d912d456f858", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611970498800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ee72823-f688-433f-a5bc-a2cb63b40a31", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611971174700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "171dc80e-80fc-4551-b884-96c3a95b266b", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611971416900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2b9cd04-684a-4cbf-8431-372a921e877d", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611971638000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e49a0e3-02c4-439f-9bc3-9a32e9abc486", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611972186600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10c0cae4-e336-4412-99bc-78f16eeacd6c", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611980187400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f24ba54-f9e6-4e4c-b5da-6c2cd8eb9574", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611980513900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1fe4018-637f-4c67-a40d-bfc8cf970464", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611980863800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "472073b2-2ab7-4df9-a452-87f9ade4ee9f", "name": "Update task entry:default@PreviewArkTS output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611981147400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee709584-a12b-412c-8dd1-c879bf6d5a13", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:14 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611981365900}, "additional": {"logType": "debug", "children": []}}], "workLog": []}