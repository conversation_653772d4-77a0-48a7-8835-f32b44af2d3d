if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface BankCardPage_Params {
    allCards?: BankCard[];
    boundCards?: BankCard[];
    isLoading?: boolean;
    selectedCardId?: number;
    isSelectMode?: boolean;
    viewMode?: string;
    selectedCard?: BankCard | null;
    showCreditCardDetails?: boolean;
    selectedCreditCard?: BankCard | null;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { BankCardType, BankCardStatus } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import type { BankCard, SpringBootBankCardResponse, SpringBootUnbindBankCardRequest, SpringBootSetDefaultCardRequest, BankCardPageParams } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import { tempDataManager } from "@normalized:N&&&entry/src/main/ets/common/storage/TempDataManager&";
import { httpClient } from "@normalized:N&&&entry/src/main/ets/common/http/HttpClient&";
import { storageManager } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
class BankCardPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__allCards = new ObservedPropertyObjectPU([], this, "allCards");
        this.__boundCards = new ObservedPropertyObjectPU([], this, "boundCards");
        this.__isLoading = new ObservedPropertySimplePU(true, this, "isLoading");
        this.__selectedCardId = new ObservedPropertySimplePU(0, this, "selectedCardId");
        this.isSelectMode = false;
        this.__viewMode = new ObservedPropertySimplePU('已绑定', this, "viewMode");
        this.__selectedCard = new ObservedPropertyObjectPU(null, this, "selectedCard");
        this.__showCreditCardDetails = new ObservedPropertySimplePU(false, this, "showCreditCardDetails");
        this.__selectedCreditCard = new ObservedPropertyObjectPU(null, this, "selectedCreditCard");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: BankCardPage_Params) {
        if (params.allCards !== undefined) {
            this.allCards = params.allCards;
        }
        if (params.boundCards !== undefined) {
            this.boundCards = params.boundCards;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.selectedCardId !== undefined) {
            this.selectedCardId = params.selectedCardId;
        }
        if (params.isSelectMode !== undefined) {
            this.isSelectMode = params.isSelectMode;
        }
        if (params.viewMode !== undefined) {
            this.viewMode = params.viewMode;
        }
        if (params.selectedCard !== undefined) {
            this.selectedCard = params.selectedCard;
        }
        if (params.showCreditCardDetails !== undefined) {
            this.showCreditCardDetails = params.showCreditCardDetails;
        }
        if (params.selectedCreditCard !== undefined) {
            this.selectedCreditCard = params.selectedCreditCard;
        }
    }
    updateStateVars(params: BankCardPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__allCards.purgeDependencyOnElmtId(rmElmtId);
        this.__boundCards.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedCardId.purgeDependencyOnElmtId(rmElmtId);
        this.__viewMode.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedCard.purgeDependencyOnElmtId(rmElmtId);
        this.__showCreditCardDetails.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedCreditCard.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__allCards.aboutToBeDeleted();
        this.__boundCards.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__selectedCardId.aboutToBeDeleted();
        this.__viewMode.aboutToBeDeleted();
        this.__selectedCard.aboutToBeDeleted();
        this.__showCreditCardDetails.aboutToBeDeleted();
        this.__selectedCreditCard.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __allCards: ObservedPropertyObjectPU<BankCard[]>; // 所有银行卡（包括已解绑的历史记录）
    get allCards() {
        return this.__allCards.get();
    }
    set allCards(newValue: BankCard[]) {
        this.__allCards.set(newValue);
    }
    private __boundCards: ObservedPropertyObjectPU<BankCard[]>; // 当前有效的绑定卡
    get boundCards() {
        return this.__boundCards.get();
    }
    set boundCards(newValue: BankCard[]) {
        this.__boundCards.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __selectedCardId: ObservedPropertySimplePU<number>; // 当前选中的银行卡ID
    get selectedCardId() {
        return this.__selectedCardId.get();
    }
    set selectedCardId(newValue: number) {
        this.__selectedCardId.set(newValue);
    }
    private isSelectMode: boolean; // 是否为选择模式（从其他页面跳转过来选择银行卡）
    private __viewMode: ObservedPropertySimplePU<string>; // 视图模式：'所有卡片' | '已绑定'
    get viewMode() {
        return this.__viewMode.get();
    }
    set viewMode(newValue: string) {
        this.__viewMode.set(newValue);
    }
    private __selectedCard: ObservedPropertyObjectPU<BankCard | null>; // 选中查看详情的银行卡
    get selectedCard() {
        return this.__selectedCard.get();
    }
    set selectedCard(newValue: BankCard | null) {
        this.__selectedCard.set(newValue);
    }
    private __showCreditCardDetails: ObservedPropertySimplePU<boolean>; // 显示信用卡详情弹窗
    get showCreditCardDetails() {
        return this.__showCreditCardDetails.get();
    }
    set showCreditCardDetails(newValue: boolean) {
        this.__showCreditCardDetails.set(newValue);
    }
    private __selectedCreditCard: ObservedPropertyObjectPU<BankCard | null>; // 选中的信用卡
    get selectedCreditCard() {
        return this.__selectedCreditCard.get();
    }
    set selectedCreditCard(newValue: BankCard | null) {
        this.__selectedCreditCard.set(newValue);
    }
    aboutToAppear() {
        // 检查路由参数，判断是否为选择模式
        const params = router.getParams() as BankCardPageParams;
        this.isSelectMode = params?.selectMode === true;
        this.selectedCardId = params?.selectedCardId || 0;
        console.log('BankCardPage - 选择模式:', this.isSelectMode, '已选中卡片ID:', this.selectedCardId);
        this.loadBankCards();
    }
    onPageShow() {
        // 检查是否有银行卡添加事件
        const cardAdded = tempDataManager.getData('BANK_CARD_ADDED');
        console.log('BankCardPage onPageShow - 检查银行卡添加事件:', cardAdded);
        if (cardAdded) {
            console.log('BankCardPage - 检测到银行卡添加，重新加载银行卡列表');
            // 重新加载银行卡列表
            this.loadBankCards();
            // 不删除事件标志，让MainPage也能处理
        }
        // 检查是否有银行卡解绑事件
        const cardUnbound = tempDataManager.getData('BANK_CARD_UNBOUND');
        console.log('BankCardPage onPageShow - 检查银行卡解绑事件:', cardUnbound);
        if (cardUnbound) {
            console.log('BankCardPage - 检测到银行卡解绑，重新加载银行卡列表');
            // 重新加载银行卡列表
            this.loadBankCards();
            // 不删除事件标志，让MainPage也能处理
        }
    }
    async loadBankCards(): Promise<void> {
        try {
            this.isLoading = true;
            // 从本地存储获取当前用户ID
            const cachedUserInfo = await storageManager.getUserInfo();
            if (!cachedUserInfo || !cachedUserInfo.userId) {
                console.error('无法获取用户ID');
                promptAction.showToast({ message: '用户信息获取失败' });
                return;
            }
            // 1. 加载所有银行卡（包括已解绑的历史记录）
            await this.loadAllCards(cachedUserInfo.userId);
            // 2. 加载当前有效的绑定卡
            await this.loadBoundCards(cachedUserInfo.userId);
            console.log('银行卡数据加载完成 - 所有卡片:', this.allCards.length, '已绑定:', this.boundCards.length);
        }
        catch (error) {
            console.error('加载银行卡列表失败:', error);
            promptAction.showToast({ message: '加载银行卡列表失败' });
        }
        finally {
            this.isLoading = false;
        }
    }
    /**
     * 加载所有银行卡（包括已解绑的历史记录）
     */
    async loadAllCards(userId: number): Promise<void> {
        try {
            const response = await httpClient.get<SpringBootBankCardResponse[]>(`/bank-card/user/${userId}/all`);
            const bankCardList: SpringBootBankCardResponse[] = response.data;
            this.allCards = this.convertSpringBootBankCardsToLocal(bankCardList);
        }
        catch (error) {
            console.error('加载所有银行卡失败:', error);
            // 如果后端不支持/all接口，回退到原有接口
            const response = await httpClient.get<SpringBootBankCardResponse[]>(`/bank-card/user/${userId}`);
            const bankCardList: SpringBootBankCardResponse[] = response.data;
            this.allCards = this.convertSpringBootBankCardsToLocal(bankCardList);
        }
    }
    /**
     * 加载当前有效的绑定卡
     */
    async loadBoundCards(userId: number): Promise<void> {
        try {
            const response = await httpClient.get<SpringBootBankCardResponse[]>(`/bank-card/user/${userId}/bound`);
            const bankCardList: SpringBootBankCardResponse[] = response.data;
            this.boundCards = this.convertSpringBootBankCardsToLocal(bankCardList);
        }
        catch (error) {
            console.error('加载已绑定银行卡失败:', error);
            // 如果后端不支持/bound接口，从所有卡片中筛选
            this.boundCards = this.allCards.filter(card => card.status === 1);
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(115:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
            Column.bindSheet({ value: this.showCreditCardDetails, changeEvent: newValue => { this.showCreditCardDetails = newValue; } }, { builder: () => {
                    this.CreditCardDetailsDialog.call(this);
                } }, {
                height: 600,
                showClose: true,
                dragBar: true,
                onDisappear: () => {
                    this.showCreditCardDetails = false;
                    this.selectedCreditCard = null;
                }
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(117:7)", "entry");
            // 顶部导航
            Row.width('100%');
            // 顶部导航
            Row.padding({ left: 16, right: 16, top: 12, bottom: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(118:9)", "entry");
            Button.width(40);
            Button.height(40);
            Button.borderRadius(20);
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/BankCardPage.ets(119:11)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor('#333333');
        }, Image);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.isSelectMode ? '选择银行卡' : '银行卡管理');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(132:9)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(139:9)", "entry");
            Button.width(40);
            Button.height(40);
            Button.borderRadius(20);
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.pushUrl({
                    url: 'pages/AddBankCardPage'
                });
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/BankCardPage.ets(140:11)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor('#1976D2');
        }, Image);
        Button.pop();
        // 顶部导航
        Row.pop();
        // 视图模式切换标签栏
        this.ViewModeTabsView.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.LoadingView.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.CardContentView.bind(this)();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    ViewModeTabsView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(187:5)", "entry");
            Row.width('100%');
            Row.padding({ left: 16, right: 16, bottom: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const tab = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Button.createWithLabel(tab);
                    Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(189:9)", "entry");
                    Button.fontSize(14);
                    Button.fontColor(this.viewMode === tab ? '#FFFFFF' : '#666666');
                    Button.backgroundColor(this.viewMode === tab ? '#1976D2' : '#F5F5F5');
                    Button.borderRadius(20);
                    Button.padding({ left: 16, right: 16, top: 8, bottom: 8 });
                    Button.margin({ right: 8 });
                    Button.onClick(() => {
                        this.viewMode = tab;
                    });
                }, Button);
                Button.pop();
            };
            this.forEachUpdateFunction(elmtId, ['所有卡片', '已绑定'], forEachItemGenFunction);
        }, ForEach);
        ForEach.pop();
        Row.pop();
    }
    LoadingView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(207:5)", "entry");
            Column.width('100%');
            Column.layoutWeight(1);
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            LoadingProgress.create();
            LoadingProgress.debugLine("entry/src/main/ets/pages/BankCardPage.ets(208:7)", "entry");
            LoadingProgress.width(40);
            LoadingProgress.height(40);
            LoadingProgress.color('#1976D2');
        }, LoadingProgress);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('加载中...');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(213:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#999999');
            Text.margin({ top: 16 });
        }, Text);
        Text.pop();
        Column.pop();
    }
    EmptyView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(226:5)", "entry");
            Column.width('100%');
            Column.layoutWeight(1);
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
            Column.padding(40);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/BankCardPage.ets(227:7)", "entry");
            Image.width(80);
            Image.height(80);
            Image.fillColor('#CCCCCC');
            Image.margin({ bottom: 16 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('暂无银行卡');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(233:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#666666');
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('添加银行卡后可进行充值、提现等操作');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(238:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#999999');
            Text.textAlign(TextAlign.Center);
            Text.margin({ bottom: 24 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('添加银行卡');
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(244:7)", "entry");
            Button.fontSize(16);
            Button.fontColor(Color.White);
            Button.backgroundColor('#1976D2');
            Button.borderRadius(8);
            Button.padding({ left: 24, right: 24, top: 12, bottom: 12 });
            Button.onClick(() => {
                router.pushUrl({
                    url: 'pages/AddBankCardPage'
                });
            });
        }, Button);
        Button.pop();
        Column.pop();
    }
    CardContentView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.getCurrentCards().length === 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.EmptyView.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.CardListView.bind(this)(this.getCurrentCards());
                });
            }
        }, If);
        If.pop();
    }
    /**
     * 根据当前视图模式获取要显示的卡片列表
     */
    getCurrentCards(): BankCard[] {
        switch (this.viewMode) {
            case '所有卡片':
                return this.allCards;
            case '已绑定':
                return this.boundCards;
            default:
                return this.boundCards;
        }
    }
    CardListView(cards: BankCard[], parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(288:5)", "entry");
            Column.width('100%');
            Column.height('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 使用List组件支持右滑删除
            List.create();
            List.debugLine("entry/src/main/ets/pages/BankCardPage.ets(290:7)", "entry");
            // 使用List组件支持右滑删除
            List.layoutWeight(1);
            // 使用List组件支持右滑删除
            List.scrollBar(BarState.Off);
            // 使用List组件支持右滑删除
            List.padding({ left: 16, right: 16, top: 16, bottom: 20 });
            // 使用List组件支持右滑删除
            List.divider({ strokeWidth: 0 });
        }, List);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const card = _item;
                this.BankCardItem.bind(this)(card, index);
            };
            this.forEachUpdateFunction(elmtId, cards, forEachItemGenFunction, undefined, true, false);
        }, ForEach);
        ForEach.pop();
        // 使用List组件支持右滑删除
        List.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 在选择模式下显示添加银行卡按钮
            if (this.isSelectMode) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(302:9)", "entry");
                        Row.width('100%');
                        Row.padding({ left: 16, right: 16, bottom: 16 });
                        Row.backgroundColor('#FFFFFF');
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('添加新银行卡');
                        Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(303:11)", "entry");
                        Button.fontSize(16);
                        Button.fontColor('#1976D2');
                        Button.backgroundColor('#E3F2FD');
                        Button.borderRadius(8);
                        Button.width('100%');
                        Button.height(48);
                        Button.onClick(() => {
                            router.pushUrl({
                                url: 'pages/AddBankCardPage'
                            });
                        });
                    }, Button);
                    Button.pop();
                    Row.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    BankCardItem(card: BankCard, index: number, parent = null) {
        {
            const itemCreation = (elmtId, isInitialRender) => {
                ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                itemCreation2(elmtId, isInitialRender);
                if (!isInitialRender) {
                    // 使用ListItem实现右滑删除功能
                    ListItem.pop();
                }
                ViewStackProcessor.StopGetAccessRecording();
            };
            const itemCreation2 = (elmtId, isInitialRender) => {
                ListItem.create(deepRenderFunction, true);
                // 使用ListItem实现右滑删除功能
                ListItem.swipeAction({
                    end: this.SwipeDeleteButton.bind(this, card)
                });
                // 使用ListItem实现右滑删除功能
                ListItem.margin({ bottom: 12 });
                ListItem.debugLine("entry/src/main/ets/pages/BankCardPage.ets(330:5)", "entry");
            };
            const deepRenderFunction = (elmtId, isInitialRender) => {
                itemCreation(elmtId, isInitialRender);
                // 银行卡主体内容
                this.BankCardContent.bind(this)(card);
                // 使用ListItem实现右滑删除功能
                ListItem.pop();
            };
            this.observeComponentCreation2(itemCreation2, ListItem);
            // 使用ListItem实现右滑删除功能
            ListItem.pop();
        }
    }
    BankCardContent(card: BankCard, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(342:5)", "entry");
            Column.width('100%');
            Column.backgroundColor('#FFFFFF');
            Column.borderRadius(12);
            Column.padding(16);
            Column.shadow({
                radius: 4,
                color: 'rgba(0,0,0,0.1)',
                offsetX: 0,
                offsetY: 2
            });
            Column.onClick(() => {
                // 点击卡片查看详细信息
                this.showCardDetails(card);
            });
        }, Column);
        // 真实银行卡展示
        this.BankCardDisplay.bind(this)(card);
        // 银行卡基本信息
        this.BankCardBasicInfo.bind(this)(card);
        // 操作按钮区域
        this.CardActionButtons.bind(this)(card);
        Column.pop();
    }
    /**
     * 显示银行卡详细信息
     */
    showCardDetails(card: BankCard) {
        if (this.isSelectMode) {
            // 选择模式下，选择该卡片并返回
            this.selectCard(card);
        }
        else {
            // 查看模式下，显示详细信息
            this.selectedCard = card;
            if (card.cardType === BankCardType.CREDIT) {
                // 信用卡显示详细信息弹窗
                this.selectedCreditCard = card;
                this.showCreditCardDetails = true;
            }
            else {
                // 储蓄卡跳转到详情页面
                this.goToCardDetail(card);
            }
        }
    }
    BankCardBasicInfo(card: BankCard, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(391:5)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(392:7)", "entry");
            Row.width('100%');
            Row.margin({ top: 12, bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.bankName);
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(393:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Bold);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/BankCardPage.ets(398:9)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.cardType === BankCardType.CREDIT ? '信用卡' : '储蓄卡');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(400:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.backgroundColor('#F5F5F5');
            Text.borderRadius(4);
            Text.padding({ left: 8, right: 8, top: 4, bottom: 4 });
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(410:7)", "entry");
            Row.width('100%');
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`**** **** **** ${card.cardNo.slice(-4)}`);
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(411:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/BankCardPage.ets(415:9)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.status === 1 ? '已绑定' : '已解绑');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(417:9)", "entry");
            Text.fontSize(12);
            Text.fontColor(card.status === 1 ? '#4CAF50' : '#F44336');
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (card.isDefault) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('默认卡');
                        Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(425:9)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#1976D2');
                        Text.backgroundColor('#E3F2FD');
                        Text.borderRadius(4);
                        Text.padding({ left: 8, right: 8, top: 4, bottom: 4 });
                        Text.alignSelf(ItemAlign.Start);
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    CardActionButtons(card: BankCard, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(438:5)", "entry");
            Row.width('100%');
            Row.justifyContent(FlexAlign.End);
            Row.margin({ top: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('查看详情');
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(439:7)", "entry");
            Button.fontSize(12);
            Button.fontColor('#1976D2');
            Button.backgroundColor('#E3F2FD');
            Button.borderRadius(6);
            Button.padding({ left: 12, right: 12, top: 6, bottom: 6 });
            Button.onClick(() => {
                this.showCardDetails(card);
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (card.status === 1) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('解绑');
                        Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(450:9)", "entry");
                        Button.fontSize(12);
                        Button.fontColor('#F44336');
                        Button.backgroundColor('#FFEBEE');
                        Button.borderRadius(6);
                        Button.padding({ left: 12, right: 12, top: 6, bottom: 6 });
                        Button.margin({ left: 8 });
                        Button.onClick(() => {
                            this.confirmUnbindCard(card);
                        });
                    }, Button);
                    Button.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('重新绑定');
                        Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(461:9)", "entry");
                        Button.fontSize(12);
                        Button.fontColor('#4CAF50');
                        Button.backgroundColor('#E8F5E8');
                        Button.borderRadius(6);
                        Button.padding({ left: 12, right: 12, top: 6, bottom: 6 });
                        Button.margin({ left: 8 });
                        Button.onClick(() => {
                            this.rebindCard(card);
                        });
                    }, Button);
                    Button.pop();
                });
            }
        }, If);
        If.pop();
        Row.pop();
    }
    BankCardDetailInfo(card: BankCard, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(480:5)", "entry");
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行名称
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(482:7)", "entry");
            // 银行名称
            Row.width('100%');
            // 银行名称
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行名称');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(483:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.width(80);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.bankName);
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(488:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        // 银行名称
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 卡片类型
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(497:7)", "entry");
            // 卡片类型
            Row.width('100%');
            // 卡片类型
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('卡片类型');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(498:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.width(80);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getCardTypeText(card.cardType));
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(503:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        // 卡片类型
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 持卡人姓名
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(512:7)", "entry");
            // 持卡人姓名
            Row.width('100%');
            // 持卡人姓名
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('持卡人姓名');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(513:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.width(80);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.holderName);
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(518:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        // 持卡人姓名
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 卡号
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(527:7)", "entry");
            // 卡号
            Row.width('100%');
            // 卡号
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('卡号');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(528:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.width(80);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.formatCardNumber(card.cardNo));
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(533:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        // 卡号
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 绑定状态
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(542:7)", "entry");
            // 绑定状态
            Row.width('100%');
            // 绑定状态
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('绑定状态');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(543:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.width(80);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.isBound === BankCardStatus.BOUND ? '已绑定' : '未绑定');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(548:9)", "entry");
            Text.fontSize(14);
            Text.fontColor(card.isBound === BankCardStatus.BOUND ? '#4CAF50' : '#F44336');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        // 绑定状态
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 绑定时间
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(557:7)", "entry");
            // 绑定时间
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('绑定时间');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(558:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.width(80);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.createTime || '未绑定');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(563:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        // 绑定时间
        Row.pop();
        Column.pop();
    }
    QuickActionsView(card: BankCard, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(575:5)", "entry");
            Row.width('100%');
            Row.justifyContent(FlexAlign.Start);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('支付');
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(576:7)", "entry");
            Button.fontSize(14);
            Button.fontColor('#FFFFFF');
            Button.backgroundColor('#4CAF50');
            Button.borderRadius(8);
            Button.width(80);
            Button.height(36);
            Button.onClick(() => {
                this.useCardForPayment(card);
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('转账');
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(587:7)", "entry");
            Button.fontSize(14);
            Button.fontColor('#FFFFFF');
            Button.backgroundColor('#2196F3');
            Button.borderRadius(8);
            Button.width(80);
            Button.height(36);
            Button.margin({ left: 12 });
            Button.onClick(() => {
                this.useCardForTransfer(card);
            });
        }, Button);
        Button.pop();
        Row.pop();
    }
    BankCardDisplay(card: BankCard, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 真实银行卡设计
            Stack.create({ alignContent: Alignment.TopStart });
            Stack.debugLine("entry/src/main/ets/pages/BankCardPage.ets(606:5)", "entry");
            // 真实银行卡设计
            Stack.width('100%');
            // 真实银行卡设计
            Stack.margin({ bottom: 16 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(607:7)", "entry");
            Column.width('100%');
            Column.height(120);
            Column.padding(16);
            Column.borderRadius(8);
            Column.linearGradient({
                direction: GradientDirection.Right,
                colors: this.getBankCardGradient(card.bankName)
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡顶部信息
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(609:9)", "entry");
            // 银行卡顶部信息
            Row.width('100%');
            // 银行卡顶部信息
            Row.margin({ bottom: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.bankName);
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(610:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Bold);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`¥${this.getCardBalance(card)}`);
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(616:11)", "entry");
            Text.fontSize(18);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Bold);
        }, Text);
        Text.pop();
        // 银行卡顶部信息
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡号
            Text.create(this.formatCardNumber(card.cardNo));
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(625:9)", "entry");
            // 银行卡号
            Text.fontSize(16);
            // 银行卡号
            Text.fontColor('#FFFFFF');
            // 银行卡号
            Text.fontWeight(FontWeight.Medium);
            // 银行卡号
            Text.letterSpacing(1);
            // 银行卡号
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        // 银行卡号
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡装饰图案
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/BankCardPage.ets(642:7)", "entry");
            // 银行卡装饰图案
            Image.width(60);
            // 银行卡装饰图案
            Image.height(60);
            // 银行卡装饰图案
            Image.fillColor('rgba(255,255,255,0.1)');
            // 银行卡装饰图案
            Image.position({ x: '75%', y: '20%' });
        }, Image);
        // 真实银行卡设计
        Stack.pop();
    }
    BankCardInfoAndActions(card: BankCard, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(654:5)", "entry");
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡标题和卡号
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(656:7)", "entry");
            // 银行卡标题和卡号
            Row.width('100%');
            // 银行卡标题和卡号
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`${card.bankName} ${this.getCardTypeText(card.cardType)}`);
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(657:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('管理');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(663:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#1976D2');
            Text.onClick(() => {
                this.viewCardDetail(card);
            });
        }, Text);
        Text.pop();
        // 银行卡标题和卡号
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.formatCardNumber(card.cardNo));
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(673:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 状态标签
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(680:7)", "entry");
            // 状态标签
            Row.width('100%');
            // 状态标签
            Row.margin({ bottom: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 连接状态
            Text.create(card.isBound === BankCardStatus.BOUND ? '已连接' : '未连接');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(682:9)", "entry");
            // 连接状态
            Text.fontSize(12);
            // 连接状态
            Text.fontColor('#FFFFFF');
            // 连接状态
            Text.backgroundColor(card.isBound === BankCardStatus.BOUND ? '#4CAF50' : '#999999');
            // 连接状态
            Text.borderRadius(12);
            // 连接状态
            Text.padding({ left: 8, right: 8, top: 4, bottom: 4 });
            // 连接状态
            Text.margin({ right: 8 });
        }, Text);
        // 连接状态
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 激活状态
            Text.create('已激活');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(691:9)", "entry");
            // 激活状态
            Text.fontSize(12);
            // 激活状态
            Text.fontColor('#FFFFFF');
            // 激活状态
            Text.backgroundColor('#2196F3');
            // 激活状态
            Text.borderRadius(12);
            // 激活状态
            Text.padding({ left: 8, right: 8, top: 4, bottom: 4 });
            // 激活状态
            Text.margin({ right: 8 });
        }, Text);
        // 激活状态
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 默认卡标识
            if (card.isDefault) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('默认卡片');
                        Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(701:11)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#FFFFFF');
                        Text.backgroundColor('#FF9800');
                        Text.borderRadius(12);
                        Text.padding({ left: 8, right: 8, top: 4, bottom: 4 });
                        Text.margin({ right: 8 });
                    }, Text);
                    Text.pop();
                });
            }
            // 卡片类型
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 卡片类型
            Text.create(this.getCardTypeText(card.cardType));
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(711:9)", "entry");
            // 卡片类型
            Text.fontSize(12);
            // 卡片类型
            Text.fontColor('#FFFFFF');
            // 卡片类型
            Text.backgroundColor(card.cardType === BankCardType.CREDIT ? '#9C27B0' : '#607D8B');
            // 卡片类型
            Text.borderRadius(12);
            // 卡片类型
            Text.padding({ left: 8, right: 8, top: 4, bottom: 4 });
        }, Text);
        // 卡片类型
        Text.pop();
        // 状态标签
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 操作按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(722:7)", "entry");
            // 操作按钮
            Row.width('100%');
            // 操作按钮
            Row.justifyContent(FlexAlign.Start);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('详情');
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(723:9)", "entry");
            Button.fontSize(14);
            Button.fontColor('#1976D2');
            Button.backgroundColor('#E3F2FD');
            Button.borderRadius(6);
            Button.padding({ left: 12, right: 12, top: 8, bottom: 8 });
            Button.margin({ right: 8 });
            Button.onClick(() => {
                this.goToCardDetail(card);
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 信用卡详情按钮（仅信用卡显示）
            if (card.cardType === BankCardType.CREDIT) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('信用卡详情');
                        Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(736:11)", "entry");
                        Button.fontSize(14);
                        Button.fontColor('#9C27B0');
                        Button.backgroundColor('#F3E5F5');
                        Button.borderRadius(6);
                        Button.padding({ left: 12, right: 12, top: 8, bottom: 8 });
                        Button.margin({ right: 8 });
                        Button.onClick(() => {
                            this.showCreditCardDetails = true;
                            this.selectedCreditCard = card;
                        });
                    }, Button);
                    Button.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(card.isBound === BankCardStatus.BOUND ? '取消激活' : '激活');
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(749:9)", "entry");
            Button.fontSize(14);
            Button.fontColor(card.isBound === BankCardStatus.BOUND ? '#FF5722' : '#4CAF50');
            Button.backgroundColor(card.isBound === BankCardStatus.BOUND ? '#FFEBEE' : '#E8F5E8');
            Button.borderRadius(6);
            Button.padding({ left: 12, right: 12, top: 8, bottom: 8 });
            Button.margin({ right: 8 });
            Button.onClick(() => {
                if (card.isBound === BankCardStatus.BOUND) {
                    this.confirmUnbindCard(card);
                }
                else {
                    this.bindCard(card);
                }
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('使用');
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(764:9)", "entry");
            Button.fontSize(14);
            Button.fontColor('#FFFFFF');
            Button.backgroundColor('#4CAF50');
            Button.borderRadius(6);
            Button.padding({ left: 12, right: 12, top: 8, bottom: 8 });
            Button.margin({ right: 8 });
            Button.onClick(() => {
                this.showCardOptions(card);
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('解除绑定');
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(775:9)", "entry");
            Button.fontSize(14);
            Button.fontColor('#F44336');
            Button.backgroundColor('#FFEBEE');
            Button.borderRadius(6);
            Button.padding({ left: 12, right: 12, top: 8, bottom: 8 });
            Button.onClick(() => {
                this.confirmDeleteCard(card);
            });
        }, Button);
        Button.pop();
        // 操作按钮
        Row.pop();
        Column.pop();
    }
    SwipeDeleteButton(card: BankCard, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(793:5)", "entry");
            Button.width(80);
            Button.height(180);
            Button.backgroundColor('#F44336');
            Button.borderRadius(12);
            Button.onClick(() => {
                this.confirmDeleteCard(card);
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(794:7)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🗑️');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(795:9)", "entry");
            Text.fontSize(24);
            Text.fontColor('#FFFFFF');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('删除');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(799:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#FFFFFF');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        Button.pop();
    }
    /**
     * 使用银行卡进行支付
     */
    useCardForPayment(card: BankCard) {
        console.log('使用银行卡支付:', card.bankName, card.cardNo);
        // 跳转到支付页面
        router.pushUrl({
            url: 'pages/PaymentPage',
            params: {
                selectedCard: {
                    cardId: card.cardId,
                    bankName: card.bankName,
                    cardNo: card.cardNo,
                    cardType: card.cardType,
                    holderName: card.holderName,
                    maskedCardNo: card.maskedCardNo
                }
            }
        });
    }
    /**
     * 使用银行卡进行转账
     */
    useCardForTransfer(card: BankCard) {
        console.log('使用银行卡转账:', card.bankName, card.cardNo);
        // 跳转到钱包操作页面（转账功能）
        router.pushUrl({
            url: 'pages/WalletOperationPage',
            params: {
                operationType: 'transfer',
                selectedCard: {
                    cardId: card.cardId,
                    bankName: card.bankName,
                    cardNo: card.cardNo,
                    cardType: card.cardType,
                    holderName: card.holderName,
                    maskedCardNo: card.maskedCardNo
                }
            }
        });
    }
    /**
     * 选择银行卡
     */
    selectCard(card: BankCard) {
        this.selectedCardId = card.cardId;
        console.log('选择银行卡:', card.bankName, 'ID:', card.cardId);
        if (this.isSelectMode) {
            // 选择模式下，返回选中的银行卡信息
            router.back({
                url: '',
                params: {
                    selectedCard: {
                        cardId: card.cardId,
                        bankName: card.bankName,
                        cardNo: card.cardNo,
                        cardType: card.cardType,
                        holderName: card.holderName,
                        isDefault: card.isDefault,
                        maskedCardNo: card.maskedCardNo
                    }
                }
            });
        }
    }
    /**
     * 查看银行卡详情
     */
    viewCardDetail(card: BankCard) {
        console.log('查看银行卡详情:', card.bankName, card.cardNo);
        // 显示银行卡操作选项
        this.showCardOptions(card);
    }
    showCardOptions(card: BankCard) {
        this.goToCardDetail(card);
    }
    /**
     * 跳转到银行卡详情页面
     */
    goToCardDetail(card: BankCard) {
        router.pushUrl({
            url: 'pages/BankCardDetailPage',
            params: {
                cardId: card.cardId
            }
        }).catch((error: Error) => {
            console.error('跳转银行卡详情页面失败:', error);
            promptAction.showToast({ message: '打开详情页面失败' });
        });
    }
    /**
     * 重新绑定银行卡
     */
    async rebindCard(card: BankCard): Promise<void> {
        try {
            const result = await promptAction.showDialog({
                title: '重新绑定',
                message: `确定要重新绑定银行卡 ${card.bankName}(${card.cardNo.slice(-4)}) 吗？`,
                buttons: [
                    { text: '确定', color: '#1976D2' },
                    { text: '取消', color: '#666666' }
                ]
            });
            if (result.index === 0) {
                await this.bindCard(card);
            }
        }
        catch (error) {
            console.error('显示重新绑定确认对话框失败:', error);
        }
    }
    /**
     * 确认解绑银行卡
     */
    async confirmUnbindCard(card: BankCard): Promise<void> {
        try {
            const result = await promptAction.showDialog({
                title: '确认解绑',
                message: `确定要解绑银行卡 ${card.bankName}(${card.cardNo.slice(-4)}) 吗？`,
                buttons: [
                    { text: '确定', color: '#F44336' },
                    { text: '取消', color: '#666666' }
                ]
            });
            if (result.index === 0) {
                await this.unbindCard(card);
            }
        }
        catch (error) {
            console.error('显示确认对话框失败:', error);
        }
    }
    /**
     * 设置默认银行卡
     */
    async setDefaultCard(card: BankCard): Promise<void> {
        try {
            // 从本地存储获取当前用户ID
            const cachedUserInfo = await storageManager.getUserInfo();
            if (!cachedUserInfo || !cachedUserInfo.userId) {
                console.error('无法获取用户ID');
                promptAction.showToast({ message: '用户信息获取失败' });
                return;
            }
            // 调用SpringBoot3后端API设置默认银行卡
            const setDefaultRequest: SpringBootSetDefaultCardRequest = {};
            await httpClient.put<string>(`/bank-card/set-default/${card.cardId}?userId=${cachedUserInfo.userId}`, setDefaultRequest);
            promptAction.showToast({ message: '设置默认卡成功' });
            // 通知其他页面银行卡已更新
            console.log('BankCardPage - 设置银行卡更新事件标志');
            tempDataManager.setData('BANK_CARD_UPDATED', true);
            // 重新加载银行卡列表
            await this.loadBankCards();
        }
        catch (error) {
            console.error('设置默认卡失败:', error);
            promptAction.showToast({ message: '设置默认卡失败，请重试' });
        }
    }
    /**
     * 解绑银行卡
     */
    async unbindCard(card: BankCard): Promise<void> {
        try {
            // 从本地存储获取当前用户ID
            const cachedUserInfo = await storageManager.getUserInfo();
            if (!cachedUserInfo || !cachedUserInfo.userId) {
                console.error('无法获取用户ID');
                promptAction.showToast({ message: '用户信息获取失败' });
                return;
            }
            // 调用SpringBoot3后端API解绑银行卡
            const unbindRequest: SpringBootUnbindBankCardRequest = {};
            await httpClient.put<number>(`/bank-card/unbind/${card.cardId}?userId=${cachedUserInfo.userId}`, unbindRequest);
            promptAction.showToast({ message: '解绑成功' });
            // 通知其他页面银行卡已解绑
            console.log('BankCardPage - 设置银行卡解绑事件标志');
            tempDataManager.setData('BANK_CARD_UNBOUND', true);
            // 重新加载银行卡列表
            await this.loadBankCards();
        }
        catch (error) {
            console.error('解绑银行卡失败:', error);
            promptAction.showToast({ message: '解绑失败，请重试' });
        }
    }
    /**
     * 转换SpringBoot3银行卡到本地格式
     */
    private convertSpringBootBankCardsToLocal(bankCardList: SpringBootBankCardResponse[]): BankCard[] {
        return bankCardList.map((card: SpringBootBankCardResponse): BankCard => ({
            cardId: card.cardId || 0,
            userId: card.userId || 0,
            cardNo: card.cardNumber || '',
            cardType: this.mapBankCardType(card.cardType),
            bankName: card.bankName || '',
            holderName: card.cardHolder || '',
            isBound: card.status === 1 ? BankCardStatus.BOUND : BankCardStatus.UNBOUND,
            createTime: card.createdAt || card.createTime || '',
            updateTime: card.updatedAt || card.updateTime || '',
            maskedCardNo: card.cardNumber ? this.maskCardNo(card.cardNumber) : undefined,
            isDefault: card.isDefault === 1 || card.isDefault === true // 处理默认卡标识
        }));
    }
    /**
     * 映射银行卡类型
     */
    private mapBankCardType(cardType: string | number | undefined): BankCardType {
        if (typeof cardType === 'number') {
            // 数字类型：1=储蓄卡，2=信用卡
            return cardType === 2 ? BankCardType.CREDIT : BankCardType.DEBIT;
        }
        else if (typeof cardType === 'string') {
            // 字符串类型
            if (cardType === '信用卡' || cardType === 'CREDIT') {
                return BankCardType.CREDIT;
            }
        }
        // 默认返回储蓄卡
        return BankCardType.DEBIT;
    }
    /**
     * 脱敏银行卡号
     */
    private maskCardNo(cardNo: string): string {
        if (!cardNo || cardNo.length < 8)
            return cardNo;
        return cardNo.replace(/(\d{4})\d*(\d{4})/, '$1****$2');
    }
    /**
     * 格式化银行卡号显示（带空格分隔）
     */
    private formatCardNumber(cardNo: string): string {
        if (!cardNo)
            return '';
        // 脱敏处理
        const maskedCardNo = this.maskCardNo(cardNo);
        // 添加空格分隔，每4位一组
        return maskedCardNo.replace(/(.{4})/g, '$1 ').trim();
    }
    /**
     * 获取卡片类型文本
     */
    private getCardTypeText(cardType: BankCardType): string {
        switch (cardType) {
            case BankCardType.CREDIT:
                return '信用卡';
            case BankCardType.DEBIT:
                return '储蓄卡';
            default:
                return '储蓄卡';
        }
    }
    /**
     * 获取银行卡渐变色
     */
    private getBankCardGradient(bankName: string): Array<[
        string,
        number
    ]> {
        const gradients: Record<string, Array<[
            string,
            number
        ]>> = {
            '中国工商银行': [['#C41E3A', 0], ['#8B0000', 1]],
            '中国建设银行': [['#003DA5', 0], ['#001F5C', 1]],
            '中国农业银行': [['#00A651', 0], ['#006B35', 1]],
            '中国银行': [['#B8860B', 0], ['#8B6914', 1]],
            '招商银行': [['#DC143C', 0], ['#8B0000', 1]],
            '交通银行': [['#0066CC', 0], ['#003D7A', 1]],
            '中信银行': [['#FF6B35', 0], ['#CC4125', 1]],
            '光大银行': [['#9932CC', 0], ['#6A1B9A', 1]],
            '华夏银行': [['#FF1744', 0], ['#C62828', 1]],
            '民生银行': [['#00BCD4', 0], ['#0097A7', 1]],
            '广发银行': [['#FF5722', 0], ['#D84315', 1]],
            '平安银行': [['#FF9800', 0], ['#F57C00', 1]]
        };
        return gradients[bankName] || [['#1976D2', 0], ['#0D47A1', 1]];
    }
    /**
     * 确认删除银行卡
     */
    async confirmDeleteCard(card: BankCard): Promise<void> {
        try {
            const result = await promptAction.showDialog({
                title: '确认删除',
                message: `确定要删除银行卡 ${card.bankName}(${card.cardNo.slice(-4)}) 吗？\n删除后将无法恢复。`,
                buttons: [
                    { text: '确定删除', color: '#F44336' },
                    { text: '取消', color: '#666666' }
                ]
            });
            if (result.index === 0) {
                await this.deleteCard(card);
            }
        }
        catch (error) {
            console.error('显示删除确认对话框失败:', error);
        }
    }
    /**
     * 删除银行卡
     */
    async deleteCard(card: BankCard): Promise<void> {
        try {
            // 从本地存储获取当前用户ID
            const cachedUserInfo = await storageManager.getUserInfo();
            if (!cachedUserInfo || !cachedUserInfo.userId) {
                console.error('无法获取用户ID');
                promptAction.showToast({ message: '用户信息获取失败' });
                return;
            }
            // 调用SpringBoot3后端API删除银行卡
            await httpClient.delete<number>(`/bank-card/${card.cardId}?userId=${cachedUserInfo.userId}`);
            promptAction.showToast({ message: '删除成功' });
            // 通知其他页面银行卡已删除
            console.log('BankCardPage - 设置银行卡删除事件标志');
            tempDataManager.setData('BANK_CARD_DELETED', true);
            // 重新加载银行卡列表
            await this.loadBankCards();
        }
        catch (error) {
            console.error('删除银行卡失败:', error);
            promptAction.showToast({ message: '删除失败，请重试' });
        }
    }
    /**
     * 绑定银行卡
     */
    async bindCard(card: BankCard): Promise<void> {
        try {
            // 从本地存储获取当前用户ID
            const cachedUserInfo = await storageManager.getUserInfo();
            if (!cachedUserInfo || !cachedUserInfo.userId) {
                console.error('无法获取用户ID');
                promptAction.showToast({ message: '用户信息获取失败' });
                return;
            }
            // 调用SpringBoot3后端API绑定银行卡
            const bindRequest: Record<string, never> = {};
            await httpClient.put<number>(`/bank-card/bind/${card.cardId}?userId=${cachedUserInfo.userId}`, bindRequest);
            promptAction.showToast({ message: '绑定成功' });
            // 通知其他页面银行卡已绑定
            console.log('BankCardPage - 设置银行卡绑定事件标志');
            tempDataManager.setData('BANK_CARD_BOUND', true);
            // 重新加载银行卡列表
            await this.loadBankCards();
        }
        catch (error) {
            console.error('绑定银行卡失败:', error);
            promptAction.showToast({ message: '绑定失败，请重试' });
        }
    }
    /**
     * 获取银行卡余额（模拟数据）
     */
    private getCardBalance(card: BankCard): string {
        // 这里可以从后端API获取真实余额，现在使用模拟数据
        const balances: Record<number, string> = {
            1: '1,000',
            2: '5,280',
            3: '12,500',
            4: '850'
        };
        return balances[card.cardId] || '0';
    }
    CreditCardDetailsDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.selectedCreditCard) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(1213:7)", "entry");
                        Column.width('100%');
                        Column.padding(20);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('信用卡详细信息');
                        Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(1214:9)", "entry");
                        Text.fontSize(18);
                        Text.fontWeight(FontWeight.Bold);
                        Text.margin({ bottom: 20 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 信用卡基本信息
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(1220:9)", "entry");
                        // 信用卡基本信息
                        Column.width('100%');
                        // 信用卡基本信息
                        Column.padding(16);
                        // 信用卡基本信息
                        Column.margin({ bottom: 16 });
                        // 信用卡基本信息
                        Column.borderRadius(12);
                        // 信用卡基本信息
                        Column.backgroundColor('#F8F9FA');
                    }, Column);
                    this.CreditCardInfoItem.bind(this)('银行名称', this.selectedCreditCard.bankName);
                    this.CreditCardInfoItem.bind(this)('卡号', this.formatCardNumber(this.selectedCreditCard.cardNo));
                    this.CreditCardInfoItem.bind(this)('卡片类型', '信用卡');
                    this.CreditCardInfoItem.bind(this)('持卡人', this.selectedCreditCard.holderName || '未设置');
                    this.CreditCardInfoItem.bind(this)('有效期', '12/28');
                    this.CreditCardInfoItem.bind(this)('CVV', '***');
                    // 信用卡基本信息
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 信用卡额度信息
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(1235:9)", "entry");
                        // 信用卡额度信息
                        Column.width('100%');
                        // 信用卡额度信息
                        Column.padding(16);
                        // 信用卡额度信息
                        Column.margin({ bottom: 16 });
                        // 信用卡额度信息
                        Column.borderRadius(12);
                        // 信用卡额度信息
                        Column.backgroundColor('#F8F9FA');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('额度信息');
                        Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(1236:11)", "entry");
                        Text.fontSize(16);
                        Text.fontWeight(FontWeight.Medium);
                        Text.alignSelf(ItemAlign.Start);
                        Text.margin({ bottom: 12 });
                    }, Text);
                    Text.pop();
                    this.CreditCardInfoItem.bind(this)('信用额度', '¥50,000.00');
                    this.CreditCardInfoItem.bind(this)('可用额度', '¥35,280.00');
                    this.CreditCardInfoItem.bind(this)('已用额度', '¥14,720.00');
                    this.CreditCardInfoItem.bind(this)('最低还款', '¥1,472.00');
                    this.CreditCardInfoItem.bind(this)('账单日', '每月5日');
                    this.CreditCardInfoItem.bind(this)('还款日', '每月25日');
                    // 信用卡额度信息
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 操作按钮
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(1256:9)", "entry");
                        // 操作按钮
                        Row.width('100%');
                        // 操作按钮
                        Row.margin({ bottom: 16 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('还款');
                        Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(1257:11)", "entry");
                        Button.fontSize(14);
                        Button.fontColor('#FFFFFF');
                        Button.backgroundColor('#4CAF50');
                        Button.borderRadius(8);
                        Button.layoutWeight(1);
                        Button.margin({ right: 8 });
                        Button.onClick(() => {
                            promptAction.showToast({ message: '信用卡还款功能开发中' });
                        });
                    }, Button);
                    Button.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('分期');
                        Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(1268:11)", "entry");
                        Button.fontSize(14);
                        Button.fontColor('#FFFFFF');
                        Button.backgroundColor('#FF9800');
                        Button.borderRadius(8);
                        Button.layoutWeight(1);
                        Button.margin({ left: 8 });
                        Button.onClick(() => {
                            promptAction.showToast({ message: '信用卡分期功能开发中' });
                        });
                    }, Button);
                    Button.pop();
                    // 操作按钮
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('关闭');
                        Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(1282:9)", "entry");
                        Button.width('100%');
                        Button.height(48);
                        Button.fontSize(16);
                        Button.fontColor('#FFFFFF');
                        Button.backgroundColor('#1976D2');
                        Button.borderRadius(8);
                        Button.onClick(() => {
                            this.showCreditCardDetails = false;
                            this.selectedCreditCard = null;
                        });
                    }, Button);
                    Button.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
    }
    CreditCardInfoItem(label: string, value: string, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(1301:5)", "entry");
            Row.width('100%');
            Row.padding({ top: 8, bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(1302:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(value);
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(1307:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
        }, Text);
        Text.pop();
        Row.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "BankCardPage";
    }
}
registerNamedRoute(() => new BankCardPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/BankCardPage", pageFullPath: "entry/src/main/ets/pages/BankCardPage", integratedHsp: "false", moduleType: "followWithHap" });
