{"version": "2.0", "ppid": 21780, "events": [{"head": {"id": "69bbd56a-056e-40ad-acd5-854a458047d0", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300797393100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8377b1c-b59c-4a0c-b9ce-017bf044c82a", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31460076699300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05347852-2cf4-405a-9afb-2d7ceddc3d74", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31460078089400, "endTime": 31460078114500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3a50c887-a83b-4f52-8cf8-b8dcde3ec39a", "logId": "e691ded7-acc7-4049-a80e-7d43f4f4bd33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e691ded7-acc7-4049-a80e-7d43f4f4bd33", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31460078089400, "endTime": 31460078114500}, "additional": {"logType": "info", "children": [], "durationId": "05347852-2cf4-405a-9afb-2d7ceddc3d74"}}, {"head": {"id": "bd94c210-8413-48ac-9874-34690a9a9c3e", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31461471999100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14eedd46-b41f-4fc6-8699-f574d1cbee21", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31461474727600, "endTime": 31461474775300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3a50c887-a83b-4f52-8cf8-b8dcde3ec39a", "logId": "754a76f4-d90f-4c35-9f65-ee7ce7707ed1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "754a76f4-d90f-4c35-9f65-ee7ce7707ed1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31461474727600, "endTime": 31461474775300}, "additional": {"logType": "info", "children": [], "durationId": "14eedd46-b41f-4fc6-8699-f574d1cbee21"}}, {"head": {"id": "6d8adc59-ecbc-494f-942f-1f76fd5f1055", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757265023900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af73469e-8690-42fe-83de-c5db7a1c6b4b", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757265355900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a349a6ee-c5e7-4ebc-a451-69243d6f66d0", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757774146700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efb9b767-24b9-4fde-821a-72d70c0e7acf", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757791673400, "endTime": 31758116227700}, "additional": {"children": ["24e48ea4-6bcb-4dde-9df2-3186c12596d0", "f15badc7-bb70-408f-8cec-b7005f66e300", "c2a98888-00cb-412c-8bec-d87df6d0d17e", "47f91d58-6ada-44e5-97d1-6cbc8a05ccb7", "7d9f15b6-1119-42a6-8b7f-cd1a95a9dc3f", "0ea0ffa9-3c5d-47a6-adea-096c54977ccf", "9e4484b8-4ff9-43b2-8ca7-0a6d7d58f123"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "0300f2c9-1f2a-47c9-8e2c-7fb383b8694a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24e48ea4-6bcb-4dde-9df2-3186c12596d0", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757791675900, "endTime": 31757820258400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "efb9b767-24b9-4fde-821a-72d70c0e7acf", "logId": "f0e16d25-ee0c-4115-91a3-3d92c7380c77"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f15badc7-bb70-408f-8cec-b7005f66e300", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757820344600, "endTime": 31758113889100}, "additional": {"children": ["13e33900-ff27-4627-b91d-03e1fb2ed3a0", "4613a506-21d6-4bef-b6e2-6aa0b153107a", "67777c70-d307-4a39-bcf8-48516416c796", "b9858162-ee70-42ae-a53c-a32e4a72e457", "a3245e11-d737-45fa-973e-6e2cc46df9ef", "3c93d40b-9237-4687-ba39-24a089a39bb6", "1ad84055-d959-4860-8520-e42485d66835", "bf799aad-3ecf-4a90-950f-5aabd282b922", "d1d35b9d-1ec4-48c3-8d0e-c02e22416b5f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "efb9b767-24b9-4fde-821a-72d70c0e7acf", "logId": "61ae8ce1-e3ad-401b-b66b-a9e45fe42353"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c2a98888-00cb-412c-8bec-d87df6d0d17e", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758113920600, "endTime": 31758116215200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "efb9b767-24b9-4fde-821a-72d70c0e7acf", "logId": "3313e49e-3ef6-48ff-84ca-4ecd71ea4355"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "47f91d58-6ada-44e5-97d1-6cbc8a05ccb7", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758116221200, "endTime": 31758116222900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "efb9b767-24b9-4fde-821a-72d70c0e7acf", "logId": "9bfaed95-d47e-448c-8532-56ef1814130d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d9f15b6-1119-42a6-8b7f-cd1a95a9dc3f", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757798642900, "endTime": 31757798695000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "efb9b767-24b9-4fde-821a-72d70c0e7acf", "logId": "3ae959f4-3aa2-4620-a18b-f0020ff10c3b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ae959f4-3aa2-4620-a18b-f0020ff10c3b", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757798642900, "endTime": 31757798695000}, "additional": {"logType": "info", "children": [], "durationId": "7d9f15b6-1119-42a6-8b7f-cd1a95a9dc3f", "parent": "0300f2c9-1f2a-47c9-8e2c-7fb383b8694a"}}, {"head": {"id": "0ea0ffa9-3c5d-47a6-adea-096c54977ccf", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757811506000, "endTime": 31757811538200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "efb9b767-24b9-4fde-821a-72d70c0e7acf", "logId": "1266cc52-a9b6-423e-8744-81fd11ed56ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1266cc52-a9b6-423e-8744-81fd11ed56ff", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757811506000, "endTime": 31757811538200}, "additional": {"logType": "info", "children": [], "durationId": "0ea0ffa9-3c5d-47a6-adea-096c54977ccf", "parent": "0300f2c9-1f2a-47c9-8e2c-7fb383b8694a"}}, {"head": {"id": "9d060f12-b3ba-40e1-81ca-789b8ed6487b", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757811622000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92421796-197a-462b-af1f-f9ca14244e5b", "name": "Cache service initialization finished in 9 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757820046300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0e16d25-ee0c-4115-91a3-3d92c7380c77", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757791675900, "endTime": 31757820258400}, "additional": {"logType": "info", "children": [], "durationId": "24e48ea4-6bcb-4dde-9df2-3186c12596d0", "parent": "0300f2c9-1f2a-47c9-8e2c-7fb383b8694a"}}, {"head": {"id": "13e33900-ff27-4627-b91d-03e1fb2ed3a0", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757832754100, "endTime": 31757832774800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f15badc7-bb70-408f-8cec-b7005f66e300", "logId": "811d1536-445b-44a6-a30a-71799bac7291"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4613a506-21d6-4bef-b6e2-6aa0b153107a", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757832796900, "endTime": 31757843235800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f15badc7-bb70-408f-8cec-b7005f66e300", "logId": "8d0099c6-f4dc-4069-bb47-b752e5e11be2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "67777c70-d307-4a39-bcf8-48516416c796", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757843260100, "endTime": 31757991317400}, "additional": {"children": ["bf5c29b9-e3d6-461e-81eb-70e90b7eace3", "d143efa7-076a-4935-be2c-29d2aeb12f43", "8c79e659-ab62-4fbd-838f-1eb95a953623"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f15badc7-bb70-408f-8cec-b7005f66e300", "logId": "8296f1c6-fc36-46b7-85aa-26a0d281cc78"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b9858162-ee70-42ae-a53c-a32e4a72e457", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757991361800, "endTime": 31758033819000}, "additional": {"children": ["6b45b347-8584-4712-aa47-b217949beaa5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f15badc7-bb70-408f-8cec-b7005f66e300", "logId": "f99d299d-efec-4a9c-b709-0b51688b3dff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a3245e11-d737-45fa-973e-6e2cc46df9ef", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758033835700, "endTime": 31758062519400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f15badc7-bb70-408f-8cec-b7005f66e300", "logId": "e1d01282-d760-4f65-9be8-69584ab5013c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c93d40b-9237-4687-ba39-24a089a39bb6", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758065095300, "endTime": 31758089549300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f15badc7-bb70-408f-8cec-b7005f66e300", "logId": "6fda425b-a97a-421c-b717-980669c01bcd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ad84055-d959-4860-8520-e42485d66835", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758089580800, "endTime": 31758113672700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f15badc7-bb70-408f-8cec-b7005f66e300", "logId": "9f76c0bb-6055-47fd-a07f-2f9987eddff6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf799aad-3ecf-4a90-950f-5aabd282b922", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758113702600, "endTime": 31758113871300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f15badc7-bb70-408f-8cec-b7005f66e300", "logId": "b9bef23d-40a7-4996-8e5e-0c3c2a3e88e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "811d1536-445b-44a6-a30a-71799bac7291", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757832754100, "endTime": 31757832774800}, "additional": {"logType": "info", "children": [], "durationId": "13e33900-ff27-4627-b91d-03e1fb2ed3a0", "parent": "61ae8ce1-e3ad-401b-b66b-a9e45fe42353"}}, {"head": {"id": "8d0099c6-f4dc-4069-bb47-b752e5e11be2", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757832796900, "endTime": 31757843235800}, "additional": {"logType": "info", "children": [], "durationId": "4613a506-21d6-4bef-b6e2-6aa0b153107a", "parent": "61ae8ce1-e3ad-401b-b66b-a9e45fe42353"}}, {"head": {"id": "bf5c29b9-e3d6-461e-81eb-70e90b7eace3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757844733100, "endTime": 31757844763200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "67777c70-d307-4a39-bcf8-48516416c796", "logId": "b73436e9-0e29-4327-8b07-03abe6cae8a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b73436e9-0e29-4327-8b07-03abe6cae8a8", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757844733100, "endTime": 31757844763200}, "additional": {"logType": "info", "children": [], "durationId": "bf5c29b9-e3d6-461e-81eb-70e90b7eace3", "parent": "8296f1c6-fc36-46b7-85aa-26a0d281cc78"}}, {"head": {"id": "d143efa7-076a-4935-be2c-29d2aeb12f43", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757850291100, "endTime": 31757989589300}, "additional": {"children": ["e6fcd225-cd3f-4fdb-89e7-02474d96454b", "dc9f569c-0e52-4332-b65b-53b9c677875a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "67777c70-d307-4a39-bcf8-48516416c796", "logId": "60e50f47-8b67-4466-b0f5-47d67c60cddc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6fcd225-cd3f-4fdb-89e7-02474d96454b", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757850294500, "endTime": 31757859403100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d143efa7-076a-4935-be2c-29d2aeb12f43", "logId": "d420af96-f126-4d92-a5a5-364c0b6d4d9a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc9f569c-0e52-4332-b65b-53b9c677875a", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757859432400, "endTime": 31757989565700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d143efa7-076a-4935-be2c-29d2aeb12f43", "logId": "e7d15828-4a0e-48b7-b23a-9bfd2f40455d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "905758c5-a470-4775-b2f1-31b2fc941454", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757850303100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7461fb7-2941-4411-a8c9-f895235292af", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757859200000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d420af96-f126-4d92-a5a5-364c0b6d4d9a", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757850294500, "endTime": 31757859403100}, "additional": {"logType": "info", "children": [], "durationId": "e6fcd225-cd3f-4fdb-89e7-02474d96454b", "parent": "60e50f47-8b67-4466-b0f5-47d67c60cddc"}}, {"head": {"id": "76ccb98e-13f6-45fd-a9e2-0c48057b856a", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757859455000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "578885be-15c0-4d1a-bfa9-67c761c15a75", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757876566200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fa9d194-ec95-469b-ad7a-9a6d49d8263d", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757876759100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4da9ab4-2f1b-45dc-b95f-48d66e3a649e", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757876984200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "039e26d5-17ab-4ac8-aa5f-342f3ebde8b8", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757877160700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6262b24-1bed-4d06-8373-20dd047a14d7", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757882336300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e1584ed-75de-4651-94e5-9b7d8c10f9d5", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757890555100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "becd7357-6fcd-46f3-9839-50951cda0a12", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757908910400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cad098a0-a0b9-4297-87a0-9e85c3e5d572", "name": "Sdk init in 52 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757944544300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba1cad81-d6b7-4a12-b23d-53cd1812fd3d", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757944754400}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 55}, "markType": "other"}}, {"head": {"id": "c5b74f0d-9fd1-4f2e-982e-399c9f9a61ad", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757944776900}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 55}, "markType": "other"}}, {"head": {"id": "6c64123b-3c66-48c3-af9c-c912d3e0989a", "name": "Project task initialization takes 42 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757989082400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "773a6d70-5981-4a47-94e6-34f9a67af0d5", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757989319400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3255bc9a-b077-4d36-8636-8c0cef626a27", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757989413100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb0f5cb3-6500-49d5-8763-804c042d64dd", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757989480400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7d15828-4a0e-48b7-b23a-9bfd2f40455d", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757859432400, "endTime": 31757989565700}, "additional": {"logType": "info", "children": [], "durationId": "dc9f569c-0e52-4332-b65b-53b9c677875a", "parent": "60e50f47-8b67-4466-b0f5-47d67c60cddc"}}, {"head": {"id": "60e50f47-8b67-4466-b0f5-47d67c60cddc", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757850291100, "endTime": 31757989589300}, "additional": {"logType": "info", "children": ["d420af96-f126-4d92-a5a5-364c0b6d4d9a", "e7d15828-4a0e-48b7-b23a-9bfd2f40455d"], "durationId": "d143efa7-076a-4935-be2c-29d2aeb12f43", "parent": "8296f1c6-fc36-46b7-85aa-26a0d281cc78"}}, {"head": {"id": "8c79e659-ab62-4fbd-838f-1eb95a953623", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757991260000, "endTime": 31757991290400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "67777c70-d307-4a39-bcf8-48516416c796", "logId": "fb6426ba-e718-4d7c-9624-a6734c39051f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb6426ba-e718-4d7c-9624-a6734c39051f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757991260000, "endTime": 31757991290400}, "additional": {"logType": "info", "children": [], "durationId": "8c79e659-ab62-4fbd-838f-1eb95a953623", "parent": "8296f1c6-fc36-46b7-85aa-26a0d281cc78"}}, {"head": {"id": "8296f1c6-fc36-46b7-85aa-26a0d281cc78", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757843260100, "endTime": 31757991317400}, "additional": {"logType": "info", "children": ["b73436e9-0e29-4327-8b07-03abe6cae8a8", "60e50f47-8b67-4466-b0f5-47d67c60cddc", "fb6426ba-e718-4d7c-9624-a6734c39051f"], "durationId": "67777c70-d307-4a39-bcf8-48516416c796", "parent": "61ae8ce1-e3ad-401b-b66b-a9e45fe42353"}}, {"head": {"id": "6b45b347-8584-4712-aa47-b217949beaa5", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757993406100, "endTime": 31758033804200}, "additional": {"children": ["e346b70c-4731-4abd-8b4a-6ea079e4a3ad", "14da1c8c-e4f5-4a13-866d-e86ae5d1cf07", "2a8bbc1c-3509-43ad-ba1d-0ec05986980b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b9858162-ee70-42ae-a53c-a32e4a72e457", "logId": "0200686e-aaf9-4b67-8b25-32fa35e46d6b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e346b70c-4731-4abd-8b4a-6ea079e4a3ad", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758000498600, "endTime": 31758000527100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6b45b347-8584-4712-aa47-b217949beaa5", "logId": "a9fcb244-33db-4332-a92c-122c7e57f0b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9fcb244-33db-4332-a92c-122c7e57f0b3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758000498600, "endTime": 31758000527100}, "additional": {"logType": "info", "children": [], "durationId": "e346b70c-4731-4abd-8b4a-6ea079e4a3ad", "parent": "0200686e-aaf9-4b67-8b25-32fa35e46d6b"}}, {"head": {"id": "14da1c8c-e4f5-4a13-866d-e86ae5d1cf07", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758005132500, "endTime": 31758031241300}, "additional": {"children": ["537c3f6d-d7fc-4b11-8849-b5471bce00e1", "f033af9e-0567-4e06-b45c-cd87b00154ea"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6b45b347-8584-4712-aa47-b217949beaa5", "logId": "e5e3c624-bcc7-4c48-a3b9-91bbdce802cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "537c3f6d-d7fc-4b11-8849-b5471bce00e1", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758005135100, "endTime": 31758009073400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "14da1c8c-e4f5-4a13-866d-e86ae5d1cf07", "logId": "f243c923-772f-4987-b1b5-5fef84e99278"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f033af9e-0567-4e06-b45c-cd87b00154ea", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758009097900, "endTime": 31758031219100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "14da1c8c-e4f5-4a13-866d-e86ae5d1cf07", "logId": "11560df9-83ac-47d7-bce7-121272475d48"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24a8ed99-a1e6-467c-b46c-dbb2343b1ad5", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758005141700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a384adf7-ce0f-40e1-8d7e-1ee91fc75f88", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758008920600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f243c923-772f-4987-b1b5-5fef84e99278", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758005135100, "endTime": 31758009073400}, "additional": {"logType": "info", "children": [], "durationId": "537c3f6d-d7fc-4b11-8849-b5471bce00e1", "parent": "e5e3c624-bcc7-4c48-a3b9-91bbdce802cd"}}, {"head": {"id": "26e84016-7921-4595-8852-762847612ad3", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758009107300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73cb05d5-9b9b-4316-80b0-bdb3161e5d26", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758022897100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f927188e-cca9-4c3d-8375-962c19aa3316", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758023078200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8048b79e-7a11-4434-977a-3ec712546b3b", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758023379300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f06807e2-0a2a-4cb0-bfc3-a4fdce55fbe2", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758024545000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "041a10a5-35ac-4a38-84b6-9c64faceb375", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758024684500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33067eb7-7e38-416f-bcdb-459033435fd2", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758024764900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49d4eed7-2b0e-485a-95d3-eeb3649f5fff", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758024849000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddb06b1b-b8a2-4044-b413-01a68e0fd6e7", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758030777800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27d7bc53-3503-4a14-8a1f-522b913de05e", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758030989000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b8bbced-2db4-4dee-8354-e3c39f8e948a", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758031069600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a677d0dd-dae0-4b31-ba0f-7c80f07a9283", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758031153100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11560df9-83ac-47d7-bce7-121272475d48", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758009097900, "endTime": 31758031219100}, "additional": {"logType": "info", "children": [], "durationId": "f033af9e-0567-4e06-b45c-cd87b00154ea", "parent": "e5e3c624-bcc7-4c48-a3b9-91bbdce802cd"}}, {"head": {"id": "e5e3c624-bcc7-4c48-a3b9-91bbdce802cd", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758005132500, "endTime": 31758031241300}, "additional": {"logType": "info", "children": ["f243c923-772f-4987-b1b5-5fef84e99278", "11560df9-83ac-47d7-bce7-121272475d48"], "durationId": "14da1c8c-e4f5-4a13-866d-e86ae5d1cf07", "parent": "0200686e-aaf9-4b67-8b25-32fa35e46d6b"}}, {"head": {"id": "2a8bbc1c-3509-43ad-ba1d-0ec05986980b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758033762400, "endTime": 31758033782500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6b45b347-8584-4712-aa47-b217949beaa5", "logId": "83e2bf1d-e0d8-40d6-8297-776d863eb449"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "83e2bf1d-e0d8-40d6-8297-776d863eb449", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758033762400, "endTime": 31758033782500}, "additional": {"logType": "info", "children": [], "durationId": "2a8bbc1c-3509-43ad-ba1d-0ec05986980b", "parent": "0200686e-aaf9-4b67-8b25-32fa35e46d6b"}}, {"head": {"id": "0200686e-aaf9-4b67-8b25-32fa35e46d6b", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757993406100, "endTime": 31758033804200}, "additional": {"logType": "info", "children": ["a9fcb244-33db-4332-a92c-122c7e57f0b3", "e5e3c624-bcc7-4c48-a3b9-91bbdce802cd", "83e2bf1d-e0d8-40d6-8297-776d863eb449"], "durationId": "6b45b347-8584-4712-aa47-b217949beaa5", "parent": "f99d299d-efec-4a9c-b709-0b51688b3dff"}}, {"head": {"id": "f99d299d-efec-4a9c-b709-0b51688b3dff", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757991361800, "endTime": 31758033819000}, "additional": {"logType": "info", "children": ["0200686e-aaf9-4b67-8b25-32fa35e46d6b"], "durationId": "b9858162-ee70-42ae-a53c-a32e4a72e457", "parent": "61ae8ce1-e3ad-401b-b66b-a9e45fe42353"}}, {"head": {"id": "aac4b3c2-9a1b-43d2-8a02-a731c55ff89b", "name": "watch files: [\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\hvigorfile.ts',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758061937400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17892eeb-d779-4a92-be27-adf79388c331", "name": "hvigorfile, resolve hvigorfile dependencies in 29 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758062419500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1d01282-d760-4f65-9be8-69584ab5013c", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758033835700, "endTime": 31758062519400}, "additional": {"logType": "info", "children": [], "durationId": "a3245e11-d737-45fa-973e-6e2cc46df9ef", "parent": "61ae8ce1-e3ad-401b-b66b-a9e45fe42353"}}, {"head": {"id": "d1d35b9d-1ec4-48c3-8d0e-c02e22416b5f", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758064552000, "endTime": 31758065060100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f15badc7-bb70-408f-8cec-b7005f66e300", "logId": "4aada2c2-ce7a-4493-85e1-185606420ddb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17fbd3a2-9496-45bc-b04a-4b33b819e29d", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758064596400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4aada2c2-ce7a-4493-85e1-185606420ddb", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758064552000, "endTime": 31758065060100}, "additional": {"logType": "info", "children": [], "durationId": "d1d35b9d-1ec4-48c3-8d0e-c02e22416b5f", "parent": "61ae8ce1-e3ad-401b-b66b-a9e45fe42353"}}, {"head": {"id": "bfe229e9-e588-4aff-8b52-35b0952e9824", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758069317700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "813d0af0-2355-4c2b-9426-890f93d3eab0", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758087849900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fda425b-a97a-421c-b717-980669c01bcd", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758065095300, "endTime": 31758089549300}, "additional": {"logType": "info", "children": [], "durationId": "3c93d40b-9237-4687-ba39-24a089a39bb6", "parent": "61ae8ce1-e3ad-401b-b66b-a9e45fe42353"}}, {"head": {"id": "69a4121e-ac42-4c5b-9ac9-71712973dc27", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758089604300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "648933f6-94db-49cf-871e-9e848eda6eda", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758103543500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "707824a0-8bf5-4ca5-aad6-fe9f21d04f04", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758103715100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "873f155c-93d7-44ae-aa9a-50e0e28cd08f", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758104017300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2985e40e-d4c1-4f4a-987a-6efc5d4ab971", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758108650500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e731099-e20b-443a-83e8-398cbd25933d", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758108800900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f76c0bb-6055-47fd-a07f-2f9987eddff6", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758089580800, "endTime": 31758113672700}, "additional": {"logType": "info", "children": [], "durationId": "1ad84055-d959-4860-8520-e42485d66835", "parent": "61ae8ce1-e3ad-401b-b66b-a9e45fe42353"}}, {"head": {"id": "a8b10835-c904-44cc-bf90-81d7bffe9695", "name": "Configuration phase cost:281 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758113733100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9bef23d-40a7-4996-8e5e-0c3c2a3e88e0", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758113702600, "endTime": 31758113871300}, "additional": {"logType": "info", "children": [], "durationId": "bf799aad-3ecf-4a90-950f-5aabd282b922", "parent": "61ae8ce1-e3ad-401b-b66b-a9e45fe42353"}}, {"head": {"id": "61ae8ce1-e3ad-401b-b66b-a9e45fe42353", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757820344600, "endTime": 31758113889100}, "additional": {"logType": "info", "children": ["811d1536-445b-44a6-a30a-71799bac7291", "8d0099c6-f4dc-4069-bb47-b752e5e11be2", "8296f1c6-fc36-46b7-85aa-26a0d281cc78", "f99d299d-efec-4a9c-b709-0b51688b3dff", "e1d01282-d760-4f65-9be8-69584ab5013c", "6fda425b-a97a-421c-b717-980669c01bcd", "9f76c0bb-6055-47fd-a07f-2f9987eddff6", "b9bef23d-40a7-4996-8e5e-0c3c2a3e88e0", "4aada2c2-ce7a-4493-85e1-185606420ddb"], "durationId": "f15badc7-bb70-408f-8cec-b7005f66e300", "parent": "0300f2c9-1f2a-47c9-8e2c-7fb383b8694a"}}, {"head": {"id": "9e4484b8-4ff9-43b2-8ca7-0a6d7d58f123", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758116182900, "endTime": 31758116201700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "efb9b767-24b9-4fde-821a-72d70c0e7acf", "logId": "15a993ac-5173-4f85-9d0b-75960a142463"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15a993ac-5173-4f85-9d0b-75960a142463", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758116182900, "endTime": 31758116201700}, "additional": {"logType": "info", "children": [], "durationId": "9e4484b8-4ff9-43b2-8ca7-0a6d7d58f123", "parent": "0300f2c9-1f2a-47c9-8e2c-7fb383b8694a"}}, {"head": {"id": "3313e49e-3ef6-48ff-84ca-4ecd71ea4355", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758113920600, "endTime": 31758116215200}, "additional": {"logType": "info", "children": [], "durationId": "c2a98888-00cb-412c-8bec-d87df6d0d17e", "parent": "0300f2c9-1f2a-47c9-8e2c-7fb383b8694a"}}, {"head": {"id": "9bfaed95-d47e-448c-8532-56ef1814130d", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758116221200, "endTime": 31758116222900}, "additional": {"logType": "info", "children": [], "durationId": "47f91d58-6ada-44e5-97d1-6cbc8a05ccb7", "parent": "0300f2c9-1f2a-47c9-8e2c-7fb383b8694a"}}, {"head": {"id": "0300f2c9-1f2a-47c9-8e2c-7fb383b8694a", "name": "init", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757791673400, "endTime": 31758116227700}, "additional": {"logType": "info", "children": ["f0e16d25-ee0c-4115-91a3-3d92c7380c77", "61ae8ce1-e3ad-401b-b66b-a9e45fe42353", "3313e49e-3ef6-48ff-84ca-4ecd71ea4355", "9bfaed95-d47e-448c-8532-56ef1814130d", "3ae959f4-3aa2-4620-a18b-f0020ff10c3b", "1266cc52-a9b6-423e-8744-81fd11ed56ff", "15a993ac-5173-4f85-9d0b-75960a142463"], "durationId": "efb9b767-24b9-4fde-821a-72d70c0e7acf"}}, {"head": {"id": "24385240-0d59-4233-8d94-61d0e9ad2353", "name": "Configuration task cost before running: 333 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758116422000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae1e5070-4bd4-4632-a8e6-c07e4750500f", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758125182400, "endTime": 31758144874900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "d4de8359-7470-4929-8fa0-28b34593c566", "logId": "c63a29a7-7fb4-4c0e-ba6c-9749e050e17b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4de8359-7470-4929-8fa0-28b34593c566", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758118752200}, "additional": {"logType": "detail", "children": [], "durationId": "ae1e5070-4bd4-4632-a8e6-c07e4750500f"}}, {"head": {"id": "7c4b4ba9-1b67-4884-9648-01089e8b632a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758119714400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbfebccd-d325-4137-9b7f-950681778f9e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758119847600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5228ddde-b885-437f-b249-4e4bf1320a57", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758125200700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "298180d2-57b3-4452-96ff-ac439a34051f", "name": "Incremental task entry:default@PreBuild pre-execution cost: 15 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758144399300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32e1499d-9e69-4cc0-bde0-4be5a02906ea", "name": "entry : default@PreBuild cost memory 0.283294677734375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758144706900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c63a29a7-7fb4-4c0e-ba6c-9749e050e17b", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758125182400, "endTime": 31758144874900}, "additional": {"logType": "info", "children": [], "durationId": "ae1e5070-4bd4-4632-a8e6-c07e4750500f"}}, {"head": {"id": "0dd5129f-fa07-40db-a134-73177171d4ff", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758157775700, "endTime": 31758163065000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "63529ad5-1df5-4721-a57c-3294efc71830", "logId": "f0916e17-501d-4230-be6c-51bb60d3d330"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "63529ad5-1df5-4721-a57c-3294efc71830", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758155066500}, "additional": {"logType": "detail", "children": [], "durationId": "0dd5129f-fa07-40db-a134-73177171d4ff"}}, {"head": {"id": "7776f0e7-4545-4a5a-bd06-0b65cad6217a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758156155500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82be3f85-d83c-4e06-88d4-275acf8d828c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758156302800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68967ea4-9a1b-4b0b-8485-152e7c78330b", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758157788000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b97dd7b-2f88-431a-b31f-7c84db6c5ea9", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758162698400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70634b6e-2d07-4837-a550-89385dd48f8f", "name": "entry : default@MergeProfile cost memory 0.10773468017578125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758162867600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0916e17-501d-4230-be6c-51bb60d3d330", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758157775700, "endTime": 31758163065000}, "additional": {"logType": "info", "children": [], "durationId": "0dd5129f-fa07-40db-a134-73177171d4ff"}}, {"head": {"id": "ed80603f-a713-45fd-9778-28b939c46221", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758170678500, "endTime": 31758175804800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a1aa9bc7-89c1-4e56-b368-bd5079ccbded", "logId": "1daef679-38e4-48b8-baf0-e78f8d95558c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1aa9bc7-89c1-4e56-b368-bd5079ccbded", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758166931100}, "additional": {"logType": "detail", "children": [], "durationId": "ed80603f-a713-45fd-9778-28b939c46221"}}, {"head": {"id": "3b7ce67b-71a5-4f3b-89e6-ab834d7f21c2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758168169800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "228119bc-4c2b-4e00-bffc-e7c84deb4dec", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758168460700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37098015-1f22-4d4b-a303-455bbd374258", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758170695300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39ddb2dc-f727-4ef5-bfe5-8b5115cbda79", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758172803300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6de0cb23-fb5b-43ce-b767-430d7afb1b08", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758175549000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5922d435-cb91-41a8-9602-625654ae2f37", "name": "entry : default@CreateBuildProfile cost memory 0.10219573974609375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758175696900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1daef679-38e4-48b8-baf0-e78f8d95558c", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758170678500, "endTime": 31758175804800}, "additional": {"logType": "info", "children": [], "durationId": "ed80603f-a713-45fd-9778-28b939c46221"}}, {"head": {"id": "20bcf415-aed2-4355-a5ed-e6683232aaeb", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758182600600, "endTime": 31758183209400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "6f781d59-bd1d-4648-9a3d-ed188017b3bb", "logId": "fa815cfa-473e-4c5d-a576-74ee298cc7a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f781d59-bd1d-4648-9a3d-ed188017b3bb", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758179347800}, "additional": {"logType": "detail", "children": [], "durationId": "20bcf415-aed2-4355-a5ed-e6683232aaeb"}}, {"head": {"id": "98c254f7-8cdc-4efa-acee-da7c0e070383", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758180424200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a988b719-b408-477b-9ccf-de9aa7eb9cfb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758180602800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ecba7ae-3ae9-40a2-8290-07b5a8492b62", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758182615600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50921f94-ebff-400c-806c-60fc1ef39813", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758182796500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "710e9dc9-3692-46f9-a608-98eaa425b637", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758182884300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb564375-aa99-4141-950b-7b5ec8d31cf1", "name": "entry : default@PreCheckSyscap cost memory 0.03680419921875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758182990600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70f28b6a-2b4f-4bb7-bbb4-00ca2a162d60", "name": "runTaskFromQueue task cost before running: 399 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758183135700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa815cfa-473e-4c5d-a576-74ee298cc7a0", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758182600600, "endTime": 31758183209400, "totalTime": 502000}, "additional": {"logType": "info", "children": [], "durationId": "20bcf415-aed2-4355-a5ed-e6683232aaeb"}}, {"head": {"id": "ee338dd1-96ec-43dc-902b-7bf33020fd50", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758210228700, "endTime": 31758211994200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e6a3963a-00a2-410a-9d6b-84b16b9a56f7", "logId": "0cce0f1b-5eb3-4313-83c7-a0aca3629a84"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6a3963a-00a2-410a-9d6b-84b16b9a56f7", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758188142500}, "additional": {"logType": "detail", "children": [], "durationId": "ee338dd1-96ec-43dc-902b-7bf33020fd50"}}, {"head": {"id": "7354e66d-0d5e-4d6e-ada3-2fb3379024bf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758191063100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b9a4b41-5a85-47e9-8108-d994e5308ccf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758191287900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46acacaf-7fc8-4e27-9080-36dae647e4cb", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758210247400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cfc069b-5313-44cf-8065-5a6e94554865", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758210541100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59fc3b7e-99e9-4c3e-b696-9fe3caa8fe7c", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758211743400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84deac6d-af89-42f9-83bb-b4e307aa536a", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06439971923828125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758211893100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cce0f1b-5eb3-4313-83c7-a0aca3629a84", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758210228700, "endTime": 31758211994200}, "additional": {"logType": "info", "children": [], "durationId": "ee338dd1-96ec-43dc-902b-7bf33020fd50"}}, {"head": {"id": "63c4ef94-516d-4159-836b-ab877ca59a98", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758218970000, "endTime": 31758223071900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "d4ebe7f9-e078-40ae-831b-38591c163da1", "logId": "438bb685-6ef9-4c11-aa46-6519ef20a6bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4ebe7f9-e078-40ae-831b-38591c163da1", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758214749600}, "additional": {"logType": "detail", "children": [], "durationId": "63c4ef94-516d-4159-836b-ab877ca59a98"}}, {"head": {"id": "9e29d5d3-0c67-4018-a588-838656967274", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758215695800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7abc0b2c-5d67-4551-99bc-f128910fd51b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758215827300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25b663c4-2df4-48c3-8d26-57cde34c83af", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758218991100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d9bc37d-36ef-421d-bfda-9eb495231e2a", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758222552300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "070e594a-4fb2-44a9-a86b-8bdefc571d37", "name": "entry : default@ProcessProfile cost memory 0.055419921875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758222856300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "438bb685-6ef9-4c11-aa46-6519ef20a6bf", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758218970000, "endTime": 31758223071900}, "additional": {"logType": "info", "children": [], "durationId": "63c4ef94-516d-4159-836b-ab877ca59a98"}}, {"head": {"id": "4492fdee-fda2-48f2-90e4-6d1e07190998", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758232544800, "endTime": 31758246562900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b375cf30-edb7-417f-9275-33c9490aae47", "logId": "e17ec45c-741b-47d8-b008-a115de093595"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b375cf30-edb7-417f-9275-33c9490aae47", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758226659200}, "additional": {"logType": "detail", "children": [], "durationId": "4492fdee-fda2-48f2-90e4-6d1e07190998"}}, {"head": {"id": "ebb11f31-ee6a-4c3b-845b-505b0ffa8299", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758228097900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f9125bf-759a-4a30-8271-be8e65acdf09", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758228249700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4372a271-b189-46b4-a4ca-d1ea10cdf2b3", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758232568800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0447b8fa-c691-411c-9875-833ad75ab66e", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758246256100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5aec8c00-a8da-4d16-ba7d-b2973ceec338", "name": "entry : default@ProcessRouterMap cost memory 0.19419097900390625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758246449700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e17ec45c-741b-47d8-b008-a115de093595", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758232544800, "endTime": 31758246562900}, "additional": {"logType": "info", "children": [], "durationId": "4492fdee-fda2-48f2-90e4-6d1e07190998"}}, {"head": {"id": "bc80b780-b1bf-4d5c-b3d2-be3aa4b554db", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758258470500, "endTime": 31758263257800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "2057a495-6fa0-47fc-8fb0-343937eac678", "logId": "0d620e18-9833-49ed-815d-4c485d5556b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2057a495-6fa0-47fc-8fb0-343937eac678", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758252005600}, "additional": {"logType": "detail", "children": [], "durationId": "bc80b780-b1bf-4d5c-b3d2-be3aa4b554db"}}, {"head": {"id": "bcdac985-663a-465f-b877-1f0d2b8752c8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758253011800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abc520ea-f8b9-4e83-a261-a3a6210d6c7a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758253159100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca048181-e60a-49e2-b4b3-6f0d8c95c0b9", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758254885500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fca7b362-10f9-4ad9-baa0-2d69378fb716", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758260546300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e08b908e-c880-46da-9e14-b581c0c8d497", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758260780800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a6eddbc-b91a-40e9-bf8c-fc5f5a5c2df8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758260869000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "583a7dc5-78c8-4e39-80a8-55d555c86a26", "name": "entry : default@PreviewProcessResource cost memory 0.0698699951171875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758260989000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12141a5b-eb37-4ec7-b27c-f58d5d6a9eea", "name": "runTaskFromQueue task cost before running: 479 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758263130800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d620e18-9833-49ed-815d-4c485d5556b4", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758258470500, "endTime": 31758263257800, "totalTime": 2630600}, "additional": {"logType": "info", "children": [], "durationId": "bc80b780-b1bf-4d5c-b3d2-be3aa4b554db"}}, {"head": {"id": "71ab77db-9585-439b-a7ed-7657dd0ebc70", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758275192300, "endTime": 31758323863000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "13a5b32c-ad75-4a9c-88da-7a2e430f3de2", "logId": "45b5c9ff-11f3-40e2-8a56-79fb62c9db8c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13a5b32c-ad75-4a9c-88da-7a2e430f3de2", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758267909700}, "additional": {"logType": "detail", "children": [], "durationId": "71ab77db-9585-439b-a7ed-7657dd0ebc70"}}, {"head": {"id": "d80c5c63-b41d-40a8-8676-7247b7424791", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758268790900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77f29384-fcc0-449f-8629-08eddd3339e0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758268909700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e36d8d52-bf6f-40fd-b2b6-c3d74119fcbd", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758275210200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1985c61-ee42-4e35-be4e-ac6cf6861614", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 31 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758323295500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b208cb4-e0a0-4cda-92bd-5b5ad9fdbe42", "name": "entry : default@GenerateLoaderJson cost memory 0.7455520629882812", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758323593200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45b5c9ff-11f3-40e2-8a56-79fb62c9db8c", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758275192300, "endTime": 31758323863000}, "additional": {"logType": "info", "children": [], "durationId": "71ab77db-9585-439b-a7ed-7657dd0ebc70"}}, {"head": {"id": "38d99ddf-af27-45d2-b4c2-13bd7ab4f172", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758346675100, "endTime": 31758656595900}, "additional": {"children": ["83efffe7-c9f6-4d8e-8e79-0821ebee0d78", "6601adbf-8955-4b60-9624-214c9e78163e", "395558e3-c392-4765-84f2-db6b6377d6cd", "3ea195ba-885c-4ceb-94ad-600684e35d68"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "c8755960-0e2e-4365-b146-d309a803a86d", "logId": "f26c637d-de18-4160-b6cb-7a3003bbf95f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8755960-0e2e-4365-b146-d309a803a86d", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758338617900}, "additional": {"logType": "detail", "children": [], "durationId": "38d99ddf-af27-45d2-b4c2-13bd7ab4f172"}}, {"head": {"id": "42a76eed-ec3f-4421-b8af-707d460cd471", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758339419700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d755a87d-ffc7-4e77-8cdc-4661e483c8d8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758339535200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "710819b3-938b-4734-a776-6fa5fe0ff3ae", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758341547200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b69ad84c-9b49-413d-ae19-d0c1e8133a47", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758346714800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2a09996-da84-4c1b-b72e-dadf43306714", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758389209700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc18ead9-e344-4995-99f0-6e40d382199e", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 42 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758389732300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83efffe7-c9f6-4d8e-8e79-0821ebee0d78", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758394204100, "endTime": 31758445292800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "38d99ddf-af27-45d2-b4c2-13bd7ab4f172", "logId": "41844d57-efcf-43ab-a642-119a02fc12f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41844d57-efcf-43ab-a642-119a02fc12f6", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758394204100, "endTime": 31758445292800}, "additional": {"logType": "info", "children": [], "durationId": "83efffe7-c9f6-4d8e-8e79-0821ebee0d78", "parent": "f26c637d-de18-4160-b6cb-7a3003bbf95f"}}, {"head": {"id": "e66cc4c4-5bd1-4c7a-a0c1-1b72775a2f39", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758445434800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6601adbf-8955-4b60-9624-214c9e78163e", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758446846900, "endTime": 31758506177700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "38d99ddf-af27-45d2-b4c2-13bd7ab4f172", "logId": "e96f0187-666b-4e8f-affa-7937a51fb2ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "57c31fc7-6133-4896-8320-14286d8825c8", "name": "current process  memoryUsage: {\n  rss: 108367872,\n  heapTotal: 127754240,\n  heapUsed: 111820616,\n  external: 3117179,\n  arrayBuffers: 111080\n} os memoryUsage :12.701457977294922", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758448316000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3da015e-4913-484f-8fef-0cd6f98eded4", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758503125400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e96f0187-666b-4e8f-affa-7937a51fb2ec", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758446846900, "endTime": 31758506177700}, "additional": {"logType": "info", "children": [], "durationId": "6601adbf-8955-4b60-9624-214c9e78163e", "parent": "f26c637d-de18-4160-b6cb-7a3003bbf95f"}}, {"head": {"id": "c1da4935-40ad-4ce8-850c-e06b9a4b00e0", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758506351000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "395558e3-c392-4765-84f2-db6b6377d6cd", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758508647700, "endTime": 31758567726400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "38d99ddf-af27-45d2-b4c2-13bd7ab4f172", "logId": "c889ac3e-7911-44be-bdfb-d1a92373c44d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9449d704-6e15-45c0-bebd-bd8dd1114827", "name": "current process  memoryUsage: {\n  rss: 108486656,\n  heapTotal: 127754240,\n  heapUsed: 112089064,\n  external: 3117305,\n  arrayBuffers: 111221\n} os memoryUsage :12.704608917236328", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758511432900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df44c63f-1253-45e5-9386-44b447208eb8", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758563584700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c889ac3e-7911-44be-bdfb-d1a92373c44d", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758508647700, "endTime": 31758567726400}, "additional": {"logType": "info", "children": [], "durationId": "395558e3-c392-4765-84f2-db6b6377d6cd", "parent": "f26c637d-de18-4160-b6cb-7a3003bbf95f"}}, {"head": {"id": "7d92cde3-0d9e-47e7-b616-cf19bd762d39", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758567986500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ea195ba-885c-4ceb-94ad-600684e35d68", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758569649100, "endTime": 31758653653000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "38d99ddf-af27-45d2-b4c2-13bd7ab4f172", "logId": "89e35b60-10da-476f-a989-5cecadaccaf2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7623251e-b2ca-4eb7-834f-c2604b955055", "name": "current process  memoryUsage: {\n  rss: 108490752,\n  heapTotal: 127754240,\n  heapUsed: 112374904,\n  external: 3117431,\n  arrayBuffers: 112231\n} os memoryUsage :12.722976684570312", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758573035500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea42caf5-bd42-434e-b382-03bd7b79e93f", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758649336500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89e35b60-10da-476f-a989-5cecadaccaf2", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758569649100, "endTime": 31758653653000}, "additional": {"logType": "info", "children": [], "durationId": "3ea195ba-885c-4ceb-94ad-600684e35d68", "parent": "f26c637d-de18-4160-b6cb-7a3003bbf95f"}}, {"head": {"id": "2bd56433-85d8-4e34-8ffa-1fa1bf0958b7", "name": "entry : default@PreviewCompileResource cost memory -0.0301971435546875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758655879000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5bc5755-58b0-4dc4-b2a7-1d047addcb14", "name": "runTaskFromQueue task cost before running: 873 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758656356900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f26c637d-de18-4160-b6cb-7a3003bbf95f", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758346675100, "endTime": 31758656595900, "totalTime": 309564900}, "additional": {"logType": "info", "children": ["41844d57-efcf-43ab-a642-119a02fc12f6", "e96f0187-666b-4e8f-affa-7937a51fb2ec", "c889ac3e-7911-44be-bdfb-d1a92373c44d", "89e35b60-10da-476f-a989-5cecadaccaf2"], "durationId": "38d99ddf-af27-45d2-b4c2-13bd7ab4f172"}}, {"head": {"id": "fe62454a-6039-4773-a133-b43a8d6d16d7", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758666166200, "endTime": 31758666908400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "b4256eff-44d0-4f9c-a0c8-a379d55f9e44", "logId": "57ed7217-47ae-4b6b-a19b-c42e3f277b8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4256eff-44d0-4f9c-a0c8-a379d55f9e44", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758663513000}, "additional": {"logType": "detail", "children": [], "durationId": "fe62454a-6039-4773-a133-b43a8d6d16d7"}}, {"head": {"id": "d8e2b489-ac9c-4526-8dc7-624b23539cc7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758665721700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f5b239c-9d94-4150-aceb-da6b0c0b6806", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758665949500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9319c5d7-9529-4961-bc7b-b50798ab7d56", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758666182200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e34dc1d0-d0c2-473d-ab0c-3e3df95cd96a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758666381000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2068aa8-741a-4687-8b70-f551aa6ff565", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758666550600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb56a24d-2e35-407b-b613-ae7eb36f765d", "name": "entry : default@PreviewHookCompileResource cost memory 0.0513153076171875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758666670200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8369b2f-1684-46e2-b4b9-2cb02f2dd781", "name": "runTaskFromQueue task cost before running: 883 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758666813200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57ed7217-47ae-4b6b-a19b-c42e3f277b8d", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758666166200, "endTime": 31758666908400, "totalTime": 621000}, "additional": {"logType": "info", "children": [], "durationId": "fe62454a-6039-4773-a133-b43a8d6d16d7"}}, {"head": {"id": "b0f550aa-9f63-4bbe-9d01-b44252d672ef", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758675212100, "endTime": 31758694527800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "5fa4040d-630a-46bf-b7cd-38b1e16a3e7a", "logId": "c90346e0-7d11-41a5-be51-fea032493dd1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5fa4040d-630a-46bf-b7cd-38b1e16a3e7a", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758671923300}, "additional": {"logType": "detail", "children": [], "durationId": "b0f550aa-9f63-4bbe-9d01-b44252d672ef"}}, {"head": {"id": "1c6610f0-55e1-4c7e-b15f-40de77643e78", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758673373500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dd34679-60ed-40d9-93f4-497f2f992c45", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758673590400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9597981-f9a2-4d3e-9186-a127b35d5aed", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758675235400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f6e0f87-bd21-45b5-8159-58c3616ce65d", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758678338600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ecce59a-7407-4417-8dcc-8e1446240245", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758678542900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5c414a9-85a0-4735-9077-9813f6eefaab", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758678659300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c97d2cc-53ec-46f2-9d98-6a31778db41c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758678727200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "993b0d10-f401-4923-bc2f-97cf9e491783", "name": "entry : default@CopyPreviewProfile cost memory -1.5436019897460938", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758694178600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76396134-46be-40ce-a0a9-ed8078c1d26b", "name": "runTaskFromQueue task cost before running: 911 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758694423300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c90346e0-7d11-41a5-be51-fea032493dd1", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758675212100, "endTime": 31758694527800, "totalTime": 19173700}, "additional": {"logType": "info", "children": [], "durationId": "b0f550aa-9f63-4bbe-9d01-b44252d672ef"}}, {"head": {"id": "f9be2601-8a51-4801-9f36-9b79ef33f61e", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758704954600, "endTime": 31758705925700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "14dfa6ef-8b1f-46ce-9f9c-e0fcceabb4f8", "logId": "34181fb4-c3d2-495a-9c16-f0c92c674318"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14dfa6ef-8b1f-46ce-9f9c-e0fcceabb4f8", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758700071100}, "additional": {"logType": "detail", "children": [], "durationId": "f9be2601-8a51-4801-9f36-9b79ef33f61e"}}, {"head": {"id": "81de5b87-f03b-49ce-b80f-1c3b40e4a396", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758702065400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b79227e2-f21a-4eb5-8a70-9be14ab6719b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758702307600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "553f8c75-eeb3-4676-8a25-f9bcfcaf4931", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758704977200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46517e6a-fa6b-4326-9fdb-ef208a1256b1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758705267900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd73b312-7cbd-44b4-a5f2-92d1dcc88287", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758705428200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "869b55a9-2b3c-4da4-bed4-6bb19a636d86", "name": "entry : default@ReplacePreviewerPage cost memory 0.037872314453125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758705620500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23188e9f-8ef0-44c4-984e-57da80e944b4", "name": "runTaskFromQueue task cost before running: 922 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758705800600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34181fb4-c3d2-495a-9c16-f0c92c674318", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758704954600, "endTime": 31758705925700, "totalTime": 814200}, "additional": {"logType": "info", "children": [], "durationId": "f9be2601-8a51-4801-9f36-9b79ef33f61e"}}, {"head": {"id": "3e0f9b27-967d-471f-abd7-a374fa7d23b8", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758711388500, "endTime": 31758712250900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "b78a4759-c85e-4ef0-99fb-1d62b3dfcbff", "logId": "001c652a-08ea-4e6d-90e8-b2b807d7618e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b78a4759-c85e-4ef0-99fb-1d62b3dfcbff", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758711239000}, "additional": {"logType": "detail", "children": [], "durationId": "3e0f9b27-967d-471f-abd7-a374fa7d23b8"}}, {"head": {"id": "a2560a5a-93ae-413f-aca1-2699a980b1dd", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758711413200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50827fee-89b7-49ff-aa83-652d9da01984", "name": "entry : buildPreviewerResource cost memory 0.0116424560546875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758711765600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7e2cd59-a5bb-4345-8bea-07271db84c58", "name": "runTaskFromQueue task cost before running: 928 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758712042000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "001c652a-08ea-4e6d-90e8-b2b807d7618e", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758711388500, "endTime": 31758712250900, "totalTime": 597800}, "additional": {"logType": "info", "children": [], "durationId": "3e0f9b27-967d-471f-abd7-a374fa7d23b8"}}, {"head": {"id": "90826af8-cb4a-494d-b7ef-6e6bf2db2de4", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758723329000, "endTime": 31758733518700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "ea8f7b26-7cc4-4f8d-b9a6-ffb0fe9eccac", "logId": "ea1fd525-3772-4c35-87e7-3bb54f310ea1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea8f7b26-7cc4-4f8d-b9a6-ffb0fe9eccac", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758718411500}, "additional": {"logType": "detail", "children": [], "durationId": "90826af8-cb4a-494d-b7ef-6e6bf2db2de4"}}, {"head": {"id": "eb04af7e-0f80-4a8f-b19a-ebc06eb19425", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758720712000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d053a706-e728-4132-b8d6-4f65b5826aec", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758720962800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35dddcc6-54ed-46f8-ae11-821f0017aa01", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758723345000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2475ff36-a862-4dab-b435-05bb7277baee", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758729113300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e239ad12-672f-4a4a-be7f-9c5affd24889", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758729364400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "540535ef-6aff-4e29-bb97-42fb9e6c4358", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758729575400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b6bdacb-d2aa-4e51-b175-bc6f0c10c5e0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758729686900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e8f37ed-653d-4fd3-ac3c-f48b91e9f84b", "name": "entry : default@PreviewUpdateAssets cost memory 0.14072418212890625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758733210900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc0095e6-b41c-44a4-a9e8-a4bdad3c0620", "name": "runTaskFromQueue task cost before running: 950 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758733416700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea1fd525-3772-4c35-87e7-3bb54f310ea1", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758723329000, "endTime": 31758733518700, "totalTime": 10051400}, "additional": {"logType": "info", "children": [], "durationId": "90826af8-cb4a-494d-b7ef-6e6bf2db2de4"}}, {"head": {"id": "a5b4b407-901e-4bfd-bfa4-d7a84de9bb82", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758757558600, "endTime": 31769303377100}, "additional": {"children": ["5f3e2a3b-38a0-4ea3-bfac-0136199f364d"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "8cc6b2f5-3f6a-4291-92e9-1b24af41a9a9", "logId": "0b8fb638-7d27-4093-b713-092f62d044dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8cc6b2f5-3f6a-4291-92e9-1b24af41a9a9", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758739619500}, "additional": {"logType": "detail", "children": [], "durationId": "a5b4b407-901e-4bfd-bfa4-d7a84de9bb82"}}, {"head": {"id": "3fbfc20c-23e1-4b01-97a9-b3bf622e8f76", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758740657100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e072303-7f78-4b22-a47f-89b07e295707", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758740860400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5ef385d-977f-4f98-b0e2-b834a316e362", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758757583300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "317c7f13-88e9-4643-be50-f4223184efa0", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758799148900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09632be9-47e0-40c0-b66a-9359e33bf4d9", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 26 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758799509300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f3e2a3b-38a0-4ea3-bfac-0136199f364d", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker20", "startTime": 31758851646000, "endTime": 31769300613600}, "additional": {"children": ["8ebf6b34-163f-445e-b963-f90aad637467", "32ae8cdc-3d63-4797-9252-2ba74606ce1e", "d29946bb-8c9a-4b8b-8513-f40a791b23bf", "83560385-0af4-42c8-a544-c5a4b0d20443", "e4b6c562-76aa-4a29-acd7-ccc389be4fa5", "201447bd-42c7-4ddc-af0f-56b4b735884d"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "a5b4b407-901e-4bfd-bfa4-d7a84de9bb82", "logId": "3e1b3d0a-d2bc-4dd4-9f90-17175d283f1e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a134d32-fe39-4d84-94e3-0063d6542468", "name": "entry : default@PreviewArkTS cost memory -0.5533370971679688", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758858515600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61af625b-b8a1-4bdb-88d9-783d42143fe0", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31764140135500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ebf6b34-163f-445e-b963-f90aad637467", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker20", "startTime": 31764141239100, "endTime": 31764141258500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5f3e2a3b-38a0-4ea3-bfac-0136199f364d", "logId": "aa22afd9-97bf-4d95-a1da-62ac3aaefcb5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa22afd9-97bf-4d95-a1da-62ac3aaefcb5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31764141239100, "endTime": 31764141258500}, "additional": {"logType": "info", "children": [], "durationId": "8ebf6b34-163f-445e-b963-f90aad637467", "parent": "3e1b3d0a-d2bc-4dd4-9f90-17175d283f1e"}}, {"head": {"id": "cc38d3fa-e4e3-431a-801a-09e3779de41d", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769299625800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32ae8cdc-3d63-4797-9252-2ba74606ce1e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker20", "startTime": 31769300528200, "endTime": 31769300543800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5f3e2a3b-38a0-4ea3-bfac-0136199f364d", "logId": "70913145-52e7-44e8-92d2-d99e934653f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "70913145-52e7-44e8-92d2-d99e934653f1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769300528200, "endTime": 31769300543800}, "additional": {"logType": "info", "children": [], "durationId": "32ae8cdc-3d63-4797-9252-2ba74606ce1e", "parent": "3e1b3d0a-d2bc-4dd4-9f90-17175d283f1e"}}, {"head": {"id": "3e1b3d0a-d2bc-4dd4-9f90-17175d283f1e", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Worker20", "startTime": 31758851646000, "endTime": 31769300613600}, "additional": {"logType": "info", "children": ["aa22afd9-97bf-4d95-a1da-62ac3aaefcb5", "70913145-52e7-44e8-92d2-d99e934653f1", "8ba8d6cc-2af8-4e36-af85-5e0acdfa136c", "69b096e4-752d-4b42-b64a-9e0783a4c7c4", "3a521ff1-73bc-408a-8d2a-e3633e715b55", "4d598d33-c566-490a-a7a8-9a8810034b02"], "durationId": "5f3e2a3b-38a0-4ea3-bfac-0136199f364d", "parent": "0b8fb638-7d27-4093-b713-092f62d044dd"}}, {"head": {"id": "d29946bb-8c9a-4b8b-8513-f40a791b23bf", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker20", "startTime": 31762635929200, "endTime": 31764070979200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5f3e2a3b-38a0-4ea3-bfac-0136199f364d", "logId": "8ba8d6cc-2af8-4e36-af85-5e0acdfa136c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ba8d6cc-2af8-4e36-af85-5e0acdfa136c", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31762635929200, "endTime": 31764070979200}, "additional": {"logType": "info", "children": [], "durationId": "d29946bb-8c9a-4b8b-8513-f40a791b23bf", "parent": "3e1b3d0a-d2bc-4dd4-9f90-17175d283f1e"}}, {"head": {"id": "83560385-0af4-42c8-a544-c5a4b0d20443", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker20", "startTime": 31764071149300, "endTime": 31764108538300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5f3e2a3b-38a0-4ea3-bfac-0136199f364d", "logId": "69b096e4-752d-4b42-b64a-9e0783a4c7c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69b096e4-752d-4b42-b64a-9e0783a4c7c4", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31764071149300, "endTime": 31764108538300}, "additional": {"logType": "info", "children": [], "durationId": "83560385-0af4-42c8-a544-c5a4b0d20443", "parent": "3e1b3d0a-d2bc-4dd4-9f90-17175d283f1e"}}, {"head": {"id": "e4b6c562-76aa-4a29-acd7-ccc389be4fa5", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker20", "startTime": 31764108850200, "endTime": 31764109038300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5f3e2a3b-38a0-4ea3-bfac-0136199f364d", "logId": "3a521ff1-73bc-408a-8d2a-e3633e715b55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a521ff1-73bc-408a-8d2a-e3633e715b55", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31764108850200, "endTime": 31764109038300}, "additional": {"logType": "info", "children": [], "durationId": "e4b6c562-76aa-4a29-acd7-ccc389be4fa5", "parent": "3e1b3d0a-d2bc-4dd4-9f90-17175d283f1e"}}, {"head": {"id": "201447bd-42c7-4ddc-af0f-56b4b735884d", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker20", "startTime": 31764109127500, "endTime": 31769299603700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5f3e2a3b-38a0-4ea3-bfac-0136199f364d", "logId": "4d598d33-c566-490a-a7a8-9a8810034b02"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4d598d33-c566-490a-a7a8-9a8810034b02", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31764109127500, "endTime": 31769299603700}, "additional": {"logType": "info", "children": [], "durationId": "201447bd-42c7-4ddc-af0f-56b4b735884d", "parent": "3e1b3d0a-d2bc-4dd4-9f90-17175d283f1e"}}, {"head": {"id": "0b8fb638-7d27-4093-b713-092f62d044dd", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31758757558600, "endTime": 31769303377100, "totalTime": 10545810600}, "additional": {"logType": "info", "children": ["3e1b3d0a-d2bc-4dd4-9f90-17175d283f1e"], "durationId": "a5b4b407-901e-4bfd-bfa4-d7a84de9bb82"}}, {"head": {"id": "05f7d6f5-8d81-47ef-b4f9-3edf5edb60e7", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769307106500, "endTime": 31769307293500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "9a63c091-0a6c-48fd-a40f-9c33e0b43375", "logId": "98d45374-65d3-48be-a07a-7710cc7eecbe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a63c091-0a6c-48fd-a40f-9c33e0b43375", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769307068300}, "additional": {"logType": "detail", "children": [], "durationId": "05f7d6f5-8d81-47ef-b4f9-3edf5edb60e7"}}, {"head": {"id": "8e58f352-fc8a-45bc-b37e-fc6bfdc39b18", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769307114400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14aec47a-26f2-4f49-a9f1-5cce8378c613", "name": "entry : PreviewBuild cost memory 0.0115203857421875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769307209700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7db1344d-1b57-4164-9844-1fb6ae8a54a9", "name": "runTaskFromQueue task cost before running: 11 s 524 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769307264000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98d45374-65d3-48be-a07a-7710cc7eecbe", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769307106500, "endTime": 31769307293500, "totalTime": 143300}, "additional": {"logType": "info", "children": [], "durationId": "05f7d6f5-8d81-47ef-b4f9-3edf5edb60e7"}}, {"head": {"id": "973869dc-c4f2-4be9-8cd6-1843598e6509", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769315884400, "endTime": 31769315906900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "279253d9-663f-45eb-825a-8f90b178c992", "logId": "3ca43da8-c2e1-49e8-8631-183ff07b3453"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ca43da8-c2e1-49e8-8631-183ff07b3453", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769315884400, "endTime": 31769315906900}, "additional": {"logType": "info", "children": [], "durationId": "973869dc-c4f2-4be9-8cd6-1843598e6509"}}, {"head": {"id": "495f7e63-c805-4b5c-be54-49954cfb3a3c", "name": "BUILD SUCCESSFUL in 11 s 532 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769315947200}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "61892866-5433-4b60-8164-d81428dfcefd", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31757784145200, "endTime": 31769316124000}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 55}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "43e971c5-5d06-4333-8709-281b13f9117a", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769316140200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96bee030-38a0-40ff-82c8-411f031d7a51", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769316191100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30ebfe47-d2f7-458e-ba20-c21efcf931ba", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769316216500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc0994a5-a75c-49bb-a88b-1cdc053ea426", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769316235900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "356db357-d096-4b64-ac38-9d513660745b", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769316257200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b54e981-eeeb-4188-b1ff-61e91af25b8e", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769316279400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2caff621-6a2a-4aac-aea5-bf94adbd1258", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769316299000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c43d4ba9-65e0-4eeb-b94d-d32be2980d9a", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769317035400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92f20d89-3c4e-4acf-970f-88c11777db01", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769322764500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f155909b-123c-46b9-b128-35dd12ac0d4b", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769323053300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d44ae2fa-29b0-4738-82ee-efc85150278b", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769330041000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f198a94-5ed4-4500-8e67-9fe2ca52155c", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:15 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769330547800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56aa3563-a918-4855-be24-e5f13456988e", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769330717400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41effa2c-0342-4f6d-a3a3-06c6ec92d92f", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769331261300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a057608-0c0f-4c05-bbe9-32bceb2ffa85", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769331855500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1225172-0c15-4335-b158-8791863c2a13", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769332193700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40c90803-4244-4b6d-9249-bee9f1dde53a", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769332422800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98dddb7c-54d6-4bff-8ace-77f6c30f6012", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769332647000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3be0da7c-0c00-4857-8605-5fbe2accfd96", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769334939400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a84890bd-043a-469e-b65e-b626f763c490", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769335617000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "593ea5a8-2ba8-4673-9aca-f434dc22be83", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769335681800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b539d497-b6f0-4f92-ace6-097fc4f2abaa", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769336012600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc476d70-4892-451c-b537-6f5b169fae73", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769336609700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eed747cc-101a-4d66-8231-ad5e5363415e", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769343952600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d941ecd7-92ef-4f00-9dca-b698a36b1690", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769344192200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ce21fc3-e68b-4929-89bf-d025100c122a", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769344393000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08bff102-c49b-48e4-a4da-6078fcf4bd34", "name": "Update task entry:default@PreviewArkTS output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769344613500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fce11b32-5feb-4525-9faa-55c7ed515d1c", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:13 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769344824200}, "additional": {"logType": "debug", "children": []}}], "workLog": []}