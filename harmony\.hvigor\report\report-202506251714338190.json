{"version": "2.0", "ppid": 21780, "events": [{"head": {"id": "f6105d8b-f95b-4e7b-9e36-fb3792db5cea", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303988810100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9f5f860-4da0-43e7-8d5e-8d98dd1bd2ca", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303998025200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5668199b-e6b9-421d-80fb-6cc605dedf25", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303998271800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54de8581-f6c2-4b45-8194-d697d2921710", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316564400400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d59832ea-8b79-48ab-8252-99b5763d72fa", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316572240100, "endTime": 29316721704600}, "additional": {"children": ["4f56d674-5a2b-4a41-81f9-afb38321b9c2", "7a8b992c-5e25-4f2a-9adc-5c40d0f8290e", "8cbb2d29-92ed-40f0-9792-e655267ed231", "a92b7b35-f052-4b10-a482-79d376b70343", "56afe621-6561-422f-8de9-4ed305bb4885", "e721fb54-1e6f-445b-bed5-3b0f059d06a8", "f7683a7e-2db5-48d1-8933-a7a031194866"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "0643bec9-907f-4f16-9bf2-67b0ad33abce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f56d674-5a2b-4a41-81f9-afb38321b9c2", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316572241900, "endTime": 29316586719900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d59832ea-8b79-48ab-8252-99b5763d72fa", "logId": "23eb8a24-f92c-48a6-b6eb-ca834bd63a9b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7a8b992c-5e25-4f2a-9adc-5c40d0f8290e", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316586730600, "endTime": 29316720697100}, "additional": {"children": ["ea8bc38c-0b44-4266-bcc0-847277c891e0", "2df574f2-50ae-4a60-b48e-1a09e9fda904", "e8f26ab5-2964-4e64-ac70-43ae880ec339", "3ddc22dc-7f6d-4ae4-b170-164ace679a3a", "29466f79-1782-4b95-9651-d3fc1d3c4138", "0ac0577f-7480-4a3c-80db-1561b6dd83a5", "80b3da91-c42c-4d5a-ad46-cf84f70ec249", "85034b92-3d8f-4787-a59b-4e61e3274227", "85d6de91-7e1d-4704-93c9-5116bc7c020f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d59832ea-8b79-48ab-8252-99b5763d72fa", "logId": "146eb2f2-5734-4bf3-9b4e-c1b382919526"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8cbb2d29-92ed-40f0-9792-e655267ed231", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316720711700, "endTime": 29316721694200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d59832ea-8b79-48ab-8252-99b5763d72fa", "logId": "748d9964-33e8-4c57-ab77-19ccbd3f7417"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a92b7b35-f052-4b10-a482-79d376b70343", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316721701400, "endTime": 29316721702800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d59832ea-8b79-48ab-8252-99b5763d72fa", "logId": "ed99f238-fca8-4291-9fcf-9cfbcc4ab9a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56afe621-6561-422f-8de9-4ed305bb4885", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316576273900, "endTime": 29316576310500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d59832ea-8b79-48ab-8252-99b5763d72fa", "logId": "f59e40af-e4d9-428f-a3cb-fb812b9c86dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f59e40af-e4d9-428f-a3cb-fb812b9c86dd", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316576273900, "endTime": 29316576310500}, "additional": {"logType": "info", "children": [], "durationId": "56afe621-6561-422f-8de9-4ed305bb4885", "parent": "0643bec9-907f-4f16-9bf2-67b0ad33abce"}}, {"head": {"id": "e721fb54-1e6f-445b-bed5-3b0f059d06a8", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316581680000, "endTime": 29316581697300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d59832ea-8b79-48ab-8252-99b5763d72fa", "logId": "c85cfa87-812f-4fad-b199-26e5f86ddbe9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c85cfa87-812f-4fad-b199-26e5f86ddbe9", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316581680000, "endTime": 29316581697300}, "additional": {"logType": "info", "children": [], "durationId": "e721fb54-1e6f-445b-bed5-3b0f059d06a8", "parent": "0643bec9-907f-4f16-9bf2-67b0ad33abce"}}, {"head": {"id": "35ee313f-32a6-41da-99be-755f72f3a237", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316581732800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9129e152-0c12-4226-aeaf-b075be771884", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316586588600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23eb8a24-f92c-48a6-b6eb-ca834bd63a9b", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316572241900, "endTime": 29316586719900}, "additional": {"logType": "info", "children": [], "durationId": "4f56d674-5a2b-4a41-81f9-afb38321b9c2", "parent": "0643bec9-907f-4f16-9bf2-67b0ad33abce"}}, {"head": {"id": "ea8bc38c-0b44-4266-bcc0-847277c891e0", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316592277000, "endTime": 29316592285900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a8b992c-5e25-4f2a-9adc-5c40d0f8290e", "logId": "3d1f457c-bfcb-4484-8004-a91ccdd47dc0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2df574f2-50ae-4a60-b48e-1a09e9fda904", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316592295600, "endTime": 29316596760700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a8b992c-5e25-4f2a-9adc-5c40d0f8290e", "logId": "517dd811-9bd7-4e85-854d-1626434cadc6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e8f26ab5-2964-4e64-ac70-43ae880ec339", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316596770000, "endTime": 29316668146300}, "additional": {"children": ["f89453a0-3918-4d7f-acbb-22ed55d26d82", "1685a70b-5c17-40ce-9d83-65fa1a6688ea", "0ec06071-c299-46ca-ab2a-6f16e7b769c1"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a8b992c-5e25-4f2a-9adc-5c40d0f8290e", "logId": "33703170-320e-4bc6-bad8-572b6f0190b0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ddc22dc-7f6d-4ae4-b170-164ace679a3a", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316668163200, "endTime": 29316690712400}, "additional": {"children": ["64545625-997e-4694-96ed-2415ea0ac0b2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a8b992c-5e25-4f2a-9adc-5c40d0f8290e", "logId": "56723d4e-902b-4ee7-a3df-875d2d037c54"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29466f79-1782-4b95-9651-d3fc1d3c4138", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316690717600, "endTime": 29316702306800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a8b992c-5e25-4f2a-9adc-5c40d0f8290e", "logId": "b397c535-fc34-4506-bf0d-71148b4a2831"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ac0577f-7480-4a3c-80db-1561b6dd83a5", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316703246200, "endTime": 29316711906900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a8b992c-5e25-4f2a-9adc-5c40d0f8290e", "logId": "ea76cfc8-ff59-43ad-82eb-470566867ad3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "80b3da91-c42c-4d5a-ad46-cf84f70ec249", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316711924000, "endTime": 29316720546900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a8b992c-5e25-4f2a-9adc-5c40d0f8290e", "logId": "936f8280-05fd-4529-bd6b-3d4af605dd84"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85034b92-3d8f-4787-a59b-4e61e3274227", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316720569800, "endTime": 29316720688900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a8b992c-5e25-4f2a-9adc-5c40d0f8290e", "logId": "74d33650-ff5c-4fef-813d-4b277515a4eb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d1f457c-bfcb-4484-8004-a91ccdd47dc0", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316592277000, "endTime": 29316592285900}, "additional": {"logType": "info", "children": [], "durationId": "ea8bc38c-0b44-4266-bcc0-847277c891e0", "parent": "146eb2f2-5734-4bf3-9b4e-c1b382919526"}}, {"head": {"id": "517dd811-9bd7-4e85-854d-1626434cadc6", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316592295600, "endTime": 29316596760700}, "additional": {"logType": "info", "children": [], "durationId": "2df574f2-50ae-4a60-b48e-1a09e9fda904", "parent": "146eb2f2-5734-4bf3-9b4e-c1b382919526"}}, {"head": {"id": "f89453a0-3918-4d7f-acbb-22ed55d26d82", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316597490200, "endTime": 29316597508400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e8f26ab5-2964-4e64-ac70-43ae880ec339", "logId": "3461db6e-8774-4e1e-847b-f318dd9caaef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3461db6e-8774-4e1e-847b-f318dd9caaef", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316597490200, "endTime": 29316597508400}, "additional": {"logType": "info", "children": [], "durationId": "f89453a0-3918-4d7f-acbb-22ed55d26d82", "parent": "33703170-320e-4bc6-bad8-572b6f0190b0"}}, {"head": {"id": "1685a70b-5c17-40ce-9d83-65fa1a6688ea", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316599577900, "endTime": 29316667068000}, "additional": {"children": ["ad3f0aaa-fc9a-4b4c-8489-6080e5090af7", "9c0a57c2-5b17-4021-876c-947d43be594e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e8f26ab5-2964-4e64-ac70-43ae880ec339", "logId": "78fde6d9-ab71-4cc8-bce2-ff9c40333d4a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad3f0aaa-fc9a-4b4c-8489-6080e5090af7", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316599578900, "endTime": 29316602551000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1685a70b-5c17-40ce-9d83-65fa1a6688ea", "logId": "068e5845-3520-4327-9c0b-a6429a3a7f2b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c0a57c2-5b17-4021-876c-947d43be594e", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316602561300, "endTime": 29316667058800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1685a70b-5c17-40ce-9d83-65fa1a6688ea", "logId": "1b06e025-99a9-46b5-a356-c4276b951b4d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f5811685-a1af-4539-91c6-165ddf787ee0", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316599584200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fc94c35-d1d1-49af-9403-77cf68c93c2f", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316602425800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "068e5845-3520-4327-9c0b-a6429a3a7f2b", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316599578900, "endTime": 29316602551000}, "additional": {"logType": "info", "children": [], "durationId": "ad3f0aaa-fc9a-4b4c-8489-6080e5090af7", "parent": "78fde6d9-ab71-4cc8-bce2-ff9c40333d4a"}}, {"head": {"id": "7b65055f-0189-4e42-b494-c0a203852511", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316602570700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dea50c1e-080a-499d-93d5-5da9f81fbdc4", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316609607000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b7459c1-6c36-4f51-aa7a-ea1eae107b00", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316609714300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cfb8a1f-8382-4065-9287-49a2fe29b664", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316609814500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b95a347-6fce-45b9-8e6d-2fad8e78d17e", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316609880100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54b1fe85-236d-4653-a488-7599fbb70689", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316611272000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0190decd-b0ad-4892-83bb-ca2e455d8ea9", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316614299900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f1eb28e-1a2e-43be-a3bf-78e74dbfb29e", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316623984900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8da9e9c2-83a7-464b-994f-166d94dd59c0", "name": "Sdk init in 29 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316643543300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d44677f9-03ff-4259-b3eb-549f6b1800b3", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316643670700}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 14}, "markType": "other"}}, {"head": {"id": "07d5dc4e-e492-42f5-97c4-bac447d200ab", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316643682500}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 14}, "markType": "other"}}, {"head": {"id": "bbd359d1-ed4b-46c8-b51a-e61083d14648", "name": "Project task initialization takes 22 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316666844100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0a01013-46f5-4afa-a9ef-bb1d66a8c74d", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316666959800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4a1bdf0-677e-41d6-a5fc-c597002dfbc0", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316667001100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b732e3d7-9f31-4627-a703-0f497cf88811", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316667029400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b06e025-99a9-46b5-a356-c4276b951b4d", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316602561300, "endTime": 29316667058800}, "additional": {"logType": "info", "children": [], "durationId": "9c0a57c2-5b17-4021-876c-947d43be594e", "parent": "78fde6d9-ab71-4cc8-bce2-ff9c40333d4a"}}, {"head": {"id": "78fde6d9-ab71-4cc8-bce2-ff9c40333d4a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316599577900, "endTime": 29316667068000}, "additional": {"logType": "info", "children": ["068e5845-3520-4327-9c0b-a6429a3a7f2b", "1b06e025-99a9-46b5-a356-c4276b951b4d"], "durationId": "1685a70b-5c17-40ce-9d83-65fa1a6688ea", "parent": "33703170-320e-4bc6-bad8-572b6f0190b0"}}, {"head": {"id": "0ec06071-c299-46ca-ab2a-6f16e7b769c1", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316668116700, "endTime": 29316668136100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e8f26ab5-2964-4e64-ac70-43ae880ec339", "logId": "caabc71e-1753-4aea-853d-fb1bc1ec1bfb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "caabc71e-1753-4aea-853d-fb1bc1ec1bfb", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316668116700, "endTime": 29316668136100}, "additional": {"logType": "info", "children": [], "durationId": "0ec06071-c299-46ca-ab2a-6f16e7b769c1", "parent": "33703170-320e-4bc6-bad8-572b6f0190b0"}}, {"head": {"id": "33703170-320e-4bc6-bad8-572b6f0190b0", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316596770000, "endTime": 29316668146300}, "additional": {"logType": "info", "children": ["3461db6e-8774-4e1e-847b-f318dd9caaef", "78fde6d9-ab71-4cc8-bce2-ff9c40333d4a", "caabc71e-1753-4aea-853d-fb1bc1ec1bfb"], "durationId": "e8f26ab5-2964-4e64-ac70-43ae880ec339", "parent": "146eb2f2-5734-4bf3-9b4e-c1b382919526"}}, {"head": {"id": "64545625-997e-4694-96ed-2415ea0ac0b2", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316669383500, "endTime": 29316690700000}, "additional": {"children": ["78cc0b73-b511-47ad-b5ca-fb70a0f19274", "949db861-c992-48d3-b9f3-82638fe53238", "28a4efd0-8290-482d-9228-38a84d9a905a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3ddc22dc-7f6d-4ae4-b170-164ace679a3a", "logId": "928a557c-d533-4a27-a434-9e0210009e82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78cc0b73-b511-47ad-b5ca-fb70a0f19274", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316674522500, "endTime": 29316674539100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "64545625-997e-4694-96ed-2415ea0ac0b2", "logId": "46fb0155-99f9-41e6-946a-a525b7561731"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "46fb0155-99f9-41e6-946a-a525b7561731", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316674522500, "endTime": 29316674539100}, "additional": {"logType": "info", "children": [], "durationId": "78cc0b73-b511-47ad-b5ca-fb70a0f19274", "parent": "928a557c-d533-4a27-a434-9e0210009e82"}}, {"head": {"id": "949db861-c992-48d3-b9f3-82638fe53238", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316676261000, "endTime": 29316689378100}, "additional": {"children": ["5a87f62a-3a5d-418c-9ec0-f26672bd2bd9", "db931e9b-0ea2-4000-a3c5-f43c9d100151"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "64545625-997e-4694-96ed-2415ea0ac0b2", "logId": "2fe80663-7b41-4d61-a3a3-702161f5c027"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5a87f62a-3a5d-418c-9ec0-f26672bd2bd9", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316676261800, "endTime": 29316679457600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "949db861-c992-48d3-b9f3-82638fe53238", "logId": "2f1a7815-e95e-41f9-aa13-dc479a77de22"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db931e9b-0ea2-4000-a3c5-f43c9d100151", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316679468800, "endTime": 29316689369700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "949db861-c992-48d3-b9f3-82638fe53238", "logId": "4a889bc0-8e09-4b9a-ab4f-02cdc9b71039"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "91b151c1-9283-4ab3-b8b7-ba6f7cacf324", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316676266600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90e048f2-f800-456a-98c8-00c5593c9205", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316679341200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f1a7815-e95e-41f9-aa13-dc479a77de22", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316676261800, "endTime": 29316679457600}, "additional": {"logType": "info", "children": [], "durationId": "5a87f62a-3a5d-418c-9ec0-f26672bd2bd9", "parent": "2fe80663-7b41-4d61-a3a3-702161f5c027"}}, {"head": {"id": "403b7740-618a-46d3-9ed4-f221f91f3e24", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316679482500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96b6eb08-76a7-43cc-ab14-80702a40b6fd", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316685396300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd3bec2a-d83d-40e5-b92b-cf27add4a461", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316685506100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1f454b4-8519-450d-a0a7-206d3db9b616", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316685627800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb3d006c-dbad-4122-be5b-ddf38f286610", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316685703000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f03a7142-2496-4a3e-8d70-da50ce554c46", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316685734500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41004320-11f1-4f2d-aca0-828a80c5546a", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316685759000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79b89633-ee0f-47be-881c-34ddd8dd644b", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316685786100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a935b48-345c-494d-98b5-4<PERSON>afa4d2da", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316689145300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da8f7d46-da78-4261-b7c6-785203f28702", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316689282600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03a1b136-b77a-48b4-96b9-4fe6832f4591", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316689322200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f0a11c5-9b83-4d5a-9c01-721b2d605493", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316689346900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a889bc0-8e09-4b9a-ab4f-02cdc9b71039", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316679468800, "endTime": 29316689369700}, "additional": {"logType": "info", "children": [], "durationId": "db931e9b-0ea2-4000-a3c5-f43c9d100151", "parent": "2fe80663-7b41-4d61-a3a3-702161f5c027"}}, {"head": {"id": "2fe80663-7b41-4d61-a3a3-702161f5c027", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316676261000, "endTime": 29316689378100}, "additional": {"logType": "info", "children": ["2f1a7815-e95e-41f9-aa13-dc479a77de22", "4a889bc0-8e09-4b9a-ab4f-02cdc9b71039"], "durationId": "949db861-c992-48d3-b9f3-82638fe53238", "parent": "928a557c-d533-4a27-a434-9e0210009e82"}}, {"head": {"id": "28a4efd0-8290-482d-9228-38a84d9a905a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316690679100, "endTime": 29316690691900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "64545625-997e-4694-96ed-2415ea0ac0b2", "logId": "d68341c3-b79d-444d-83d6-5c7dc6647a75"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d68341c3-b79d-444d-83d6-5c7dc6647a75", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316690679100, "endTime": 29316690691900}, "additional": {"logType": "info", "children": [], "durationId": "28a4efd0-8290-482d-9228-38a84d9a905a", "parent": "928a557c-d533-4a27-a434-9e0210009e82"}}, {"head": {"id": "928a557c-d533-4a27-a434-9e0210009e82", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316669383500, "endTime": 29316690700000}, "additional": {"logType": "info", "children": ["46fb0155-99f9-41e6-946a-a525b7561731", "2fe80663-7b41-4d61-a3a3-702161f5c027", "d68341c3-b79d-444d-83d6-5c7dc6647a75"], "durationId": "64545625-997e-4694-96ed-2415ea0ac0b2", "parent": "56723d4e-902b-4ee7-a3df-875d2d037c54"}}, {"head": {"id": "56723d4e-902b-4ee7-a3df-875d2d037c54", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316668163200, "endTime": 29316690712400}, "additional": {"logType": "info", "children": ["928a557c-d533-4a27-a434-9e0210009e82"], "durationId": "3ddc22dc-7f6d-4ae4-b170-164ace679a3a", "parent": "146eb2f2-5734-4bf3-9b4e-c1b382919526"}}, {"head": {"id": "404026b7-8522-4026-bb28-aef3cbb84094", "name": "watch files: [\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\hvigorfile.ts',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316702004100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfe82a52-dfeb-4cc1-aca9-12e5e56dff1c", "name": "hvigorfile, resolve hvigorfile dependencies in 12 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316702256900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b397c535-fc34-4506-bf0d-71148b4a2831", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316690717600, "endTime": 29316702306800}, "additional": {"logType": "info", "children": [], "durationId": "29466f79-1782-4b95-9651-d3fc1d3c4138", "parent": "146eb2f2-5734-4bf3-9b4e-c1b382919526"}}, {"head": {"id": "85d6de91-7e1d-4704-93c9-5116bc7c020f", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316703081200, "endTime": 29316703238200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a8b992c-5e25-4f2a-9adc-5c40d0f8290e", "logId": "93e5e916-131d-484e-b529-4133641b461a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f44ae83-fa62-485e-b9f1-adeeb6afdf35", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316703102400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93e5e916-131d-484e-b529-4133641b461a", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316703081200, "endTime": 29316703238200}, "additional": {"logType": "info", "children": [], "durationId": "85d6de91-7e1d-4704-93c9-5116bc7c020f", "parent": "146eb2f2-5734-4bf3-9b4e-c1b382919526"}}, {"head": {"id": "8807444a-cdff-4864-99a3-37c2e4c7a186", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316704716700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25da569c-775c-4958-ad11-07a2bab272f7", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316710965900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea76cfc8-ff59-43ad-82eb-470566867ad3", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316703246200, "endTime": 29316711906900}, "additional": {"logType": "info", "children": [], "durationId": "0ac0577f-7480-4a3c-80db-1561b6dd83a5", "parent": "146eb2f2-5734-4bf3-9b4e-c1b382919526"}}, {"head": {"id": "ea4686e2-d01c-4f22-95a6-9164790578e6", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316711936000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efb3ace9-f186-4e43-992f-eadf0e239603", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316716181500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfbdcf00-30cd-4632-baf1-85a98e569e64", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316716301000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3999d3f3-917d-4ff9-ad35-ea9ef2a9a74c", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316716484300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d91c229-df99-426a-82e7-5391381323b4", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316718194600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b1a74da-a328-45ee-95c8-42477394270a", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316718268200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "936f8280-05fd-4529-bd6b-3d4af605dd84", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316711924000, "endTime": 29316720546900}, "additional": {"logType": "info", "children": [], "durationId": "80b3da91-c42c-4d5a-ad46-cf84f70ec249", "parent": "146eb2f2-5734-4bf3-9b4e-c1b382919526"}}, {"head": {"id": "68afe31b-f0f7-4ed5-9414-3ef70cff9def", "name": "Configuration phase cost:129 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316720592800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74d33650-ff5c-4fef-813d-4b277515a4eb", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316720569800, "endTime": 29316720688900}, "additional": {"logType": "info", "children": [], "durationId": "85034b92-3d8f-4787-a59b-4e61e3274227", "parent": "146eb2f2-5734-4bf3-9b4e-c1b382919526"}}, {"head": {"id": "146eb2f2-5734-4bf3-9b4e-c1b382919526", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316586730600, "endTime": 29316720697100}, "additional": {"logType": "info", "children": ["3d1f457c-bfcb-4484-8004-a91ccdd47dc0", "517dd811-9bd7-4e85-854d-1626434cadc6", "33703170-320e-4bc6-bad8-572b6f0190b0", "56723d4e-902b-4ee7-a3df-875d2d037c54", "b397c535-fc34-4506-bf0d-71148b4a2831", "ea76cfc8-ff59-43ad-82eb-470566867ad3", "936f8280-05fd-4529-bd6b-3d4af605dd84", "74d33650-ff5c-4fef-813d-4b277515a4eb", "93e5e916-131d-484e-b529-4133641b461a"], "durationId": "7a8b992c-5e25-4f2a-9adc-5c40d0f8290e", "parent": "0643bec9-907f-4f16-9bf2-67b0ad33abce"}}, {"head": {"id": "f7683a7e-2db5-48d1-8933-a7a031194866", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316721675300, "endTime": 29316721688200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d59832ea-8b79-48ab-8252-99b5763d72fa", "logId": "fe2bf6d0-a95a-41dc-b369-4b70e3a2d5f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe2bf6d0-a95a-41dc-b369-4b70e3a2d5f4", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316721675300, "endTime": 29316721688200}, "additional": {"logType": "info", "children": [], "durationId": "f7683a7e-2db5-48d1-8933-a7a031194866", "parent": "0643bec9-907f-4f16-9bf2-67b0ad33abce"}}, {"head": {"id": "748d9964-33e8-4c57-ab77-19ccbd3f7417", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316720711700, "endTime": 29316721694200}, "additional": {"logType": "info", "children": [], "durationId": "8cbb2d29-92ed-40f0-9792-e655267ed231", "parent": "0643bec9-907f-4f16-9bf2-67b0ad33abce"}}, {"head": {"id": "ed99f238-fca8-4291-9fcf-9cfbcc4ab9a9", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316721701400, "endTime": 29316721702800}, "additional": {"logType": "info", "children": [], "durationId": "a92b7b35-f052-4b10-a482-79d376b70343", "parent": "0643bec9-907f-4f16-9bf2-67b0ad33abce"}}, {"head": {"id": "0643bec9-907f-4f16-9bf2-67b0ad33abce", "name": "init", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316572240100, "endTime": 29316721704600}, "additional": {"logType": "info", "children": ["23eb8a24-f92c-48a6-b6eb-ca834bd63a9b", "146eb2f2-5734-4bf3-9b4e-c1b382919526", "748d9964-33e8-4c57-ab77-19ccbd3f7417", "ed99f238-fca8-4291-9fcf-9cfbcc4ab9a9", "f59e40af-e4d9-428f-a3cb-fb812b9c86dd", "c85cfa87-812f-4fad-b199-26e5f86ddbe9", "fe2bf6d0-a95a-41dc-b369-4b70e3a2d5f4"], "durationId": "d59832ea-8b79-48ab-8252-99b5763d72fa"}}, {"head": {"id": "7861d9d9-f48f-4e00-9cc5-18cda8e56ef9", "name": "Configuration task cost before running: 154 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316721799300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f4092dd-0705-450a-9610-6003b7355169", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316725817700, "endTime": 29316733043100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "a937ad80-550f-46fa-a771-1a934d073674", "logId": "27ae99b9-f057-4e85-933d-8e8ef0e02471"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a937ad80-550f-46fa-a771-1a934d073674", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316723000000}, "additional": {"logType": "detail", "children": [], "durationId": "1f4092dd-0705-450a-9610-6003b7355169"}}, {"head": {"id": "29b88c82-d0c5-4ce4-a0b1-3bbb4c45488e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316723417800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b4df528-f9bc-4dc1-8739-5b4981cba1a7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316723499300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8594106-db13-4fec-8e47-303cfce945ec", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316725829200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "194a024f-fec3-43d5-98f5-e934f337f289", "name": "Incremental task entry:default@PreBuild pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316732854800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8174fec-8a5d-4638-94f7-02f59866a428", "name": "entry : default@PreBuild cost memory -1.533050537109375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316732991200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27ae99b9-f057-4e85-933d-8e8ef0e02471", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316725817700, "endTime": 29316733043100}, "additional": {"logType": "info", "children": [], "durationId": "1f4092dd-0705-450a-9610-6003b7355169"}}, {"head": {"id": "03a49f17-7144-4e23-b162-e3c530c97750", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316737345400, "endTime": 29316739136700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "baad0cb1-5ef6-4832-8989-3a72a178c1fd", "logId": "5a4a5b9e-9ba9-4a9e-9ac8-906df8c16288"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "baad0cb1-5ef6-4832-8989-3a72a178c1fd", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316736152800}, "additional": {"logType": "detail", "children": [], "durationId": "03a49f17-7144-4e23-b162-e3c530c97750"}}, {"head": {"id": "97b1a2f9-6cb6-49d8-8684-45a4225a64cf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316736604600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3b14497-ddd7-4dcf-ad78-03209e8e3a71", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316736690300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5f29ff5-85c6-4446-a81a-b65a0491b708", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316737353100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad7c37e5-81ac-45bd-8e7b-82a685e7bb67", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316739001000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab9dd567-c4c2-413d-b5cb-06ec710f9bb3", "name": "entry : default@MergeProfile cost memory 0.11238861083984375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316739088000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a4a5b9e-9ba9-4a9e-9ac8-906df8c16288", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316737345400, "endTime": 29316739136700}, "additional": {"logType": "info", "children": [], "durationId": "03a49f17-7144-4e23-b162-e3c530c97750"}}, {"head": {"id": "dcee3077-d5c2-4603-996d-d352d92b7b5d", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316742112800, "endTime": 29316744330700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "58bb133d-47d3-451f-b1a8-b0fbd966b777", "logId": "9e9536b6-917f-4c5b-b8a8-bd14eee7a09f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58bb133d-47d3-451f-b1a8-b0fbd966b777", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316740728800}, "additional": {"logType": "detail", "children": [], "durationId": "dcee3077-d5c2-4603-996d-d352d92b7b5d"}}, {"head": {"id": "74bd1768-2fd9-4d15-93ec-db458a1aa3a1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316741287400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c697ba7f-2e61-4422-8ca4-20002d28404a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316741382100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b65ad050-bad7-4804-b23c-dbfbaaea9fd2", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316742118600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77e6f3b2-e4b9-42d4-a95b-8857bc91e9bf", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316743037400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85973884-5c9e-4022-afba-76a9f95b4a9a", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316744191500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9bbbd62-2b78-47d6-a250-004394da6019", "name": "entry : default@CreateBuildProfile cost memory 0.098388671875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316744282900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e9536b6-917f-4c5b-b8a8-bd14eee7a09f", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316742112800, "endTime": 29316744330700}, "additional": {"logType": "info", "children": [], "durationId": "dcee3077-d5c2-4603-996d-d352d92b7b5d"}}, {"head": {"id": "ec6b5160-e6ab-40c1-9785-d86f71760e26", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316747043800, "endTime": 29316747324500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "8fb77593-c690-4967-93f8-f333a4d509fb", "logId": "bb98398f-7c1e-48a2-9e2a-788f950858d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8fb77593-c690-4967-93f8-f333a4d509fb", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316745705400}, "additional": {"logType": "detail", "children": [], "durationId": "ec6b5160-e6ab-40c1-9785-d86f71760e26"}}, {"head": {"id": "006a0ced-477d-4410-a4ae-77009adfa4c5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316746198900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e9e7f51-995c-4713-860a-8e520d43b430", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316746292100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "077abcc7-fa9b-4c8f-a0ba-1df2897684f1", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316747053300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "007c5f73-8a50-405d-93a2-e066824d0095", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316747143900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4158a5db-5cd0-4936-892c-0ca2cbad2d94", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316747178700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76fa9c2b-44b5-451b-86ce-906ed2b6ad82", "name": "entry : default@PreCheckSyscap cost memory 0.03691864013671875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316747237100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c159a3dc-02e4-460e-bd07-466c0fc3aa37", "name": "runTaskFromQueue task cost before running: 180 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316747295100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb98398f-7c1e-48a2-9e2a-788f950858d2", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316747043800, "endTime": 29316747324500, "totalTime": 233400}, "additional": {"logType": "info", "children": [], "durationId": "ec6b5160-e6ab-40c1-9785-d86f71760e26"}}, {"head": {"id": "be7c0561-48f6-483d-81fa-b66af4409074", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316755368800, "endTime": 29316756330800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b255811e-477d-4b58-aa7e-bf32f791d3ec", "logId": "1a8b0f68-ec40-40c3-9bbd-612fbadba0b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b255811e-477d-4b58-aa7e-bf32f791d3ec", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316748928300}, "additional": {"logType": "detail", "children": [], "durationId": "be7c0561-48f6-483d-81fa-b66af4409074"}}, {"head": {"id": "28da9a98-cf81-4dc4-be47-e95ee6ae6de3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316749444600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "352ad3c9-c5be-41f2-99bb-6242f25415d6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316749544700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "196419d8-b446-4fc9-af1f-9a8ee5a04c02", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316755380700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0722c256-f763-40de-b0b0-d0a5bf917536", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316755582200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ba815c9-c795-4d3e-a531-78a5894132d9", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316756181900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "969d42f0-df47-492f-86c6-605d0a1096fc", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06610107421875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316756283300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a8b0f68-ec40-40c3-9bbd-612fbadba0b9", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316755368800, "endTime": 29316756330800}, "additional": {"logType": "info", "children": [], "durationId": "be7c0561-48f6-483d-81fa-b66af4409074"}}, {"head": {"id": "f5378845-d8f0-4b13-a0ab-61d249eb7c56", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316760388800, "endTime": 29316761421900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "23d5e460-b450-4209-8bf2-739884303cdc", "logId": "624adeb9-240d-4d2a-a7e8-2ed71643123d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23d5e460-b450-4209-8bf2-739884303cdc", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316758652200}, "additional": {"logType": "detail", "children": [], "durationId": "f5378845-d8f0-4b13-a0ab-61d249eb7c56"}}, {"head": {"id": "c5d1e49e-895d-4d1f-8d5e-ed3ef0a6a15b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316759233200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0c98ad8-60e6-4872-b1d5-76a8fa6b96c4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316759330500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0b51afa-15bb-4398-a312-caf189a1aad5", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316760396500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a97a551-e40c-4840-b716-54854c818e16", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316761288100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "979f8425-b74e-465a-a6d6-6c4a68481fb3", "name": "entry : default@ProcessProfile cost memory 0.05683135986328125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316761378800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "624adeb9-240d-4d2a-a7e8-2ed71643123d", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316760388800, "endTime": 29316761421900}, "additional": {"logType": "info", "children": [], "durationId": "f5378845-d8f0-4b13-a0ab-61d249eb7c56"}}, {"head": {"id": "32a3bcad-1ec7-4272-8b01-425ca4252e30", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316765099600, "endTime": 29316770269500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b94bd7d6-133f-4d21-8476-95eee0119331", "logId": "d21c0a88-30d1-4875-954e-13871a496033"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b94bd7d6-133f-4d21-8476-95eee0119331", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316762852200}, "additional": {"logType": "detail", "children": [], "durationId": "32a3bcad-1ec7-4272-8b01-425ca4252e30"}}, {"head": {"id": "24b15481-c9bb-4842-868d-4078cf3856c4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316763415200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5668f6dd-9f25-469f-a263-04386e73e83a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316763511000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b2a2984-84bc-46fd-9fa1-e7aface04993", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316765108300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a746324b-b4ae-404e-9a89-67e4cac18715", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316770086200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e90b99f1-3ee2-4119-9599-6319429b34db", "name": "entry : default@ProcessRouterMap cost memory 0.19708251953125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316770220200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d21c0a88-30d1-4875-954e-13871a496033", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316765099600, "endTime": 29316770269500}, "additional": {"logType": "info", "children": [], "durationId": "32a3bcad-1ec7-4272-8b01-425ca4252e30"}}, {"head": {"id": "6cea0ea3-2916-4bf9-86d7-bee67f796cb1", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316777217400, "endTime": 29316779480600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "68182891-e666-4f4f-812c-835a974838c8", "logId": "12f680d9-6cd2-4163-a2cd-caee2584d029"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68182891-e666-4f4f-812c-835a974838c8", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316773932600}, "additional": {"logType": "detail", "children": [], "durationId": "6cea0ea3-2916-4bf9-86d7-bee67f796cb1"}}, {"head": {"id": "507649f9-df25-437d-be80-bc828b44a8a6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316774414900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b720606-27a8-4fc4-ba05-6fa2fb3fc855", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316774518500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b83bb68-0f50-4755-8adb-37ac1ba23732", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316775403600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c7331a2-a541-4bd4-8293-a76535d6bfc2", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316778123100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "387356b4-c0aa-48aa-a088-54386a43af80", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316778266500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d853f8bd-2c6c-4793-b378-c8dedaa6ac4e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316778307600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ff162ff-a8ee-4dcc-8d8d-8ce70469fa2b", "name": "entry : default@PreviewProcessResource cost memory 0.07006072998046875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316778364900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "136805aa-c842-44ea-958f-70f3f6500cdc", "name": "runTaskFromQueue task cost before running: 212 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316779388300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12f680d9-6cd2-4163-a2cd-caee2584d029", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316777217400, "endTime": 29316779480600, "totalTime": 1184500}, "additional": {"logType": "info", "children": [], "durationId": "6cea0ea3-2916-4bf9-86d7-bee67f796cb1"}}, {"head": {"id": "201bb4c2-ecb3-40da-ab6d-138e32f6d69f", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316785271500, "endTime": 29316802395600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7425715a-de36-47a1-9872-9fa1d2d4f40e", "logId": "fcc79e90-ca21-46e6-9832-34a0e824513d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7425715a-de36-47a1-9872-9fa1d2d4f40e", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316781868900}, "additional": {"logType": "detail", "children": [], "durationId": "201bb4c2-ecb3-40da-ab6d-138e32f6d69f"}}, {"head": {"id": "0065f805-5194-4384-bbec-2c950ff87886", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316782365800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0069a86e-ea4d-4bdc-a4c0-91da61375a3c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316782453500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13b7022a-d77c-409b-afee-69fc0d670395", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316785283800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c47bf77-2964-41fa-aeb9-692318e24cc7", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316802201000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c32b4466-0647-4e0e-af2c-704b8c37fc0d", "name": "entry : default@GenerateLoaderJson cost memory -0.9660720825195312", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316802326000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcc79e90-ca21-46e6-9832-34a0e824513d", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316785271500, "endTime": 29316802395600}, "additional": {"logType": "info", "children": [], "durationId": "201bb4c2-ecb3-40da-ab6d-138e32f6d69f"}}, {"head": {"id": "aee9f075-fa42-4d75-8887-d4b764e426a3", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316811309000, "endTime": 29316829020300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "7d52f877-7d04-445a-9a5b-d4af08d8e6ae", "logId": "3a83236d-48ec-4c8f-b5ae-292b32743fac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d52f877-7d04-445a-9a5b-d4af08d8e6ae", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316808088700}, "additional": {"logType": "detail", "children": [], "durationId": "aee9f075-fa42-4d75-8887-d4b764e426a3"}}, {"head": {"id": "50a8742a-c225-4bb2-97b0-e6798bea94b7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316808552700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcf355f7-0ea1-43c8-a1c1-114151ceaac9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316808699700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da16a34e-f0dc-4f33-a62f-b3c9873a4eee", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316809492000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a45159a9-4c2e-480e-8fa2-c21efe643f1e", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316811328400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d31bfac-99d5-4d72-8ba8-830ed431dfa7", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 17 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316828812400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82d0732a-6dc1-4681-8ab1-a51c6a26d72b", "name": "entry : default@PreviewCompileResource cost memory 0.7297592163085938", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316828954100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a83236d-48ec-4c8f-b5ae-292b32743fac", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316811309000, "endTime": 29316829020300}, "additional": {"logType": "info", "children": [], "durationId": "aee9f075-fa42-4d75-8887-d4b764e426a3"}}, {"head": {"id": "76e02db7-9cb5-42ca-b6a0-5b34982b162f", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316832155200, "endTime": 29316832403600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "9ac849d3-c1d5-4911-8ba4-b71dbfc845c0", "logId": "141a2b25-5b62-4438-995e-311880f63f95"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ac849d3-c1d5-4911-8ba4-b71dbfc845c0", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316831131700}, "additional": {"logType": "detail", "children": [], "durationId": "76e02db7-9cb5-42ca-b6a0-5b34982b162f"}}, {"head": {"id": "0b51b638-be90-4f4e-9807-50b9b4d4d2f9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316832002900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae353173-bb6f-4347-90a8-3eeb94af27c2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316832086200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "917d5d27-070a-42a7-85de-ab817e0f0bc3", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316832159400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f95d4f6e-3264-4a3f-b220-ed422b5bd0aa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316832217100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21915269-7c8a-4f66-847c-19b6c6e6f63a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316832239100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "066589d9-af4e-40e9-8609-361d9670212e", "name": "entry : default@PreviewHookCompileResource cost memory 0.03803253173828125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316832318200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51636771-490b-4992-8975-a35207748d57", "name": "runTaskFromQueue task cost before running: 265 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316832377000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "141a2b25-5b62-4438-995e-311880f63f95", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316832155200, "endTime": 29316832403600, "totalTime": 204000}, "additional": {"logType": "info", "children": [], "durationId": "76e02db7-9cb5-42ca-b6a0-5b34982b162f"}}, {"head": {"id": "6b6ce933-46a3-4b81-9fe9-c456e5384e96", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316834753100, "endTime": 29316836634700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "04620f82-9bbe-4c18-8bea-50e196bb84d0", "logId": "468a8ef4-1fd1-4b8a-9749-14469e7c98f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04620f82-9bbe-4c18-8bea-50e196bb84d0", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316833677100}, "additional": {"logType": "detail", "children": [], "durationId": "6b6ce933-46a3-4b81-9fe9-c456e5384e96"}}, {"head": {"id": "c88610e0-1e2c-4f58-ac3a-92d8a9cf96d9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316834083600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32598123-6398-4499-ba67-8a2ef36c2b75", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316834158700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "482170f9-78d8-4d95-81f6-ae144f1f6ccf", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316834760000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "188e186a-0614-44e9-aaf5-835831cb6855", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316836501500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f01f94f7-8b39-4e01-af52-e4632744062c", "name": "entry : default@CopyPreviewProfile cost memory 0.09860992431640625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316836591500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "468a8ef4-1fd1-4b8a-9749-14469e7c98f1", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316834753100, "endTime": 29316836634700}, "additional": {"logType": "info", "children": [], "durationId": "6b6ce933-46a3-4b81-9fe9-c456e5384e96"}}, {"head": {"id": "257e62c8-969b-40c0-b9d3-22fc943a5338", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316838975100, "endTime": 29316839255100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "dcdef536-8a22-48da-afbd-2a6e56737d52", "logId": "77e8f1c3-afda-48cd-a2a3-d6324f37d044"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dcdef536-8a22-48da-afbd-2a6e56737d52", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316837941900}, "additional": {"logType": "detail", "children": [], "durationId": "257e62c8-969b-40c0-b9d3-22fc943a5338"}}, {"head": {"id": "7e4aca36-5bd4-41f5-8550-19f39e04b101", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316838338500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cc7f756-8ac1-44be-ae1b-a1f485403463", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316838410800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30d265a9-caf2-41d0-b70b-dcb769cd3460", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316838981100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c50aa5e-bba7-4e33-881e-7c16f59b2bcb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316839081000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aad495ff-2a64-49d6-afb7-270325662c81", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316839114500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17eeb788-8661-4d07-936b-5a9f3bfb7713", "name": "entry : default@ReplacePreviewerPage cost memory 0.03798675537109375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316839180000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f927ef8-6235-4aab-871f-8f9b76c3985d", "name": "runTaskFromQueue task cost before running: 272 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316839229900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77e8f1c3-afda-48cd-a2a3-d6324f37d044", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316838975100, "endTime": 29316839255100, "totalTime": 236400}, "additional": {"logType": "info", "children": [], "durationId": "257e62c8-969b-40c0-b9d3-22fc943a5338"}}, {"head": {"id": "1a256824-1a85-4bcf-8b0d-a81a8eb58aa4", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316840594300, "endTime": 29316840788500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "c9a20c1f-de50-4f76-a89e-c758d2f15932", "logId": "f535e860-bedc-487f-a0a5-3498a44be097"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9a20c1f-de50-4f76-a89e-c758d2f15932", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316840542800}, "additional": {"logType": "detail", "children": [], "durationId": "1a256824-1a85-4bcf-8b0d-a81a8eb58aa4"}}, {"head": {"id": "f90394e1-8c2e-42d5-9695-14611415ce14", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316840599200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e07947f-b3e1-41e1-9e01-de678b56b784", "name": "entry : buildPreviewerResource cost memory 0.01168060302734375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316840704700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a512656-5a95-42f2-93ed-af113cef1552", "name": "runTaskFromQueue task cost before running: 273 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316840757500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f535e860-bedc-487f-a0a5-3498a44be097", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316840594300, "endTime": 29316840788500, "totalTime": 147200}, "additional": {"logType": "info", "children": [], "durationId": "1a256824-1a85-4bcf-8b0d-a81a8eb58aa4"}}, {"head": {"id": "975c9eeb-f035-4aa4-b42c-6c1c9fe75a28", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316843184300, "endTime": 29316845355600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "105c1618-8f3d-45a0-a8a3-ab4a2a0513da", "logId": "c51d03bf-1a3a-4300-8949-d294dea14142"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "105c1618-8f3d-45a0-a8a3-ab4a2a0513da", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316841956000}, "additional": {"logType": "detail", "children": [], "durationId": "975c9eeb-f035-4aa4-b42c-6c1c9fe75a28"}}, {"head": {"id": "a35b7dd0-122a-40fe-8e78-25a9bd11d51f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316842348400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15398322-0b4c-44f0-8644-13f780edd5d4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316842417900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07e8f488-6660-44f0-b0c8-fb97abe0ba2a", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316843191600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac69856e-3c7c-43aa-950f-d6f4efb0f75c", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316845221800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "590b264b-fc8c-4518-94a1-221592786b21", "name": "entry : default@PreviewUpdateAssets cost memory 0.11544036865234375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316845313100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c51d03bf-1a3a-4300-8949-d294dea14142", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316843184300, "endTime": 29316845355600}, "additional": {"logType": "info", "children": [], "durationId": "975c9eeb-f035-4aa4-b42c-6c1c9fe75a28"}}, {"head": {"id": "cad43100-87f7-47aa-a6cb-37d51bc27963", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316852197300, "endTime": 29327640226300}, "additional": {"children": ["48b8c94f-94a3-4702-9bbf-52997b3ed493"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9a0d59bd-0c73-4b77-8cbd-94c859f8cdb2", "logId": "f58ce23e-caa9-4288-8d7e-612c02d2c97b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a0d59bd-0c73-4b77-8cbd-94c859f8cdb2", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316847173600}, "additional": {"logType": "detail", "children": [], "durationId": "cad43100-87f7-47aa-a6cb-37d51bc27963"}}, {"head": {"id": "64d74b9d-20ce-4f1b-9a4a-81f03a9d0624", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316847726300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1f183f6-2fdb-4bba-ae5e-0159fb5607c3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316847814700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ac940dc-c513-4431-8ccc-d54a0cc188c2", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316852208000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48b8c94f-94a3-4702-9bbf-52997b3ed493", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker9", "startTime": 29316871044700, "endTime": 29327639915100}, "additional": {"children": ["dd5324d6-cdbd-4fd2-bfd4-596eb5f6466a", "17256128-697d-490c-b5ee-5c8027dd0601", "3b9daa07-5055-4424-8985-433617d6d1fd", "fa2727f0-3372-4498-bf56-91653e62c837", "69af5b28-61c3-46e1-a7c9-ebca48e02c86", "4591084b-6ab8-4f62-bade-165ec191505c", "65aaa150-365c-4b1d-aae9-d9fab0c84982", "3cffe8ca-c73a-498b-8bbe-5a55a923262f", "e2a27b6b-9961-4787-9e29-ee0d67e77a86"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "cad43100-87f7-47aa-a6cb-37d51bc27963", "logId": "f238600d-73f5-46ee-bb67-91fa05de2cd8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5b66c73a-f65d-4908-8682-bd4ee7807afe", "name": "entry : default@PreviewArkTS cost memory -0.8366470336914062", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316873385700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b6f5963-f6c3-4f27-9b35-7a66fd1c9a66", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29320492750600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd5324d6-cdbd-4fd2-bfd4-596eb5f6466a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29320493763800, "endTime": 29320493778400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "48b8c94f-94a3-4702-9bbf-52997b3ed493", "logId": "e3bf4b99-fbb2-4ac6-9533-b93038f3bf71"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3bf4b99-fbb2-4ac6-9533-b93038f3bf71", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29320493763800, "endTime": 29320493778400}, "additional": {"logType": "info", "children": [], "durationId": "dd5324d6-cdbd-4fd2-bfd4-596eb5f6466a", "parent": "f238600d-73f5-46ee-bb67-91fa05de2cd8"}}, {"head": {"id": "edaecfd7-b321-4b70-ac3d-c5583d75d86a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29324285073100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17256128-697d-490c-b5ee-5c8027dd0601", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29324287163100, "endTime": 29324287186300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "48b8c94f-94a3-4702-9bbf-52997b3ed493", "logId": "76b6b51a-231d-46a0-a6e5-ed444cb42161"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "76b6b51a-231d-46a0-a6e5-ed444cb42161", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29324287163100, "endTime": 29324287186300}, "additional": {"logType": "info", "children": [], "durationId": "17256128-697d-490c-b5ee-5c8027dd0601", "parent": "f238600d-73f5-46ee-bb67-91fa05de2cd8"}}, {"head": {"id": "f4a12554-b009-413c-a07c-060dd9289e3f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29324288132300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b9daa07-5055-4424-8985-433617d6d1fd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29324289453600, "endTime": 29324289472200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "48b8c94f-94a3-4702-9bbf-52997b3ed493", "logId": "a0a0e617-1893-44b3-8837-1c1ad8579769"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0a0e617-1893-44b3-8837-1c1ad8579769", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29324289453600, "endTime": 29324289472200}, "additional": {"logType": "info", "children": [], "durationId": "3b9daa07-5055-4424-8985-433617d6d1fd", "parent": "f238600d-73f5-46ee-bb67-91fa05de2cd8"}}, {"head": {"id": "1e4fb1eb-2d8a-47d7-adad-972b58d354c1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29324289617000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa2727f0-3372-4498-bf56-91653e62c837", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29324291298500, "endTime": 29324291316900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "48b8c94f-94a3-4702-9bbf-52997b3ed493", "logId": "ecc47a5a-33e6-4907-a6ab-d99ec2728661"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ecc47a5a-33e6-4907-a6ab-d99ec2728661", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29324291298500, "endTime": 29324291316900}, "additional": {"logType": "info", "children": [], "durationId": "fa2727f0-3372-4498-bf56-91653e62c837", "parent": "f238600d-73f5-46ee-bb67-91fa05de2cd8"}}, {"head": {"id": "86e6b378-48e9-4254-af6c-00a950db7dba", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29324291397300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69af5b28-61c3-46e1-a7c9-ebca48e02c86", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29324292566500, "endTime": 29324292593500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "48b8c94f-94a3-4702-9bbf-52997b3ed493", "logId": "bf0b9f1f-c1f4-4602-856c-1a8b4f4c43d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf0b9f1f-c1f4-4602-856c-1a8b4f4c43d3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29324292566500, "endTime": 29324292593500}, "additional": {"logType": "info", "children": [], "durationId": "69af5b28-61c3-46e1-a7c9-ebca48e02c86", "parent": "f238600d-73f5-46ee-bb67-91fa05de2cd8"}}, {"head": {"id": "593c4df4-093f-41fb-801b-9fb77f522e1c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29324292691400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4591084b-6ab8-4f62-bade-165ec191505c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29324294516200, "endTime": 29324294534700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "48b8c94f-94a3-4702-9bbf-52997b3ed493", "logId": "2f7b11c3-c375-46e7-b960-c849cb72e227"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f7b11c3-c375-46e7-b960-c849cb72e227", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29324294516200, "endTime": 29324294534700}, "additional": {"logType": "info", "children": [], "durationId": "4591084b-6ab8-4f62-bade-165ec191505c", "parent": "f238600d-73f5-46ee-bb67-91fa05de2cd8"}}, {"head": {"id": "251e254c-e078-4461-91ee-7c69e46f51bf", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29324294612300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65aaa150-365c-4b1d-aae9-d9fab0c84982", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29324295748900, "endTime": 29324295771200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "48b8c94f-94a3-4702-9bbf-52997b3ed493", "logId": "72ea72fb-2a4a-425f-90fa-4baf6fb8a54f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "72ea72fb-2a4a-425f-90fa-4baf6fb8a54f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29324295748900, "endTime": 29324295771200}, "additional": {"logType": "info", "children": [], "durationId": "65aaa150-365c-4b1d-aae9-d9fab0c84982", "parent": "f238600d-73f5-46ee-bb67-91fa05de2cd8"}}, {"head": {"id": "6c969405-a22f-4bb9-9a95-fca7bdaec3fb", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29325484767400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cffe8ca-c73a-498b-8bbe-5a55a923262f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29325486483900, "endTime": 29325486506200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "48b8c94f-94a3-4702-9bbf-52997b3ed493", "logId": "48961b9d-117b-46a8-8b73-ee0179fe4961"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48961b9d-117b-46a8-8b73-ee0179fe4961", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29325486483900, "endTime": 29325486506200}, "additional": {"logType": "info", "children": [], "durationId": "3cffe8ca-c73a-498b-8bbe-5a55a923262f", "parent": "f238600d-73f5-46ee-bb67-91fa05de2cd8"}}, {"head": {"id": "065e3a63-bbd6-4b4a-92e3-05ec79a6dae8", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327639001800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2a27b6b-9961-4787-9e29-ee0d67e77a86", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327639841600, "endTime": 29327639856900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "48b8c94f-94a3-4702-9bbf-52997b3ed493", "logId": "a8d0e7bd-67cb-4d53-b510-ad57d6823376"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8d0e7bd-67cb-4d53-b510-ad57d6823376", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327639841600, "endTime": 29327639856900}, "additional": {"logType": "info", "children": [], "durationId": "e2a27b6b-9961-4787-9e29-ee0d67e77a86", "parent": "f238600d-73f5-46ee-bb67-91fa05de2cd8"}}, {"head": {"id": "f238600d-73f5-46ee-bb67-91fa05de2cd8", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Worker9", "startTime": 29316871044700, "endTime": 29327639915100}, "additional": {"logType": "error", "children": ["e3bf4b99-fbb2-4ac6-9533-b93038f3bf71", "76b6b51a-231d-46a0-a6e5-ed444cb42161", "a0a0e617-1893-44b3-8837-1c1ad8579769", "ecc47a5a-33e6-4907-a6ab-d99ec2728661", "bf0b9f1f-c1f4-4602-856c-1a8b4f4c43d3", "2f7b11c3-c375-46e7-b960-c849cb72e227", "72ea72fb-2a4a-425f-90fa-4baf6fb8a54f", "48961b9d-117b-46a8-8b73-ee0179fe4961", "a8d0e7bd-67cb-4d53-b510-ad57d6823376"], "durationId": "48b8c94f-94a3-4702-9bbf-52997b3ed493", "parent": "f58ce23e-caa9-4288-8d7e-612c02d2c97b"}}, {"head": {"id": "d06360a1-993e-47a3-ac93-b424c7e996e4", "name": "default@PreviewArkTS watch work[9] failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327640103700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f58ce23e-caa9-4288-8d7e-612c02d2c97b", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316852197300, "endTime": 29327640226300}, "additional": {"logType": "error", "children": ["f238600d-73f5-46ee-bb67-91fa05de2cd8"], "durationId": "cad43100-87f7-47aa-a6cb-37d51bc27963"}}, {"head": {"id": "9e6829e5-96ab-484e-a892-cea8d91f2898", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327640303600}, "additional": {"logType": "debug", "children": [], "durationId": "cad43100-87f7-47aa-a6cb-37d51bc27963"}}, {"head": {"id": "47b0612b-a479-48d6-9fd3-821d1cd7844a", "name": "ERROR: stacktrace = Error: \u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:709:46\n Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/PaymentPage.ets:521:35\n Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown)\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/PaymentPage.ets:521:76\n Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown)\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:53:25\n Cannot find name 'WalletInfo'.\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:710:9\n Type '{ userId: number; toPhone: string; amount: number; payPassword: string; description: string; }' is not assignable to type 'WalletTransferRequest'.\n  Object literal may only specify known properties, and 'userId' does not exist in type 'WalletTransferRequest'.\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:165:7\n Only UI component syntax can be written in build method.\n    at handleResponse (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327640927300}, "additional": {"logType": "debug", "children": [], "durationId": "cad43100-87f7-47aa-a6cb-37d51bc27963"}}, {"head": {"id": "fe7be189-f61e-4283-924d-0da951cadca5", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327649577700, "endTime": 29327649628400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d44b14ff-6135-4c62-9971-fa3f8cb6285d", "logId": "9fd0efe1-9e81-4001-853b-7c833b47defa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9fd0efe1-9e81-4001-853b-7c833b47defa", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327649577700, "endTime": 29327649628400}, "additional": {"logType": "info", "children": [], "durationId": "fe7be189-f61e-4283-924d-0da951cadca5"}}, {"head": {"id": "13cea48b-957a-49b3-9d86-236c75d0d542", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29316567900600, "endTime": 29327649725800}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 14}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "b8c01fb3-c46e-4e45-ac3d-2f37d1739ced", "name": "BUILD FAILED in 11 s 82 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327649749900}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "b1ec3d03-3779-4b9f-ae09-9e49d3ecc4be", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327649874000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "650483ec-17ae-4a21-b70a-f2b1fea9fdfe", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327649936300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "108758a5-c460-4c9f-a4af-d14a315c4487", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327649963400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b70bc88-56de-4523-b68f-9a5e5afe3849", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327649986000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0341b416-86a4-4c85-bd81-7eecca5f7639", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327650016300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66a40cb2-ef36-495f-bacf-64e65ccb4da3", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327650036000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22049ad3-5991-4263-bd8f-4e4cab1f3b14", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327650055100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41a5eabd-e3a1-4991-aa27-eaf4767a4fa5", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327650072900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbe68f3f-b9a3-4579-8554-5e04c8b0b8c3", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327650642200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47851be9-f006-4a8a-b4bb-5462ec4324e5", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327650728000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c474a5d-d528-4392-a488-5a1b8a630e3f", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327654460700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5f169c6-d58c-4bf6-bd2d-47a21720bdb5", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327656256900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70c4d3b6-2d53-4bc6-89a4-2e13dae79dd9", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327657222000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cbc6610-fb7a-4f59-abca-3e26266d655a", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327657758500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "747a03c0-689f-42eb-b2f7-26803fa1d3d7", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327658973700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4341780-d2c5-4954-90ae-7e7fcb6451c8", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327674913000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc7e39bc-7b55-4049-8dd7-c55cc5f06cf6", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327676310400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1d7f217-0de1-4f94-bff8-5b29beeb8326", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327677566200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c917a5c4-7a7e-446a-a7b5-fdb4efe4fc70", "name": "Update task entry:default@PreviewArkTS output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327678834000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03f08d05-be5e-40f8-a2a5-56deabbec618", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:29 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327679404900}, "additional": {"logType": "debug", "children": []}}], "workLog": []}