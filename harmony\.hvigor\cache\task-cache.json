{":harmony:entry:default@PreBuild": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apiType\",\"_value\":\"stageMode\",\"_valueType\":\"string\",\"_hash\":\"699c7565645ea3ea8c88551a4926a3d5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"byteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"1918a3ccb645ccacd0aedd84fc6cf8a4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"codeType\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"d6dcf21a9f078b661283507536e57ce8\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleApiVersion\",\"_value\":15,\"_valueType\":\"number\",\"_hash\":\"fb2a1d73eb1fbae90a8cf98855837a9d\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileApiVersion\",\"_value\":15,\"_valueType\":\"number\",\"_hash\":\"f35d706752ca7c42be23eafc45024d27\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"configuration\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"d12f0038691f8f34d654391bbcee2f8e\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"configurationFile<PERSON><PERSON>\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"2b55287f40f7e8896b21bab4028e156b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"970a2695bffac1c5a4fa283dc36050e3\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceType\",\"_value\":[\"phone\",\"tablet\",\"2in1\"],\"_valueType\":\"object\",\"_hash\":\"82734343919a9219c848a93ce88dfb4d\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integrated_hsp\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"2538281751f182d9123d2ab28efaf9be\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isSupportOhpmProj\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"f3a249d7e3f751316e931b8a08b074b4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"mockConfigSources\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"c489be8273867a50afbc86c53d938c92\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleJsonOpt\",\"_value\":\"{\\\"jsonFilePath\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\src\\\\\\\\main\\\\\\\\module.json5\\\",\\\"profile\\\":{\\\"module\\\":{\\\"name\\\":\\\"entry\\\",\\\"type\\\":\\\"entry\\\",\\\"description\\\":\\\"$string:module_desc\\\",\\\"mainElement\\\":\\\"EntryAbility\\\",\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\",\\\"2in1\\\"],\\\"deliveryWithInstall\\\":true,\\\"installationFree\\\":false,\\\"pages\\\":\\\"$profile:main_pages\\\",\\\"requestPermissions\\\":[{\\\"name\\\":\\\"ohos.permission.INTERNET\\\",\\\"reason\\\":\\\"$string:internet_permission_reason\\\",\\\"usedScene\\\":{\\\"abilities\\\":[\\\"EntryAbility\\\"],\\\"when\\\":\\\"inuse\\\"}}],\\\"abilities\\\":[{\\\"name\\\":\\\"EntryAbility\\\",\\\"srcEntry\\\":\\\"./ets/entryability/EntryAbility.ets\\\",\\\"description\\\":\\\"$string:EntryAbility_desc\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:EntryAbility_label\\\",\\\"startWindowIcon\\\":\\\"$media:startIcon\\\",\\\"startWindowBackground\\\":\\\"$color:start_window_background\\\",\\\"exported\\\":true,\\\"skills\\\":[{\\\"entities\\\":[\\\"entity.system.home\\\"],\\\"actions\\\":[\\\"action.system.home\\\"]}]}],\\\"extensionAbilities\\\":[{\\\"name\\\":\\\"EntryBackupAbility\\\",\\\"srcEntry\\\":\\\"./ets/entrybackupability/EntryBackupAbility.ets\\\",\\\"type\\\":\\\"backup\\\",\\\"exported\\\":false,\\\"metadata\\\":[{\\\"name\\\":\\\"ohos.extension.backup\\\",\\\"resource\\\":\\\"$profile:backup_config\\\"}]}]}},\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\",\\\"2in1\\\"],\\\"deviceConfig\\\":\\\"deviceTypes\\\",\\\"configurationProfile\\\":\\\"module.json5\\\"}\",\"_valueType\":\"string\",\"_hash\":\"11e81840c2cea516ab14f0a4ddb13606\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"profileModuleName\",\"_value\":\"entry\",\"_valueType\":\"string\",\"_hash\":\"b174ea6ff5824844dde5ad92f6b3ef2b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"removePermissions\",\"_valueType\":\"undefined\",\"_hash\":\"0703cfc523a152c6195a8fd1935503e5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sdkToolchainsComponentVersion\",\"_value\":\"5.0.3.135\",\"_valueType\":\"string\",\"_hash\":\"1a0ee02ad70bbe8a0246b9e84bfd4cbd\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sourceRoots\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"a369c115d2c4122f3819759804ec9d35\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetStatusCode\",\"_value\":2,\"_valueType\":\"number\",\"_hash\":\"5a9255c0f4ee50904a9349ebafca8369\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"7d270d0ce7ae5c6e2e32760cb396ea5a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@PreBuild", "_key": ":harmony:entry:default@PreBuild", "_executionId": ":harmony:entry:default@PreBuild:1750824639168", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\harmony\\AppScope\\app.json5", {"isDirectory": false, "fileSnapShotHashValue": "2970e180e89f0960686c2daefdaf9d0f"}], ["D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\module.json5", {"isDirectory": false, "fileSnapShotHashValue": "1ef8fb50441356e76d6d73d459353aa3"}], ["D:\\vue\\daxiangmuwallet\\harmony\\build-profile.json5", {"fileSnapShotHashValue": "54278948d390cc5916d8657b167ad439"}], ["D:\\vue\\daxiangmuwallet\\harmony\\entry\\build-profile.json5", {"fileSnapShotHashValue": "91abe431fc73b7628df3ca6b04b251fb"}], ["D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources\\base\\profile\\main_pages.json", {"fileSnapShotHashValue": "54b436d5b6fca30df2a3c1f3fdd835c4"}], ["D:\\vue\\daxiangmuwallet\\harmony\\hvigor\\hvigor-config.json5", {"isDirectory": true, "fileSnapShotHashValue": "e14d994b54b74c0ea2bbf7fe5dd0844e"}], ["D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\mock\\mock-config.json5", {"isDirectory": false, "fileSnapShotHashValue": "cefda810c64eaacd3309e849c045b713"}], ["D:\\vue\\daxiangmuwallet\\harmony\\entry\\oh-package.json5", {"fileSnapShotHashValue": "8ffda34409d008706114e2650257696b"}], ["D:\\vue\\daxiangmuwallet\\harmony\\oh-package.json5", {"fileSnapShotHashValue": "a7fa5c2e1b62f1b8a68d52caf86b3f48"}]]}, "_outputFiles": {"dataType": "Map", "value": []}}, ":harmony:entry:default@MergeProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"appJsonOpt\",\"_value\":\"{\\\"app\\\":{\\\"bundleName\\\":\\\"com.example.harmony\\\",\\\"vendor\\\":\\\"E-Wallet Team\\\",\\\"versionCode\\\":1000000,\\\"versionName\\\":\\\"1.0.0\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:app_name\\\",\\\"description\\\":\\\"$string:app_description\\\"}}\",\"_valueType\":\"string\",\"_hash\":\"d8bf8915204eb34411c5095a6f759681\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"asanEnable\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"bc2129ba20a21b7e5234139ede1b4d7b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildProfileAbilities\",\"_valueType\":\"undefined\",\"_hash\":\"40d5093f345351dd6d67ce5d6a209345\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildRoot\",\"_value\":\".preview\",\"_valueType\":\"string\",\"_hash\":\"88e9a315669657b02bc470a13f92befe\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersion\",\"_value\":15,\"_valueType\":\"number\",\"_hash\":\"44964f8abd318606862a2cee062554fe\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"hwasan<PERSON><PERSON><PERSON>\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"d35c8440e915c3a94c482ddd6f7af075\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integratedHsp\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"e67900c25b1f9fb70cc779de77dc6912\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a69aa552317c7a6e087cb84a5722e050\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isDebug\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"edbf05a2d2be2c385e75d9565a48d419\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isHarModule\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a15b7a3ed818faa99a4a10d67f52cb72\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleJsonOpt\",\"_value\":\"{\\\"module\\\":{\\\"name\\\":\\\"entry\\\",\\\"type\\\":\\\"entry\\\",\\\"description\\\":\\\"$string:module_desc\\\",\\\"mainElement\\\":\\\"EntryAbility\\\",\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\",\\\"2in1\\\"],\\\"deliveryWithInstall\\\":true,\\\"installationFree\\\":false,\\\"pages\\\":\\\"$profile:main_pages\\\",\\\"requestPermissions\\\":[{\\\"name\\\":\\\"ohos.permission.INTERNET\\\",\\\"reason\\\":\\\"$string:internet_permission_reason\\\",\\\"usedScene\\\":{\\\"abilities\\\":[\\\"EntryAbility\\\"],\\\"when\\\":\\\"inuse\\\"}}],\\\"abilities\\\":[{\\\"name\\\":\\\"EntryAbility\\\",\\\"srcEntry\\\":\\\"./ets/entryability/EntryAbility.ets\\\",\\\"description\\\":\\\"$string:EntryAbility_desc\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:EntryAbility_label\\\",\\\"startWindowIcon\\\":\\\"$media:startIcon\\\",\\\"startWindowBackground\\\":\\\"$color:start_window_background\\\",\\\"exported\\\":true,\\\"skills\\\":[{\\\"entities\\\":[\\\"entity.system.home\\\"],\\\"actions\\\":[\\\"action.system.home\\\"]}]}],\\\"extensionAbilities\\\":[{\\\"name\\\":\\\"EntryBackupAbility\\\",\\\"srcEntry\\\":\\\"./ets/entrybackupability/EntryBackupAbility.ets\\\",\\\"type\\\":\\\"backup\\\",\\\"exported\\\":false,\\\"metadata\\\":[{\\\"name\\\":\\\"ohos.extension.backup\\\",\\\"resource\\\":\\\"$profile:backup_config\\\"}]}]}}\",\"_valueType\":\"string\",\"_hash\":\"e0aa1132f06f8ec686a7fcdcdb02041e\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"multiProjects\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"555604752defc243b4e4c55d1549fc06\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectConfigAppOpt\",\"_valueType\":\"undefined\",\"_hash\":\"b52997704fa206ed96a13a1f2e464a85\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"releaseType\",\"_value\":\"Release\",\"_valueType\":\"string\",\"_hash\":\"bbcabdda034e97584f8c36f85b3ec517\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"removePermissions\",\"_valueType\":\"undefined\",\"_hash\":\"0703cfc523a152c6195a8fd1935503e5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetSdkVersion\",\"_value\":15,\"_valueType\":\"number\",\"_hash\":\"b2877a8a5e4d85b48b0208a17da3ae75\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"tsan<PERSON>nable\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"086cac69f102cdd9ee25e54982ad7b76\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ubsanEnable\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"3b15da42c5f4b695fbd1d0b43191764a\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@MergeProfile", "_key": ":harmony:entry:default@MergeProfile", "_executionId": ":harmony:entry:default@MergeProfile:1750824639206", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\harmony\\AppScope\\app.json5", {"fileSnapShotHashValue": "2970e180e89f0960686c2daefdaf9d0f"}], ["D:\\vue\\daxiangmuwallet\\harmony\\build-profile.json5", {"fileSnapShotHashValue": "54278948d390cc5916d8657b167ad439"}], ["D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "1ef8fb50441356e76d6d73d459353aa3"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "93b5e77fd537121335fd51520ec18445"}]]}}, ":harmony:entry:default@CreateBuildProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildMode\",\"_value\":\"Debug\",\"_valueType\":\"string\",\"_hash\":\"8120d22ada0d6de22b101e1f4ea16e81\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildModeName\",\"_value\":\"debug\",\"_valueType\":\"string\",\"_hash\":\"3f0246ea410fd9efa9fc7196cca045e3\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildProfileFields\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"21090e125326cef17357e44b789a1ab5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectOhosConfigAppOpt\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"7613f1b2cb16d78bb723d12882b0d923\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@CreateBuildProfile", "_key": ":harmony:entry:default@CreateBuildProfile", "_executionId": ":harmony:entry:default@CreateBuildProfile:1750824639214", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\harmony\\AppScope\\app.json5", {"fileSnapShotHashValue": "2970e180e89f0960686c2daefdaf9d0f"}], ["D:\\vue\\daxiangmuwallet\\harmony\\build-profile.json5", {"fileSnapShotHashValue": "54278948d390cc5916d8657b167ad439"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "7b01e7b3d9329db879b975f56d1ad83c"}]]}}, ":harmony:entry:default@GeneratePkgContextInfo": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-@ohos/hamock\",\"_value\":\"566d0d1ba1afb93928c8984a8fae6421\",\"_valueType\":\"string\",\"_hash\":\"52638b5b8d5967d85f7d558e6c0897dd\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-@ohos/hypium\",\"_value\":\"69dbd9141bebef47c71175aada1bd7bf\",\"_valueType\":\"string\",\"_hash\":\"65eb1ae89d72758b386a93dddd5db61d\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-entry\",\"_value\":\"a8164913e569f3b3b2485e1811ddf44d\",\"_valueType\":\"string\",\"_hash\":\"6fbb0d2287cd1f34051162075393f37a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@GeneratePkgContextInfo", "_key": ":harmony:entry:default@GeneratePkgContextInfo", "_executionId": ":harmony:entry:default@GeneratePkgContextInfo:1750824639227", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"isDirectory": false, "fileSnapShotHashValue": "c6a980c7c1c2a166f47228a491f1d930"}]]}}, ":harmony:entry:default@ProcessProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"arkEnable\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"750b4bda198545a67903dfb3f6a00a95\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersionStage\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"713e499a13beffe12f1dfb936f957a2a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileMode\",\"_value\":\"esmodule\",\"_valueType\":\"string\",\"_hash\":\"dacafc8e0b77a950178a0b4d142dc32c\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"dependency\",\"_value\":\"[]\",\"_valueType\":\"string\",\"_hash\":\"ac54f3d4ced2d4c1d666d40e4f7c454a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceTypes\",\"_value\":[\"phone\",\"tablet\",\"2in1\"],\"_valueType\":\"object\",\"_hash\":\"955f8760c6b7289b81ed107c2c4df075\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@ProcessProfile", "_key": ":harmony:entry:default@ProcessProfile", "_executionId": ":harmony:entry:default@ProcessProfile:1750824639232", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "93b5e77fd537121335fd51520ec18445"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json", {"fileSnapShotHashValue": "d776d07d2b5ac8586aa4d2920456833d"}]]}}, ":harmony:entry:default@ProcessRouterMap": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"byteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"1918a3ccb645ccacd0aedd84fc6cf8a4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"obfuscated\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"a69c27b9cf01a6710d3662cfe180239f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@ProcessRouterMap", "_key": ":harmony:entry:default@ProcessRouterMap", "_executionId": ":harmony:entry:default@ProcessRouterMap:1750824639336", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\harmony\\entry\\oh-package.json5", {"fileSnapShotHashValue": "8ffda34409d008706114e2650257696b"}], ["D:\\vue\\daxiangmuwallet\\harmony\\oh-package.json5", {"fileSnapShotHashValue": "a7fa5c2e1b62f1b8a68d52caf86b3f48"}], ["D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "1ef8fb50441356e76d6d73d459353aa3"}], ["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"fileSnapShotHashValue": "c6a980c7c1c2a166f47228a491f1d930"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\router_map\\default\\temp-router-map.json", {"fileSnapShotHashValue": "bdd687db483c49612dcadfdf8cd4bffb"}], ["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\router_map\\default\\loader-router-map.json", {"fileSnapShotHashValue": "4c1ca5eccee07c37b3ff498768e4890f"}]]}}, ":harmony:entry:default@GenerateLoaderJson": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"anBuildMode\",\"_value\":\"type\",\"_valueType\":\"string\",\"_hash\":\"de241a1eec94a2a622ff1c89f32846a2\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apPath\",\"_value\":\"D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\modules.ap\",\"_valueType\":\"string\",\"_hash\":\"3202d480355d80282e6ad1522dbb6359\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleApiVersion\",\"_value\":15,\"_valueType\":\"number\",\"_hash\":\"fb2a1d73eb1fbae90a8cf98855837a9d\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileApiVersion\",\"_value\":15,\"_valueType\":\"number\",\"_hash\":\"f35d706752ca7c42be23eafc45024d27\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileMode\",\"_value\":\"esmodule\",\"_valueType\":\"string\",\"_hash\":\"dacafc8e0b77a950178a0b4d142dc32c\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"fallbackAnBuild\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a63e98bb3f368ced6ed4d5579ea7ca39\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"harNameOhmMap\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"0faf006bccbcdc2c7f04ff2d8c87894f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"hspNameOhmMap\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"0e2d87e0c1ed279c66bc3efb8683e5d1\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a69aa552317c7a6e087cb84a5722e050\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isByteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"c5f9b9e5cabee4253d52d0d01ca64ba2\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isFullCompilationEnabled\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"98b683d049e4e7fc32ef1be5321fe0b6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isHarWithCoverage\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"7224a86bd9c5f83cb9a1a61584afcfb4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isOhosTest\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"d82a43074b4d7726f9a69dbce1ae80d2\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"modulePathMap\",\"_value\":\"{\\\"entry\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\harmony\\\\\\\\entry\\\"}\",\"_valueType\":\"string\",\"_hash\":\"2643540d793d4974e4a273fabb9553f9\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"module_dependencies\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"974757f304b5bfd1c1454ea7a38cd0d5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needSubmitArkTsWidget\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"dc1ab65720a503a3d9098eab280b7116\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"nodeModulesPath\",\"_value\":\"D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\node_modules\",\"_valueType\":\"string\",\"_hash\":\"d8d260f30d49a1e5bab754b6e7cc6c29\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"overrides\",\"_valueType\":\"undefined\",\"_hash\":\"0e8f66f8eb79c6f33fb153c3fc3942f4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"patchConfig\",\"_valueType\":\"undefined\",\"_hash\":\"4620e35a57f3f6f55564cea6f6128e50\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectRootPath\",\"_value\":\"D:\\\\vue\\\\daxiangmuwallet\\\\harmony\",\"_valueType\":\"string\",\"_hash\":\"e19f1ca3924f6de8a3f45244dd32670b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"project_dependencies\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"e15496bf1de2273597f444f07f1ca6d5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"shouldTreatHarAsHap\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"24c9a48f68bd9cc73238825ca10c9629\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetConfig\",\"_value\":\"{\\\"name\\\":\\\"default\\\"}\",\"_valueType\":\"string\",\"_hash\":\"44f0c01d44e2bbf4013c5bb1f232e1fe\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@GenerateLoaderJson", "_key": ":harmony:entry:default@GenerateLoaderJson", "_executionId": ":harmony:entry:default@GenerateLoaderJson:1750824639361", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\mock\\mock-config.json5", {"fileSnapShotHashValue": "cefda810c64eaacd3309e849c045b713"}], ["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"fileSnapShotHashValue": "c6a980c7c1c2a166f47228a491f1d930"}], ["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\router_map\\default\\temp-router-map.json", {"fileSnapShotHashValue": "bdd687db483c49612dcadfdf8cd4bffb"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json", {"isDirectory": false, "fileSnapShotHashValue": "2bf5cf499286863bcff0393da7712d51"}]]}}, ":harmony:entry:default@PreviewCompileResource": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"PREVIEWER_REPLACE_PAGE\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"PREVIEWER_REPLACE_SRCPATH\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"TARGET_CONFIG\",\"_value\":\"{\\\"name\\\":\\\"default\\\"}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@PreviewCompileResource", "_key": ":harmony:entry:default@PreviewCompileResource", "_executionId": ":harmony:entry:default@PreviewCompileResource:1750839089815", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources", {"fileSnapShotHashValue": "e315a69dfd16a27ca9485331eeb34c03"}], ["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "93b5e77fd537121335fd51520ec18445"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default", {"isDirectory": true, "fileSnapShotHashValue": "0535e1f4520a0b6aa5cb0fd6c63ffa1a"}], ["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\r\\default", {"isDirectory": true, "fileSnapShotHashValue": "27d78b63f5c2ff1c658e6cc99ef9606d"}]]}}, ":harmony:entry:default@CopyPreviewProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@CopyPreviewProfile", "_key": ":harmony:entry:default@CopyPreviewProfile", "_executionId": ":harmony:entry:default@CopyPreviewProfile:1750839090146", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources\\base\\profile", {"fileSnapShotHashValue": "ba8f1e8a503030e0543abcebe4dcf974"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", {"fileSnapShotHashValue": "2768975aed8550620f40f46e59a4cac8"}]]}}, ":harmony:entry:default@PreviewUpdateAssets": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"previewBuildConfigJson\",\"_value\":\"{\\\"deviceType\\\":\\\"phone,tablet,2in1\\\",\\\"buildMode\\\":\\\"debug\\\",\\\"note\\\":\\\"false\\\",\\\"logLevel\\\":\\\"3\\\",\\\"isPreview\\\":\\\"true\\\",\\\"checkEntry\\\":\\\"true\\\",\\\"localPropertiesPath\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\harmony\\\\\\\\local.properties\\\",\\\"Path\\\":\\\"D:\\\\\\\\app\\\\\\\\devecostudio\\\\\\\\DevEco Studio\\\\\\\\tools\\\\\\\\node\\\\\\\\\\\",\\\"aceProfilePath\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\resources\\\\\\\\base\\\\\\\\profile\\\",\\\"hapMode\\\":\\\"false\\\",\\\"img2bin\\\":\\\"true\\\",\\\"projectProfilePath\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\harmony\\\\\\\\build-profile.json5\\\",\\\"watchMode\\\":\\\"true\\\",\\\"appResource\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\ResourceTable.txt\\\",\\\"aceBuildJson\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\loader\\\\\\\\default\\\\\\\\loader.json\\\",\\\"aceModuleRoot\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\src\\\\\\\\main\\\\\\\\ets\\\",\\\"aceSoPath\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\.preview\\\\\\\\cache\\\\\\\\nativeDependencies.txt\\\",\\\"cachePath\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\.preview\\\\\\\\cache\\\\\\\\.default\\\",\\\"aceModuleBuild\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\assets\\\\\\\\default\\\\\\\\ets\\\",\\\"aceModuleJsonPath\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\module.json\\\",\\\"stageRouterConfig\\\":{\\\"paths\\\":[\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\module.json\\\",\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\resources\\\\\\\\base\\\\\\\\profile\\\\\\\\main_pages.json\\\"],\\\"contents\\\":[\\\"{\\\\\\\"module\\\\\\\":{\\\\\\\"pages\\\\\\\":\\\\\\\"$profile:main_pages\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"entry\\\\\\\"}}\\\",\\\"{\\\\\\\"src\\\\\\\":[\\\\\\\"pages/Index\\\\\\\",\\\\\\\"pages/LoginPage\\\\\\\",\\\\\\\"pages/RegisterPage\\\\\\\",\\\\\\\"pages/ForgotPasswordPage\\\\\\\",\\\\\\\"pages/MainPage\\\\\\\",\\\\\\\"pages/BankCardPage\\\\\\\",\\\\\\\"pages/BankCardDetailPage\\\\\\\",\\\\\\\"pages/AddBankCardPage\\\\\\\",\\\\\\\"pages/TransactionListPage\\\\\\\",\\\\\\\"pages/TransactionDetailPage\\\\\\\",\\\\\\\"pages/WalletOperationPage\\\\\\\",\\\\\\\"pages/PaymentPage\\\\\\\",\\\\\\\"pages/SettingsPage\\\\\\\",\\\\\\\"pages/ChangePayPasswordPage\\\\\\\",\\\\\\\"pages/PayLimitSettingPage\\\\\\\",\\\\\\\"pages/BankSelectorPage\\\\\\\",\\\\\\\"pages/CardTypeSelectorPage\\\\\\\",\\\\\\\"pages/HelpCenterPage\\\\\\\",\\\\\\\"pages/AboutPage\\\\\\\"]}\\\"]}}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@PreviewUpdateAssets", "_key": ":harmony:entry:default@PreviewUpdateAssets", "_executionId": ":harmony:entry:default@PreviewUpdateAssets:*************", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", {"isDirectory": false, "fileSnapShotHashValue": "15ab3890c02854dd3d59191f33a2f4d0"}], ["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", {"isDirectory": false, "fileSnapShotHashValue": "b7c48bdb0907ed057ca2013ddc53ff50"}]]}}, ":harmony:entry:default@PreviewArkTS": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"debuggable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isArk\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needCoverageInsert\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ark.tsImportSendable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"caseSensitive<PERSON><PERSON><PERSON>\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersionStage\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"autoLazyImport\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"allowEmptyBundleName\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"arkTsWdiget\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"OBFUSCATION_ENABLE\",\"_value\":\"undefined\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"copyCodeResourceEnable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"copyCodeResourceExcludes\",\"_value\":[],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"noExternalImportByPath\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": false, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@PreviewArkTS", "_key": ":harmony:entry:default@PreviewArkTS", "_executionId": ":harmony:entry:default@PreviewArkTS:1750839090248", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default", {"isDirectory": true, "fileSnapShotHashValue": "e67caa100969add09c9811873d37e13f"}], ["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt", {"fileSnapShotHashValue": "50ade341ff74f0b6c39f3943514b1d1a"}], ["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json", {"fileSnapShotHashValue": "d776d07d2b5ac8586aa4d2920456833d"}], ["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", {"isDirectory": true, "fileSnapShotHashValue": "2768975aed8550620f40f46e59a4cac8"}], ["D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets", {"isDirectory": true, "fileSnapShotHashValue": "036302176d63abb014ccad3380e65eb8"}], ["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"fileSnapShotHashValue": "c6a980c7c1c2a166f47228a491f1d930"}], ["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "7b01e7b3d9329db879b975f56d1ad83c"}], ["D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\mock\\mock-config.json5", {"isDirectory": false, "fileSnapShotHashValue": "cefda810c64eaacd3309e849c045b713"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}}