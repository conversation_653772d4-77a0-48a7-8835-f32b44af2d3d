if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface WalletOperationPage_Params {
    operationType?: string;
    amount?: string;
    payPassword?: string;
    selectedCardId?: number;
    bankCards?: BankCard[];
    walletBalance?: number;
    payLimit?: number;
    isLoading?: boolean;
    toPhone?: string;
    description?: string;
    receiveAmount?: string;
    receiveDescription?: string;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { WalletApi } from "@normalized:N&&&entry/src/main/ets/api/WalletApi&";
import { BankCardApi } from "@normalized:N&&&entry/src/main/ets/api/BankCardApi&";
import { storageManager } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
import { globalStateManager, RefreshTypes } from "@normalized:N&&&entry/src/main/ets/common/utils/EventManager&";
import type { WalletRechargeRequest, WalletWithdrawRequest, WalletTransferRequest, BankCard, WalletOperationParams, WalletInfo } from '../common/types/index';
class WalletOperationPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__operationType = new ObservedPropertySimplePU('recharge', this, "operationType");
        this.__amount = new ObservedPropertySimplePU('', this, "amount");
        this.__payPassword = new ObservedPropertySimplePU('', this, "payPassword");
        this.__selectedCardId = new ObservedPropertySimplePU(-1, this, "selectedCardId");
        this.__bankCards = new ObservedPropertyObjectPU([], this, "bankCards");
        this.__walletBalance = new ObservedPropertySimplePU(0, this, "walletBalance");
        this.__payLimit = new ObservedPropertySimplePU(0, this, "payLimit");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__toPhone = new ObservedPropertySimplePU('', this, "toPhone");
        this.__description = new ObservedPropertySimplePU('', this, "description");
        this.__receiveAmount = new ObservedPropertySimplePU('', this, "receiveAmount");
        this.__receiveDescription = new ObservedPropertySimplePU('', this, "receiveDescription");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: WalletOperationPage_Params) {
        if (params.operationType !== undefined) {
            this.operationType = params.operationType;
        }
        if (params.amount !== undefined) {
            this.amount = params.amount;
        }
        if (params.payPassword !== undefined) {
            this.payPassword = params.payPassword;
        }
        if (params.selectedCardId !== undefined) {
            this.selectedCardId = params.selectedCardId;
        }
        if (params.bankCards !== undefined) {
            this.bankCards = params.bankCards;
        }
        if (params.walletBalance !== undefined) {
            this.walletBalance = params.walletBalance;
        }
        if (params.payLimit !== undefined) {
            this.payLimit = params.payLimit;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.toPhone !== undefined) {
            this.toPhone = params.toPhone;
        }
        if (params.description !== undefined) {
            this.description = params.description;
        }
        if (params.receiveAmount !== undefined) {
            this.receiveAmount = params.receiveAmount;
        }
        if (params.receiveDescription !== undefined) {
            this.receiveDescription = params.receiveDescription;
        }
    }
    updateStateVars(params: WalletOperationPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__operationType.purgeDependencyOnElmtId(rmElmtId);
        this.__amount.purgeDependencyOnElmtId(rmElmtId);
        this.__payPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedCardId.purgeDependencyOnElmtId(rmElmtId);
        this.__bankCards.purgeDependencyOnElmtId(rmElmtId);
        this.__walletBalance.purgeDependencyOnElmtId(rmElmtId);
        this.__payLimit.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__toPhone.purgeDependencyOnElmtId(rmElmtId);
        this.__description.purgeDependencyOnElmtId(rmElmtId);
        this.__receiveAmount.purgeDependencyOnElmtId(rmElmtId);
        this.__receiveDescription.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__operationType.aboutToBeDeleted();
        this.__amount.aboutToBeDeleted();
        this.__payPassword.aboutToBeDeleted();
        this.__selectedCardId.aboutToBeDeleted();
        this.__bankCards.aboutToBeDeleted();
        this.__walletBalance.aboutToBeDeleted();
        this.__payLimit.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__toPhone.aboutToBeDeleted();
        this.__description.aboutToBeDeleted();
        this.__receiveAmount.aboutToBeDeleted();
        this.__receiveDescription.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __operationType: ObservedPropertySimplePU<string>; // 'recharge' | 'withdraw' | 'transfer' | 'receive'
    get operationType() {
        return this.__operationType.get();
    }
    set operationType(newValue: string) {
        this.__operationType.set(newValue);
    }
    private __amount: ObservedPropertySimplePU<string>;
    get amount() {
        return this.__amount.get();
    }
    set amount(newValue: string) {
        this.__amount.set(newValue);
    }
    private __payPassword: ObservedPropertySimplePU<string>;
    get payPassword() {
        return this.__payPassword.get();
    }
    set payPassword(newValue: string) {
        this.__payPassword.set(newValue);
    }
    private __selectedCardId: ObservedPropertySimplePU<number>;
    get selectedCardId() {
        return this.__selectedCardId.get();
    }
    set selectedCardId(newValue: number) {
        this.__selectedCardId.set(newValue);
    }
    private __bankCards: ObservedPropertyObjectPU<BankCard[]>;
    get bankCards() {
        return this.__bankCards.get();
    }
    set bankCards(newValue: BankCard[]) {
        this.__bankCards.set(newValue);
    }
    private __walletBalance: ObservedPropertySimplePU<number>;
    get walletBalance() {
        return this.__walletBalance.get();
    }
    set walletBalance(newValue: number) {
        this.__walletBalance.set(newValue);
    }
    private __payLimit: ObservedPropertySimplePU<number>;
    get payLimit() {
        return this.__payLimit.get();
    }
    set payLimit(newValue: number) {
        this.__payLimit.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    // 转账相关
    private __toPhone: ObservedPropertySimplePU<string>;
    get toPhone() {
        return this.__toPhone.get();
    }
    set toPhone(newValue: string) {
        this.__toPhone.set(newValue);
    }
    private __description: ObservedPropertySimplePU<string>;
    get description() {
        return this.__description.get();
    }
    set description(newValue: string) {
        this.__description.set(newValue);
    }
    // 收钱相关
    private __receiveAmount: ObservedPropertySimplePU<string>;
    get receiveAmount() {
        return this.__receiveAmount.get();
    }
    set receiveAmount(newValue: string) {
        this.__receiveAmount.set(newValue);
    }
    private __receiveDescription: ObservedPropertySimplePU<string>;
    get receiveDescription() {
        return this.__receiveDescription.get();
    }
    set receiveDescription(newValue: string) {
        this.__receiveDescription.set(newValue);
    }
    aboutToAppear() {
        // 检查路由参数，确定操作类型
        const params = router.getParams() as WalletOperationParams;
        if (params?.operationType) {
            this.operationType = params.operationType;
        }
        this.loadData();
    }
    async loadData() {
        try {
            this.isLoading = true;
            // 获取当前用户信息
            const userInfo = await storageManager.getUserInfo();
            if (!userInfo) {
                promptAction.showToast({ message: '请先登录' });
                router.back();
                return;
            }
            // 加载银行卡列表
            this.bankCards = await BankCardApi.getCardList(userInfo.userId);
            // 加载钱包余额
            const walletInfo: WalletInfo = await WalletApi.getBalance();
            this.walletBalance = walletInfo?.balance || 0;
            // 加载用户支付限额
            this.payLimit = userInfo?.payLimit || 0;
        }
        catch (error) {
            console.error('加载数据失败:', error);
            promptAction.showToast({ message: '加载数据失败' });
        }
        finally {
            this.isLoading = false;
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(78:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        // 顶部导航栏
        this.TopNavigationView.bind(this)();
        // 操作类型切换标签
        this.OperationTypeTabsView.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.LoadingView.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    // 根据操作类型显示不同内容
                    this.OperationContentView.bind(this)();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    TopNavigationView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(99:5)", "entry");
            Row.width('100%');
            Row.height(56);
            Row.padding({ left: 16, right: 16 });
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(100:7)", "entry");
            Button.width(40);
            Button.height(40);
            Button.backgroundColor('transparent');
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777238, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(101:9)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor('#333333');
        }, Image);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getPageTitle());
            Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(113:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 占位，保持标题居中
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(121:7)", "entry");
            // 占位，保持标题居中
            Row.width(40);
            // 占位，保持标题居中
            Row.height(40);
        }, Row);
        // 占位，保持标题居中
        Row.pop();
        Row.pop();
    }
    OperationTypeTabsView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(131:5)", "entry");
            Row.width('100%');
            Row.padding({ left: 16, right: 16, top: 12, bottom: 12 });
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const type = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Button.createWithLabel(this.getOperationTypeText(type));
                    Button.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(133:9)", "entry");
                    Button.fontSize(14);
                    Button.fontColor(this.operationType === type ? '#FFFFFF' : '#666666');
                    Button.backgroundColor(this.operationType === type ? '#1976D2' : '#F8F9FA');
                    Button.borderRadius(20);
                    Button.height(36);
                    Button.padding({ left: 16, right: 16 });
                    Button.margin({ right: 8 });
                    Button.onClick(() => {
                        this.operationType = type;
                        this.resetForm();
                    });
                }, Button);
                Button.pop();
            };
            this.forEachUpdateFunction(elmtId, ['recharge', 'withdraw', 'transfer', 'receive'], forEachItemGenFunction);
        }, ForEach);
        ForEach.pop();
        Row.pop();
    }
    LoadingView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(154:5)", "entry");
            Column.width('100%');
            Column.height(200);
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            LoadingProgress.create();
            LoadingProgress.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(155:7)", "entry");
            LoadingProgress.width(40);
            LoadingProgress.height(40);
            LoadingProgress.color('#1976D2');
        }, LoadingProgress);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('加载中...');
            Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(160:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.margin({ top: 12 });
        }, Text);
        Text.pop();
        Column.pop();
    }
    OperationContentView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(173:5)", "entry");
            Column.layoutWeight(1);
            Column.padding(16);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.operationType === 'recharge') {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.RechargeContentView.bind(this)();
                });
            }
            else if (this.operationType === 'withdraw') {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.WithdrawContentView.bind(this)();
                });
            }
            else if (this.operationType === 'transfer') {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.TransferContentView.bind(this)();
                });
            }
            else if (this.operationType === 'receive') {
                this.ifElseBranchUpdateFunction(3, () => {
                    this.ReceiveContentView.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(4, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    /**
     * 获取页面标题
     */
    getPageTitle(): string {
        switch (this.operationType) {
            case 'recharge': return '钱包充值';
            case 'withdraw': return '钱包提现';
            case 'transfer': return '钱包转账';
            case 'receive': return '收钱码';
            default: return '钱包操作';
        }
    }
    /**
     * 获取操作类型文本
     */
    getOperationTypeText(type: string): string {
        switch (type) {
            case 'recharge': return '充值';
            case 'withdraw': return '提现';
            case 'transfer': return '转账';
            case 'receive': return '收钱';
            default: return type;
        }
    }
    /**
     * 重置表单
     */
    resetForm() {
        this.amount = '';
        this.payPassword = '';
        this.selectedCardId = -1;
        this.toPhone = '';
        this.description = '';
        this.receiveAmount = '';
        this.receiveDescription = '';
    }
    RechargeContentView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(229:5)", "entry");
            Column.width('100%');
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        // 钱包余额显示
        this.WalletBalanceView.bind(this)();
        // 充值金额输入
        this.AmountInputView.bind(this)('请输入充值金额');
        // 银行卡选择
        this.BankCardSelectorView.bind(this)();
        // 支付密码输入
        this.PayPasswordInputView.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 充值按钮
            Button.createWithLabel('立即充值');
            Button.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(243:7)", "entry");
            // 充值按钮
            Button.width('100%');
            // 充值按钮
            Button.height(48);
            // 充值按钮
            Button.fontSize(16);
            // 充值按钮
            Button.fontColor('#FFFFFF');
            // 充值按钮
            Button.backgroundColor('#1976D2');
            // 充值按钮
            Button.borderRadius(8);
            // 充值按钮
            Button.margin({ top: 24 });
            // 充值按钮
            Button.enabled(!this.isLoading && !!this.amount && this.selectedCardId > 0 && !!this.payPassword);
            // 充值按钮
            Button.onClick(() => {
                this.performRecharge();
            });
        }, Button);
        // 充值按钮
        Button.pop();
        Column.pop();
    }
    WithdrawContentView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(262:5)", "entry");
            Column.width('100%');
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        // 钱包余额显示
        this.WalletBalanceView.bind(this)();
        // 提现金额输入
        this.AmountInputView.bind(this)('请输入提现金额');
        // 银行卡选择
        this.BankCardSelectorView.bind(this)();
        // 支付密码输入
        this.PayPasswordInputView.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 提现按钮
            Button.createWithLabel('立即提现');
            Button.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(276:7)", "entry");
            // 提现按钮
            Button.width('100%');
            // 提现按钮
            Button.height(48);
            // 提现按钮
            Button.fontSize(16);
            // 提现按钮
            Button.fontColor('#FFFFFF');
            // 提现按钮
            Button.backgroundColor('#FF9800');
            // 提现按钮
            Button.borderRadius(8);
            // 提现按钮
            Button.margin({ top: 24 });
            // 提现按钮
            Button.enabled(!this.isLoading && !!this.amount && this.selectedCardId > 0 && !!this.payPassword);
            // 提现按钮
            Button.onClick(() => {
                this.performWithdraw();
            });
        }, Button);
        // 提现按钮
        Button.pop();
        Column.pop();
    }
    TransferContentView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(295:5)", "entry");
            Column.width('100%');
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        // 钱包余额显示
        this.WalletBalanceView.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 收款人手机号输入
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(300:7)", "entry");
            // 收款人手机号输入
            Column.width('100%');
            // 收款人手机号输入
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('收款人手机号');
            Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(301:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入收款人手机号', text: this.toPhone });
            TextInput.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(307:9)", "entry");
            TextInput.fontSize(16);
            TextInput.borderRadius(8);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.onChange((value: string) => {
                this.toPhone = value;
            });
        }, TextInput);
        // 收款人手机号输入
        Column.pop();
        // 转账金额输入
        this.AmountInputView.bind(this)('请输入转账金额');
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 转账说明
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(322:7)", "entry");
            // 转账说明
            Column.width('100%');
            // 转账说明
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('转账说明（可选）');
            Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(323:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入转账说明', text: this.description });
            TextInput.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(329:9)", "entry");
            TextInput.fontSize(16);
            TextInput.borderRadius(8);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.onChange((value: string) => {
                this.description = value;
            });
        }, TextInput);
        // 转账说明
        Column.pop();
        // 支付密码输入
        this.PayPasswordInputView.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 转账按钮
            Button.createWithLabel('立即转账');
            Button.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(344:7)", "entry");
            // 转账按钮
            Button.width('100%');
            // 转账按钮
            Button.height(48);
            // 转账按钮
            Button.fontSize(16);
            // 转账按钮
            Button.fontColor('#FFFFFF');
            // 转账按钮
            Button.backgroundColor('#4CAF50');
            // 转账按钮
            Button.borderRadius(8);
            // 转账按钮
            Button.margin({ top: 24 });
            // 转账按钮
            Button.enabled(!this.isLoading && !!this.amount && !!this.toPhone && !!this.payPassword);
            // 转账按钮
            Button.onClick(() => {
                this.performTransfer();
            });
        }, Button);
        // 转账按钮
        Button.pop();
        Column.pop();
    }
    ReceiveContentView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(363:5)", "entry");
            Column.width('100%');
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 收钱金额输入
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(365:7)", "entry");
            // 收钱金额输入
            Column.width('100%');
            // 收钱金额输入
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('收钱金额');
            Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(366:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入收钱金额', text: this.receiveAmount });
            TextInput.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(372:9)", "entry");
            TextInput.fontSize(16);
            TextInput.borderRadius(8);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.onChange((value: string) => {
                this.receiveAmount = value;
            });
        }, TextInput);
        // 收钱金额输入
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 收钱说明
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(384:7)", "entry");
            // 收钱说明
            Column.width('100%');
            // 收钱说明
            Column.margin({ bottom: 24 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('收钱说明（可选）');
            Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(385:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入收钱说明', text: this.receiveDescription });
            TextInput.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(391:9)", "entry");
            TextInput.fontSize(16);
            TextInput.borderRadius(8);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.onChange((value: string) => {
                this.receiveDescription = value;
            });
        }, TextInput);
        // 收钱说明
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 收钱码显示区域
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(403:7)", "entry");
            // 收钱码显示区域
            Column.width('100%');
            // 收钱码显示区域
            Column.height(200);
            // 收钱码显示区域
            Column.justifyContent(FlexAlign.Center);
            // 收钱码显示区域
            Column.alignItems(HorizontalAlign.Center);
            // 收钱码显示区域
            Column.backgroundColor('#FFFFFF');
            // 收钱码显示区域
            Column.borderRadius(12);
            // 收钱码显示区域
            Column.border({ width: 2, color: '#4CAF50', style: BorderStyle.Dashed });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💰');
            Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(404:9)", "entry");
            Text.fontSize(80);
            Text.fontColor('#4CAF50');
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('向我付钱');
            Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(409:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.receiveAmount) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`¥${this.receiveAmount}`);
                        Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(416:11)", "entry");
                        Text.fontSize(24);
                        Text.fontWeight(FontWeight.Bold);
                        Text.fontColor('#4CAF50');
                        Text.margin({ bottom: 8 });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.receiveDescription) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.receiveDescription);
                        Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(424:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#666666');
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        // 收钱码显示区域
        Column.pop();
        Column.pop();
    }
    WalletBalanceView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(443:5)", "entry");
            Column.width('100%');
            Column.padding(16);
            Column.backgroundColor('#FFFFFF');
            Column.borderRadius(12);
            Column.margin({ bottom: 16 });
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包余额');
            Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(444:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`¥${this.walletBalance.toFixed(2)}`);
            Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(449:7)", "entry");
            Text.fontSize(24);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1976D2');
        }, Text);
        Text.pop();
        Column.pop();
    }
    AmountInputView(placeholder: string, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(464:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('金额');
            Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(465:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: placeholder, text: this.amount });
            TextInput.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(471:7)", "entry");
            TextInput.fontSize(16);
            TextInput.borderRadius(8);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.type(InputType.Number);
            TextInput.onChange((value: string) => {
                this.amount = value;
            });
        }, TextInput);
        Column.pop();
    }
    BankCardSelectorView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(486:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('选择银行卡');
            Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(487:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.bankCards.length === 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('添加银行卡');
                        Button.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(494:9)", "entry");
                        Button.width('100%');
                        Button.height(48);
                        Button.fontSize(14);
                        Button.fontColor('#1976D2');
                        Button.backgroundColor('#E3F2FD');
                        Button.borderRadius(8);
                        Button.onClick(() => {
                            router.pushUrl({ url: 'pages/AddBankCardPage' });
                        });
                    }, Button);
                    Button.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(505:9)", "entry");
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const card = _item;
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Row.create();
                                Row.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(507:13)", "entry");
                                Row.width('100%');
                                Row.padding(12);
                                Row.backgroundColor('#FFFFFF');
                                Row.borderRadius(8);
                                Row.margin({ bottom: 8 });
                                Row.onClick(() => {
                                    this.selectedCardId = card.cardId;
                                });
                            }, Row);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Radio.create({ value: card.cardId.toString(), group: 'bankCardGroup' });
                                Radio.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(508:15)", "entry");
                                Radio.checked(this.selectedCardId === card.cardId);
                                Radio.onChange((isChecked: boolean) => {
                                    if (isChecked) {
                                        this.selectedCardId = card.cardId;
                                    }
                                });
                                Radio.margin({ right: 12 });
                            }, Radio);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Column.create();
                                Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(517:15)", "entry");
                                Column.layoutWeight(1);
                                Column.alignItems(HorizontalAlign.Start);
                            }, Column);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create(card.bankName);
                                Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(518:17)", "entry");
                                Text.fontSize(16);
                                Text.fontColor('#333333');
                                Text.fontWeight(FontWeight.Medium);
                                Text.alignSelf(ItemAlign.Start);
                            }, Text);
                            Text.pop();
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create(`**** **** **** ${card.cardNo.slice(-4)}`);
                                Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(524:17)", "entry");
                                Text.fontSize(14);
                                Text.fontColor('#666666');
                                Text.alignSelf(ItemAlign.Start);
                                Text.margin({ top: 4 });
                            }, Text);
                            Text.pop();
                            Column.pop();
                            Row.pop();
                        };
                        this.forEachUpdateFunction(elmtId, this.bankCards, forEachItemGenFunction);
                    }, ForEach);
                    ForEach.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    PayPasswordInputView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(551:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付密码');
            Text.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(552:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入支付密码', text: this.payPassword });
            TextInput.debugLine("entry/src/main/ets/pages/WalletOperationPage.ets(558:7)", "entry");
            TextInput.fontSize(16);
            TextInput.borderRadius(8);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.type(InputType.Password);
            TextInput.onChange((value: string) => {
                this.payPassword = value;
            });
        }, TextInput);
        Column.pop();
    }
    /**
     * 执行充值操作
     */
    async performRecharge() {
        try {
            this.isLoading = true;
            const amountNum = parseFloat(this.amount);
            if (isNaN(amountNum) || amountNum <= 0) {
                promptAction.showToast({ message: '请输入有效的充值金额' });
                return;
            }
            if (amountNum > this.payLimit) {
                promptAction.showToast({ message: `充值金额不能超过支付限额 ¥${this.payLimit}` });
                return;
            }
            // 获取用户信息
            const userInfo = await storageManager.getUserInfo();
            if (!userInfo) {
                promptAction.showToast({ message: "用户信息获取失败" });
                return;
            }
            const request: WalletRechargeRequest = {
                userId: userInfo.userId,
                amount: amountNum,
                cardId: this.selectedCardId,
                payPassword: this.payPassword
            };
            await WalletApi.recharge(request);
            promptAction.showToast({ message: '充值成功' });
            // 刷新钱包余额
            globalStateManager.notifyRefresh(RefreshTypes.WALLET_BALANCE);
            // 返回上一页
            router.back();
        }
        catch (error) {
            console.error('充值失败:', error);
            promptAction.showToast({ message: '充值失败，请重试' });
        }
        finally {
            this.isLoading = false;
        }
    }
    /**
     * 执行提现操作
     */
    async performWithdraw() {
        try {
            this.isLoading = true;
            const amountNum = parseFloat(this.amount);
            if (isNaN(amountNum) || amountNum <= 0) {
                promptAction.showToast({ message: '请输入有效的提现金额' });
                return;
            }
            if (amountNum > this.walletBalance) {
                promptAction.showToast({ message: '提现金额不能超过钱包余额' });
                return;
            }
            if (amountNum > this.payLimit) {
                promptAction.showToast({ message: `提现金额不能超过支付限额 ¥${this.payLimit}` });
                return;
            }
            // 获取用户信息
            const userInfo = await storageManager.getUserInfo();
            if (!userInfo) {
                promptAction.showToast({ message: "用户信息获取失败" });
                return;
            }
            const request: WalletWithdrawRequest = {
                userId: userInfo.userId,
                amount: amountNum,
                cardId: this.selectedCardId,
                payPassword: this.payPassword
            };
            await WalletApi.withdraw(request);
            promptAction.showToast({ message: '提现成功' });
            // 刷新钱包余额
            globalStateManager.notifyRefresh(RefreshTypes.WALLET_BALANCE);
            // 返回上一页
            router.back();
        }
        catch (error) {
            console.error('提现失败:', error);
            promptAction.showToast({ message: '提现失败，请重试' });
        }
        finally {
            this.isLoading = false;
        }
    }
    /**
     * 执行转账操作
     */
    async performTransfer() {
        try {
            this.isLoading = true;
            const amountNum = parseFloat(this.amount);
            if (isNaN(amountNum) || amountNum <= 0) {
                promptAction.showToast({ message: '请输入有效的转账金额' });
                return;
            }
            if (amountNum > this.walletBalance) {
                promptAction.showToast({ message: '转账金额不能超过钱包余额' });
                return;
            }
            if (amountNum > this.payLimit) {
                promptAction.showToast({ message: `转账金额不能超过支付限额 ¥${this.payLimit}` });
                return;
            }
            // 验证手机号格式
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(this.toPhone)) {
                promptAction.showToast({ message: '请输入有效的手机号' });
                return;
            }
            // 获取用户信息
            const userInfo = await storageManager.getUserInfo();
            if (!userInfo) {
                promptAction.showToast({ message: "用户信息获取失败" });
                return;
            }
            const request: WalletTransferRequest = {
                userId: userInfo.userId,
                toPhone: this.toPhone,
                amount: amountNum,
                payPassword: this.payPassword,
                description: this.description
            };
            await WalletApi.transfer(request);
            promptAction.showToast({ message: '转账成功' });
            // 刷新钱包余额
            globalStateManager.notifyRefresh(RefreshTypes.WALLET_BALANCE);
            // 返回上一页
            router.back();
        }
        catch (error) {
            console.error('转账失败:', error);
            promptAction.showToast({ message: '转账失败，请重试' });
        }
        finally {
            this.isLoading = false;
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "WalletOperationPage";
    }
}
registerNamedRoute(() => new WalletOperationPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/WalletOperationPage", pageFullPath: "entry/src/main/ets/pages/WalletOperationPage", integratedHsp: "false", moduleType: "followWithHap" });
