{"version": "2.0", "ppid": 21780, "events": [{"head": {"id": "0edddc1c-6f7f-4826-b6c5-f8430fc1d26c", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30609828582500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46a47df3-f777-44bf-8e93-20b85ef0159c", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30609844404600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96117222-35b1-4ab8-b45d-879f09523f30", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30609844916500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33f0c501-0800-4b0a-bb6f-1e072a1edf80", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692190173400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57f8d782-72ad-4449-8da7-c869316afd62", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692202726700, "endTime": 30692427110600}, "additional": {"children": ["3a28831c-619a-48e5-a226-6520e09028fe", "2a6c8291-3dce-42cd-aaf0-6e860be4427e", "c8ea890f-0ecc-4b78-88ac-8ad3cad415db", "f5e6748e-80c1-4305-9bdc-0b693c1bfdba", "31d6892b-7dbb-4006-a9ec-cbbfad98ab60", "c5089468-48ab-43d4-88bf-fb4cb31a6494", "e8e0389d-49da-4b95-aa3a-a1e71c7b2e65"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "f5bf8742-982c-48e1-b9b7-187a1e47abac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a28831c-619a-48e5-a226-6520e09028fe", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692202729500, "endTime": 30692226790200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "57f8d782-72ad-4449-8da7-c869316afd62", "logId": "1d3edc04-6fd4-4654-8c2d-5fd42de1216f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a6c8291-3dce-42cd-aaf0-6e860be4427e", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692226815000, "endTime": 30692425218500}, "additional": {"children": ["21b0447f-d7b6-46a6-964f-5d45a45de341", "41e8e8c0-9cc0-43e4-b08f-3a580f514281", "f7735f20-aa25-4b0b-a30e-0bb37222c985", "81c3df63-fcf3-4bd7-96c3-7fe371698bc6", "9cb997fe-499c-4386-8d66-a5828fe123bd", "90537853-345a-4df2-a107-41f8d7194c35", "881d8fe4-bd59-4340-a773-eec6f8f458ac", "2557b468-1120-4305-97e2-c9ffd18d29ce", "3b23a412-dd2a-4e46-a5ee-8e531bc75ce0"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "57f8d782-72ad-4449-8da7-c869316afd62", "logId": "601cf351-294e-4c52-a476-9e35c204ca8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8ea890f-0ecc-4b78-88ac-8ad3cad415db", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692425244600, "endTime": 30692427099300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "57f8d782-72ad-4449-8da7-c869316afd62", "logId": "1d64bb07-47ab-4809-bebb-ce497500f1a1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f5e6748e-80c1-4305-9bdc-0b693c1bfdba", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692427104400, "endTime": 30692427107000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "57f8d782-72ad-4449-8da7-c869316afd62", "logId": "d98428d5-5183-4df8-a2d6-676f0b516570"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "31d6892b-7dbb-4006-a9ec-cbbfad98ab60", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692208707000, "endTime": 30692208747000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "57f8d782-72ad-4449-8da7-c869316afd62", "logId": "bea63816-e5ba-4d5e-af3d-82758fd3ee37"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bea63816-e5ba-4d5e-af3d-82758fd3ee37", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692208707000, "endTime": 30692208747000}, "additional": {"logType": "info", "children": [], "durationId": "31d6892b-7dbb-4006-a9ec-cbbfad98ab60", "parent": "f5bf8742-982c-48e1-b9b7-187a1e47abac"}}, {"head": {"id": "c5089468-48ab-43d4-88bf-fb4cb31a6494", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692219004300, "endTime": 30692219026100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "57f8d782-72ad-4449-8da7-c869316afd62", "logId": "df175a95-9ee3-4007-a788-1630d9e551e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df175a95-9ee3-4007-a788-1630d9e551e7", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692219004300, "endTime": 30692219026100}, "additional": {"logType": "info", "children": [], "durationId": "c5089468-48ab-43d4-88bf-fb4cb31a6494", "parent": "f5bf8742-982c-48e1-b9b7-187a1e47abac"}}, {"head": {"id": "30ecab84-4d6e-4734-9486-108024a017cb", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692219086500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e9c9a44-5e0f-4f94-a29b-ece4350f0e3f", "name": "Cache service initialization finished in 8 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692226632800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d3edc04-6fd4-4654-8c2d-5fd42de1216f", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692202729500, "endTime": 30692226790200}, "additional": {"logType": "info", "children": [], "durationId": "3a28831c-619a-48e5-a226-6520e09028fe", "parent": "f5bf8742-982c-48e1-b9b7-187a1e47abac"}}, {"head": {"id": "21b0447f-d7b6-46a6-964f-5d45a45de341", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692237714300, "endTime": 30692237724600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a6c8291-3dce-42cd-aaf0-6e860be4427e", "logId": "9292a7a9-c386-4748-801f-7fb89c27e34e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41e8e8c0-9cc0-43e4-b08f-3a580f514281", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692237743900, "endTime": 30692243956500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a6c8291-3dce-42cd-aaf0-6e860be4427e", "logId": "ac6c8ce7-b19b-4edb-b189-85722eb4ac7a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7735f20-aa25-4b0b-a30e-0bb37222c985", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692243973700, "endTime": 30692361632200}, "additional": {"children": ["a13fe581-4d1a-4aeb-be03-64a38ad5ef91", "038a7885-604b-44f6-9a02-4fb2f8b10db6", "035b25fd-696f-4c2c-9d57-e2b2e26f5928"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a6c8291-3dce-42cd-aaf0-6e860be4427e", "logId": "74d6f170-412a-49a4-ad48-a7511cfad92b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "81c3df63-fcf3-4bd7-96c3-7fe371698bc6", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692361643900, "endTime": 30692384255500}, "additional": {"children": ["834c6f24-2873-479f-99a4-1ba4ca31ddfd"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a6c8291-3dce-42cd-aaf0-6e860be4427e", "logId": "0469f09c-b62d-4c35-abd2-d174a06fbcdd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9cb997fe-499c-4386-8d66-a5828fe123bd", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692384262000, "endTime": 30692396494500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a6c8291-3dce-42cd-aaf0-6e860be4427e", "logId": "d62ca590-c707-407f-b66f-1474b28cf469"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "90537853-345a-4df2-a107-41f8d7194c35", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692397692200, "endTime": 30692408781900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a6c8291-3dce-42cd-aaf0-6e860be4427e", "logId": "873d3de6-1dd8-4e3c-8c88-293f6962f33a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "881d8fe4-bd59-4340-a773-eec6f8f458ac", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692408815200, "endTime": 30692424972700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a6c8291-3dce-42cd-aaf0-6e860be4427e", "logId": "3b7ccf76-54b2-4f9b-8d3e-15fe0621d448"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2557b468-1120-4305-97e2-c9ffd18d29ce", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692425017300, "endTime": 30692425201800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a6c8291-3dce-42cd-aaf0-6e860be4427e", "logId": "9b66472a-6fb9-42c5-abfc-cecfd9193bd8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9292a7a9-c386-4748-801f-7fb89c27e34e", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692237714300, "endTime": 30692237724600}, "additional": {"logType": "info", "children": [], "durationId": "21b0447f-d7b6-46a6-964f-5d45a45de341", "parent": "601cf351-294e-4c52-a476-9e35c204ca8d"}}, {"head": {"id": "ac6c8ce7-b19b-4edb-b189-85722eb4ac7a", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692237743900, "endTime": 30692243956500}, "additional": {"logType": "info", "children": [], "durationId": "41e8e8c0-9cc0-43e4-b08f-3a580f514281", "parent": "601cf351-294e-4c52-a476-9e35c204ca8d"}}, {"head": {"id": "a13fe581-4d1a-4aeb-be03-64a38ad5ef91", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692245233300, "endTime": 30692245254200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f7735f20-aa25-4b0b-a30e-0bb37222c985", "logId": "a4fc831c-7790-47ea-ba45-ca22d66f6c9e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4fc831c-7790-47ea-ba45-ca22d66f6c9e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692245233300, "endTime": 30692245254200}, "additional": {"logType": "info", "children": [], "durationId": "a13fe581-4d1a-4aeb-be03-64a38ad5ef91", "parent": "74d6f170-412a-49a4-ad48-a7511cfad92b"}}, {"head": {"id": "038a7885-604b-44f6-9a02-4fb2f8b10db6", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692250398800, "endTime": 30692361058000}, "additional": {"children": ["9d796c61-1768-4ebe-9f36-826bd5b58349", "580c60b4-bd10-4cff-94c2-01970dd87cb9"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f7735f20-aa25-4b0b-a30e-0bb37222c985", "logId": "90722bf3-e532-4d87-bc40-1fa7bdc76bde"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d796c61-1768-4ebe-9f36-826bd5b58349", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692250401900, "endTime": 30692258346200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "038a7885-604b-44f6-9a02-4fb2f8b10db6", "logId": "cf700221-9bd1-4e07-b853-193cbdf1b189"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "580c60b4-bd10-4cff-94c2-01970dd87cb9", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692258368500, "endTime": 30692361046200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "038a7885-604b-44f6-9a02-4fb2f8b10db6", "logId": "17d0d4eb-17c6-45e8-989d-ca8b655c0d75"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4369507e-51a5-403b-b92d-c38317a3c400", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692250415400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df8aac2f-d115-430a-846f-329b534ae2ae", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692258196100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf700221-9bd1-4e07-b853-193cbdf1b189", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692250401900, "endTime": 30692258346200}, "additional": {"logType": "info", "children": [], "durationId": "9d796c61-1768-4ebe-9f36-826bd5b58349", "parent": "90722bf3-e532-4d87-bc40-1fa7bdc76bde"}}, {"head": {"id": "1cd7256d-2f7c-4e11-927a-2e009e82efd4", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692258384600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24b5b3d8-3ac7-4d51-b509-6491e446c740", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692270004300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6c5b869-ad83-45be-b25e-5662518d8a87", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692271140900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d10e99a-abf1-4563-9512-1b6a637be039", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692271432300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba9a08be-270f-407a-9ba7-af6b49ef6849", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692271561400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e00ff21d-e299-415e-b30f-0442c8787bfd", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692275041800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "406614bc-41ae-4135-a2eb-402a2d102fda", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692283255400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c670df81-2ffd-4f48-8e03-7b6625466bb4", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692300093100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e8d51ad-0cf9-4fc5-805a-be722f365628", "name": "Sdk init in 44 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692328532100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "381647c9-f817-4797-8f7c-02e3d1fd967e", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692328644800}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 37}, "markType": "other"}}, {"head": {"id": "628f1e33-d51b-456f-914b-471ec5e6575d", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692328673900}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 37}, "markType": "other"}}, {"head": {"id": "2b865550-15bc-4811-8291-5d87378997d7", "name": "Project task initialization takes 30 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692360700200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e42cb01-a0c3-4c20-99fe-3776903a352b", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692360805900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "625a4178-efa9-4f34-9024-b577a59eb7a8", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692360836500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93b35d29-db09-4ae6-8507-6e664ba5d7c0", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692361004600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17d0d4eb-17c6-45e8-989d-ca8b655c0d75", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692258368500, "endTime": 30692361046200}, "additional": {"logType": "info", "children": [], "durationId": "580c60b4-bd10-4cff-94c2-01970dd87cb9", "parent": "90722bf3-e532-4d87-bc40-1fa7bdc76bde"}}, {"head": {"id": "90722bf3-e532-4d87-bc40-1fa7bdc76bde", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692250398800, "endTime": 30692361058000}, "additional": {"logType": "info", "children": ["cf700221-9bd1-4e07-b853-193cbdf1b189", "17d0d4eb-17c6-45e8-989d-ca8b655c0d75"], "durationId": "038a7885-604b-44f6-9a02-4fb2f8b10db6", "parent": "74d6f170-412a-49a4-ad48-a7511cfad92b"}}, {"head": {"id": "035b25fd-696f-4c2c-9d57-e2b2e26f5928", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692361589500, "endTime": 30692361603900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f7735f20-aa25-4b0b-a30e-0bb37222c985", "logId": "930ee839-61a6-4276-a828-230d6e515036"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "930ee839-61a6-4276-a828-230d6e515036", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692361589500, "endTime": 30692361603900}, "additional": {"logType": "info", "children": [], "durationId": "035b25fd-696f-4c2c-9d57-e2b2e26f5928", "parent": "74d6f170-412a-49a4-ad48-a7511cfad92b"}}, {"head": {"id": "74d6f170-412a-49a4-ad48-a7511cfad92b", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692243973700, "endTime": 30692361632200}, "additional": {"logType": "info", "children": ["a4fc831c-7790-47ea-ba45-ca22d66f6c9e", "90722bf3-e532-4d87-bc40-1fa7bdc76bde", "930ee839-61a6-4276-a828-230d6e515036"], "durationId": "f7735f20-aa25-4b0b-a30e-0bb37222c985", "parent": "601cf351-294e-4c52-a476-9e35c204ca8d"}}, {"head": {"id": "834c6f24-2873-479f-99a4-1ba4ca31ddfd", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692362128200, "endTime": 30692384245400}, "additional": {"children": ["856f53a8-3acf-4c8c-b5e8-949b486fd706", "aa9d19f9-b460-48d8-9b81-c3f5eb30e65f", "9878a8dd-fcf3-4895-aa85-1829d02b34f8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "81c3df63-fcf3-4bd7-96c3-7fe371698bc6", "logId": "70c9f39f-e081-4f5b-b508-f3127a695dfa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "856f53a8-3acf-4c8c-b5e8-949b486fd706", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692365263300, "endTime": 30692365281800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "834c6f24-2873-479f-99a4-1ba4ca31ddfd", "logId": "40a433b5-de9a-42e7-a972-3<PERSON><PERSON><PERSON>514b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40a433b5-de9a-42e7-a972-3<PERSON><PERSON><PERSON>514b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692365263300, "endTime": 30692365281800}, "additional": {"logType": "info", "children": [], "durationId": "856f53a8-3acf-4c8c-b5e8-949b486fd706", "parent": "70c9f39f-e081-4f5b-b508-f3127a695dfa"}}, {"head": {"id": "aa9d19f9-b460-48d8-9b81-c3f5eb30e65f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692367751100, "endTime": 30692382822100}, "additional": {"children": ["8824d736-4371-451f-84c8-069d9d1e2c94", "8462c73f-4b6d-4c25-9500-126fd7352f82"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "834c6f24-2873-479f-99a4-1ba4ca31ddfd", "logId": "edef743b-459a-46c9-997f-d75ddcfc4588"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8824d736-4371-451f-84c8-069d9d1e2c94", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692367752800, "endTime": 30692371157600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aa9d19f9-b460-48d8-9b81-c3f5eb30e65f", "logId": "aef42957-27fe-4ed8-95c0-8146630ff05e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8462c73f-4b6d-4c25-9500-126fd7352f82", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692371179800, "endTime": 30692382809200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aa9d19f9-b460-48d8-9b81-c3f5eb30e65f", "logId": "fe8ca3b1-641d-42de-b80e-137245ab0f65"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "05b24872-b097-4bc8-8beb-8c3d7e4f4c30", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692367757700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8532519-5156-4421-9794-eba99b2de6b1", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692370981700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aef42957-27fe-4ed8-95c0-8146630ff05e", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692367752800, "endTime": 30692371157600}, "additional": {"logType": "info", "children": [], "durationId": "8824d736-4371-451f-84c8-069d9d1e2c94", "parent": "edef743b-459a-46c9-997f-d75ddcfc4588"}}, {"head": {"id": "aaedf5d5-3707-4e96-b806-891b4071f959", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692371194100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e39e7dde-0f0e-47aa-95e4-43bbda33ea27", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692378200000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63955e5f-b20d-4d43-b4d3-aa7ba22815bf", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692378332400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4bc9274-4dd7-4a77-9389-d7c3987db5c8", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692378543900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa370f58-3c2e-4f66-98cd-2e71b2ebf56c", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692378720600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21d4afa2-cbbc-41d0-b742-0f48e70b3d42", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692378835500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a53ccbc5-eac1-4f96-934a-f02639438161", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692378935600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b634499-e6b0-4a69-b040-bf8d04ea6c3e", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692378978000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c6f9be0-9c59-49cd-add7-ba2cc91ea9c3", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692382583600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "daff85aa-7077-4486-91af-72808a689a4b", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692382717800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa89dd9f-1b9c-439d-a3e4-c6542a8ad2da", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692382755800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1495210-0c95-4b42-bb00-fd3aa1859304", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692382783900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe8ca3b1-641d-42de-b80e-137245ab0f65", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692371179800, "endTime": 30692382809200}, "additional": {"logType": "info", "children": [], "durationId": "8462c73f-4b6d-4c25-9500-126fd7352f82", "parent": "edef743b-459a-46c9-997f-d75ddcfc4588"}}, {"head": {"id": "edef743b-459a-46c9-997f-d75ddcfc4588", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692367751100, "endTime": 30692382822100}, "additional": {"logType": "info", "children": ["aef42957-27fe-4ed8-95c0-8146630ff05e", "fe8ca3b1-641d-42de-b80e-137245ab0f65"], "durationId": "aa9d19f9-b460-48d8-9b81-c3f5eb30e65f", "parent": "70c9f39f-e081-4f5b-b508-f3127a695dfa"}}, {"head": {"id": "9878a8dd-fcf3-4895-aa85-1829d02b34f8", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692384198700, "endTime": 30692384230700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "834c6f24-2873-479f-99a4-1ba4ca31ddfd", "logId": "df5e0f8f-820c-48a9-a62e-2a4b33392ad3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df5e0f8f-820c-48a9-a62e-2a4b33392ad3", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692384198700, "endTime": 30692384230700}, "additional": {"logType": "info", "children": [], "durationId": "9878a8dd-fcf3-4895-aa85-1829d02b34f8", "parent": "70c9f39f-e081-4f5b-b508-f3127a695dfa"}}, {"head": {"id": "70c9f39f-e081-4f5b-b508-f3127a695dfa", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692362128200, "endTime": 30692384245400}, "additional": {"logType": "info", "children": ["40a433b5-de9a-42e7-a972-3<PERSON><PERSON><PERSON>514b", "edef743b-459a-46c9-997f-d75ddcfc4588", "df5e0f8f-820c-48a9-a62e-2a4b33392ad3"], "durationId": "834c6f24-2873-479f-99a4-1ba4ca31ddfd", "parent": "0469f09c-b62d-4c35-abd2-d174a06fbcdd"}}, {"head": {"id": "0469f09c-b62d-4c35-abd2-d174a06fbcdd", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692361643900, "endTime": 30692384255500}, "additional": {"logType": "info", "children": ["70c9f39f-e081-4f5b-b508-f3127a695dfa"], "durationId": "81c3df63-fcf3-4bd7-96c3-7fe371698bc6", "parent": "601cf351-294e-4c52-a476-9e35c204ca8d"}}, {"head": {"id": "275e2f86-afaa-47d4-acda-41dcb696278d", "name": "watch files: [\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\hvigorfile.ts',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692396115300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de891e10-a144-47d8-9dda-780463738b6f", "name": "hvigorfile, resolve hvigorfile dependencies in 13 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692396440200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d62ca590-c707-407f-b66f-1474b28cf469", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692384262000, "endTime": 30692396494500}, "additional": {"logType": "info", "children": [], "durationId": "9cb997fe-499c-4386-8d66-a5828fe123bd", "parent": "601cf351-294e-4c52-a476-9e35c204ca8d"}}, {"head": {"id": "3b23a412-dd2a-4e46-a5ee-8e531bc75ce0", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692397364300, "endTime": 30692397672600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a6c8291-3dce-42cd-aaf0-6e860be4427e", "logId": "9d5635f5-6a9e-4dee-8186-70b9938f688c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef27dfb6-9238-4051-9950-0737fdd2535a", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692397404300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d5635f5-6a9e-4dee-8186-70b9938f688c", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692397364300, "endTime": 30692397672600}, "additional": {"logType": "info", "children": [], "durationId": "3b23a412-dd2a-4e46-a5ee-8e531bc75ce0", "parent": "601cf351-294e-4c52-a476-9e35c204ca8d"}}, {"head": {"id": "83970032-18a1-42e5-8e46-4b6dc5c2a7e3", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692399693400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f524b0f-f9d1-43c2-ba47-331cf72e99a9", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692407475200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "873d3de6-1dd8-4e3c-8c88-293f6962f33a", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692397692200, "endTime": 30692408781900}, "additional": {"logType": "info", "children": [], "durationId": "90537853-345a-4df2-a107-41f8d7194c35", "parent": "601cf351-294e-4c52-a476-9e35c204ca8d"}}, {"head": {"id": "0cb7a733-5f24-48f9-b4d6-7908f4f57eb7", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692408832700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2596ac84-7650-4e85-9f69-04f37431dd5a", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692417315800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee4092fe-0889-425c-a231-8ba7a0efaecc", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692417459000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ff23efd-101f-4b49-a722-bbb3a66479d9", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692417675200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb1f761e-af56-4609-9f30-d0267cb35573", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692420757100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f635dc61-af9e-42eb-8f7b-cb680137ba9b", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692420893300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b7ccf76-54b2-4f9b-8d3e-15fe0621d448", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692408815200, "endTime": 30692424972700}, "additional": {"logType": "info", "children": [], "durationId": "881d8fe4-bd59-4340-a773-eec6f8f458ac", "parent": "601cf351-294e-4c52-a476-9e35c204ca8d"}}, {"head": {"id": "11f0cdfc-10ef-4bf5-9753-974e92e8d7cf", "name": "Configuration phase cost:188 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692425052200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b66472a-6fb9-42c5-abfc-cecfd9193bd8", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692425017300, "endTime": 30692425201800}, "additional": {"logType": "info", "children": [], "durationId": "2557b468-1120-4305-97e2-c9ffd18d29ce", "parent": "601cf351-294e-4c52-a476-9e35c204ca8d"}}, {"head": {"id": "601cf351-294e-4c52-a476-9e35c204ca8d", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692226815000, "endTime": 30692425218500}, "additional": {"logType": "info", "children": ["9292a7a9-c386-4748-801f-7fb89c27e34e", "ac6c8ce7-b19b-4edb-b189-85722eb4ac7a", "74d6f170-412a-49a4-ad48-a7511cfad92b", "0469f09c-b62d-4c35-abd2-d174a06fbcdd", "d62ca590-c707-407f-b66f-1474b28cf469", "873d3de6-1dd8-4e3c-8c88-293f6962f33a", "3b7ccf76-54b2-4f9b-8d3e-15fe0621d448", "9b66472a-6fb9-42c5-abfc-cecfd9193bd8", "9d5635f5-6a9e-4dee-8186-70b9938f688c"], "durationId": "2a6c8291-3dce-42cd-aaf0-6e860be4427e", "parent": "f5bf8742-982c-48e1-b9b7-187a1e47abac"}}, {"head": {"id": "e8e0389d-49da-4b95-aa3a-a1e71c7b2e65", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692427053900, "endTime": 30692427081300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "57f8d782-72ad-4449-8da7-c869316afd62", "logId": "ea7f8fdd-573c-43a4-825a-744a2a0fed28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea7f8fdd-573c-43a4-825a-744a2a0fed28", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692427053900, "endTime": 30692427081300}, "additional": {"logType": "info", "children": [], "durationId": "e8e0389d-49da-4b95-aa3a-a1e71c7b2e65", "parent": "f5bf8742-982c-48e1-b9b7-187a1e47abac"}}, {"head": {"id": "1d64bb07-47ab-4809-bebb-ce497500f1a1", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692425244600, "endTime": 30692427099300}, "additional": {"logType": "info", "children": [], "durationId": "c8ea890f-0ecc-4b78-88ac-8ad3cad415db", "parent": "f5bf8742-982c-48e1-b9b7-187a1e47abac"}}, {"head": {"id": "d98428d5-5183-4df8-a2d6-676f0b516570", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692427104400, "endTime": 30692427107000}, "additional": {"logType": "info", "children": [], "durationId": "f5e6748e-80c1-4305-9bdc-0b693c1bfdba", "parent": "f5bf8742-982c-48e1-b9b7-187a1e47abac"}}, {"head": {"id": "f5bf8742-982c-48e1-b9b7-187a1e47abac", "name": "init", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692202726700, "endTime": 30692427110600}, "additional": {"logType": "info", "children": ["1d3edc04-6fd4-4654-8c2d-5fd42de1216f", "601cf351-294e-4c52-a476-9e35c204ca8d", "1d64bb07-47ab-4809-bebb-ce497500f1a1", "d98428d5-5183-4df8-a2d6-676f0b516570", "bea63816-e5ba-4d5e-af3d-82758fd3ee37", "df175a95-9ee3-4007-a788-1630d9e551e7", "ea7f8fdd-573c-43a4-825a-744a2a0fed28"], "durationId": "57f8d782-72ad-4449-8da7-c869316afd62"}}, {"head": {"id": "59b3021b-6de8-4b57-868d-37d9eb9b9765", "name": "Configuration task cost before running: 231 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692427356000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3e81119-66d3-482c-b8a4-d57c8634115a", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692435116700, "endTime": 30692446129300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "611e58da-a8fc-470e-b769-fed5c00d82db", "logId": "51fc5715-bce5-49dc-8007-061151d8f6c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "611e58da-a8fc-470e-b769-fed5c00d82db", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692429801700}, "additional": {"logType": "detail", "children": [], "durationId": "b3e81119-66d3-482c-b8a4-d57c8634115a"}}, {"head": {"id": "e6282c9f-9205-4163-8bc6-a4c2d12e795b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692430616300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84aa0758-f087-4e0c-bfef-b246cfc3847c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692430751900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d02503c6-70c2-429b-98fa-b5068a08f2d7", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692435135100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be4cb631-f5e8-4be2-a8d2-15d8505ef2c0", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692445794300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "927a67f1-c113-4ead-8574-0bd652830308", "name": "entry : default@PreBuild cost memory 0.29434967041015625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692446022300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51fc5715-bce5-49dc-8007-061151d8f6c8", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692435116700, "endTime": 30692446129300}, "additional": {"logType": "info", "children": [], "durationId": "b3e81119-66d3-482c-b8a4-d57c8634115a"}}, {"head": {"id": "92bcfddf-60cc-460f-b904-c7a6f5209b80", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692456605200, "endTime": 30692459786200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "613551f6-8fde-4163-bb7b-b5acfc63f49e", "logId": "750e41b5-68cb-4c54-b24c-26b88c36fa7c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "613551f6-8fde-4163-bb7b-b5acfc63f49e", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692453124000}, "additional": {"logType": "detail", "children": [], "durationId": "92bcfddf-60cc-460f-b904-c7a6f5209b80"}}, {"head": {"id": "b0e1267f-a122-4bfa-bcdb-7c49ac3fdcc6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692454626100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1e3831c-c308-449d-b75b-d25529160c98", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692454869300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55a5ae6f-1c8d-4db6-90af-2f8330cc8e98", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692456622300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce42fa40-30b5-4559-a5b5-aed0a93e6124", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692459541700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ffd4ac7-4695-4c00-9264-9ef79034c63c", "name": "entry : default@MergeProfile cost memory 0.11283111572265625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692459677700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "750e41b5-68cb-4c54-b24c-26b88c36fa7c", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692456605200, "endTime": 30692459786200}, "additional": {"logType": "info", "children": [], "durationId": "92bcfddf-60cc-460f-b904-c7a6f5209b80"}}, {"head": {"id": "9c1fb0f9-6036-4337-a457-41356934a641", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692463697100, "endTime": 30692466226000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "de938322-43fe-455c-9b52-7ca564167960", "logId": "c794e389-083f-4717-a996-4a6535c1968c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de938322-43fe-455c-9b52-7ca564167960", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692461892500}, "additional": {"logType": "detail", "children": [], "durationId": "9c1fb0f9-6036-4337-a457-41356934a641"}}, {"head": {"id": "9a72b5bd-13ab-4d9c-a44f-a793328680af", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692462509500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24198515-4fa0-4662-85ea-aeeafa5eeb7b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692462632800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12d98eff-e97c-4ceb-ab4b-543caf4a3bcd", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692463708300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96a9e19f-164d-4a45-a4f1-ea56aa219f8f", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692464823100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e6d050f-055f-47e5-bfc2-51d95c5a8db0", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692466048000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "435b0c7f-8d66-45df-a613-db452821e85c", "name": "entry : default@CreateBuildProfile cost memory 0.09755706787109375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692466167200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c794e389-083f-4717-a996-4a6535c1968c", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692463697100, "endTime": 30692466226000}, "additional": {"logType": "info", "children": [], "durationId": "9c1fb0f9-6036-4337-a457-41356934a641"}}, {"head": {"id": "68460d3d-a60d-43d9-857c-2fbef0d2fa76", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692472734800, "endTime": 30692473364500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "2b227368-8c52-42e6-acdc-3903408de3ec", "logId": "d119513a-591d-4137-b015-6d0c8b2d8d76"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b227368-8c52-42e6-acdc-3903408de3ec", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692469603700}, "additional": {"logType": "detail", "children": [], "durationId": "68460d3d-a60d-43d9-857c-2fbef0d2fa76"}}, {"head": {"id": "0d4e5c5b-6444-48e4-a200-0fdbe1fa0f89", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692471155300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdec4025-b192-4ad0-ae0a-291d82540346", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692471283700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4460209f-ead1-4f4b-8a1b-6f1cb6fbc341", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692472750200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71dee5f3-aa10-4599-a599-849bfc9e5b22", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692472960900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bcd184a-d098-48c5-8cbb-7534794f5096", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692473041500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e9afdac-6f84-445a-ad2a-6e841e65ae61", "name": "entry : default@PreCheckSyscap cost memory 0.03680419921875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692473170900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f7ca987-e679-410f-bf36-28cb8dbf9530", "name": "runTaskFromQueue task cost before running: 277 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692473290600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d119513a-591d-4137-b015-6d0c8b2d8d76", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692472734800, "endTime": 30692473364500, "totalTime": 529000}, "additional": {"logType": "info", "children": [], "durationId": "68460d3d-a60d-43d9-857c-2fbef0d2fa76"}}, {"head": {"id": "8b34f9e7-4c67-4921-bb8e-2f365ef33a82", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692491469900, "endTime": 30692493267300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a023fab7-1c97-4684-8668-50e79d15f728", "logId": "6c558da9-d53e-418d-9b26-e0253e000fba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a023fab7-1c97-4684-8668-50e79d15f728", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692476228900}, "additional": {"logType": "detail", "children": [], "durationId": "8b34f9e7-4c67-4921-bb8e-2f365ef33a82"}}, {"head": {"id": "d7f02a90-22c4-4f06-89a2-5a61c0fe87ee", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692476793600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71ef7186-2bcd-47e9-8f09-a404fb682676", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692476911800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a464421-0874-4284-b0f3-4ad3f6af6d11", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692491489300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16a6bd78-2957-4d9c-9bfa-baed833f5646", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692491786800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "203f225c-0811-4baf-8e3a-c8e5cdadaf3e", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692493023000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f6b32cd-6b85-4855-9aff-6861798012a6", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06621551513671875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692493169400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c558da9-d53e-418d-9b26-e0253e000fba", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692491469900, "endTime": 30692493267300}, "additional": {"logType": "info", "children": [], "durationId": "8b34f9e7-4c67-4921-bb8e-2f365ef33a82"}}, {"head": {"id": "25393cc0-69e5-46aa-ad9c-d997c9ff0406", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692499965600, "endTime": 30692502588100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "913fe1ba-d0fe-4e60-9413-584c36597289", "logId": "35627cbb-9b0b-4542-a5b1-eb1efe758049"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "913fe1ba-d0fe-4e60-9413-584c36597289", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692496265400}, "additional": {"logType": "detail", "children": [], "durationId": "25393cc0-69e5-46aa-ad9c-d997c9ff0406"}}, {"head": {"id": "a8e0a29d-b196-4f37-af1e-e874308b5d0e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692497331600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "613d69b1-4e9c-478f-ad8e-2c9fadb28c9f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692497480800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abb2fedd-ef63-4384-acaf-d3f32b5099d9", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692499980500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ae6ba10-c984-4f20-a680-c8af00082729", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692502258000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "560b7933-65d3-479c-becf-c78f1175e921", "name": "entry : default@ProcessProfile cost memory 0.0572967529296875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692502482900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35627cbb-9b0b-4542-a5b1-eb1efe758049", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692499965600, "endTime": 30692502588100}, "additional": {"logType": "info", "children": [], "durationId": "25393cc0-69e5-46aa-ad9c-d997c9ff0406"}}, {"head": {"id": "b5885e7b-2821-4de6-b0d2-a1bed60e62b2", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692506543500, "endTime": 30692513004700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "cab2419b-4912-4e07-872a-4cbc6e334e93", "logId": "15c6e379-129c-4b88-8114-863a8cc80ffb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cab2419b-4912-4e07-872a-4cbc6e334e93", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692504588400}, "additional": {"logType": "detail", "children": [], "durationId": "b5885e7b-2821-4de6-b0d2-a1bed60e62b2"}}, {"head": {"id": "805110ff-6471-473f-8836-b5cca6e3847d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692505030100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4b5310a-5f14-4256-bf12-c22c1a3d94d5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692505107000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a5c4af3-c5cf-43cb-9630-0325934060cf", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692506553100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87716db0-92a5-48c2-bd3a-6d429db16157", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692512758600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f2b863b-1944-4fbe-8ceb-435fd6880e9c", "name": "entry : default@ProcessRouterMap cost memory 0.195526123046875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692512908800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15c6e379-129c-4b88-8114-863a8cc80ffb", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692506543500, "endTime": 30692513004700}, "additional": {"logType": "info", "children": [], "durationId": "b5885e7b-2821-4de6-b0d2-a1bed60e62b2"}}, {"head": {"id": "e1d0c3e6-4980-47f5-b54a-8b3da9fbc042", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692526629200, "endTime": 30692530959100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "f714166e-def8-4f96-9e16-f4ec1a6b5f5d", "logId": "12e084d6-68c1-452a-8f39-66d417e42f1f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f714166e-def8-4f96-9e16-f4ec1a6b5f5d", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692518736400}, "additional": {"logType": "detail", "children": [], "durationId": "e1d0c3e6-4980-47f5-b54a-8b3da9fbc042"}}, {"head": {"id": "97479634-3566-4120-a451-7945ba823a38", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692519944500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb9ee0c8-aadc-4a88-ae82-ead4b49f9bea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692520125100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1050094c-fc17-42ee-8f13-6f300c311dc4", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692523622900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c27c2649-557b-4f1b-a02d-9180b4e1749f", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692527871400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "caf8e0c8-0cee-4d21-8c9e-c34f6bde8850", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692528068900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27afb406-82ef-448c-a140-8a2ec100d996", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692528134100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bad86e1e-eeb3-4d95-b6f8-b0f8a4f32483", "name": "entry : default@PreviewProcessResource cost memory 0.0698699951171875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692528248900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e86382a-e0b4-4607-b48c-73ee9a2652b5", "name": "runTaskFromQueue task cost before running: 334 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692530832500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12e084d6-68c1-452a-8f39-66d417e42f1f", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692526629200, "endTime": 30692530959100, "totalTime": 1692700}, "additional": {"logType": "info", "children": [], "durationId": "e1d0c3e6-4980-47f5-b54a-8b3da9fbc042"}}, {"head": {"id": "c209685f-c6d3-4c23-8148-968e77cacb73", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692538743000, "endTime": 30692571851600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f20060be-159b-4299-9ce1-5e958064fbb2", "logId": "6e06bdcc-65ae-41fc-a3bb-449920cca47f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f20060be-159b-4299-9ce1-5e958064fbb2", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692533749600}, "additional": {"logType": "detail", "children": [], "durationId": "c209685f-c6d3-4c23-8148-968e77cacb73"}}, {"head": {"id": "c1055fe6-5209-4b8c-b79a-3813e8a34014", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692534536100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f1a4de0-a4a5-44f9-ac33-8c4d2e2d95d1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692534671400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "490e7a8a-5567-49eb-a5fe-0d055af4137b", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692538755400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d6aeff0-e42a-4d45-9494-946542abb599", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 17 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692571537300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a022c922-b2be-4a77-b9f0-e11d79e1ced5", "name": "entry : default@GenerateLoaderJson cost memory 0.7577972412109375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692571744500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e06bdcc-65ae-41fc-a3bb-449920cca47f", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692538743000, "endTime": 30692571851600}, "additional": {"logType": "info", "children": [], "durationId": "c209685f-c6d3-4c23-8148-968e77cacb73"}}, {"head": {"id": "e5c33f4c-1983-47af-83ae-75dcdc6b99da", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692597512900, "endTime": 30692632753400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "622e280a-c3a7-496d-a2d1-c5030f82eb01", "logId": "d63107cb-6d5b-43a6-aca3-93e917adc186"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "622e280a-c3a7-496d-a2d1-c5030f82eb01", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692588681200}, "additional": {"logType": "detail", "children": [], "durationId": "e5c33f4c-1983-47af-83ae-75dcdc6b99da"}}, {"head": {"id": "b542ca88-c266-4b9f-b08a-53b33b4e064c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692589998300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afb0b2d2-8c78-46de-82fe-2b1b2e3d5438", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692590260200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ada36ac0-a869-44fd-b794-ca44e99c4994", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692592208400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fae94d4e-6a5d-4e10-8f07-98759a6faa29", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692597544800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc75c947-8f52-4b59-a75f-0a52a41bed77", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 34 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692632561200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3217e29d-6588-4c76-91fa-367183933df8", "name": "entry : default@PreviewCompileResource cost memory 0.7234420776367188", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692632686300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d63107cb-6d5b-43a6-aca3-93e917adc186", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692597512900, "endTime": 30692632753400}, "additional": {"logType": "info", "children": [], "durationId": "e5c33f4c-1983-47af-83ae-75dcdc6b99da"}}, {"head": {"id": "e4cbf055-780b-4d6b-a557-2120eee43808", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692635570100, "endTime": 30692635981100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "63440f5e-cba0-4ad6-97e9-44b33d832a7a", "logId": "2db789ee-9b98-49dd-8c63-17d592b4ce78"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "63440f5e-cba0-4ad6-97e9-44b33d832a7a", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692634843200}, "additional": {"logType": "detail", "children": [], "durationId": "e4cbf055-780b-4d6b-a557-2120eee43808"}}, {"head": {"id": "1188a68a-357f-4294-9e28-c78bfc06154b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692635350600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b2a4f6b-6b15-4f2a-928e-cf2d91699ddf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692635454700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c48765d3-5084-4ca0-b67f-2e09be8a85d5", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692635579600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82f5a72b-f345-4c39-b5cc-4a80f42a41ff", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692635696000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfc07f99-05e7-467d-9589-24cd7668e695", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692635754100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b4b1fd0-fe49-428e-882e-dcbe732da029", "name": "entry : default@PreviewHookCompileResource cost memory 0.039886474609375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692635833800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ae3124e-f02c-4e3f-9d3f-c87ef72fb907", "name": "runTaskFromQueue task cost before running: 440 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692635922800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2db789ee-9b98-49dd-8c63-17d592b4ce78", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692635570100, "endTime": 30692635981100, "totalTime": 331300}, "additional": {"logType": "info", "children": [], "durationId": "e4cbf055-780b-4d6b-a557-2120eee43808"}}, {"head": {"id": "24b043f9-a800-4d90-8a75-28868210608c", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692639305100, "endTime": 30692643771400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "632d2ab3-8c5d-41a6-a4be-c5fc19f53645", "logId": "b04fb31a-d5f0-49d1-b564-1cd31e18ffb5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "632d2ab3-8c5d-41a6-a4be-c5fc19f53645", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692638176800}, "additional": {"logType": "detail", "children": [], "durationId": "24b043f9-a800-4d90-8a75-28868210608c"}}, {"head": {"id": "c2c4afd7-5380-459d-b2f4-0b5ae52ab9de", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692638612700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba98136f-8e76-4eb4-8998-7a055fc0c9df", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692638685300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da7a550d-9916-4b48-aeed-728653edf8f3", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692639312400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0abe3f1e-8e14-4f0d-9d87-e1700c1ddb77", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692643520400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a7e41bc-5a76-475d-ad23-8039fbeafd4a", "name": "entry : default@CopyPreviewProfile cost memory 0.09758758544921875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692643682400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b04fb31a-d5f0-49d1-b564-1cd31e18ffb5", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692639305100, "endTime": 30692643771400}, "additional": {"logType": "info", "children": [], "durationId": "24b043f9-a800-4d90-8a75-28868210608c"}}, {"head": {"id": "b74159b5-fa07-4805-8cb9-7845f3ff7812", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692647041100, "endTime": 30692647309200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "75020519-97b8-4c6a-a6d6-176bafcff17f", "logId": "f7381f85-d403-49e3-a8c3-d231557cf8e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "75020519-97b8-4c6a-a6d6-176bafcff17f", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692645892900}, "additional": {"logType": "detail", "children": [], "durationId": "b74159b5-fa07-4805-8cb9-7845f3ff7812"}}, {"head": {"id": "979ce3a4-0e83-426a-bd5b-f8fabf69f03c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692646379800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c3b76e7-c888-45fc-a45b-4b09c582baf6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692646460600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc604a25-c8e8-43dc-8820-84c25b6e1d6c", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692647048400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9757306-4cd1-4910-a551-0c510cb9b214", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692647137600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "763203ae-7213-41da-8d6b-c08c2832ca21", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692647174200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40f03144-8ec8-43ef-8452-1c5490b3c0d8", "name": "entry : default@ReplacePreviewerPage cost memory 0.037872314453125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692647235600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "256d68b5-991f-48b6-b729-ad0fd93ff4d8", "name": "runTaskFromQueue task cost before running: 451 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692647283300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7381f85-d403-49e3-a8c3-d231557cf8e3", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692647041100, "endTime": 30692647309200, "totalTime": 228500}, "additional": {"logType": "info", "children": [], "durationId": "b74159b5-fa07-4805-8cb9-7845f3ff7812"}}, {"head": {"id": "efcd9b8e-8501-43a8-9281-ea49c82a8401", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692648736100, "endTime": 30692648952000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "40b5f4c5-1f41-49ed-96bb-a78125b9a22d", "logId": "1b1ec235-1409-4bc4-afbd-1913376533ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40b5f4c5-1f41-49ed-96bb-a78125b9a22d", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692648683000}, "additional": {"logType": "detail", "children": [], "durationId": "efcd9b8e-8501-43a8-9281-ea49c82a8401"}}, {"head": {"id": "759013cd-8651-45bf-b75b-bbbf7218e74a", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692648741500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c2bcace-5013-4425-bb00-316c0564bab9", "name": "entry : buildPreviewerResource cost memory 0.03818511962890625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692648863400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a05b8c25-9903-426d-a242-ceabb149b861", "name": "runTaskFromQueue task cost before running: 453 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692648921900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b1ec235-1409-4bc4-afbd-1913376533ac", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692648736100, "endTime": 30692648952000, "totalTime": 174600}, "additional": {"logType": "info", "children": [], "durationId": "efcd9b8e-8501-43a8-9281-ea49c82a8401"}}, {"head": {"id": "4ba1587d-0739-40ab-ac73-3d3da8847609", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692654509000, "endTime": 30692658183900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "f033ad2e-1271-499b-a10d-0503f5e06ec7", "logId": "effccc9f-73c0-4caa-896a-7fd51275551a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f033ad2e-1271-499b-a10d-0503f5e06ec7", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692652247500}, "additional": {"logType": "detail", "children": [], "durationId": "4ba1587d-0739-40ab-ac73-3d3da8847609"}}, {"head": {"id": "fd9ec028-b75f-4933-b329-5f988885cde9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692653049000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95d1acc0-1c18-4f3c-89f0-aab52aed9fcc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692653174700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "250dbb16-181f-474c-874d-19a523e7ba15", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692654523300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc437ae7-ec67-47d3-b7ff-86df2bc084b0", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692658028100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "971522bc-2b86-4c09-b13f-779f0e60b18b", "name": "entry : default@PreviewUpdateAssets cost memory 0.12386322021484375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692658137700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "effccc9f-73c0-4caa-896a-7fd51275551a", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692654509000, "endTime": 30692658183900}, "additional": {"logType": "info", "children": [], "durationId": "4ba1587d-0739-40ab-ac73-3d3da8847609"}}, {"head": {"id": "b82f9e3c-eef7-4164-9dc9-3d7830586322", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692668155000, "endTime": 30703954391100}, "additional": {"children": ["091c4596-7701-4e3a-b971-778114331b2e"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "526bdad5-7827-48ef-a064-fdd1f81a7f57", "logId": "4096002d-746e-4d19-aed0-d2e223450686"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "526bdad5-7827-48ef-a064-fdd1f81a7f57", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692660832600}, "additional": {"logType": "detail", "children": [], "durationId": "b82f9e3c-eef7-4164-9dc9-3d7830586322"}}, {"head": {"id": "286f179d-d8f2-465f-bb28-0ec64959017f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692661772200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "739b7cfd-32f0-4a87-acd4-06b524c08974", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692661900500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edc80b06-784e-4816-bbbd-6923ae7f5023", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692668165300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "091c4596-7701-4e3a-b971-778114331b2e", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker14", "startTime": 30692695189900, "endTime": 30703950486700}, "additional": {"children": ["a176efba-c752-4260-af57-36746c24568d", "564491cd-5128-488f-8959-b632176aab9a", "1dd958e0-1f6d-494c-b63c-964947ed7654", "1e124c40-5c09-4aa2-a180-cd309500d2ea", "ecd68eb9-09c2-4c10-97db-bbc650cf523d"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "b82f9e3c-eef7-4164-9dc9-3d7830586322", "logId": "1b7f8a31-4a51-4e3d-9983-855148d177e5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bfe823db-d425-40ff-8f2c-0bf5f7faa9b2", "name": "entry : default@PreviewArkTS cost memory 1.0028228759765625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692697241400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01bd2172-f995-4580-9d40-581707f10143", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30697061745100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a176efba-c752-4260-af57-36746c24568d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker14", "startTime": 30697062834700, "endTime": 30697062848500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "091c4596-7701-4e3a-b971-778114331b2e", "logId": "09caf809-7a91-475a-91e9-73d740f05adc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "09caf809-7a91-475a-91e9-73d740f05adc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30697062834700, "endTime": 30697062848500}, "additional": {"logType": "info", "children": [], "durationId": "a176efba-c752-4260-af57-36746c24568d", "parent": "1b7f8a31-4a51-4e3d-9983-855148d177e5"}}, {"head": {"id": "7e2b5b3e-06be-4586-8f10-03140a5a1db0", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703948622800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "564491cd-5128-488f-8959-b632176aab9a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker14", "startTime": 30703950366400, "endTime": 30703950383400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "091c4596-7701-4e3a-b971-778114331b2e", "logId": "843e670a-be10-4852-9268-e9ec826bc94d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "843e670a-be10-4852-9268-e9ec826bc94d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703950366400, "endTime": 30703950383400}, "additional": {"logType": "info", "children": [], "durationId": "564491cd-5128-488f-8959-b632176aab9a", "parent": "1b7f8a31-4a51-4e3d-9983-855148d177e5"}}, {"head": {"id": "1b7f8a31-4a51-4e3d-9983-855148d177e5", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Worker14", "startTime": 30692695189900, "endTime": 30703950486700}, "additional": {"logType": "info", "children": ["09caf809-7a91-475a-91e9-73d740f05adc", "843e670a-be10-4852-9268-e9ec826bc94d", "2f28cecc-3932-4530-ae56-7f938427fbb9", "342344bc-036d-4dda-ad02-518d06af2181", "92916654-7f1c-4f73-828c-0d8bcf796e06"], "durationId": "091c4596-7701-4e3a-b971-778114331b2e", "parent": "4096002d-746e-4d19-aed0-d2e223450686"}}, {"head": {"id": "1dd958e0-1f6d-494c-b63c-964947ed7654", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker14", "startTime": 30695876668700, "endTime": 30697021056400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "091c4596-7701-4e3a-b971-778114331b2e", "logId": "2f28cecc-3932-4530-ae56-7f938427fbb9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f28cecc-3932-4530-ae56-7f938427fbb9", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30695876668700, "endTime": 30697021056400}, "additional": {"logType": "info", "children": [], "durationId": "1dd958e0-1f6d-494c-b63c-964947ed7654", "parent": "1b7f8a31-4a51-4e3d-9983-855148d177e5"}}, {"head": {"id": "1e124c40-5c09-4aa2-a180-cd309500d2ea", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker14", "startTime": 30697021371000, "endTime": 30697021579000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "091c4596-7701-4e3a-b971-778114331b2e", "logId": "342344bc-036d-4dda-ad02-518d06af2181"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "342344bc-036d-4dda-ad02-518d06af2181", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30697021371000, "endTime": 30697021579000}, "additional": {"logType": "info", "children": [], "durationId": "1e124c40-5c09-4aa2-a180-cd309500d2ea", "parent": "1b7f8a31-4a51-4e3d-9983-855148d177e5"}}, {"head": {"id": "ecd68eb9-09c2-4c10-97db-bbc650cf523d", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker14", "startTime": 30697021739700, "endTime": 30703948644700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "091c4596-7701-4e3a-b971-778114331b2e", "logId": "92916654-7f1c-4f73-828c-0d8bcf796e06"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92916654-7f1c-4f73-828c-0d8bcf796e06", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30697021739700, "endTime": 30703948644700}, "additional": {"logType": "info", "children": [], "durationId": "ecd68eb9-09c2-4c10-97db-bbc650cf523d", "parent": "1b7f8a31-4a51-4e3d-9983-855148d177e5"}}, {"head": {"id": "4096002d-746e-4d19-aed0-d2e223450686", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692668155000, "endTime": 30703954391100, "totalTime": 11286225500}, "additional": {"logType": "info", "children": ["1b7f8a31-4a51-4e3d-9983-855148d177e5"], "durationId": "b82f9e3c-eef7-4164-9dc9-3d7830586322"}}, {"head": {"id": "988fb9fb-4c56-4028-a26d-128070071e1c", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703958814800, "endTime": 30703959023100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "96a4096b-4569-47a4-a831-7f5d5052d8a7", "logId": "98e9eb2d-eef7-4822-900b-24858040ec2e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96a4096b-4569-47a4-a831-7f5d5052d8a7", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703958768200}, "additional": {"logType": "detail", "children": [], "durationId": "988fb9fb-4c56-4028-a26d-128070071e1c"}}, {"head": {"id": "6e86eb3d-42d9-4c27-8d71-980d340fa5fa", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703958823800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01b8f402-f8c9-448f-86b4-e1bf0562d935", "name": "entry : PreviewBuild cost memory 0.0187835693359375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703958918700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6321f8bf-1b4c-4f44-b2d1-2d0f38b57675", "name": "runTaskFromQueue task cost before running: 11 s 763 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703958992700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98e9eb2d-eef7-4822-900b-24858040ec2e", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703958814800, "endTime": 30703959023100, "totalTime": 140800}, "additional": {"logType": "info", "children": [], "durationId": "988fb9fb-4c56-4028-a26d-128070071e1c"}}, {"head": {"id": "8503b925-d658-4969-840e-e2ee2767cb09", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703967860400, "endTime": 30703967881300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c33e40ed-60fa-497f-82f4-396e1f473262", "logId": "a34894a8-10fb-42be-864a-062b651fb735"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a34894a8-10fb-42be-864a-062b651fb735", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703967860400, "endTime": 30703967881300}, "additional": {"logType": "info", "children": [], "durationId": "8503b925-d658-4969-840e-e2ee2767cb09"}}, {"head": {"id": "43bd260c-eae7-40b2-b96a-6ee7aa19bb86", "name": "BUILD SUCCESSFUL in 11 s 772 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703967922700}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "130f380f-9dca-43b8-b123-4e4b5a80c7ac", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30692196834400, "endTime": 30703968128300}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 37}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "9ceeaa03-bec9-402d-8f00-9b07bc967b6a", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703968149200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a022490c-e341-4051-983c-b83ce4355aae", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703968182400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "291cdafa-d1a2-4d30-8e8a-350c5d24f642", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703968205300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5378eb33-71cc-467c-872f-6ff12389e13f", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703968224100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8afa1179-d0a7-4cf9-80f4-e2575ed2c232", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703968246000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9278b4c-1756-48b6-9a5e-d95bf31f3e1b", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703968268800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3184c34-d29f-437e-b5f7-99c036dc56c4", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703968287500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0970b6dc-d938-4af3-a1fa-22b47bd5f8ce", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703968306200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f58f6ca2-6956-4d13-a88d-f79ca906f79b", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703968326700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0fcd301-0b6f-4f0d-aaaa-59e35bd8dc3a", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703968349500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "627d45d2-8148-43fe-9834-8e58e7b12a64", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703970941600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7b957eb-4da6-448a-b300-2ddda0712162", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703971669700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "721c81bd-e8ed-4761-a20c-21314f48edf2", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703972109500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adeae70e-d164-4d34-b3bb-00de5d8fdaf7", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703972389400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "728813b4-eb15-4c45-bce9-40b2e93acb4d", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703973020200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e38e5700-5b1b-45e4-8dad-db73955c57e9", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703980583200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9e6049e-4e04-4499-9952-b5a9b7d2bf95", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703980865000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77758929-38da-4e0b-86f9-a9c299950a97", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703981064100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82f520e8-9c5c-4e77-8e00-e5eeb7cc3a63", "name": "Update task entry:default@PreviewArkTS output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703981273100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05cb9607-01f3-43bf-9e50-c414013db3e6", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:14 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 30703981519100}, "additional": {"logType": "debug", "children": []}}], "workLog": []}