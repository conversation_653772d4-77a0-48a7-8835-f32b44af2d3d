if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface BankCardDetailPage_Params {
    cardDetail?: BankCard | null;
    isLoading?: boolean;
    cardId?: number;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { BankCardApi } from "@normalized:N&&&entry/src/main/ets/api/BankCardApi&";
import type { BankCard } from '../common/types/index';
import { tempDataManager } from "@normalized:N&&&entry/src/main/ets/common/storage/TempDataManager&";
class BankCardDetailPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__cardDetail = new ObservedPropertyObjectPU(null, this, "cardDetail");
        this.__isLoading = new ObservedPropertySimplePU(true, this, "isLoading");
        this.__cardId = new ObservedPropertySimplePU(0, this, "cardId");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: BankCardDetailPage_Params) {
        if (params.cardDetail !== undefined) {
            this.cardDetail = params.cardDetail;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.cardId !== undefined) {
            this.cardId = params.cardId;
        }
    }
    updateStateVars(params: BankCardDetailPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__cardDetail.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__cardId.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__cardDetail.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__cardId.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __cardDetail: ObservedPropertyObjectPU<BankCard | null>;
    get cardDetail() {
        return this.__cardDetail.get();
    }
    set cardDetail(newValue: BankCard | null) {
        this.__cardDetail.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __cardId: ObservedPropertySimplePU<number>;
    get cardId() {
        return this.__cardId.get();
    }
    set cardId(newValue: number) {
        this.__cardId.set(newValue);
    }
    aboutToAppear() {
        // 获取传入的银行卡ID
        const params = router.getParams() as Record<string, number>;
        this.cardId = params?.cardId || 0;
        if (this.cardId > 0) {
            this.loadCardDetail();
        }
        else {
            promptAction.showToast({ message: '银行卡信息错误' });
            router.back();
        }
    }
    async loadCardDetail() {
        try {
            this.isLoading = true;
            this.cardDetail = await BankCardApi.getCardDetail(this.cardId);
        }
        catch (error) {
            console.error('获取银行卡详情失败:', error);
            promptAction.showToast({ message: '获取银行卡详情失败' });
            router.back();
        }
        finally {
            this.isLoading = false;
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(41:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(43:7)", "entry");
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(56);
            // 顶部导航栏
            Row.padding({ left: 16, right: 16 });
            // 顶部导航栏
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('返回');
            Button.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(44:9)", "entry");
            Button.fontSize(16);
            Button.fontColor('#1976D2');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡详情');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(52:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(58:9)", "entry");
            Text.width(60);
        }, Text);
        Text.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 加载状态
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(68:9)", "entry");
                        // 加载状态
                        Column.width('100%');
                        // 加载状态
                        Column.height('100%');
                        // 加载状态
                        Column.justifyContent(FlexAlign.Center);
                        // 加载状态
                        Column.alignItems(HorizontalAlign.Center);
                        // 加载状态
                        Column.backgroundColor('#F5F5F5');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(69:11)", "entry");
                        LoadingProgress.width(40);
                        LoadingProgress.height(40);
                        LoadingProgress.color('#1976D2');
                    }, LoadingProgress);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('加载中...');
                        Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(74:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                        Text.margin({ top: 16 });
                    }, Text);
                    Text.pop();
                    // 加载状态
                    Column.pop();
                });
            }
            else if (this.cardDetail) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 银行卡详情内容
                        Scroll.create();
                        Scroll.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(86:9)", "entry");
                        // 银行卡详情内容
                        Scroll.layoutWeight(1);
                        // 银行卡详情内容
                        Scroll.backgroundColor('#F5F5F5');
                    }, Scroll);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(87:11)", "entry");
                        Column.padding({ left: 16, right: 16, bottom: 20 });
                    }, Column);
                    // 银行卡卡片
                    this.BankCardDisplay.bind(this)();
                    // 详细信息
                    this.CardDetailInfo.bind(this)();
                    // 操作按钮
                    this.ActionButtons.bind(this)();
                    Column.pop();
                    // 银行卡详情内容
                    Scroll.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    BankCardDisplay(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 真实银行卡设计
            Stack.create({ alignContent: Alignment.TopStart });
            Stack.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(111:5)", "entry");
            // 真实银行卡设计
            Stack.width('100%');
            // 真实银行卡设计
            Stack.margin({ top: 16, bottom: 24 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(112:7)", "entry");
            Column.width('100%');
            Column.height(200);
            Column.padding(24);
            Column.borderRadius(16);
            Column.linearGradient({
                direction: GradientDirection.Right,
                colors: this.getBankCardGradient(this.cardDetail?.bankName || '')
            });
            Column.shadow({
                radius: 12,
                color: 'rgba(0,0,0,0.2)',
                offsetX: 0,
                offsetY: 6
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡顶部信息
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(114:9)", "entry");
            // 银行卡顶部信息
            Row.width('100%');
            // 银行卡顶部信息
            Row.margin({ bottom: 24 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(115:11)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.cardDetail?.bankName || '');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(116:13)", "entry");
            Text.fontSize(18);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Bold);
            Text.maxLines(1);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.cardDetail?.cardType || '');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(123:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#E0E0E0');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(131:11)", "entry");
            Column.alignItems(HorizontalAlign.End);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 绑定状态标识
            Text.create(this.getCardStatus());
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(133:13)", "entry");
            // 绑定状态标识
            Text.fontSize(12);
            // 绑定状态标识
            Text.fontColor('#FFFFFF');
            // 绑定状态标识
            Text.backgroundColor(this.cardDetail?.isBound === 1 ? '#4CAF50' : '#FF5722');
            // 绑定状态标识
            Text.borderRadius(10);
            // 绑定状态标识
            Text.padding({ left: 8, right: 8, top: 4, bottom: 4 });
            // 绑定状态标识
            Text.margin({ bottom: 6 });
        }, Text);
        // 绑定状态标识
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 默认卡标识
            if (this.cardDetail?.isDefault) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('默认');
                        Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(143:15)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#FFFFFF');
                        Text.backgroundColor('#FF9800');
                        Text.borderRadius(10);
                        Text.padding({ left: 8, right: 8, top: 4, bottom: 4 });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
        // 银行卡顶部信息
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡号 - 格式化显示
            Text.create(this.formatCardNumberWithSpaces());
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(157:9)", "entry");
            // 银行卡号 - 格式化显示
            Text.fontSize(20);
            // 银行卡号 - 格式化显示
            Text.fontColor('#FFFFFF');
            // 银行卡号 - 格式化显示
            Text.fontWeight(FontWeight.Medium);
            // 银行卡号 - 格式化显示
            Text.letterSpacing(3);
            // 银行卡号 - 格式化显示
            Text.margin({ bottom: 20 });
            // 银行卡号 - 格式化显示
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        // 银行卡号 - 格式化显示
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 持卡人信息
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(166:9)", "entry");
            // 持卡人信息
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(167:11)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('持卡人');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(168:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#E0E0E0');
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.cardDetail?.holderName || '');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(173:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Medium);
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(181:11)", "entry");
            Column.alignItems(HorizontalAlign.End);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('有效期');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(182:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#E0E0E0');
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('**/**');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(187:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Medium);
        }, Text);
        Text.pop();
        Column.pop();
        // 持卡人信息
        Row.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡装饰图案
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(212:7)", "entry");
            // 银行卡装饰图案
            Image.width(100);
            // 银行卡装饰图案
            Image.height(100);
            // 银行卡装饰图案
            Image.fillColor('rgba(255,255,255,0.1)');
            // 银行卡装饰图案
            Image.position({ x: '65%', y: '10%' });
        }, Image);
        // 真实银行卡设计
        Stack.pop();
    }
    CardDetailInfo(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(224:5)", "entry");
            Column.width('100%');
            Column.padding(20);
            Column.borderRadius(12);
            Column.backgroundColor('#FFFFFF');
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡信息');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(225:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.DetailItem.bind(this)('银行名称', this.cardDetail?.bankName || '');
        this.DetailItem.bind(this)('卡片类型', this.cardDetail?.cardType || '');
        this.DetailItem.bind(this)('持卡人姓名', this.cardDetail?.holderName || '');
        this.DetailItem.bind(this)('卡号', this.maskCardNumber(this.cardDetail?.cardNo || ''));
        this.DetailItem.bind(this)('绑定状态', this.getCardStatus());
        this.DetailItem.bind(this)('绑定时间', this.formatDateTime(this.cardDetail?.createTime || ''));
        Column.pop();
    }
    DetailItem(label: string, value: string, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(248:5)", "entry");
            Row.width('100%');
            Row.height(44);
            Row.justifyContent(FlexAlign.SpaceBetween);
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(249:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.width(80);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(value);
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(254:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.End);
        }, Text);
        Text.pop();
        Row.pop();
    }
    ActionButtons(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(268:5)", "entry");
            Column.width('100%');
            Column.padding(20);
            Column.borderRadius(12);
            Column.backgroundColor('#FFFFFF');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 快捷操作按钮
            Text.create('快捷操作');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(270:7)", "entry");
            // 快捷操作按钮
            Text.fontSize(16);
            // 快捷操作按钮
            Text.fontColor('#333333');
            // 快捷操作按钮
            Text.fontWeight(FontWeight.Medium);
            // 快捷操作按钮
            Text.alignSelf(ItemAlign.Start);
            // 快捷操作按钮
            Text.margin({ bottom: 16 });
        }, Text);
        // 快捷操作按钮
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(277:7)", "entry");
            Row.width('100%');
            Row.margin({ bottom: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('支付');
            Button.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(278:9)", "entry");
            Button.fontSize(16);
            Button.fontColor('#FFFFFF');
            Button.backgroundColor('#4CAF50');
            Button.borderRadius(8);
            Button.layoutWeight(1);
            Button.height(48);
            Button.margin({ right: 8 });
            Button.onClick(() => {
                this.useCardForPayment();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('转账');
            Button.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(290:9)", "entry");
            Button.fontSize(16);
            Button.fontColor('#FFFFFF');
            Button.backgroundColor('#2196F3');
            Button.borderRadius(8);
            Button.layoutWeight(1);
            Button.height(48);
            Button.margin({ left: 8 });
            Button.onClick(() => {
                this.useCardForTransfer();
            });
        }, Button);
        Button.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('解绑银行卡');
            Button.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(305:7)", "entry");
            Button.width('100%');
            Button.height(48);
            Button.fontSize(16);
            Button.fontColor('#F44336');
            Button.backgroundColor('#FFFFFF');
            Button.borderRadius(8);
            Button.border({ width: 1, color: '#F44336' });
            Button.onClick(() => {
                this.confirmUnbindCard();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('解绑后将无法使用此银行卡进行充值和提现');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(317:7)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
            Text.textAlign(TextAlign.Center);
            Text.width('100%');
            Text.margin({ top: 8 });
        }, Text);
        Text.pop();
        Column.pop();
    }
    // 工具方法
    getCardStatus(): string {
        return this.cardDetail?.isBound === 1 ? '已绑定' : '未绑定';
    }
    getCardStatusColor(): string {
        return this.cardDetail?.isBound === 1 ? '#4CAF50' : '#F44336';
    }
    getCardStatusBgColor(): string {
        return this.cardDetail?.isBound === 1 ? '#E8F5E8' : '#FFEBEE';
    }
    maskCardNumber(cardNo: string): string {
        if (!cardNo || cardNo.length < 8) {
            return cardNo;
        }
        // 显示前4位和后4位
        const start = cardNo.substring(0, 4);
        const end = cardNo.substring(cardNo.length - 4);
        const middle = '*'.repeat(cardNo.length - 8);
        return `${start} ${middle} ${end}`;
    }
    formatDateTime(dateTime: string): string {
        if (!dateTime)
            return '';
        const date = new Date(dateTime);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        });
    }
    /**
     * 格式化银行卡号显示（带空格分隔）
     */
    formatCardNumberWithSpaces(): string {
        if (!this.cardDetail?.cardNo)
            return '';
        // 脱敏处理
        const maskedCardNo = this.maskCardNumber(this.cardDetail.cardNo);
        // 添加空格分隔，每4位一组
        return maskedCardNo.replace(/(.{4})/g, '$1 ').trim();
    }
    /**
     * 获取银行卡渐变色
     */
    getBankCardGradient(bankName: string): Array<[
        string,
        number
    ]> {
        const gradients: Record<string, Array<[
            string,
            number
        ]>> = {
            '中国工商银行': [['#C41E3A', 0], ['#8B0000', 1]],
            '中国建设银行': [['#003DA5', 0], ['#001F5C', 1]],
            '中国农业银行': [['#00A651', 0], ['#006B35', 1]],
            '中国银行': [['#B8860B', 0], ['#8B6914', 1]],
            '招商银行': [['#DC143C', 0], ['#8B0000', 1]],
            '交通银行': [['#0066CC', 0], ['#003D7A', 1]],
            '中信银行': [['#FF6B35', 0], ['#CC4125', 1]],
            '光大银行': [['#9932CC', 0], ['#6A1B9A', 1]],
            '华夏银行': [['#FF1744', 0], ['#C62828', 1]],
            '民生银行': [['#00BCD4', 0], ['#0097A7', 1]],
            '广发银行': [['#FF5722', 0], ['#D84315', 1]],
            '平安银行': [['#FF9800', 0], ['#F57C00', 1]]
        };
        return gradients[bankName] || [['#1976D2', 0], ['#0D47A1', 1]];
    }
    async confirmUnbindCard() {
        try {
            const result = await promptAction.showDialog({
                title: '确认解绑',
                message: `确定要解绑银行卡 ${this.cardDetail?.bankName}(${this.cardDetail?.cardNo.slice(-4)}) 吗？`,
                buttons: [
                    { text: '确定', color: '#F44336' },
                    { text: '取消', color: '#666666' }
                ]
            });
            if (result.index === 0) {
                await this.unbindCard();
            }
        }
        catch (error) {
            console.error('显示确认对话框失败:', error);
        }
    }
    async unbindCard() {
        try {
            await BankCardApi.unbindCard(this.cardId);
            promptAction.showToast({ message: '解绑成功' });
            // 通知银行卡列表页面刷新
            console.log('BankCardDetailPage - 设置银行卡解绑事件标志');
            tempDataManager.setData('BANK_CARD_UNBOUND', true);
            // 返回银行卡列表页
            router.back();
        }
        catch (error) {
            console.error('解绑银行卡失败:', error);
            promptAction.showToast({ message: '解绑失败，请重试' });
        }
    }
    /**
     * 使用银行卡进行支付
     */
    useCardForPayment() {
        if (!this.cardDetail)
            return;
        console.log('使用银行卡支付:', this.cardDetail.bankName, this.cardDetail.cardNo);
        // 跳转到支付页面，传递银行卡信息
        router.pushUrl({
            url: 'pages/PaymentPage',
            params: {
                selectedCard: {
                    cardId: this.cardDetail.cardId,
                    bankName: this.cardDetail.bankName,
                    cardNo: this.cardDetail.cardNo,
                    cardType: this.cardDetail.cardType,
                    holderName: this.cardDetail.holderName,
                    maskedCardNo: this.maskCardNumber(this.cardDetail.cardNo)
                },
                paymentMethod: 'bankCard'
            }
        }).catch((error: Error) => {
            console.error('跳转支付页面失败:', error);
            promptAction.showToast({ message: '打开支付页面失败' });
        });
    }
    /**
     * 使用银行卡进行转账
     */
    useCardForTransfer() {
        if (!this.cardDetail)
            return;
        console.log('使用银行卡转账:', this.cardDetail.bankName, this.cardDetail.cardNo);
        // 跳转到钱包操作页面（转账功能），传递银行卡信息
        router.pushUrl({
            url: 'pages/WalletOperationPage',
            params: {
                operationType: 'transfer',
                selectedCard: {
                    cardId: this.cardDetail.cardId,
                    bankName: this.cardDetail.bankName,
                    cardNo: this.cardDetail.cardNo,
                    cardType: this.cardDetail.cardType,
                    holderName: this.cardDetail.holderName,
                    maskedCardNo: this.maskCardNumber(this.cardDetail.cardNo)
                },
                transferMethod: 'bankCard'
            }
        }).catch((error: Error) => {
            console.error('跳转钱包操作页面失败:', error);
            promptAction.showToast({ message: '打开钱包操作页面失败' });
        });
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "BankCardDetailPage";
    }
}
registerNamedRoute(() => new BankCardDetailPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/BankCardDetailPage", pageFullPath: "entry/src/main/ets/pages/BankCardDetailPage", integratedHsp: "false", moduleType: "followWithHap" });
