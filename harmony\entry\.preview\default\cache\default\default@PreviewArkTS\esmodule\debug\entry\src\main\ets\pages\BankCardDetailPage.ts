if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface BankCardDetailPage_Params {
    cardDetail?: BankCard | null;
    isLoading?: boolean;
    cardId?: number;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { BankCardApi } from "@normalized:N&&&entry/src/main/ets/api/BankCardApi&";
import { BankCardType } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import type { BankCard } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
class BankCardDetailPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__cardDetail = new ObservedPropertyObjectPU(null, this, "cardDetail");
        this.__isLoading = new ObservedPropertySimplePU(true, this, "isLoading");
        this.__cardId = new ObservedPropertySimplePU(0, this, "cardId");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: BankCardDetailPage_Params) {
        if (params.cardDetail !== undefined) {
            this.cardDetail = params.cardDetail;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.cardId !== undefined) {
            this.cardId = params.cardId;
        }
    }
    updateStateVars(params: BankCardDetailPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__cardDetail.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__cardId.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__cardDetail.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__cardId.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __cardDetail: ObservedPropertyObjectPU<BankCard | null>;
    get cardDetail() {
        return this.__cardDetail.get();
    }
    set cardDetail(newValue: BankCard | null) {
        this.__cardDetail.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __cardId: ObservedPropertySimplePU<number>;
    get cardId() {
        return this.__cardId.get();
    }
    set cardId(newValue: number) {
        this.__cardId.set(newValue);
    }
    aboutToAppear() {
        // 获取传入的银行卡ID
        const params = router.getParams() as Record<string, number>;
        this.cardId = params?.cardId || 0;
        if (this.cardId > 0) {
            this.loadCardDetail();
        }
        else {
            promptAction.showToast({ message: '银行卡信息错误' });
            router.back();
        }
    }
    async loadCardDetail() {
        try {
            this.isLoading = true;
            this.cardDetail = await BankCardApi.getCardDetail(this.cardId);
        }
        catch (error) {
            console.error('获取银行卡详情失败:', error);
            promptAction.showToast({ message: '获取银行卡详情失败' });
            router.back();
        }
        finally {
            this.isLoading = false;
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(42:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(44:7)", "entry");
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(56);
            // 顶部导航栏
            Row.padding({ left: 16, right: 16 });
            // 顶部导航栏
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('返回');
            Button.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(45:9)", "entry");
            Button.fontSize(16);
            Button.fontColor('#1976D2');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡详情');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(53:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(59:9)", "entry");
            Text.width(60);
        }, Text);
        Text.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 加载状态
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(69:9)", "entry");
                        // 加载状态
                        Column.width('100%');
                        // 加载状态
                        Column.height('100%');
                        // 加载状态
                        Column.justifyContent(FlexAlign.Center);
                        // 加载状态
                        Column.alignItems(HorizontalAlign.Center);
                        // 加载状态
                        Column.backgroundColor('#F5F5F5');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(70:11)", "entry");
                        LoadingProgress.width(40);
                        LoadingProgress.height(40);
                        LoadingProgress.color('#1976D2');
                    }, LoadingProgress);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('加载中...');
                        Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(75:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                        Text.margin({ top: 16 });
                    }, Text);
                    Text.pop();
                    // 加载状态
                    Column.pop();
                });
            }
            else if (this.cardDetail) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 银行卡详情内容
                        Scroll.create();
                        Scroll.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(87:9)", "entry");
                        // 银行卡详情内容
                        Scroll.layoutWeight(1);
                        // 银行卡详情内容
                        Scroll.backgroundColor('#F5F5F5');
                    }, Scroll);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(88:11)", "entry");
                        Column.padding(16);
                    }, Column);
                    // 银行卡卡片显示
                    this.BankCardDisplay.bind(this)();
                    // 银行卡信息列表
                    this.CardInfoList.bind(this)();
                    // 解绑按钮
                    this.UnbindButton.bind(this)();
                    Column.pop();
                    // 银行卡详情内容
                    Scroll.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    BankCardDisplay(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 按照用户提供的格式设计银行卡
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(112:5)", "entry");
            // 按照用户提供的格式设计银行卡
            Column.width('100%');
            // 按照用户提供的格式设计银行卡
            Column.margin({ top: 16, bottom: 24 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡卡片
            Stack.create({ alignContent: Alignment.TopStart });
            Stack.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(114:7)", "entry");
            // 银行卡卡片
            Stack.width('100%');
            // 银行卡卡片
            Stack.height(200);
            // 银行卡卡片
            Stack.backgroundColor('#1976D2');
            // 银行卡卡片
            Stack.borderRadius(16);
            // 银行卡卡片
            Stack.shadow({
                radius: 8,
                color: 'rgba(0,0,0,0.2)',
                offsetX: 0,
                offsetY: 6
            });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(115:9)", "entry");
            Column.width('100%');
            Column.padding(20);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行名称和卡类型
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(117:11)", "entry");
            // 银行名称和卡类型
            Row.width('100%');
            // 银行名称和卡类型
            Row.margin({ bottom: 30 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.cardDetail?.bankName || '中国工商银行');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(118:13)", "entry");
            Text.fontSize(18);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Bold);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(123:13)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getCardTypeText());
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(125:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#FFFFFF');
            Text.backgroundColor('rgba(255,255,255,0.2)');
            Text.padding({ left: 8, right: 8, top: 4, bottom: 4 });
            Text.borderRadius(12);
        }, Text);
        Text.pop();
        // 银行名称和卡类型
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 卡号显示
            Text.create(`**** **** **** ${this.cardDetail?.cardNo?.slice(-4) || '8888'}`);
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(136:11)", "entry");
            // 卡号显示
            Text.fontSize(22);
            // 卡号显示
            Text.fontColor('#FFFFFF');
            // 卡号显示
            Text.fontWeight(FontWeight.Medium);
            // 卡号显示
            Text.letterSpacing(3);
            // 卡号显示
            Text.margin({ bottom: 30 });
        }, Text);
        // 卡号显示
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 持卡人姓名和绑定状态
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(144:11)", "entry");
            // 持卡人姓名和绑定状态
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.cardDetail?.holderName || '罗曼勾');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(145:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FFFFFF');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(149:13)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.cardDetail?.status === 1 ? '已绑定' : '未绑定');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(151:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#FFFFFF');
            Text.backgroundColor('rgba(255,255,255,0.2)');
            Text.padding({ left: 8, right: 8, top: 4, bottom: 4 });
            Text.borderRadius(12);
        }, Text);
        Text.pop();
        // 持卡人姓名和绑定状态
        Row.pop();
        Column.pop();
        // 银行卡卡片
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡装饰图案
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(175:7)", "entry");
            // 银行卡装饰图案
            Image.width(100);
            // 银行卡装饰图案
            Image.height(100);
            // 银行卡装饰图案
            Image.fillColor('rgba(255,255,255,0.1)');
            // 银行卡装饰图案
            Image.position({ x: '65%', y: '10%' });
        }, Image);
        // 按照用户提供的格式设计银行卡
        Column.pop();
    }
    CardInfoList(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡信息列表
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(188:5)", "entry");
            // 银行卡信息列表
            Column.width('100%');
            // 银行卡信息列表
            Column.alignItems(HorizontalAlign.Start);
            // 银行卡信息列表
            Column.margin({ bottom: 30 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡信息');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(189:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#333333');
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 信息项列表
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(196:7)", "entry");
            // 信息项列表
            Column.backgroundColor('#FFFFFF');
            // 信息项列表
            Column.borderRadius(12);
            // 信息项列表
            Column.padding(16);
        }, Column);
        this.InfoItem.bind(this)('银行名称', this.cardDetail?.bankName || '中国工商银行');
        this.InfoItem.bind(this)('卡片类型', this.getCardTypeText());
        this.InfoItem.bind(this)('持卡人姓名', this.cardDetail?.holderName || '罗曼勾');
        this.InfoItem.bind(this)('卡号', `${this.cardDetail?.cardNo?.slice(-4) || '8888'} ************ ${this.cardDetail?.cardNo?.slice(-4) || '8888'}`);
        this.InfoItem.bind(this)('绑定状态', this.cardDetail?.status === 1 ? '已绑定' : '未绑定');
        this.InfoItem.bind(this)('绑定时间', this.formatBindTime());
        // 信息项列表
        Column.pop();
        // 银行卡信息列表
        Column.pop();
    }
    InfoItem(label: string, value: string, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(215:5)", "entry");
            Row.width('100%');
            Row.height(50);
            Row.justifyContent(FlexAlign.SpaceBetween);
            Row.alignItems(VerticalAlign.Center);
            Row.border({ width: { bottom: 1 }, color: '#F0F0F0' });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(216:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#666666');
            Text.width(100);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(value);
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(221:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.End);
        }, Text);
        Text.pop();
        Row.pop();
    }
    UnbindButton(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 解绑银行卡按钮
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(237:5)", "entry");
            // 解绑银行卡按钮
            Column.width('100%');
            // 解绑银行卡按钮
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('解绑银行卡');
            Button.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(238:7)", "entry");
            Button.width('100%');
            Button.height(50);
            Button.fontSize(16);
            Button.fontColor('#FF5722');
            Button.backgroundColor('#FFFFFF');
            Button.border({ width: 1, color: '#FF5722' });
            Button.borderRadius(25);
            Button.onClick(() => {
                this.showUnbindDialog();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('解绑后将无法使用此银行卡进行充值和消费');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(250:7)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
            Text.textAlign(TextAlign.Center);
            Text.margin({ top: 8 });
        }, Text);
        Text.pop();
        // 解绑银行卡按钮
        Column.pop();
    }
    private formatBindTime(): string {
        if (this.cardDetail?.createTime) {
            return this.cardDetail.createTime.slice(0, 16).replace('T', ' ');
        }
        return '2025/06/23 14:30';
    }
    private getCardTypeText(): string {
        if (this.cardDetail?.cardType === BankCardType.CREDIT) {
            return '信用卡';
        }
        return '储蓄卡';
    }
    private showUnbindDialog() {
        // 显示解绑确认对话框
        promptAction.showDialog({
            title: '确认解绑',
            message: '确定要解绑此银行卡吗？解绑后将无法使用此卡进行支付。',
            buttons: [
                { text: '取消', color: '#666666' },
                { text: '确认解绑', color: '#FF5722' }
            ]
        }).then((result) => {
            if (result.index === 1) {
                this.unbindCard();
            }
        });
    }
    private async unbindCard() {
        try {
            await BankCardApi.unbindCard(this.cardId);
            promptAction.showToast({ message: '解绑成功' });
            router.back();
        }
        catch (error) {
            console.error('解绑银行卡失败:', error);
            promptAction.showToast({ message: '解绑失败，请重试' });
        }
    }
    /**
     * 使用银行卡进行支付
     */
    private useCardForPayment() {
        if (!this.cardDetail)
            return;
        console.log('使用银行卡支付:', this.cardDetail.bankName, this.cardDetail.cardNo);
        // 跳转到支付页面，传递银行卡信息
        router.pushUrl({
            url: 'pages/PaymentPage',
            params: {
                selectedCard: {
                    cardId: this.cardDetail.cardId,
                    bankName: this.cardDetail.bankName,
                    cardNo: this.cardDetail.cardNo,
                    cardType: this.cardDetail.cardType,
                    holderName: this.cardDetail.holderName,
                    maskedCardNo: this.maskCardNumber(this.cardDetail.cardNo)
                },
                paymentMethod: 'bankCard'
            }
        }).catch((error: Error) => {
            console.error('跳转支付页面失败:', error);
            promptAction.showToast({ message: '打开支付页面失败' });
        });
    }
    /**
     * 使用银行卡进行转账
     */
    private useCardForTransfer() {
        if (!this.cardDetail)
            return;
        console.log('使用银行卡转账:', this.cardDetail.bankName, this.cardDetail.cardNo);
        // 跳转到钱包操作页面（转账功能），传递银行卡信息
        router.pushUrl({
            url: 'pages/WalletOperationPage',
            params: {
                operationType: 'transfer',
                selectedCard: {
                    cardId: this.cardDetail.cardId,
                    bankName: this.cardDetail.bankName,
                    cardNo: this.cardDetail.cardNo,
                    cardType: this.cardDetail.cardType,
                    holderName: this.cardDetail.holderName,
                    maskedCardNo: this.maskCardNumber(this.cardDetail.cardNo)
                },
                transferMethod: 'bankCard'
            }
        }).catch((error: Error) => {
            console.error('跳转钱包操作页面失败:', error);
            promptAction.showToast({ message: '打开钱包操作页面失败' });
        });
    }
    /**
     * 格式化银行卡号显示（带空格分隔）
     */
    private formatCardNumberWithSpaces(): string {
        if (!this.cardDetail?.cardNo)
            return '';
        // 脱敏处理
        const maskedCardNo = this.maskCardNumber(this.cardDetail.cardNo);
        // 添加空格分隔，每4位一组
        return maskedCardNo.replace(/(.{4})/g, '$1 ').trim();
    }
    /**
     * 获取银行卡渐变色
     */
    private getBankCardGradient(bankName: string): Array<[
        string,
        number
    ]> {
        const gradients: Record<string, Array<[
            string,
            number
        ]>> = {
            '中国工商银行': [['#C41E3A', 0], ['#8B0000', 1]],
            '中国建设银行': [['#003DA5', 0], ['#001F5C', 1]],
            '中国农业银行': [['#00A651', 0], ['#006B35', 1]],
            '中国银行': [['#B8860B', 0], ['#8B6914', 1]],
            '招商银行': [['#DC143C', 0], ['#8B0000', 1]],
            '交通银行': [['#0066CC', 0], ['#003D7A', 1]],
            '中信银行': [['#FF6B35', 0], ['#CC4125', 1]],
            '光大银行': [['#9932CC', 0], ['#6A1B9A', 1]],
            '华夏银行': [['#FF1744', 0], ['#C62828', 1]],
            '民生银行': [['#00BCD4', 0], ['#0097A7', 1]],
            '广发银行': [['#FF5722', 0], ['#D84315', 1]],
            '平安银行': [['#FF9800', 0], ['#F57C00', 1]]
        };
        return gradients[bankName] || [['#1976D2', 0], ['#0D47A1', 1]];
    }
    private maskCardNumber(cardNo: string): string {
        if (!cardNo || cardNo.length < 8) {
            return cardNo;
        }
        // 显示前4位和后4位
        const start = cardNo.substring(0, 4);
        const end = cardNo.substring(cardNo.length - 4);
        const middle = '*'.repeat(cardNo.length - 8);
        return `${start} ${middle} ${end}`;
    }
    private formatDateTime(dateTime: string): string {
        if (!dateTime)
            return '';
        const date = new Date(dateTime);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        });
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "BankCardDetailPage";
    }
}
registerNamedRoute(() => new BankCardDetailPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/BankCardDetailPage", pageFullPath: "entry/src/main/ets/pages/BankCardDetailPage", integratedHsp: "false", moduleType: "followWithHap" });
