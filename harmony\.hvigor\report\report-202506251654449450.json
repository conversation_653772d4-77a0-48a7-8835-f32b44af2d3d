{"version": "2.0", "ppid": 21780, "events": [{"head": {"id": "fb8cc914-0279-43c7-ae87-1ffe19cadd52", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26390919783500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "185396e6-05cb-4aa3-913a-f0c6fb7e159d", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26391061254600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6af50afd-4c26-4149-9650-61029cd47fe9", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26391061541700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79c553b6-3c2a-4679-b441-60fbf88f95d6", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127688870300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "912da9a3-7ef8-4e84-aa5b-57d909c5709e", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127699036800, "endTime": 28127854439300}, "additional": {"children": ["52ac0395-102f-4519-9e91-9503d11d8c4c", "de36775d-52ee-4185-b6e8-7c46c4abff56", "af57360d-c95d-4c86-baba-7c77a2182b6d", "0aafca47-a8fa-4bdd-b48b-eec68858aee6", "b87dca1c-0b43-4dc7-855f-847988d89826", "d431b6ab-df27-4166-ad6e-a142196947e0", "cf2a9994-fd33-45fe-96c4-e9bbdd183a14"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "426f30a0-ab72-4023-abc0-1170a8480ecc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52ac0395-102f-4519-9e91-9503d11d8c4c", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127699037500, "endTime": 28127711681900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "912da9a3-7ef8-4e84-aa5b-57d909c5709e", "logId": "9f2a175f-d393-49b9-8a4a-4e8585c700cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de36775d-52ee-4185-b6e8-7c46c4abff56", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127711696400, "endTime": 28127853398800}, "additional": {"children": ["acd2204c-457e-4902-a7c7-7110db6c33e4", "4990f3da-478a-4688-9b82-c5f5677a3bdc", "a53cecef-073a-4a1c-beac-ed9c0f618396", "de1876c9-a69d-4e2a-993e-f09d6b7e35cd", "629eb01f-78e6-437b-ac84-4d21edb3298c", "1a9ee1d5-a603-4e44-a14c-4387f882d89f", "f4485f78-f791-48f8-8da3-71096d556283", "33938b3f-dfaa-4669-8cfa-fa1210b19d92", "2f069efb-fda4-4c6b-b31a-54245548081f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "912da9a3-7ef8-4e84-aa5b-57d909c5709e", "logId": "af40f498-f5bb-4782-8888-8ebfb8f0f09f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af57360d-c95d-4c86-baba-7c77a2182b6d", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127853414000, "endTime": 28127854411500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "912da9a3-7ef8-4e84-aa5b-57d909c5709e", "logId": "52295f73-eefc-4d14-9d3d-ac068c890142"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0aafca47-a8fa-4bdd-b48b-eec68858aee6", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127854418700, "endTime": 28127854437000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "912da9a3-7ef8-4e84-aa5b-57d909c5709e", "logId": "e66e6f00-6119-48c0-a267-e2bb415bd7d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b87dca1c-0b43-4dc7-855f-847988d89826", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127702578200, "endTime": 28127702773300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "912da9a3-7ef8-4e84-aa5b-57d909c5709e", "logId": "e8c5d956-c32d-459d-861c-5afbf7634c4e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e8c5d956-c32d-459d-861c-5afbf7634c4e", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127702578200, "endTime": 28127702773300}, "additional": {"logType": "info", "children": [], "durationId": "b87dca1c-0b43-4dc7-855f-847988d89826", "parent": "426f30a0-ab72-4023-abc0-1170a8480ecc"}}, {"head": {"id": "d431b6ab-df27-4166-ad6e-a142196947e0", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127707309700, "endTime": 28127707338100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "912da9a3-7ef8-4e84-aa5b-57d909c5709e", "logId": "43e614e4-9694-42f7-b529-e906b50456b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "43e614e4-9694-42f7-b529-e906b50456b7", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127707309700, "endTime": 28127707338100}, "additional": {"logType": "info", "children": [], "durationId": "d431b6ab-df27-4166-ad6e-a142196947e0", "parent": "426f30a0-ab72-4023-abc0-1170a8480ecc"}}, {"head": {"id": "375428c7-4d42-4e48-bfb2-01affec57dbb", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127707392200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b1a8dae-a67d-4091-ac09-a4787744c078", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127711550400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f2a175f-d393-49b9-8a4a-4e8585c700cc", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127699037500, "endTime": 28127711681900}, "additional": {"logType": "info", "children": [], "durationId": "52ac0395-102f-4519-9e91-9503d11d8c4c", "parent": "426f30a0-ab72-4023-abc0-1170a8480ecc"}}, {"head": {"id": "acd2204c-457e-4902-a7c7-7110db6c33e4", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127716298100, "endTime": 28127716306800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de36775d-52ee-4185-b6e8-7c46c4abff56", "logId": "5f17acd7-3ec3-4539-b495-45c3cbe7d0b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4990f3da-478a-4688-9b82-c5f5677a3bdc", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127716315900, "endTime": 28127720838200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de36775d-52ee-4185-b6e8-7c46c4abff56", "logId": "b2b84f24-ceec-41c6-9d5a-fafa367f219e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a53cecef-073a-4a1c-beac-ed9c0f618396", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127720847700, "endTime": 28127798060700}, "additional": {"children": ["f9bf4654-96c4-4be6-8746-4fb7814c7c50", "6d966e1c-f564-4770-9324-a06a35d344e2", "679de0bf-4502-42bb-aa0f-66f175e6ebb0"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de36775d-52ee-4185-b6e8-7c46c4abff56", "logId": "1f529cf9-42ee-4356-b769-28c1358f50b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de1876c9-a69d-4e2a-993e-f09d6b7e35cd", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127798072000, "endTime": 28127819160100}, "additional": {"children": ["5f3b324e-6f8d-4a99-ad63-a63e2b1bb2a2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de36775d-52ee-4185-b6e8-7c46c4abff56", "logId": "d338a18e-7bb9-4500-aebf-cc76c3176b8c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "629eb01f-78e6-437b-ac84-4d21edb3298c", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127819165400, "endTime": 28127831447300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de36775d-52ee-4185-b6e8-7c46c4abff56", "logId": "e2eaf181-c508-4dcf-ac31-f9026a71dd9c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a9ee1d5-a603-4e44-a14c-4387f882d89f", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127832388600, "endTime": 28127842465700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de36775d-52ee-4185-b6e8-7c46c4abff56", "logId": "91d2c8b6-7700-4db1-87b0-daa1aa1a1b53"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4485f78-f791-48f8-8da3-71096d556283", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127842485200, "endTime": 28127853242400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de36775d-52ee-4185-b6e8-7c46c4abff56", "logId": "0857bf5b-3834-4ea7-933b-2845ef675436"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33938b3f-dfaa-4669-8cfa-fa1210b19d92", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127853265500, "endTime": 28127853390500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de36775d-52ee-4185-b6e8-7c46c4abff56", "logId": "d3dd217d-3cf3-43c1-832d-991d75a1feef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f17acd7-3ec3-4539-b495-45c3cbe7d0b7", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127716298100, "endTime": 28127716306800}, "additional": {"logType": "info", "children": [], "durationId": "acd2204c-457e-4902-a7c7-7110db6c33e4", "parent": "af40f498-f5bb-4782-8888-8ebfb8f0f09f"}}, {"head": {"id": "b2b84f24-ceec-41c6-9d5a-fafa367f219e", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127716315900, "endTime": 28127720838200}, "additional": {"logType": "info", "children": [], "durationId": "4990f3da-478a-4688-9b82-c5f5677a3bdc", "parent": "af40f498-f5bb-4782-8888-8ebfb8f0f09f"}}, {"head": {"id": "f9bf4654-96c4-4be6-8746-4fb7814c7c50", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127721422100, "endTime": 28127721606600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a53cecef-073a-4a1c-beac-ed9c0f618396", "logId": "b711fb2a-0233-4ab7-8a65-40a7d4cbce4c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b711fb2a-0233-4ab7-8a65-40a7d4cbce4c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127721422100, "endTime": 28127721606600}, "additional": {"logType": "info", "children": [], "durationId": "f9bf4654-96c4-4be6-8746-4fb7814c7c50", "parent": "1f529cf9-42ee-4356-b769-28c1358f50b2"}}, {"head": {"id": "6d966e1c-f564-4770-9324-a06a35d344e2", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127723445700, "endTime": 28127797390500}, "additional": {"children": ["06e2c9e4-2ec6-4cb4-9e67-7f7388b68b24", "c402a72e-4208-4f3b-8cab-8770eac2eac1"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a53cecef-073a-4a1c-beac-ed9c0f618396", "logId": "86896beb-5d01-4526-b2cf-2097671ed5c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06e2c9e4-2ec6-4cb4-9e67-7f7388b68b24", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127723446300, "endTime": 28127728445200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6d966e1c-f564-4770-9324-a06a35d344e2", "logId": "01ded883-3d4e-4e20-aa3f-fa4b0a808ed3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c402a72e-4208-4f3b-8cab-8770eac2eac1", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127728454600, "endTime": 28127797383800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6d966e1c-f564-4770-9324-a06a35d344e2", "logId": "122b892d-bf8a-4a42-ad2a-7eb82e0537c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "531ebbea-6ea2-4fc9-a7e8-ecd59a8bc316", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127723451000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96eaaacb-9a8f-4218-bcb8-079bfd1e37a5", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127728322500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01ded883-3d4e-4e20-aa3f-fa4b0a808ed3", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127723446300, "endTime": 28127728445200}, "additional": {"logType": "info", "children": [], "durationId": "06e2c9e4-2ec6-4cb4-9e67-7f7388b68b24", "parent": "86896beb-5d01-4526-b2cf-2097671ed5c2"}}, {"head": {"id": "b283e90b-4a06-4de4-b492-ce42bd6520ce", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127728463100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e9e4501-11c8-48a6-9269-a93d8cdc6681", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127734966800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faca62e8-dfd3-4f75-9794-3e6d90566462", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127735083200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0bd868b-0fc3-4aeb-935f-a6bdfb544c82", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127735206000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "565fafbc-a83f-492d-a2b1-d6e9e9c3b292", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127735271000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24c99a1d-e512-4a97-9bea-afb4ccae106d", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127736928600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04bb7dcd-eeb2-40ba-8bbd-5447f41e0118", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127742121700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfdc82b2-da7c-4deb-9dd5-ba6e23c94e52", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127751434300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "148c149a-f5be-4a8a-ae1a-d5ce1a44eba1", "name": "Sdk init in 32 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127774708200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b55abaad-d678-4d0e-a05e-14fc05ca7dac", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127774857700}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 16, "minute": 54}, "markType": "other"}}, {"head": {"id": "342c4234-4a16-424d-a929-a45094e666a1", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127774872200}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 16, "minute": 54}, "markType": "other"}}, {"head": {"id": "2b16c61b-ac0a-42aa-9331-c76bd37ac012", "name": "Project task initialization takes 21 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127797142300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4ef9e3d-5554-40c0-9911-761dc5f8f30c", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127797258200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f54c8ae-1f3b-49a7-b08e-70e3470f0474", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127797299500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b9ec9ca-e0d6-4a7d-a1d5-e547251060a5", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127797357500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "122b892d-bf8a-4a42-ad2a-7eb82e0537c1", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127728454600, "endTime": 28127797383800}, "additional": {"logType": "info", "children": [], "durationId": "c402a72e-4208-4f3b-8cab-8770eac2eac1", "parent": "86896beb-5d01-4526-b2cf-2097671ed5c2"}}, {"head": {"id": "86896beb-5d01-4526-b2cf-2097671ed5c2", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127723445700, "endTime": 28127797390500}, "additional": {"logType": "info", "children": ["01ded883-3d4e-4e20-aa3f-fa4b0a808ed3", "122b892d-bf8a-4a42-ad2a-7eb82e0537c1"], "durationId": "6d966e1c-f564-4770-9324-a06a35d344e2", "parent": "1f529cf9-42ee-4356-b769-28c1358f50b2"}}, {"head": {"id": "679de0bf-4502-42bb-aa0f-66f175e6ebb0", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127798038600, "endTime": 28127798051500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a53cecef-073a-4a1c-beac-ed9c0f618396", "logId": "7e0c9d33-eeaa-489b-a8ad-34bbed5bf367"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e0c9d33-eeaa-489b-a8ad-34bbed5bf367", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127798038600, "endTime": 28127798051500}, "additional": {"logType": "info", "children": [], "durationId": "679de0bf-4502-42bb-aa0f-66f175e6ebb0", "parent": "1f529cf9-42ee-4356-b769-28c1358f50b2"}}, {"head": {"id": "1f529cf9-42ee-4356-b769-28c1358f50b2", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127720847700, "endTime": 28127798060700}, "additional": {"logType": "info", "children": ["b711fb2a-0233-4ab7-8a65-40a7d4cbce4c", "86896beb-5d01-4526-b2cf-2097671ed5c2", "7e0c9d33-eeaa-489b-a8ad-34bbed5bf367"], "durationId": "a53cecef-073a-4a1c-beac-ed9c0f618396", "parent": "af40f498-f5bb-4782-8888-8ebfb8f0f09f"}}, {"head": {"id": "5f3b324e-6f8d-4a99-ad63-a63e2b1bb2a2", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127798633900, "endTime": 28127819149300}, "additional": {"children": ["42d3790b-4d51-45c8-a702-43ba6486c75e", "88aa8cc9-46f3-4cba-8ec0-cae36001b9ef", "2804ecef-ca0c-46ef-9a28-27be71e40e1a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de1876c9-a69d-4e2a-993e-f09d6b7e35cd", "logId": "21e5dd2c-931d-460f-b0b7-dcb2c293c36e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42d3790b-4d51-45c8-a702-43ba6486c75e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127801743100, "endTime": 28127801759000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5f3b324e-6f8d-4a99-ad63-a63e2b1bb2a2", "logId": "f9fbc5e0-af4d-4285-951b-21a3718e62b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f9fbc5e0-af4d-4285-951b-21a3718e62b5", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127801743100, "endTime": 28127801759000}, "additional": {"logType": "info", "children": [], "durationId": "42d3790b-4d51-45c8-a702-43ba6486c75e", "parent": "21e5dd2c-931d-460f-b0b7-dcb2c293c36e"}}, {"head": {"id": "88aa8cc9-46f3-4cba-8ec0-cae36001b9ef", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127803369400, "endTime": 28127817828800}, "additional": {"children": ["cd2bbd8b-97b0-4c95-9b87-47399fd9807b", "f979bb8f-5bdf-46e2-b278-110146dc0a00"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5f3b324e-6f8d-4a99-ad63-a63e2b1bb2a2", "logId": "3dbe9e5f-77ff-4f72-9213-21b903f7e986"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd2bbd8b-97b0-4c95-9b87-47399fd9807b", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127803370000, "endTime": 28127806410500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "88aa8cc9-46f3-4cba-8ec0-cae36001b9ef", "logId": "c92f02b8-d31b-44a0-8ade-b16aa7e857d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f979bb8f-5bdf-46e2-b278-110146dc0a00", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127806419900, "endTime": 28127817821800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "88aa8cc9-46f3-4cba-8ec0-cae36001b9ef", "logId": "002f311a-c38b-4f0f-b866-67e26b9d14a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2395e515-6e68-4d2f-91f9-8d547f9ce17d", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127803373700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3cba16e-8ecc-434f-a556-510bbee3e402", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127806280700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c92f02b8-d31b-44a0-8ade-b16aa7e857d4", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127803370000, "endTime": 28127806410500}, "additional": {"logType": "info", "children": [], "durationId": "cd2bbd8b-97b0-4c95-9b87-47399fd9807b", "parent": "3dbe9e5f-77ff-4f72-9213-21b903f7e986"}}, {"head": {"id": "d814b716-1dbc-471d-b045-4b175b286dea", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127806428900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "999977ca-7fb0-4de3-9a06-5b9674111251", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127813472100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70f10d09-dfd3-463f-9462-9bdcf2de33bd", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127813586900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a75c26c-187c-41fd-ba72-6850f48d32fd", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127813723600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17377866-a680-4484-8c5b-39f7a744be3d", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127813810200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ccda59d-222c-4588-89d1-a49693dfb177", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127813841400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6d5b761-fbca-4e92-9c09-797d7bdf0131", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127813868700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90762846-b200-47fa-abff-55ba74706919", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127813970800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdd52af3-a202-4a47-a0e5-bf4f1332742c", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127817577500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cabe0df4-9f9e-485c-99ef-705504cfcd53", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127817716700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95cb3150-2d63-42f3-afed-f4eaf435c1d3", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127817760600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d47f77a-6352-4af9-9a88-65986dcbfefd", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127817787900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "002f311a-c38b-4f0f-b866-67e26b9d14a8", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127806419900, "endTime": 28127817821800}, "additional": {"logType": "info", "children": [], "durationId": "f979bb8f-5bdf-46e2-b278-110146dc0a00", "parent": "3dbe9e5f-77ff-4f72-9213-21b903f7e986"}}, {"head": {"id": "3dbe9e5f-77ff-4f72-9213-21b903f7e986", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127803369400, "endTime": 28127817828800}, "additional": {"logType": "info", "children": ["c92f02b8-d31b-44a0-8ade-b16aa7e857d4", "002f311a-c38b-4f0f-b866-67e26b9d14a8"], "durationId": "88aa8cc9-46f3-4cba-8ec0-cae36001b9ef", "parent": "21e5dd2c-931d-460f-b0b7-dcb2c293c36e"}}, {"head": {"id": "2804ecef-ca0c-46ef-9a28-27be71e40e1a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127819130700, "endTime": 28127819141400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5f3b324e-6f8d-4a99-ad63-a63e2b1bb2a2", "logId": "9935c436-8592-42a8-a6df-ff75225d9e59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9935c436-8592-42a8-a6df-ff75225d9e59", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127819130700, "endTime": 28127819141400}, "additional": {"logType": "info", "children": [], "durationId": "2804ecef-ca0c-46ef-9a28-27be71e40e1a", "parent": "21e5dd2c-931d-460f-b0b7-dcb2c293c36e"}}, {"head": {"id": "21e5dd2c-931d-460f-b0b7-dcb2c293c36e", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127798633900, "endTime": 28127819149300}, "additional": {"logType": "info", "children": ["f9fbc5e0-af4d-4285-951b-21a3718e62b5", "3dbe9e5f-77ff-4f72-9213-21b903f7e986", "9935c436-8592-42a8-a6df-ff75225d9e59"], "durationId": "5f3b324e-6f8d-4a99-ad63-a63e2b1bb2a2", "parent": "d338a18e-7bb9-4500-aebf-cc76c3176b8c"}}, {"head": {"id": "d338a18e-7bb9-4500-aebf-cc76c3176b8c", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127798072000, "endTime": 28127819160100}, "additional": {"logType": "info", "children": ["21e5dd2c-931d-460f-b0b7-dcb2c293c36e"], "durationId": "de1876c9-a69d-4e2a-993e-f09d6b7e35cd", "parent": "af40f498-f5bb-4782-8888-8ebfb8f0f09f"}}, {"head": {"id": "9df7b951-06b2-45be-9d46-60457b6595cf", "name": "watch files: [\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\hvigorfile.ts',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127830832400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bfe377f-690f-418d-8717-0f3fff230312", "name": "hvigorfile, resolve hvigorfile dependencies in 13 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127831372300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2eaf181-c508-4dcf-ac31-f9026a71dd9c", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127819165400, "endTime": 28127831447300}, "additional": {"logType": "info", "children": [], "durationId": "629eb01f-78e6-437b-ac84-4d21edb3298c", "parent": "af40f498-f5bb-4782-8888-8ebfb8f0f09f"}}, {"head": {"id": "2f069efb-fda4-4c6b-b31a-54245548081f", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127832197100, "endTime": 28127832379900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de36775d-52ee-4185-b6e8-7c46c4abff56", "logId": "5afb26bf-0502-4fbd-8704-2e2e9f5d764a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b9a3e89-3c06-49b9-9ac4-f29bad220bcc", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127832223100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5afb26bf-0502-4fbd-8704-2e2e9f5d764a", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127832197100, "endTime": 28127832379900}, "additional": {"logType": "info", "children": [], "durationId": "2f069efb-fda4-4c6b-b31a-54245548081f", "parent": "af40f498-f5bb-4782-8888-8ebfb8f0f09f"}}, {"head": {"id": "bd7ed108-551a-4d14-bffe-f05fd96c21e6", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127833732500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a655a98c-1c4c-4a47-9438-5d736ea862cd", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127841407100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91d2c8b6-7700-4db1-87b0-daa1aa1a1b53", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127832388600, "endTime": 28127842465700}, "additional": {"logType": "info", "children": [], "durationId": "1a9ee1d5-a603-4e44-a14c-4387f882d89f", "parent": "af40f498-f5bb-4782-8888-8ebfb8f0f09f"}}, {"head": {"id": "7fdc4233-3f58-485a-a2d0-c5ecb189014d", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127842507100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e501c303-1479-450e-a1fe-41d62c2f1bc4", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127847549400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55442efa-46b3-4272-a997-02dbefa65fc6", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127847666400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f0e58f6-2c59-4f43-99e4-7d17c58a75d2", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127847871500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5efa565f-ce72-4c7a-a13d-6b1d16c9105d", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127850204700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00bbf581-0c73-441f-b5b1-7443c5f93865", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127850316700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0857bf5b-3834-4ea7-933b-2845ef675436", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127842485200, "endTime": 28127853242400}, "additional": {"logType": "info", "children": [], "durationId": "f4485f78-f791-48f8-8da3-71096d556283", "parent": "af40f498-f5bb-4782-8888-8ebfb8f0f09f"}}, {"head": {"id": "2a880c9d-0089-42c2-ae1a-cfbcf4ccc37b", "name": "Configuration phase cost:137 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127853290500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3dd217d-3cf3-43c1-832d-991d75a1feef", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127853265500, "endTime": 28127853390500}, "additional": {"logType": "info", "children": [], "durationId": "33938b3f-dfaa-4669-8cfa-fa1210b19d92", "parent": "af40f498-f5bb-4782-8888-8ebfb8f0f09f"}}, {"head": {"id": "af40f498-f5bb-4782-8888-8ebfb8f0f09f", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127711696400, "endTime": 28127853398800}, "additional": {"logType": "info", "children": ["5f17acd7-3ec3-4539-b495-45c3cbe7d0b7", "b2b84f24-ceec-41c6-9d5a-fafa367f219e", "1f529cf9-42ee-4356-b769-28c1358f50b2", "d338a18e-7bb9-4500-aebf-cc76c3176b8c", "e2eaf181-c508-4dcf-ac31-f9026a71dd9c", "91d2c8b6-7700-4db1-87b0-daa1aa1a1b53", "0857bf5b-3834-4ea7-933b-2845ef675436", "d3dd217d-3cf3-43c1-832d-991d75a1feef", "5afb26bf-0502-4fbd-8704-2e2e9f5d764a"], "durationId": "de36775d-52ee-4185-b6e8-7c46c4abff56", "parent": "426f30a0-ab72-4023-abc0-1170a8480ecc"}}, {"head": {"id": "cf2a9994-fd33-45fe-96c4-e9bbdd183a14", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127854390000, "endTime": 28127854403500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "912da9a3-7ef8-4e84-aa5b-57d909c5709e", "logId": "7e4bba31-e85c-4eac-9544-88122986d753"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e4bba31-e85c-4eac-9544-88122986d753", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127854390000, "endTime": 28127854403500}, "additional": {"logType": "info", "children": [], "durationId": "cf2a9994-fd33-45fe-96c4-e9bbdd183a14", "parent": "426f30a0-ab72-4023-abc0-1170a8480ecc"}}, {"head": {"id": "52295f73-eefc-4d14-9d3d-ac068c890142", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127853414000, "endTime": 28127854411500}, "additional": {"logType": "info", "children": [], "durationId": "af57360d-c95d-4c86-baba-7c77a2182b6d", "parent": "426f30a0-ab72-4023-abc0-1170a8480ecc"}}, {"head": {"id": "e66e6f00-6119-48c0-a267-e2bb415bd7d6", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127854418700, "endTime": 28127854437000}, "additional": {"logType": "info", "children": [], "durationId": "0aafca47-a8fa-4bdd-b48b-eec68858aee6", "parent": "426f30a0-ab72-4023-abc0-1170a8480ecc"}}, {"head": {"id": "426f30a0-ab72-4023-abc0-1170a8480ecc", "name": "init", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127699036800, "endTime": 28127854439300}, "additional": {"logType": "info", "children": ["9f2a175f-d393-49b9-8a4a-4e8585c700cc", "af40f498-f5bb-4782-8888-8ebfb8f0f09f", "52295f73-eefc-4d14-9d3d-ac068c890142", "e66e6f00-6119-48c0-a267-e2bb415bd7d6", "e8c5d956-c32d-459d-861c-5afbf7634c4e", "43e614e4-9694-42f7-b529-e906b50456b7", "7e4bba31-e85c-4eac-9544-88122986d753"], "durationId": "912da9a3-7ef8-4e84-aa5b-57d909c5709e"}}, {"head": {"id": "7cf0e8ae-0e06-4bd0-9832-70b1b1a8af95", "name": "Configuration task cost before running: 160 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127854554800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1483d0ae-330c-4a54-a2fc-7e16a39ed90d", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127858914100, "endTime": 28127866169000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "34033bf6-787d-4e47-b139-aa0017912a74", "logId": "a8b26df8-bde4-4eb0-a142-9689908a3a69"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34033bf6-787d-4e47-b139-aa0017912a74", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127855897300}, "additional": {"logType": "detail", "children": [], "durationId": "1483d0ae-330c-4a54-a2fc-7e16a39ed90d"}}, {"head": {"id": "debd118d-c2b3-4bc7-924c-be434ea269b7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127856365600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49a1d903-9a6e-4198-95b7-9dffdce5749e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127856443400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d35bfed-7861-4c67-b089-9152e20e42ea", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127858922600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e9e2d50-5092-40e6-bdb8-45a6ea1669be", "name": "Incremental task entry:default@PreBuild pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127865955000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66b1e72a-b562-4bd6-aa81-4ee5d333754c", "name": "entry : default@PreBuild cost memory 0.28934478759765625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127866092100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8b26df8-bde4-4eb0-a142-9689908a3a69", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127858914100, "endTime": 28127866169000}, "additional": {"logType": "info", "children": [], "durationId": "1483d0ae-330c-4a54-a2fc-7e16a39ed90d"}}, {"head": {"id": "c10583f0-3c6b-4c79-aa86-e42a2cada089", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127870722100, "endTime": 28127872982400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "344a9e07-b8e6-4ff0-88b5-7f51050a3776", "logId": "4ccdb7d9-485b-4aaa-a9bd-4b2d7720c876"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "344a9e07-b8e6-4ff0-88b5-7f51050a3776", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127869512200}, "additional": {"logType": "detail", "children": [], "durationId": "c10583f0-3c6b-4c79-aa86-e42a2cada089"}}, {"head": {"id": "cfdfe502-1089-4c3e-b45d-512b4bb696a7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127869993200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03ea2101-a0e1-4d64-9868-48e7da79825b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127870084400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8bca7cd-47f3-4694-b06f-5013bb7d858a", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127870730900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6ea20aa-37eb-4c20-ab74-d5c3e24ca710", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127872825300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f72df86d-1942-4ebd-8661-b308735d18c7", "name": "entry : default@MergeProfile cost memory -1.599212646484375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127872928200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ccdb7d9-485b-4aaa-a9bd-4b2d7720c876", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127870722100, "endTime": 28127872982400}, "additional": {"logType": "info", "children": [], "durationId": "c10583f0-3c6b-4c79-aa86-e42a2cada089"}}, {"head": {"id": "70d98ecc-3e17-4d59-b872-57dd686a8ed0", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127875732100, "endTime": 28127878065900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ba86eaf1-ca8e-4a8a-b416-b50589842040", "logId": "a3d3b093-b05e-491d-80f5-3bdcbefb86ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ba86eaf1-ca8e-4a8a-b416-b50589842040", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127874487000}, "additional": {"logType": "detail", "children": [], "durationId": "70d98ecc-3e17-4d59-b872-57dd686a8ed0"}}, {"head": {"id": "ccf167c7-bc6d-415c-8a07-68a3cdf5fdb4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127874959100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45b02119-8f80-4f9e-ae7a-615b7d74aabf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127875042800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db66b73e-55ef-4f29-ad24-65dbfff99625", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127875739400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08166986-778b-4ce5-a08c-759a9a674d4e", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127876814300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "010849ca-9076-4909-bbdc-9b3fa859f8a5", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127877903600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4479147c-35b7-4eba-a336-03b4a465f1d6", "name": "entry : default@CreateBuildProfile cost memory 0.0981597900390625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127878009600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3d3b093-b05e-491d-80f5-3bdcbefb86ae", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127875732100, "endTime": 28127878065900}, "additional": {"logType": "info", "children": [], "durationId": "70d98ecc-3e17-4d59-b872-57dd686a8ed0"}}, {"head": {"id": "b066a873-3138-45f1-bb0c-831937471caf", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127880595300, "endTime": 28127880942700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "19b8ae0c-6727-4d9d-9bff-7a03f8f1cf3b", "logId": "a9ec9203-cde7-418d-9d64-f3ecc59c4917"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19b8ae0c-6727-4d9d-9bff-7a03f8f1cf3b", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127879402300}, "additional": {"logType": "detail", "children": [], "durationId": "b066a873-3138-45f1-bb0c-831937471caf"}}, {"head": {"id": "d1551574-8ef4-4a24-8f86-3a6e8579d8da", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127879830400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1faed437-9116-400a-8923-ada9768311d7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127879935500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f89e8a80-0997-4029-a88c-e9d375cefea9", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127880602700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "388c87e7-786d-4b21-8478-bb34b56bc096", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127880753300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0fa8c7c-42cd-40e2-ae4d-14aa49b3cdf8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127880799400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05debf4b-b696-48eb-a153-6302fbbedac9", "name": "entry : default@PreCheckSyscap cost memory 0.03701019287109375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127880857000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91d546a6-c150-4556-a179-726b4b4b4e03", "name": "runTaskFromQueue task cost before running: 187 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127880912700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9ec9203-cde7-418d-9d64-f3ecc59c4917", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127880595300, "endTime": 28127880942700, "totalTime": 298400}, "additional": {"logType": "info", "children": [], "durationId": "b066a873-3138-45f1-bb0c-831937471caf"}}, {"head": {"id": "fa85ca35-3a53-40b2-9a0d-3a6404441d45", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127888519700, "endTime": 28127889656800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e6d45901-1452-40f0-8062-f9e046f7cd24", "logId": "20af8f8c-d4a8-49b7-a24e-6b50fc361aac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6d45901-1452-40f0-8062-f9e046f7cd24", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127882441300}, "additional": {"logType": "detail", "children": [], "durationId": "fa85ca35-3a53-40b2-9a0d-3a6404441d45"}}, {"head": {"id": "182e84a5-3969-4968-a928-be6222ec618b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127882901500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cac517f-7ccc-46fb-97a0-20a3deedf2e3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127882984800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05d0f19b-7a57-45bc-a3e8-49e7440c82f7", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127888531500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6f729cd-2b5c-4533-989e-dcb45de24181", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127888839900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7aea3da5-e064-4631-8bbb-4f89646a1655", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127889507100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddf956ff-39c7-46db-819b-c1aa309b253c", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06623077392578125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127889606900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20af8f8c-d4a8-49b7-a24e-6b50fc361aac", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127888519700, "endTime": 28127889656800}, "additional": {"logType": "info", "children": [], "durationId": "fa85ca35-3a53-40b2-9a0d-3a6404441d45"}}, {"head": {"id": "b7b6d648-4456-4a2a-adeb-ef16fb26baa4", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127892553400, "endTime": 28127893513100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "b4ac773f-0748-47cc-ab7c-dcadd7f6035a", "logId": "aaff141e-9496-4d7e-b1aa-d849fb719416"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4ac773f-0748-47cc-ab7c-dcadd7f6035a", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127891058800}, "additional": {"logType": "detail", "children": [], "durationId": "b7b6d648-4456-4a2a-adeb-ef16fb26baa4"}}, {"head": {"id": "730ccb31-c01f-4f9f-8de0-7fc8e0cd12c2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127891512000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74aeab0a-f766-4415-b986-caf010cb077b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127891585500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3162cd1d-09da-440a-9ab6-81ef4225310c", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127892560300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0455e6d-3a5a-424f-bc86-aa5bfd2baa6a", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127893376900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bafad00b-340c-49e1-b13f-6eac6e8f79e7", "name": "entry : default@ProcessProfile cost memory 0.05692291259765625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127893467400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaff141e-9496-4d7e-b1aa-d849fb719416", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127892553400, "endTime": 28127893513100}, "additional": {"logType": "info", "children": [], "durationId": "b7b6d648-4456-4a2a-adeb-ef16fb26baa4"}}, {"head": {"id": "19e6f0dc-9b06-4111-abab-1a007c72e411", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127898549900, "endTime": 28127905519300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "19a64236-5e25-4660-ba1b-346f4ba3b24b", "logId": "5ce67c4b-f598-4e09-92b3-abda7ca4a4cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19a64236-5e25-4660-ba1b-346f4ba3b24b", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127894858800}, "additional": {"logType": "detail", "children": [], "durationId": "19e6f0dc-9b06-4111-abab-1a007c72e411"}}, {"head": {"id": "1542259e-3393-4200-9fda-f22bc3db3e8b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127895308400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af401bc2-b03a-4cd4-a94d-b4fb2a78df62", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127895400300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f163d253-5e59-4811-9fc3-8871f48a0642", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127898560500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ca90199-16bb-49a8-8e8d-c3b2aa3999ca", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127905339400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55c09aca-7b23-472c-89ac-03995aaa6e2b", "name": "entry : default@ProcessRouterMap cost memory 0.19759368896484375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127905468500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ce67c4b-f598-4e09-92b3-abda7ca4a4cb", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127898549900, "endTime": 28127905519300}, "additional": {"logType": "info", "children": [], "durationId": "19e6f0dc-9b06-4111-abab-1a007c72e411"}}, {"head": {"id": "224a5ba4-5320-4d6d-a314-c0eeef68972b", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127911881800, "endTime": 28127915180100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "80d827a1-0a39-4a58-8e31-baf9d5087be1", "logId": "bd6b9cbc-f15f-4f62-8709-e0904732b974"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "80d827a1-0a39-4a58-8e31-baf9d5087be1", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127908524700}, "additional": {"logType": "detail", "children": [], "durationId": "224a5ba4-5320-4d6d-a314-c0eeef68972b"}}, {"head": {"id": "0c373824-6f0a-4320-8284-ce61c76f12ca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127909043900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3399d196-a0cc-4cbf-bb96-6f51b43ad670", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127909156100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "336ba2cd-654f-4944-93a0-d3eda14d0b6f", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127909975600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec514ca1-ec9a-4704-94b3-2fe615acaa8a", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127913681500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1915127-0f69-4dd0-aa8e-9624addc51dd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127913921600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eab78c93-29b4-4a27-8091-ea32832f07fb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127914024600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c83edb5e-3e8f-45e0-89ca-6a04e86a083c", "name": "entry : default@PreviewProcessResource cost memory 0.07037353515625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127914105100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc2db7d4-8442-4cb4-8b50-9378eb8de810", "name": "runTaskFromQueue task cost before running: 221 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127915083500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd6b9cbc-f15f-4f62-8709-e0904732b974", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127911881800, "endTime": 28127915180100, "totalTime": 2265400}, "additional": {"logType": "info", "children": [], "durationId": "224a5ba4-5320-4d6d-a314-c0eeef68972b"}}, {"head": {"id": "3432a27c-6f28-4214-bed2-c19d9cfea370", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127921534300, "endTime": 28127939504400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6b2b6ffb-2da6-4211-8554-157584e3d4ac", "logId": "140c4923-3428-4066-8540-8849f7389369"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b2b6ffb-2da6-4211-8554-157584e3d4ac", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127917839000}, "additional": {"logType": "detail", "children": [], "durationId": "3432a27c-6f28-4214-bed2-c19d9cfea370"}}, {"head": {"id": "d36df2cb-ee98-4298-a715-d39d164cc5d9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127918326600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30336f4d-33c1-492f-a055-770fcd4a76c5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127918427100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05a4700a-ce1c-41ab-bb39-679070c251ff", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127921547800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31edab6e-e60d-4914-bf8b-ae9c916cc587", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127939302900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "558986c5-13c7-4ef9-b244-5c924eaf2caa", "name": "entry : default@GenerateLoaderJson cost memory 0.7590866088867188", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127939444100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "140c4923-3428-4066-8540-8849f7389369", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127921534300, "endTime": 28127939504400}, "additional": {"logType": "info", "children": [], "durationId": "3432a27c-6f28-4214-bed2-c19d9cfea370"}}, {"head": {"id": "f863bb32-555d-41e9-9bd0-1acb6b3f0ad4", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127949894900, "endTime": 28127965098400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "92f98736-eae2-4ab9-a438-61310e84f3ea", "logId": "8c067547-ae66-4453-ac11-22a5b9d894d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92f98736-eae2-4ab9-a438-61310e84f3ea", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127946701500}, "additional": {"logType": "detail", "children": [], "durationId": "f863bb32-555d-41e9-9bd0-1acb6b3f0ad4"}}, {"head": {"id": "b28ec757-1756-448f-abec-9c617a3ba6c0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127947199400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7bc6385-bd69-4e70-9c1e-db4bf621f49c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127947289900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0355572c-ef9a-4f09-a220-adcf798f1720", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127948053500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9472cd3a-f284-457a-9b9b-89971a782088", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127949914500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68c02f60-044f-4bb7-bff5-c73b61a3675f", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 15 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127964864600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00eb40ec-7ec0-4c6c-8b0b-ee59d01da914", "name": "entry : default@PreviewCompileResource cost memory 0.694305419921875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127965018300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c067547-ae66-4453-ac11-22a5b9d894d3", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127949894900, "endTime": 28127965098400}, "additional": {"logType": "info", "children": [], "durationId": "f863bb32-555d-41e9-9bd0-1acb6b3f0ad4"}}, {"head": {"id": "f6b055ac-962f-43d5-9283-b3a74f4a48f8", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127967636000, "endTime": 28127967847600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "ffa69695-c7f1-4b13-910f-dfd0e91e71b6", "logId": "f510faee-f9f1-4872-aa5f-e7305eb526d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ffa69695-c7f1-4b13-910f-dfd0e91e71b6", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127966978600}, "additional": {"logType": "detail", "children": [], "durationId": "f6b055ac-962f-43d5-9283-b3a74f4a48f8"}}, {"head": {"id": "c94f3e72-2fe1-46a5-bc1c-0192d3994d9a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127967495400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0116fe8-0960-434c-bbd0-45e3588725d1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127967572500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d074fcd2-6f61-4e77-9b95-d3e8764a56f8", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127967640900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "774059a7-8456-44db-b158-3e6306624b0c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127967698400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f34f6e54-da3b-420b-a183-036f8b2614e1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127967725600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b8ff6ff-9731-4d8d-b1c4-5fdbb46c0666", "name": "entry : default@PreviewHookCompileResource cost memory 0.03812408447265625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127967768000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c1d997c-e1a4-4ef5-9079-67e58ebd9888", "name": "runTaskFromQueue task cost before running: 273 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127967819900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f510faee-f9f1-4872-aa5f-e7305eb526d6", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127967636000, "endTime": 28127967847600, "totalTime": 168400}, "additional": {"logType": "info", "children": [], "durationId": "f6b055ac-962f-43d5-9283-b3a74f4a48f8"}}, {"head": {"id": "4ec93d12-70e7-4552-a3a5-9dab897d36e9", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127970247800, "endTime": 28127972280600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "18abf5e1-4cac-466e-b333-1999ad0eee99", "logId": "31f9c785-a5d6-496b-bb16-a7ed0c41c91e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18abf5e1-4cac-466e-b333-1999ad0eee99", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127969224400}, "additional": {"logType": "detail", "children": [], "durationId": "4ec93d12-70e7-4552-a3a5-9dab897d36e9"}}, {"head": {"id": "4d66ff4f-1bc8-46dc-8a4a-bf432d778584", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127969673700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad757f7a-d8c1-4247-afbe-a3a0d8835880", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127969760500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b50841a-7557-493e-aa1c-1f5a9aee14f4", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127970253700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "356e88e5-7182-4719-b635-729de0d69288", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127972142300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c8e00a0-80db-479d-8d32-223d44a50229", "name": "entry : default@CopyPreviewProfile cost memory 0.09874725341796875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127972236400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31f9c785-a5d6-496b-bb16-a7ed0c41c91e", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127970247800, "endTime": 28127972280600}, "additional": {"logType": "info", "children": [], "durationId": "4ec93d12-70e7-4552-a3a5-9dab897d36e9"}}, {"head": {"id": "d92086b1-a2c4-4e49-a7dc-cb25e7cb12f1", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127976017000, "endTime": 28127976517000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "2cde992f-22c4-46b8-bee9-8787b8c79b8f", "logId": "f44dd3d2-f19e-4c11-90a6-d83945cbaf95"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2cde992f-22c4-46b8-bee9-8787b8c79b8f", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127974649800}, "additional": {"logType": "detail", "children": [], "durationId": "d92086b1-a2c4-4e49-a7dc-cb25e7cb12f1"}}, {"head": {"id": "e6393fc0-52ee-417d-aecf-a9992e363c33", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127975181800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "607bb20b-1517-493c-8758-cca16c8dedce", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127975275300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a30e4e14-8b23-4a33-b5b8-d883acb836b8", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127976026000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48508ac6-f9d8-45d7-a6c8-e93cdddf981c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127976145000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1075cf1c-83a5-42f1-b4e5-5829363d6054", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127976186300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33c94810-9d41-47d0-ab2a-1c599a073185", "name": "entry : default@ReplacePreviewerPage cost memory 0.03812408447265625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127976268000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eac27c70-0261-41bd-a5bf-4111546cb111", "name": "runTaskFromQueue task cost before running: 282 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127976474000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f44dd3d2-f19e-4c11-90a6-d83945cbaf95", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127976017000, "endTime": 28127976517000, "totalTime": 432000}, "additional": {"logType": "info", "children": [], "durationId": "d92086b1-a2c4-4e49-a7dc-cb25e7cb12f1"}}, {"head": {"id": "ee61bac2-2f87-47de-ac9a-e2cd50c9e0cb", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127977951400, "endTime": 28127978159700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "a86f0849-3e45-4f5f-8152-9ddbcf223c51", "logId": "47598380-7a3f-49fe-99c0-5109f60c7191"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a86f0849-3e45-4f5f-8152-9ddbcf223c51", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127977905900}, "additional": {"logType": "detail", "children": [], "durationId": "ee61bac2-2f87-47de-ac9a-e2cd50c9e0cb"}}, {"head": {"id": "de61fab7-9d4d-45f6-af66-8e3a732bd1a2", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127977957000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec9a60af-02de-4c88-acb2-fdf35cbc5a1b", "name": "entry : buildPreviewerResource cost memory 0.01168060302734375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127978070200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dba3c589-23c0-415c-bd7f-8337494cf39c", "name": "runTaskFromQueue task cost before running: 284 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127978127600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47598380-7a3f-49fe-99c0-5109f60c7191", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127977951400, "endTime": 28127978159700, "totalTime": 159400}, "additional": {"logType": "info", "children": [], "durationId": "ee61bac2-2f87-47de-ac9a-e2cd50c9e0cb"}}, {"head": {"id": "5e21901b-d0a9-4e9c-a68a-a9c29424dc94", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127980471900, "endTime": 28127982470600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "32ccce44-1196-4465-9d56-a0997a519dd6", "logId": "5cb6e50a-0d45-4ceb-920f-2aac09f2df10"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "32ccce44-1196-4465-9d56-a0997a519dd6", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127979358600}, "additional": {"logType": "detail", "children": [], "durationId": "5e21901b-d0a9-4e9c-a68a-a9c29424dc94"}}, {"head": {"id": "f58f57a9-87ac-4ddc-aed5-31b73514e57f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127979764500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccb18f6f-a789-4046-ac18-b475b8822ff0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127979854100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a0583d6-38e5-412e-91bb-b5f1adc3eceb", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127980478000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbe1653a-85eb-409b-a566-287e1e8efdf3", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127982332200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bd0f2cf-8f7f-4956-9f10-9c5a394558a7", "name": "entry : default@PreviewUpdateAssets cost memory 0.11550140380859375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127982416000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cb6e50a-0d45-4ceb-920f-2aac09f2df10", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127980471900, "endTime": 28127982470600}, "additional": {"logType": "info", "children": [], "durationId": "5e21901b-d0a9-4e9c-a68a-a9c29424dc94"}}, {"head": {"id": "f97cefb5-e784-4095-abb5-ff015636f5d8", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127989172400, "endTime": 28140897205700}, "additional": {"children": ["8892da01-6ea8-42e4-b0c7-12a900e2a8b0"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8f4171a1-f7ef-4507-80ca-f03e68bc193f", "logId": "66362007-f246-4f31-a54d-83b27f3dc225"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f4171a1-f7ef-4507-80ca-f03e68bc193f", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127984353600}, "additional": {"logType": "detail", "children": [], "durationId": "f97cefb5-e784-4095-abb5-ff015636f5d8"}}, {"head": {"id": "c6ac99f6-7340-44fd-9819-95d820f3f34e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127984855400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db6f3016-543a-44eb-888a-d6510095a87e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127984949800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a0809eb-0319-4a28-a387-6a102c8bf595", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127989182400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker7", "startTime": 28128007621300, "endTime": 28140896952700}, "additional": {"children": ["70290ad6-892c-43c3-87a3-00dd18692e61", "1010ffc4-1450-4e0c-a213-b300d5ebdde4", "d7aed4a9-3cb0-4ce4-ad23-e4740ff758aa", "30a9113e-1c3f-4846-98ea-b4aa07d3fd38", "b9659043-918d-4768-97d2-caa0b6961d9a", "ecaf9b34-c4e5-40aa-9bb6-ccdee8a87647", "338928d3-fd24-4651-929c-0bb72ab8a702", "72bfa1d1-f266-4f46-9a0e-d048150c8b3e", "b6ff4c2e-2cd2-4930-a42c-84a809c11dc4", "360e6422-1109-47b8-9a6b-67882d8801a7", "fbe021ce-fb12-4951-89b1-25deb2ac2524", "a1dba487-2855-4365-aa78-4d0da1579c7c", "bf056751-8520-4dcb-b1ea-334fbdbfb9eb", "eb4e5d2a-5425-4919-9bcc-3be4d6a8664a", "0bc88c7f-df37-4ab6-8685-f16e155fb243", "0612df08-885e-4559-872e-da932a4775ab", "c16b14c2-2485-416a-93a6-d57778e02918", "57acd8f0-c2ca-482d-bd83-20ca2772915c", "40852d27-0cce-4b14-be2a-58538f5dcd39", "9296c125-6734-4571-a1c5-0552241a0e93", "613d629f-a5e7-471b-abbf-8639a8912a7b", "c9043bf9-df82-4a82-82ee-6aa1c86dfccd", "da0c39ae-01b8-47c2-b6f0-b23905848b94", "80312e82-66d9-4136-b171-9b55e54d7079", "31bf539d-02b2-46ea-8be7-34556ff8e522", "5fa8e7cc-c267-44c2-b75d-bbf28e071617", "c3e65e91-5fba-4bb6-a50c-9bbb55485657", "b3defd64-f035-439f-89dc-ff22d85b8b35", "6de9d397-6c6e-4ead-9380-f727d563a362", "4b71546a-be69-4641-9e86-29c44e44adca", "c4b60f9d-4194-4b94-914c-2fecbfaf2042"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "f97cefb5-e784-4095-abb5-ff015636f5d8", "logId": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fdf0b7e7-dd7b-4b3b-a640-b0d7d29c3624", "name": "entry : default@PreviewArkTS cost memory -0.5767288208007812", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28128010234300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bde00669-53fa-420d-8f3a-9dc31cee7020", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28131564715900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70290ad6-892c-43c3-87a3-00dd18692e61", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28131565815400, "endTime": 28131565831100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "c49cd33a-e231-46b0-8ae9-c545dd38a56e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c49cd33a-e231-46b0-8ae9-c545dd38a56e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28131565815400, "endTime": 28131565831100}, "additional": {"logType": "info", "children": [], "durationId": "70290ad6-892c-43c3-87a3-00dd18692e61", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "8a8c2014-c70a-4a78-b951-a71ec78d22f0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135502083000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1010ffc4-1450-4e0c-a213-b300d5ebdde4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135503331900, "endTime": 28135503348300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "8c8a9a96-691b-4150-ac4f-d33b607f63d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c8a9a96-691b-4150-ac4f-d33b607f63d4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135503331900, "endTime": 28135503348300}, "additional": {"logType": "info", "children": [], "durationId": "1010ffc4-1450-4e0c-a213-b300d5ebdde4", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "a449ff20-5b71-4d3b-8c6e-ed1ae1f4668e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135503413000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7aed4a9-3cb0-4ce4-ad23-e4740ff758aa", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135504078800, "endTime": 28135504091100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "3af8d90e-212a-4eae-bef9-6b755836546d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3af8d90e-212a-4eae-bef9-6b755836546d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135504078800, "endTime": 28135504091100}, "additional": {"logType": "info", "children": [], "durationId": "d7aed4a9-3cb0-4ce4-ad23-e4740ff758aa", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "15dfd44c-eb9b-4b34-9fd6-d45d7ee8e0f5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135504145700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30a9113e-1c3f-4846-98ea-b4aa07d3fd38", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135504746900, "endTime": 28135504757500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "ad6f041a-37db-4e36-9b9e-06fd58d0faec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad6f041a-37db-4e36-9b9e-06fd58d0faec", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135504746900, "endTime": 28135504757500}, "additional": {"logType": "info", "children": [], "durationId": "30a9113e-1c3f-4846-98ea-b4aa07d3fd38", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "442f4b6d-61f1-4201-a934-0f393eaafc1c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135504810200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9659043-918d-4768-97d2-caa0b6961d9a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135505453800, "endTime": 28135505465100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "8f49e447-6b4c-446c-b911-db6660ee138a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f49e447-6b4c-446c-b911-db6660ee138a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135505453800, "endTime": 28135505465100}, "additional": {"logType": "info", "children": [], "durationId": "b9659043-918d-4768-97d2-caa0b6961d9a", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "f3e81001-a40b-4ba8-b6d1-27af1dcc8f61", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135505517300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecaf9b34-c4e5-40aa-9bb6-ccdee8a87647", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135506154700, "endTime": 28135506166300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "808a700c-ab2c-46a4-ae9c-15bed9da741d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "808a700c-ab2c-46a4-ae9c-15bed9da741d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135506154700, "endTime": 28135506166300}, "additional": {"logType": "info", "children": [], "durationId": "ecaf9b34-c4e5-40aa-9bb6-ccdee8a87647", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "f50600c7-0186-4ceb-85cd-379753db306e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135506217000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "338928d3-fd24-4651-929c-0bb72ab8a702", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135506809800, "endTime": 28135506820400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "0b8807b0-a7c6-492b-a426-1e7861524618"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b8807b0-a7c6-492b-a426-1e7861524618", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135506809800, "endTime": 28135506820400}, "additional": {"logType": "info", "children": [], "durationId": "338928d3-fd24-4651-929c-0bb72ab8a702", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "38612426-451b-4200-8c45-689d492ea7b6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135506874300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72bfa1d1-f266-4f46-9a0e-d048150c8b3e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135507498600, "endTime": 28135507509900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "7639f55d-0983-44f1-9880-b55c87551c9b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7639f55d-0983-44f1-9880-b55c87551c9b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135507498600, "endTime": 28135507509900}, "additional": {"logType": "info", "children": [], "durationId": "72bfa1d1-f266-4f46-9a0e-d048150c8b3e", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "b4320369-c9fd-4d8c-a0df-6320d1b47f74", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135507562700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6ff4c2e-2cd2-4930-a42c-84a809c11dc4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135508212700, "endTime": 28135508225600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "fb55cbf4-9bf1-4934-a8f0-b28203248c81"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb55cbf4-9bf1-4934-a8f0-b28203248c81", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135508212700, "endTime": 28135508225600}, "additional": {"logType": "info", "children": [], "durationId": "b6ff4c2e-2cd2-4930-a42c-84a809c11dc4", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "9e558a7c-7cfa-4b65-9e1a-e5fd80de7167", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135508379600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "360e6422-1109-47b8-9a6b-67882d8801a7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135510007600, "endTime": 28135510051400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "0fd07105-24fc-45b5-84bf-1ca55da1d8f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0fd07105-24fc-45b5-84bf-1ca55da1d8f2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135510007600, "endTime": 28135510051400}, "additional": {"logType": "info", "children": [], "durationId": "360e6422-1109-47b8-9a6b-67882d8801a7", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "382698d2-2ee8-442f-a9a4-c5e3cf770cc3", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135510163400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbe021ce-fb12-4951-89b1-25deb2ac2524", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135511741100, "endTime": 28135511764300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "3366d672-75d6-4898-ae49-c80e923c6703"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3366d672-75d6-4898-ae49-c80e923c6703", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135511741100, "endTime": 28135511764300}, "additional": {"logType": "info", "children": [], "durationId": "fbe021ce-fb12-4951-89b1-25deb2ac2524", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "60bec7bd-7acf-4204-bb64-c557c73ed9e9", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135511864800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1dba487-2855-4365-aa78-4d0da1579c7c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135513488900, "endTime": 28135513511300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "412a8ce4-4a42-41e8-be1e-4495f97a1093"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "412a8ce4-4a42-41e8-be1e-4495f97a1093", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135513488900, "endTime": 28135513511300}, "additional": {"logType": "info", "children": [], "durationId": "a1dba487-2855-4365-aa78-4d0da1579c7c", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "f540ad05-5b5f-4b46-83d4-c1134d1e26bc", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135513630900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf056751-8520-4dcb-b1ea-334fbdbfb9eb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135515333000, "endTime": 28135515359000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "76907b81-802a-4665-8c03-0aa50ea5b4e8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "76907b81-802a-4665-8c03-0aa50ea5b4e8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135515333000, "endTime": 28135515359000}, "additional": {"logType": "info", "children": [], "durationId": "bf056751-8520-4dcb-b1ea-334fbdbfb9eb", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "0fded7cc-615a-4b5f-9c1b-b5c50a12c7b3", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135515507500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb4e5d2a-5425-4919-9bcc-3be4d6a8664a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135517278500, "endTime": 28135517299900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "96dbf4ec-d139-4e06-a493-e9ae0acd82fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96dbf4ec-d139-4e06-a493-e9ae0acd82fe", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135517278500, "endTime": 28135517299900}, "additional": {"logType": "info", "children": [], "durationId": "eb4e5d2a-5425-4919-9bcc-3be4d6a8664a", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "439a40aa-a407-49d1-9ddc-3499759b86d9", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135517425100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bc88c7f-df37-4ab6-8685-f16e155fb243", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135518876400, "endTime": 28135518895700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "7bfd0573-a917-4b20-a838-d14e3db6b409"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7bfd0573-a917-4b20-a838-d14e3db6b409", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135518876400, "endTime": 28135518895700}, "additional": {"logType": "info", "children": [], "durationId": "0bc88c7f-df37-4ab6-8685-f16e155fb243", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "7b6bcf38-2c68-4bad-91a8-175b8c5fab2a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135518989300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0612df08-885e-4559-872e-da932a4775ab", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135520448700, "endTime": 28135520468000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "521cd8dd-185c-4768-8dd5-4b87523c8991"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "521cd8dd-185c-4768-8dd5-4b87523c8991", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135520448700, "endTime": 28135520468000}, "additional": {"logType": "info", "children": [], "durationId": "0612df08-885e-4559-872e-da932a4775ab", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "62546869-1dc9-4d09-a132-7a88c1a6307a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135520568000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c16b14c2-2485-416a-93a6-d57778e02918", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135522417300, "endTime": 28135522441600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "bd20067c-bf11-4553-aa12-5a1544d2f6d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bd20067c-bf11-4553-aa12-5a1544d2f6d5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135522417300, "endTime": 28135522441600}, "additional": {"logType": "info", "children": [], "durationId": "c16b14c2-2485-416a-93a6-d57778e02918", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "3f65428b-a0cf-4768-acd3-40810860fad6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135522546400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57acd8f0-c2ca-482d-bd83-20ca2772915c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135524159700, "endTime": 28135524185000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "b3c5dfd1-5d32-4889-aaf9-69e3006ac2c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3c5dfd1-5d32-4889-aaf9-69e3006ac2c3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135524159700, "endTime": 28135524185000}, "additional": {"logType": "info", "children": [], "durationId": "57acd8f0-c2ca-482d-bd83-20ca2772915c", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "39ba5181-9c07-45eb-ae32-6c81ee851eb6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135524299100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40852d27-0cce-4b14-be2a-58538f5dcd39", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135526787000, "endTime": 28135526807700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "ecb01ba1-d978-4d5a-9dcd-2b81cd91bdbb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ecb01ba1-d978-4d5a-9dcd-2b81cd91bdbb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135526787000, "endTime": 28135526807700}, "additional": {"logType": "info", "children": [], "durationId": "40852d27-0cce-4b14-be2a-58538f5dcd39", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "cb97f12c-1fe3-486f-be82-4a2caf0bc2e8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135526907100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9296c125-6734-4571-a1c5-0552241a0e93", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135527947900, "endTime": 28135527967000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "7ae463b7-9b99-4c2c-9e92-0840fa08c50e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ae463b7-9b99-4c2c-9e92-0840fa08c50e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135527947900, "endTime": 28135527967000}, "additional": {"logType": "info", "children": [], "durationId": "9296c125-6734-4571-a1c5-0552241a0e93", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "357ada04-3c26-4510-82b0-e058dc18ffdc", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135528071200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "613d629f-a5e7-471b-abbf-8639a8912a7b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135529030600, "endTime": 28135529052300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "1e2f304b-0d5e-4667-989e-8f2a966f9777"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e2f304b-0d5e-4667-989e-8f2a966f9777", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135529030600, "endTime": 28135529052300}, "additional": {"logType": "info", "children": [], "durationId": "613d629f-a5e7-471b-abbf-8639a8912a7b", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "633fcda0-b2b3-4361-b628-af1d941b2c42", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135529157200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9043bf9-df82-4a82-82ee-6aa1c86dfccd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135530025600, "endTime": 28135530041500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "bb9c1c9c-4c5e-4d6c-aac5-e03f9eb0d5ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb9c1c9c-4c5e-4d6c-aac5-e03f9eb0d5ff", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135530025600, "endTime": 28135530041500}, "additional": {"logType": "info", "children": [], "durationId": "c9043bf9-df82-4a82-82ee-6aa1c86dfccd", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "b49225a7-579b-4ca7-a8fa-22dac08a0519", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135530111500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da0c39ae-01b8-47c2-b6f0-b23905848b94", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135531096000, "endTime": 28135531114000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "fe9d9449-a2ec-46d1-9795-08976a6df8ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe9d9449-a2ec-46d1-9795-08976a6df8ec", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135531096000, "endTime": 28135531114000}, "additional": {"logType": "info", "children": [], "durationId": "da0c39ae-01b8-47c2-b6f0-b23905848b94", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "4737d8e8-db62-4d53-8956-e818fc735a6e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135531200500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80312e82-66d9-4136-b171-9b55e54d7079", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135532326200, "endTime": 28135532347400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "f02b4b3c-8653-4094-8849-06a703ee815d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f02b4b3c-8653-4094-8849-06a703ee815d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135532326200, "endTime": 28135532347400}, "additional": {"logType": "info", "children": [], "durationId": "80312e82-66d9-4136-b171-9b55e54d7079", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "f98e2790-06a5-4cd3-8458-ef19cdf4fa1d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135532464400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31bf539d-02b2-46ea-8be7-34556ff8e522", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135533839500, "endTime": 28135533862700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "f215dad7-0aa8-480c-bac8-3eea312aa7da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f215dad7-0aa8-480c-bac8-3eea312aa7da", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135533839500, "endTime": 28135533862700}, "additional": {"logType": "info", "children": [], "durationId": "31bf539d-02b2-46ea-8be7-34556ff8e522", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "d0c5c5d0-056a-433f-8646-b82f045f0454", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135533950400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fa8e7cc-c267-44c2-b75d-bbf28e071617", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135535293900, "endTime": 28135535317400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "4574f81c-118b-4ae1-9cc4-bb69b6cc4e72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4574f81c-118b-4ae1-9cc4-bb69b6cc4e72", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135535293900, "endTime": 28135535317400}, "additional": {"logType": "info", "children": [], "durationId": "5fa8e7cc-c267-44c2-b75d-bbf28e071617", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "7e6b5842-d5ba-4403-8824-fbc800c324d2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135535447600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3e65e91-5fba-4bb6-a50c-9bbb55485657", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135537059000, "endTime": 28135537090000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "9dfce004-e0de-4742-b837-c450546a64f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9dfce004-e0de-4742-b837-c450546a64f0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28135537059000, "endTime": 28135537090000}, "additional": {"logType": "info", "children": [], "durationId": "c3e65e91-5fba-4bb6-a50c-9bbb55485657", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "425323b1-865b-4cbb-af3f-ca6c242c24b6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28136725061600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3defd64-f035-439f-89dc-ff22d85b8b35", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28136726857500, "endTime": 28136726883300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "d4c15ff2-7a8e-42af-8810-ba82ac020103"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4c15ff2-7a8e-42af-8810-ba82ac020103", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28136726857500, "endTime": 28136726883300}, "additional": {"logType": "info", "children": [], "durationId": "b3defd64-f035-439f-89dc-ff22d85b8b35", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "903af0b0-b627-4814-a6fa-2350f77d8d61", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28137094030800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6de9d397-6c6e-4ead-9380-f727d563a362", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28137096305800, "endTime": 28137096329000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "d4e48a4f-9e1d-4674-a9f4-ad0b5257efaf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4e48a4f-9e1d-4674-a9f4-ad0b5257efaf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28137096305800, "endTime": 28137096329000}, "additional": {"logType": "info", "children": [], "durationId": "6de9d397-6c6e-4ead-9380-f727d563a362", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "39e7a51b-7a3c-41fe-9091-c4d64292afee", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28137163658900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b71546a-be69-4641-9e86-29c44e44adca", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28137164671500, "endTime": 28137164688900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "ef52498d-40ea-4661-8163-43433fff3bb3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef52498d-40ea-4661-8163-43433fff3bb3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28137164671500, "endTime": 28137164688900}, "additional": {"logType": "info", "children": [], "durationId": "4b71546a-be69-4641-9e86-29c44e44adca", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "70a274d9-7ed9-46f1-a4a8-39434041504a", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140895295900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4b60f9d-4194-4b94-914c-2fecbfaf2042", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140896846100, "endTime": 28140896866600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "logId": "225248f9-195a-491e-8065-c833b866f85d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "225248f9-195a-491e-8065-c833b866f85d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140896846100, "endTime": 28140896866600}, "additional": {"logType": "info", "children": [], "durationId": "c4b60f9d-4194-4b94-914c-2fecbfaf2042", "parent": "db5c372f-60a5-475d-b052-25a2bf1d6ae7"}}, {"head": {"id": "db5c372f-60a5-475d-b052-25a2bf1d6ae7", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Worker7", "startTime": 28128007621300, "endTime": 28140896952700}, "additional": {"logType": "error", "children": ["c49cd33a-e231-46b0-8ae9-c545dd38a56e", "8c8a9a96-691b-4150-ac4f-d33b607f63d4", "3af8d90e-212a-4eae-bef9-6b755836546d", "ad6f041a-37db-4e36-9b9e-06fd58d0faec", "8f49e447-6b4c-446c-b911-db6660ee138a", "808a700c-ab2c-46a4-ae9c-15bed9da741d", "0b8807b0-a7c6-492b-a426-1e7861524618", "7639f55d-0983-44f1-9880-b55c87551c9b", "fb55cbf4-9bf1-4934-a8f0-b28203248c81", "0fd07105-24fc-45b5-84bf-1ca55da1d8f2", "3366d672-75d6-4898-ae49-c80e923c6703", "412a8ce4-4a42-41e8-be1e-4495f97a1093", "76907b81-802a-4665-8c03-0aa50ea5b4e8", "96dbf4ec-d139-4e06-a493-e9ae0acd82fe", "7bfd0573-a917-4b20-a838-d14e3db6b409", "521cd8dd-185c-4768-8dd5-4b87523c8991", "bd20067c-bf11-4553-aa12-5a1544d2f6d5", "b3c5dfd1-5d32-4889-aaf9-69e3006ac2c3", "ecb01ba1-d978-4d5a-9dcd-2b81cd91bdbb", "7ae463b7-9b99-4c2c-9e92-0840fa08c50e", "1e2f304b-0d5e-4667-989e-8f2a966f9777", "bb9c1c9c-4c5e-4d6c-aac5-e03f9eb0d5ff", "fe9d9449-a2ec-46d1-9795-08976a6df8ec", "f02b4b3c-8653-4094-8849-06a703ee815d", "f215dad7-0aa8-480c-bac8-3eea312aa7da", "4574f81c-118b-4ae1-9cc4-bb69b6cc4e72", "9dfce004-e0de-4742-b837-c450546a64f0", "d4c15ff2-7a8e-42af-8810-ba82ac020103", "d4e48a4f-9e1d-4674-a9f4-ad0b5257efaf", "ef52498d-40ea-4661-8163-43433fff3bb3", "225248f9-195a-491e-8065-c833b866f85d"], "durationId": "8892da01-6ea8-42e4-b0c7-12a900e2a8b0", "parent": "66362007-f246-4f31-a54d-83b27f3dc225"}}, {"head": {"id": "db486241-4754-4862-b3ae-29593ea96c48", "name": "default@PreviewArkTS watch work[7] failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140897027500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66362007-f246-4f31-a54d-83b27f3dc225", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127989172400, "endTime": 28140897205700}, "additional": {"logType": "error", "children": ["db5c372f-60a5-475d-b052-25a2bf1d6ae7"], "durationId": "f97cefb5-e784-4095-abb5-ff015636f5d8"}}, {"head": {"id": "aac2d94e-32c1-4abc-a28d-a32020aa227a", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140897344600}, "additional": {"logType": "debug", "children": [], "durationId": "f97cefb5-e784-4095-abb5-ff015636f5d8"}}, {"head": {"id": "34e4ceed-d2ae-4796-a7db-7eb905038fcd", "name": "ERROR: stacktrace = Error: \u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:53:13\n Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown)\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/PaymentPage.ets:504:30\n Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/PaymentPage.ets:516:9\n Indexed access is not supported for fields (arkts-no-props-by-index)\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/PaymentPage.ets:517:9\n Indexed access is not supported for fields (arkts-no-props-by-index)\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/PaymentPage.ets:521:13\n Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown)\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/PaymentPage.ets:521:30\n Type inference in case of generic function calls is limited (arkts-no-inferred-generic-params)\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/common/storage/StorageManager.ets:2:10\n Module '\"@ohos.app.ability.common\"' has no exported member 'Context'. Did you mean to use 'import Context from \"@ohos.app.ability.common\"' instead?\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/BankCardPage.ets:108:59\n Property 'status' does not exist on type 'BankCard'.\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/BankCardPage.ets:419:19\n Property 'status' does not exist on type 'BankCard'.\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/BankCardPage.ets:421:27\n Property 'status' does not exist on type 'BankCard'.\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/BankCardPage.ets:451:16\n Property 'status' does not exist on type 'BankCard'.\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/TransactionListPage.ets:324:27\n Property 'paymentChannel' does not exist on type 'Transaction'.\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/TransactionListPage.ets:325:57\n Property 'paymentChannel' does not exist on type 'Transaction'.\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:53:42\n Property 'getWalletInfo' does not exist on type 'typeof WalletApi'.\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:247:18\n Argument of type 'string' is not assignable to parameter of type 'boolean'.\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:280:18\n Argument of type 'string' is not assignable to parameter of type 'boolean'.\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:348:18\n Argument of type 'string' is not assignable to parameter of type 'boolean'.\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:585:13\n Property 'userId' is missing in type '{ amount: number; cardId: number; payPassword: string; }' but required in type 'WalletRechargeRequest'.\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:596:26\n Property 'notifyRefresh' does not exist on type 'GlobalStateManager'.\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:596:53\n Property 'WALLET_BALANCE' does not exist on type 'typeof RefreshTypes'.\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:632:13\n Property 'userId' is missing in type '{ amount: number; cardId: number; payPassword: string; }' but required in type 'WalletWithdrawRequest'.\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:643:26\n Property 'notifyRefresh' does not exist on type 'GlobalStateManager'.\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:643:53\n Property 'WALLET_BALANCE' does not exist on type 'typeof RefreshTypes'.\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:698:26\n Property 'notifyRefresh' does not exist on type 'GlobalStateManager'.\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:698:53\n Property 'WALLET_BALANCE' does not exist on type 'typeof RefreshTypes'.\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/BankCardPage.ets:265:5\n Only UI component syntax can be written in build method.\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:165:7\n Only UI component syntax can be written in build method.\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/PaymentPage.ets:293:20\n Unknown resource name 'ic_eye_off'.\n    at handleResponse (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": **************}, "additional": {"logType": "debug", "children": [], "durationId": "f97cefb5-e784-4095-abb5-ff015636f5d8"}}, {"head": {"id": "73cbb250-e35b-40ed-90bf-de46166aad9d", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140911207900, "endTime": 28140911259200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3bcbbfcb-10c9-426b-a679-f40d65486b4f", "logId": "b36525b9-c3f4-4f8b-8765-38086da82a52"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b36525b9-c3f4-4f8b-8765-38086da82a52", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140911207900, "endTime": 28140911259200}, "additional": {"logType": "info", "children": [], "durationId": "73cbb250-e35b-40ed-90bf-de46166aad9d"}}, {"head": {"id": "28471810-af93-4b07-b335-20ff42f9aba1", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28127694867100, "endTime": 28140911358600}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 16, "minute": 54}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "dce76e8f-3594-4cf3-bafb-7dae0bf4580d", "name": "BUILD FAILED in 13 s 217 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140911386400}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "6c474433-6725-47cc-93c2-9001156b1a23", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140911518600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b160958-8313-4757-8524-f8cb43b03ae1", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140911559400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a666933d-37ad-4957-b430-10c8a46c0c75", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140911584900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1a2d5c4-0ac3-420b-ae7c-15c2cdb03515", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140911606300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee89e7f3-7bad-4a6e-8e18-777a3a64e612", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140911630500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abe56147-769e-4d92-90c8-451c03dc968a", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140911653100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaca667b-4488-4672-aa6f-458cda584b79", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140911674500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8cd13ca-d090-4e97-83d2-8cacf36c982c", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140911693900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5f36bd5-4221-444e-be9b-f68bf97ae067", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140911714100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ea2b9fb-92b5-4b21-ae00-71724ca2755e", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140911737500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d291b69a-2ad3-45d1-b29a-d35a9460d872", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140918524100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89cd9ffc-9440-4827-9539-7356f7e44886", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140920223800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "621f946e-fde5-49e1-985e-c59f99a693c8", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140920752700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "718e750e-62da-4c92-8e56-c5c0e3a4f6a2", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140921292000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1326e10d-8439-4433-afbd-d6b8c7f0a177", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140922957500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e4e9037-3e24-4e43-83e1-49ee8f5b2cd3", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140945914000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a0e0f88-6685-428d-98ce-099efff1e664", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140946479600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd6f101e-b8f8-4b42-b6b3-e9747b59430b", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140947008900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f91e534d-2833-4cfe-8531-f335afe7972a", "name": "Update task entry:default@PreviewArkTS output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140947586800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "572df042-2a18-4daa-9f0d-a7dfdcc4e126", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:37 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140948055500}, "additional": {"logType": "debug", "children": []}}], "workLog": []}