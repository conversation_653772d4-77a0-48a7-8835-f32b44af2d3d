{"version": "2.0", "ppid": 21780, "events": [{"head": {"id": "a197fff4-33f1-4aa0-b883-c28b0bba7f71", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327707278200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a22463c4-f2ab-46e6-83fa-0d6976ae1cc6", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327719301400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de157499-68fc-4a7e-a513-c89db8acad4e", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29327719548200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72c9b6b8-01b6-40bb-849a-29a2683f56d1", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523185736700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bddf5a80-6277-4543-96d9-98c67c13e2cf", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523195203700, "endTime": 29523465660600}, "additional": {"children": ["b496047c-2da6-4ab3-9cfc-369d1d639594", "d1b481c3-5330-4adb-ba13-dedb70257c81", "41ffaec4-ced7-4e06-96f8-3ccd9d81d000", "53dd8eb9-49d5-4018-b600-b0c237996a67", "ef46c0e1-832b-4a8e-aeba-366e1314f1b3", "6ee09bf4-63a0-494c-a7aa-72f2c5a2962c", "d4dd8c55-46e9-4bb3-bfcb-6fc6f289c9ba"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "005d5292-51bd-43e8-b75d-9e9ef79a2b92"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b496047c-2da6-4ab3-9cfc-369d1d639594", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523195204400, "endTime": 29523215094500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bddf5a80-6277-4543-96d9-98c67c13e2cf", "logId": "9a3fd901-b5e4-46cd-be9d-8e18af5978f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1b481c3-5330-4adb-ba13-dedb70257c81", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523215111100, "endTime": 29523464569900}, "additional": {"children": ["125fdf1e-e638-47c0-b4e2-e3c16743f0b4", "43906227-f5f7-42d9-b88c-5c050a1dda54", "8626fd2d-c823-407b-9eba-441bff675e6a", "713aa4ea-ce22-49cb-86e3-3e6ab0e0461e", "42590a1a-269c-4e21-bc8d-02b35c29239b", "257b920e-d22c-4a41-83ef-056b30c0cea9", "9592294e-8a55-42b5-a042-7ee2969982f8", "ba6d47a8-8dcc-439c-b467-9e3916ed617b", "b410dbc4-8370-4af6-90a4-ecc4773dee8e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bddf5a80-6277-4543-96d9-98c67c13e2cf", "logId": "2c38ecb5-cf21-4dfb-a5d3-79a3023fc5f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41ffaec4-ced7-4e06-96f8-3ccd9d81d000", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523464605500, "endTime": 29523465651600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bddf5a80-6277-4543-96d9-98c67c13e2cf", "logId": "409caf4d-d261-4135-8589-cb2cd34735a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53dd8eb9-49d5-4018-b600-b0c237996a67", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523465657400, "endTime": 29523465658800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bddf5a80-6277-4543-96d9-98c67c13e2cf", "logId": "366e72eb-7c8c-486c-b3ff-a6f5b093687b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef46c0e1-832b-4a8e-aeba-366e1314f1b3", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523200452400, "endTime": 29523200506500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bddf5a80-6277-4543-96d9-98c67c13e2cf", "logId": "2cd89a4c-fa72-436d-830a-1689b3881202"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2cd89a4c-fa72-436d-830a-1689b3881202", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523200452400, "endTime": 29523200506500}, "additional": {"logType": "info", "children": [], "durationId": "ef46c0e1-832b-4a8e-aeba-366e1314f1b3", "parent": "005d5292-51bd-43e8-b75d-9e9ef79a2b92"}}, {"head": {"id": "6ee09bf4-63a0-494c-a7aa-72f2c5a2962c", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523208168200, "endTime": 29523208189000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bddf5a80-6277-4543-96d9-98c67c13e2cf", "logId": "bb49b62e-49ca-4dbe-9d14-de093fdf5290"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb49b62e-49ca-4dbe-9d14-de093fdf5290", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523208168200, "endTime": 29523208189000}, "additional": {"logType": "info", "children": [], "durationId": "6ee09bf4-63a0-494c-a7aa-72f2c5a2962c", "parent": "005d5292-51bd-43e8-b75d-9e9ef79a2b92"}}, {"head": {"id": "4dc3a4b3-a3d5-448f-b0ad-cf4e359b43d8", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523208239000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9673e4f-2518-4987-8b51-9c843418dbdc", "name": "Cache service initialization finished in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523214945300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a3fd901-b5e4-46cd-be9d-8e18af5978f3", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523195204400, "endTime": 29523215094500}, "additional": {"logType": "info", "children": [], "durationId": "b496047c-2da6-4ab3-9cfc-369d1d639594", "parent": "005d5292-51bd-43e8-b75d-9e9ef79a2b92"}}, {"head": {"id": "125fdf1e-e638-47c0-b4e2-e3c16743f0b4", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523227055100, "endTime": 29523227072200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d1b481c3-5330-4adb-ba13-dedb70257c81", "logId": "53971cda-ddc5-4316-88e8-befb8cce5e6f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "43906227-f5f7-42d9-b88c-5c050a1dda54", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523227090800, "endTime": 29523247980400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d1b481c3-5330-4adb-ba13-dedb70257c81", "logId": "3743ebb7-6ff7-4055-8c5c-3db39587565b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8626fd2d-c823-407b-9eba-441bff675e6a", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523247994700, "endTime": 29523359617400}, "additional": {"children": ["58afcd4d-84bd-4975-947b-44d1ff3b97e9", "fdbd6b04-15b1-4668-af93-5e111d2a0edf", "bd6df02b-0fb3-434d-bd34-c934c6b20e42"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d1b481c3-5330-4adb-ba13-dedb70257c81", "logId": "f6ab8b4c-6650-454e-833e-131eb305c151"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "713aa4ea-ce22-49cb-86e3-3e6ab0e0461e", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523359632000, "endTime": 29523396529500}, "additional": {"children": ["ff2822ee-7edf-428b-a87b-4d5abad9ed38"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d1b481c3-5330-4adb-ba13-dedb70257c81", "logId": "0101b537-34f1-4860-9b59-3dbf1b16a800"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42590a1a-269c-4e21-bc8d-02b35c29239b", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523396539600, "endTime": 29523423079300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d1b481c3-5330-4adb-ba13-dedb70257c81", "logId": "fd1253ac-e12c-4e14-b8ab-b5bb5bfb4779"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "257b920e-d22c-4a41-83ef-056b30c0cea9", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523424678500, "endTime": 29523443211400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d1b481c3-5330-4adb-ba13-dedb70257c81", "logId": "94d3a922-6ec2-489e-bd5a-07bd11ec46ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9592294e-8a55-42b5-a042-7ee2969982f8", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523443229400, "endTime": 29523464414900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d1b481c3-5330-4adb-ba13-dedb70257c81", "logId": "67af8128-239c-46c3-9bc0-62f26d7f1b40"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ba6d47a8-8dcc-439c-b467-9e3916ed617b", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523464436900, "endTime": 29523464560400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d1b481c3-5330-4adb-ba13-dedb70257c81", "logId": "50dd250f-548a-4869-8d02-226e8a3e9cd4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53971cda-ddc5-4316-88e8-befb8cce5e6f", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523227055100, "endTime": 29523227072200}, "additional": {"logType": "info", "children": [], "durationId": "125fdf1e-e638-47c0-b4e2-e3c16743f0b4", "parent": "2c38ecb5-cf21-4dfb-a5d3-79a3023fc5f0"}}, {"head": {"id": "3743ebb7-6ff7-4055-8c5c-3db39587565b", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523227090800, "endTime": 29523247980400}, "additional": {"logType": "info", "children": [], "durationId": "43906227-f5f7-42d9-b88c-5c050a1dda54", "parent": "2c38ecb5-cf21-4dfb-a5d3-79a3023fc5f0"}}, {"head": {"id": "58afcd4d-84bd-4975-947b-44d1ff3b97e9", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523249359900, "endTime": 29523249387800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8626fd2d-c823-407b-9eba-441bff675e6a", "logId": "e7500107-7c53-4c65-9474-a9ec5546fbc3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7500107-7c53-4c65-9474-a9ec5546fbc3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523249359900, "endTime": 29523249387800}, "additional": {"logType": "info", "children": [], "durationId": "58afcd4d-84bd-4975-947b-44d1ff3b97e9", "parent": "f6ab8b4c-6650-454e-833e-131eb305c151"}}, {"head": {"id": "fdbd6b04-15b1-4668-af93-5e111d2a0edf", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523253353100, "endTime": 29523358371300}, "additional": {"children": ["52eca4e4-7701-428f-9eb4-655a481c014c", "3c3a9e9a-88ef-4c1d-8bf4-8930cd199897"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8626fd2d-c823-407b-9eba-441bff675e6a", "logId": "8059519f-063a-4df0-b3fa-70271d27b257"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52eca4e4-7701-428f-9eb4-655a481c014c", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523253354600, "endTime": 29523258311700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fdbd6b04-15b1-4668-af93-5e111d2a0edf", "logId": "d67d4d9f-d37c-4fe1-9e2b-a5554343fbf0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c3a9e9a-88ef-4c1d-8bf4-8930cd199897", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523258327200, "endTime": 29523358354600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fdbd6b04-15b1-4668-af93-5e111d2a0edf", "logId": "de08b0e0-b9b1-4ad6-a366-4b7a319359de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "76c66878-ee87-4e55-adf0-1d666567333c", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523253361600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5606ad33-de61-4138-8fbd-ccf6cf7bc96b", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523258148000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d67d4d9f-d37c-4fe1-9e2b-a5554343fbf0", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523253354600, "endTime": 29523258311700}, "additional": {"logType": "info", "children": [], "durationId": "52eca4e4-7701-428f-9eb4-655a481c014c", "parent": "8059519f-063a-4df0-b3fa-70271d27b257"}}, {"head": {"id": "ed86ffb6-6d0a-44a5-815e-865b6e889af5", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523258343600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "523f18a3-5f50-4548-bdb7-e295ff2e631b", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523268873300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96081ab4-3570-496e-9155-ec9016377167", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523269009000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8703cb0-b970-4ef6-813e-668bf62c1a47", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523269127600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80339385-bd29-4002-b854-2425e8b1d409", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523269213900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0645b7d0-e963-4a6c-8afd-fc0ea1c2251d", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523271166200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a9d78f5-6da8-4fad-a84a-62476c6acb28", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523276795600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2be0b30d-c89e-4c1a-b1cd-584890cc9f71", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523291503900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d365af22-fd5a-4a5d-99f3-5c09eab13ced", "name": "Sdk init in 46 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523323199600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58134fe1-f169-434f-b1e3-ce553a7ca046", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523323320400}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 18}, "markType": "other"}}, {"head": {"id": "7c01462f-fb1c-416b-a365-82fb121ab661", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523323331000}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 18}, "markType": "other"}}, {"head": {"id": "01a793be-7a3f-4281-8ba4-cb3f4242018c", "name": "Project task initialization takes 34 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523357621700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dcb6f07-0236-48aa-bb77-3dc3180aef08", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523357835300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5479f24-5469-4b1f-8d93-9f921e4f8cdd", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523358042600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55befc80-8a34-4b77-8885-3536069ebe3f", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523358175000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de08b0e0-b9b1-4ad6-a366-4b7a319359de", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523258327200, "endTime": 29523358354600}, "additional": {"logType": "info", "children": [], "durationId": "3c3a9e9a-88ef-4c1d-8bf4-8930cd199897", "parent": "8059519f-063a-4df0-b3fa-70271d27b257"}}, {"head": {"id": "8059519f-063a-4df0-b3fa-70271d27b257", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523253353100, "endTime": 29523358371300}, "additional": {"logType": "info", "children": ["d67d4d9f-d37c-4fe1-9e2b-a5554343fbf0", "de08b0e0-b9b1-4ad6-a366-4b7a319359de"], "durationId": "fdbd6b04-15b1-4668-af93-5e111d2a0edf", "parent": "f6ab8b4c-6650-454e-833e-131eb305c151"}}, {"head": {"id": "bd6df02b-0fb3-434d-bd34-c934c6b20e42", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523359590400, "endTime": 29523359608000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8626fd2d-c823-407b-9eba-441bff675e6a", "logId": "934b307a-6523-4c5f-b8d4-615c1675ee84"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "934b307a-6523-4c5f-b8d4-615c1675ee84", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523359590400, "endTime": 29523359608000}, "additional": {"logType": "info", "children": [], "durationId": "bd6df02b-0fb3-434d-bd34-c934c6b20e42", "parent": "f6ab8b4c-6650-454e-833e-131eb305c151"}}, {"head": {"id": "f6ab8b4c-6650-454e-833e-131eb305c151", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523247994700, "endTime": 29523359617400}, "additional": {"logType": "info", "children": ["e7500107-7c53-4c65-9474-a9ec5546fbc3", "8059519f-063a-4df0-b3fa-70271d27b257", "934b307a-6523-4c5f-b8d4-615c1675ee84"], "durationId": "8626fd2d-c823-407b-9eba-441bff675e6a", "parent": "2c38ecb5-cf21-4dfb-a5d3-79a3023fc5f0"}}, {"head": {"id": "ff2822ee-7edf-428b-a87b-4d5abad9ed38", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523360216500, "endTime": 29523396509400}, "additional": {"children": ["0a50f4be-6649-4e80-bc40-1b5351b43a5f", "3ab75345-b15a-4d06-8079-1883a778956c", "c609a724-2fe3-48a6-a4d3-9bf7bc8732c9"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "713aa4ea-ce22-49cb-86e3-3e6ab0e0461e", "logId": "e3fc6764-b056-4285-a440-cc338643472b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a50f4be-6649-4e80-bc40-1b5351b43a5f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523362795800, "endTime": 29523362813700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ff2822ee-7edf-428b-a87b-4d5abad9ed38", "logId": "731c862a-c114-4ce4-a64e-d1730a5bc09d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "731c862a-c114-4ce4-a64e-d1730a5bc09d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523362795800, "endTime": 29523362813700}, "additional": {"logType": "info", "children": [], "durationId": "0a50f4be-6649-4e80-bc40-1b5351b43a5f", "parent": "e3fc6764-b056-4285-a440-cc338643472b"}}, {"head": {"id": "3ab75345-b15a-4d06-8079-1883a778956c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523368515800, "endTime": 29523393510000}, "additional": {"children": ["04d0ca73-5716-4d8f-a3ff-a3910aab7c42", "432749c6-1cd9-4504-9bc5-c39da0875375"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ff2822ee-7edf-428b-a87b-4d5abad9ed38", "logId": "d31c6443-2225-47f8-85a7-b00fed3c4420"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04d0ca73-5716-4d8f-a3ff-a3910aab7c42", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523368518400, "endTime": 29523373784100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3ab75345-b15a-4d06-8079-1883a778956c", "logId": "55360155-504b-48fc-9abc-4d1eadaa2f7b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "432749c6-1cd9-4504-9bc5-c39da0875375", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523373801800, "endTime": 29523393496900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3ab75345-b15a-4d06-8079-1883a778956c", "logId": "3a8b3561-2eb6-4bdd-ac58-cbc581ca2713"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f97ab0fe-1c2a-4ec8-a1bd-bcc67fb09088", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523368528700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "850fae3f-80c8-4356-982d-11d635d500f2", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523373608300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55360155-504b-48fc-9abc-4d1eadaa2f7b", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523368518400, "endTime": 29523373784100}, "additional": {"logType": "info", "children": [], "durationId": "04d0ca73-5716-4d8f-a3ff-a3910aab7c42", "parent": "d31c6443-2225-47f8-85a7-b00fed3c4420"}}, {"head": {"id": "10d4cf5b-d44e-4ad2-b29b-64965509234b", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523373822000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dbd6f47-7ea2-491d-abbc-4c017bac042b", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523385620100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24d4f06d-3f2d-4507-b332-b9b6281888fd", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523385788000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbd503ad-48e2-4188-bc0f-3f433fbf9aec", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523387027400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1091715f-cf8a-4bfe-b324-0a90cec423a6", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523387353200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fd2c942-97a1-47b4-9a00-fa04307a5845", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523387453100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1666e76c-dc74-43e7-a91d-2631794b16b9", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523387523000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f3dade0-f5e2-4411-871a-dec8d8f6e07a", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523387606100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "116609f2-3081-400b-9d8b-2d4aad48dc29", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523393028800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52eff649-1417-4a63-84a7-8738c391aac4", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523393268400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e805ae12-a77c-4062-ae6b-9baa810fa837", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523393363000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03b85dc1-5831-4bfd-accd-6fba3a5159ca", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523393435500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a8b3561-2eb6-4bdd-ac58-cbc581ca2713", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523373801800, "endTime": 29523393496900}, "additional": {"logType": "info", "children": [], "durationId": "432749c6-1cd9-4504-9bc5-c39da0875375", "parent": "d31c6443-2225-47f8-85a7-b00fed3c4420"}}, {"head": {"id": "d31c6443-2225-47f8-85a7-b00fed3c4420", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523368515800, "endTime": 29523393510000}, "additional": {"logType": "info", "children": ["55360155-504b-48fc-9abc-4d1eadaa2f7b", "3a8b3561-2eb6-4bdd-ac58-cbc581ca2713"], "durationId": "3ab75345-b15a-4d06-8079-1883a778956c", "parent": "e3fc6764-b056-4285-a440-cc338643472b"}}, {"head": {"id": "c609a724-2fe3-48a6-a4d3-9bf7bc8732c9", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523396474300, "endTime": 29523396496600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ff2822ee-7edf-428b-a87b-4d5abad9ed38", "logId": "de4fe27f-f741-4b1d-9753-e97c6136a3cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de4fe27f-f741-4b1d-9753-e97c6136a3cc", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523396474300, "endTime": 29523396496600}, "additional": {"logType": "info", "children": [], "durationId": "c609a724-2fe3-48a6-a4d3-9bf7bc8732c9", "parent": "e3fc6764-b056-4285-a440-cc338643472b"}}, {"head": {"id": "e3fc6764-b056-4285-a440-cc338643472b", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523360216500, "endTime": 29523396509400}, "additional": {"logType": "info", "children": ["731c862a-c114-4ce4-a64e-d1730a5bc09d", "d31c6443-2225-47f8-85a7-b00fed3c4420", "de4fe27f-f741-4b1d-9753-e97c6136a3cc"], "durationId": "ff2822ee-7edf-428b-a87b-4d5abad9ed38", "parent": "0101b537-34f1-4860-9b59-3dbf1b16a800"}}, {"head": {"id": "0101b537-34f1-4860-9b59-3dbf1b16a800", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523359632000, "endTime": 29523396529500}, "additional": {"logType": "info", "children": ["e3fc6764-b056-4285-a440-cc338643472b"], "durationId": "713aa4ea-ce22-49cb-86e3-3e6ab0e0461e", "parent": "2c38ecb5-cf21-4dfb-a5d3-79a3023fc5f0"}}, {"head": {"id": "4dca5c90-efe0-43b5-8b70-e35bd62fe069", "name": "watch files: [\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\hvigorfile.ts',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523422589300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd6b65a6-18db-400f-86ee-92b30f35a349", "name": "hvigorfile, resolve hvigorfile dependencies in 27 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523422988500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd1253ac-e12c-4e14-b8ab-b5bb5bfb4779", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523396539600, "endTime": 29523423079300}, "additional": {"logType": "info", "children": [], "durationId": "42590a1a-269c-4e21-bc8d-02b35c29239b", "parent": "2c38ecb5-cf21-4dfb-a5d3-79a3023fc5f0"}}, {"head": {"id": "b410dbc4-8370-4af6-90a4-ecc4773dee8e", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523424419700, "endTime": 29523424667200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d1b481c3-5330-4adb-ba13-dedb70257c81", "logId": "0e8f9248-760d-49dd-ae6d-b38e84ad0f7f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56b9eb76-6038-4671-95e3-3da691a91555", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523424447700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e8f9248-760d-49dd-ae6d-b38e84ad0f7f", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523424419700, "endTime": 29523424667200}, "additional": {"logType": "info", "children": [], "durationId": "b410dbc4-8370-4af6-90a4-ecc4773dee8e", "parent": "2c38ecb5-cf21-4dfb-a5d3-79a3023fc5f0"}}, {"head": {"id": "14ac6c19-c77c-4936-a4e9-f98c064ef9d4", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523428103400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31cd64ec-dc57-4855-957b-e8630694c641", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523441175800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94d3a922-6ec2-489e-bd5a-07bd11ec46ff", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523424678500, "endTime": 29523443211400}, "additional": {"logType": "info", "children": [], "durationId": "257b920e-d22c-4a41-83ef-056b30c0cea9", "parent": "2c38ecb5-cf21-4dfb-a5d3-79a3023fc5f0"}}, {"head": {"id": "e255037e-542f-40ae-8a96-5cd576dcbc1e", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523443241700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c195096f-ba24-46a1-a578-a4697d634e15", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523455149100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3de3dbe2-0d94-41e4-b96c-acf24cec02ba", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523455345400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c16833fe-5fb6-43b8-a6d6-0633088e1703", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523455812500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7d1c0ca-de75-41e8-817d-68b5de314f37", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523460100200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e4d968d-93d1-46b6-97db-71535b419d1e", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523460253100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67af8128-239c-46c3-9bc0-62f26d7f1b40", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523443229400, "endTime": 29523464414900}, "additional": {"logType": "info", "children": [], "durationId": "9592294e-8a55-42b5-a042-7ee2969982f8", "parent": "2c38ecb5-cf21-4dfb-a5d3-79a3023fc5f0"}}, {"head": {"id": "bda40884-58bf-418c-84c0-696b79f1367e", "name": "Configuration phase cost:238 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523464457900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50dd250f-548a-4869-8d02-226e8a3e9cd4", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523464436900, "endTime": 29523464560400}, "additional": {"logType": "info", "children": [], "durationId": "ba6d47a8-8dcc-439c-b467-9e3916ed617b", "parent": "2c38ecb5-cf21-4dfb-a5d3-79a3023fc5f0"}}, {"head": {"id": "2c38ecb5-cf21-4dfb-a5d3-79a3023fc5f0", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523215111100, "endTime": 29523464569900}, "additional": {"logType": "info", "children": ["53971cda-ddc5-4316-88e8-befb8cce5e6f", "3743ebb7-6ff7-4055-8c5c-3db39587565b", "f6ab8b4c-6650-454e-833e-131eb305c151", "0101b537-34f1-4860-9b59-3dbf1b16a800", "fd1253ac-e12c-4e14-b8ab-b5bb5bfb4779", "94d3a922-6ec2-489e-bd5a-07bd11ec46ff", "67af8128-239c-46c3-9bc0-62f26d7f1b40", "50dd250f-548a-4869-8d02-226e8a3e9cd4", "0e8f9248-760d-49dd-ae6d-b38e84ad0f7f"], "durationId": "d1b481c3-5330-4adb-ba13-dedb70257c81", "parent": "005d5292-51bd-43e8-b75d-9e9ef79a2b92"}}, {"head": {"id": "d4dd8c55-46e9-4bb3-bfcb-6fc6f289c9ba", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523465632600, "endTime": 29523465645200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bddf5a80-6277-4543-96d9-98c67c13e2cf", "logId": "11cd7f23-f874-4903-93e4-055658b3dfcd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11cd7f23-f874-4903-93e4-055658b3dfcd", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523465632600, "endTime": 29523465645200}, "additional": {"logType": "info", "children": [], "durationId": "d4dd8c55-46e9-4bb3-bfcb-6fc6f289c9ba", "parent": "005d5292-51bd-43e8-b75d-9e9ef79a2b92"}}, {"head": {"id": "409caf4d-d261-4135-8589-cb2cd34735a2", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523464605500, "endTime": 29523465651600}, "additional": {"logType": "info", "children": [], "durationId": "41ffaec4-ced7-4e06-96f8-3ccd9d81d000", "parent": "005d5292-51bd-43e8-b75d-9e9ef79a2b92"}}, {"head": {"id": "366e72eb-7c8c-486c-b3ff-a6f5b093687b", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523465657400, "endTime": 29523465658800}, "additional": {"logType": "info", "children": [], "durationId": "53dd8eb9-49d5-4018-b600-b0c237996a67", "parent": "005d5292-51bd-43e8-b75d-9e9ef79a2b92"}}, {"head": {"id": "005d5292-51bd-43e8-b75d-9e9ef79a2b92", "name": "init", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523195203700, "endTime": 29523465660600}, "additional": {"logType": "info", "children": ["9a3fd901-b5e4-46cd-be9d-8e18af5978f3", "2c38ecb5-cf21-4dfb-a5d3-79a3023fc5f0", "409caf4d-d261-4135-8589-cb2cd34735a2", "366e72eb-7c8c-486c-b3ff-a6f5b093687b", "2cd89a4c-fa72-436d-830a-1689b3881202", "bb49b62e-49ca-4dbe-9d14-de093fdf5290", "11cd7f23-f874-4903-93e4-055658b3dfcd"], "durationId": "bddf5a80-6277-4543-96d9-98c67c13e2cf"}}, {"head": {"id": "bbf2a982-8f25-424e-94d6-cf7aca97c97d", "name": "Configuration task cost before running: 274 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523465747800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa5dd04f-41e8-4bab-b6b2-ae7b6244aa8c", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523475339100, "endTime": 29523492850300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "eaeccfb2-7f4e-49aa-92b5-563189c181ed", "logId": "7368a695-bcd6-49e5-8793-ea69cff86d71"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eaeccfb2-7f4e-49aa-92b5-563189c181ed", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523468707600}, "additional": {"logType": "detail", "children": [], "durationId": "fa5dd04f-41e8-4bab-b6b2-ae7b6244aa8c"}}, {"head": {"id": "6a05e755-5fe5-4a46-bc72-9720d9aab843", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523469762600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ca2be73-9893-4ec0-b7c9-65cd3b721958", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523469955000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3b0fe63-58b4-4dea-a392-a2d38eecc13e", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523475364500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e38ea3d8-e823-473a-bd45-ca76fd5e7000", "name": "Incremental task entry:default@PreBuild pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523492546800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acf33ba6-703b-4e49-8c72-237100f3f6dd", "name": "entry : default@PreBuild cost memory 0.27978515625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523492726900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7368a695-bcd6-49e5-8793-ea69cff86d71", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523475339100, "endTime": 29523492850300}, "additional": {"logType": "info", "children": [], "durationId": "fa5dd04f-41e8-4bab-b6b2-ae7b6244aa8c"}}, {"head": {"id": "103385ce-6a00-4cd9-949b-a30b75f3c6f4", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523503997400, "endTime": 29523508663100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "63af2784-7913-4285-8286-807dfffd4473", "logId": "af23dc1e-d2c0-4f63-bb53-80f0c3963549"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "63af2784-7913-4285-8286-807dfffd4473", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523501304800}, "additional": {"logType": "detail", "children": [], "durationId": "103385ce-6a00-4cd9-949b-a30b75f3c6f4"}}, {"head": {"id": "33a15677-42da-4f21-adb4-c953f83c4968", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523502353800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74d27ad4-506b-42c4-8b1d-7d4fb2be163c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523502520800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f74af17c-d6fd-4ce3-926c-f3a5f4a085a0", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523504016500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18cd4894-2d60-426c-80d6-3367fe60cf76", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523508364900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10703f34-3af5-4ae3-9e89-64cac9e42160", "name": "entry : default@MergeProfile cost memory 0.11208343505859375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523508544900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af23dc1e-d2c0-4f63-bb53-80f0c3963549", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523503997400, "endTime": 29523508663100}, "additional": {"logType": "info", "children": [], "durationId": "103385ce-6a00-4cd9-949b-a30b75f3c6f4"}}, {"head": {"id": "c42aee85-40cd-4c92-826c-fcadd75eb424", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523514317600, "endTime": 29523518035500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c89b0f0e-c7d1-48db-aed4-2d86f156e9ce", "logId": "b23d1cda-f0f2-4a21-835e-49a484b67e5b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c89b0f0e-c7d1-48db-aed4-2d86f156e9ce", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523511836100}, "additional": {"logType": "detail", "children": [], "durationId": "c42aee85-40cd-4c92-826c-fcadd75eb424"}}, {"head": {"id": "96aa5aec-f2e6-4967-96eb-9ef2ccafa291", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523512905800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1e0ff62-df41-4706-9e60-ea54a8687a14", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523513047000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c5e800b-f1a7-47f7-ac40-ea38fa8a175b", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523514328600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fb1ea58-2c18-417d-9b6f-257c1dc2c08a", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 3 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523516620700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f78efb3-c33d-4e10-8969-e3dbd715dad3", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523517889900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6772736-4869-40d0-872e-366122acbc02", "name": "entry : default@CreateBuildProfile cost memory 0.0988616943359375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523517985500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b23d1cda-f0f2-4a21-835e-49a484b67e5b", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523514317600, "endTime": 29523518035500}, "additional": {"logType": "info", "children": [], "durationId": "c42aee85-40cd-4c92-826c-fcadd75eb424"}}, {"head": {"id": "f1e95cd5-5c63-468b-9fab-7ea1e33d574c", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523524424600, "endTime": 29523525173700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "50c54f16-83ed-460b-880c-9d1ca4eae05d", "logId": "83518016-f43a-4e7a-a73d-428d307a166c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "50c54f16-83ed-460b-880c-9d1ca4eae05d", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523521281100}, "additional": {"logType": "detail", "children": [], "durationId": "f1e95cd5-5c63-468b-9fab-7ea1e33d574c"}}, {"head": {"id": "753cee1a-ee6e-4e40-8983-229ef451bb69", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523522385600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca858260-f321-412e-b7eb-621caf11cc67", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523522538100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c648dfd8-5bb0-459b-84a4-b6380d8cfb63", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523524455400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05f31190-d83c-4da9-9cd1-8ba3bfeedc3c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523524711700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "899cb2b9-eb01-4fc9-b6e7-f7bdeac88589", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523524811100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a428a70-de08-4f9b-a480-43ea699a8509", "name": "entry : default@PreCheckSyscap cost memory 0.03691864013671875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523524945400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a8558f1-3695-4ca1-a7d8-022ae5ca1678", "name": "runTaskFromQueue task cost before running: 334 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523525082400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83518016-f43a-4e7a-a73d-428d307a166c", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523524424600, "endTime": 29523525173700, "totalTime": 631000}, "additional": {"logType": "info", "children": [], "durationId": "f1e95cd5-5c63-468b-9fab-7ea1e33d574c"}}, {"head": {"id": "2b6f50b1-fb72-4edc-b869-937f26c0f484", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523544561500, "endTime": 29523546299300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f26508eb-35e5-430d-8492-df34eb94718f", "logId": "c2c712e6-91d8-4422-9308-6d96cf6333d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f26508eb-35e5-430d-8492-df34eb94718f", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523528237800}, "additional": {"logType": "detail", "children": [], "durationId": "2b6f50b1-fb72-4edc-b869-937f26c0f484"}}, {"head": {"id": "e6d7b598-8ba9-4f55-9049-75891425e1bc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523529165500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87bde335-0d48-40f7-851d-c7a325eff681", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523529308800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c4018f1-5ef6-45f2-859c-3b2856f8b7cd", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523544579400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93805370-f836-4ad7-a2f9-f2f6de0556c7", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523544849400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f08bba09-31f3-4fea-b454-0afcf02572e7", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523546054100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dcd05c9-1a61-40b3-afb9-9d8852f58d82", "name": "entry : default@GeneratePkgContextInfo cost memory 0.066253662109375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523546193500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2c712e6-91d8-4422-9308-6d96cf6333d0", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523544561500, "endTime": 29523546299300}, "additional": {"logType": "info", "children": [], "durationId": "2b6f50b1-fb72-4edc-b869-937f26c0f484"}}, {"head": {"id": "9f447ae8-fecc-4733-a7cc-35cf385716fc", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523552945200, "endTime": 29523555162500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "dd646b2c-76cc-4afe-b99f-1b796d2c9004", "logId": "2862698a-8237-4634-85d3-3c8c8465e8d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd646b2c-76cc-4afe-b99f-1b796d2c9004", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523549565600}, "additional": {"logType": "detail", "children": [], "durationId": "9f447ae8-fecc-4733-a7cc-35cf385716fc"}}, {"head": {"id": "26db93cb-4c58-457b-bc59-f29819ab53f1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523550731900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3556874-3ff2-497e-805e-c07d990c7ab9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523550886600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0b76466-951d-476a-b932-90b37e79aa47", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523552959800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d8c1baa-1079-4ecf-bc97-8fb874574ca3", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523554897000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4d58ec4-eb02-4e5a-ba5f-1a3783a74824", "name": "entry : default@ProcessProfile cost memory 0.0570068359375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523555044900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2862698a-8237-4634-85d3-3c8c8465e8d9", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523552945200, "endTime": 29523555162500}, "additional": {"logType": "info", "children": [], "durationId": "9f447ae8-fecc-4733-a7cc-35cf385716fc"}}, {"head": {"id": "61d8b706-09f6-44cf-94e8-c9c2516a7399", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523561684500, "endTime": 29523572300100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c3be6094-fc52-46f5-9863-d82d89b3843f", "logId": "458baa75-252e-49ae-b594-9dcb98eb814d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3be6094-fc52-46f5-9863-d82d89b3843f", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523558207700}, "additional": {"logType": "detail", "children": [], "durationId": "61d8b706-09f6-44cf-94e8-c9c2516a7399"}}, {"head": {"id": "867c28ab-274e-4254-b14c-a57084cbf97e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523559118000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3eeccd22-f9bc-4bae-a71c-75d7a45472ae", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523559280100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13342a34-3296-4b8f-93c7-d84e2fcd101f", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523561699600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff8f6555-2412-4002-857a-ebe2433a8b6e", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523572068700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9aa1eec-e7aa-4335-b0dc-bf65335d9c4b", "name": "entry : default@ProcessRouterMap cost memory 0.196868896484375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523572225600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "458baa75-252e-49ae-b594-9dcb98eb814d", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523561684500, "endTime": 29523572300100}, "additional": {"logType": "info", "children": [], "durationId": "61d8b706-09f6-44cf-94e8-c9c2516a7399"}}, {"head": {"id": "4a377738-64ba-4148-a682-762ef6ec23d1", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523584961000, "endTime": 29523591853700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "f171bfe5-2688-4bc4-9fcd-573991837639", "logId": "b0c65d14-9c7a-4ef6-b554-ec3bd3ee19a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f171bfe5-2688-4bc4-9fcd-573991837639", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523576874200}, "additional": {"logType": "detail", "children": [], "durationId": "4a377738-64ba-4148-a682-762ef6ec23d1"}}, {"head": {"id": "2ffb4a82-4e7e-46e9-8e41-e9d17ee2b2b2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523577687300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77471047-307f-4f50-9a78-8dec06a47000", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523577904300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad491364-1b1c-4a7e-b388-2d3000238489", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523580628500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94a5d2ae-866e-42f2-b8bf-fe1bab89f294", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523587621700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f997c32d-7c31-42be-acea-5ba6276ee0d3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523587967900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fe61fef-e54e-43de-b866-312cd4e90198", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523588093600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f52b302-f0fd-4ce5-a2fe-eb705099168c", "name": "entry : default@PreviewProcessResource cost memory 0.0700836181640625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523588226000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "936d6af3-9757-4ea3-a32a-fe510bf55c96", "name": "runTaskFromQueue task cost before running: 400 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523591586300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0c65d14-9c7a-4ef6-b554-ec3bd3ee19a3", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523584961000, "endTime": 29523591853700, "totalTime": 3363500}, "additional": {"logType": "info", "children": [], "durationId": "4a377738-64ba-4148-a682-762ef6ec23d1"}}, {"head": {"id": "8f2a9078-399b-4d39-b06e-8e3a70f1284f", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523609503600, "endTime": 29523650707700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "859f8557-39df-4797-9f3c-d4ccd76736c0", "logId": "3289402e-d8a9-4264-a6d1-878251de64c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "859f8557-39df-4797-9f3c-d4ccd76736c0", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523597814100}, "additional": {"logType": "detail", "children": [], "durationId": "8f2a9078-399b-4d39-b06e-8e3a70f1284f"}}, {"head": {"id": "4f06f1c4-2084-4136-8d0b-6a44cfc08db9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523599560000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46167d01-478e-461d-8e32-cbaa7563059c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523599805000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93d76297-53d0-421f-a3ce-523597fdd75e", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523609554400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8d1e708-b6c5-4621-b1bf-94a4a133747b", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 20 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523650430100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3a37fbc-5b60-4154-bae2-c8acfba63e2e", "name": "entry : default@GenerateLoaderJson cost memory -0.94921875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523650613200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3289402e-d8a9-4264-a6d1-878251de64c2", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523609503600, "endTime": 29523650707700}, "additional": {"logType": "info", "children": [], "durationId": "8f2a9078-399b-4d39-b06e-8e3a70f1284f"}}, {"head": {"id": "2f2fe8c9-cedc-438f-a930-58db122fb95d", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523665919300, "endTime": 29523970224300}, "additional": {"children": ["9098c73e-d6bc-4809-bf2d-267da054824d", "f8620e1f-9545-49e6-859f-4cc0459e5fdf", "819b10f1-d9dd-4a90-95d3-11eb174951ac", "98e89069-d15f-4646-b722-dfd1ec72715c"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "e77abd17-9ebe-4085-9ea2-f1d10e3f3e70", "logId": "5ac2629e-5592-4764-8111-56637af21e67"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e77abd17-9ebe-4085-9ea2-f1d10e3f3e70", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523658425400}, "additional": {"logType": "detail", "children": [], "durationId": "2f2fe8c9-cedc-438f-a930-58db122fb95d"}}, {"head": {"id": "99b3b81c-fd8f-4f51-9661-e3243ba946a3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523659707000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c278e29d-c3ce-4ca9-b408-40128c0ee4e8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523659855000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1aa060ff-a9dd-44de-8fe8-9283d11c7073", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523661627800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0266a4ca-930e-419f-b5a2-596e3ced5266", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523665954100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b8978d7-ef92-484f-a540-c4bb12a94ed2", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523714028300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "444aae16-a364-4e11-a7e3-41fb1eb67cc6", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 47 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523714363400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9098c73e-d6bc-4809-bf2d-267da054824d", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523717375100, "endTime": 29523786193600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2f2fe8c9-cedc-438f-a930-58db122fb95d", "logId": "332f2294-1dc3-4d91-bff7-ed8b692272e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "332f2294-1dc3-4d91-bff7-ed8b692272e7", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523717375100, "endTime": 29523786193600}, "additional": {"logType": "info", "children": [], "durationId": "9098c73e-d6bc-4809-bf2d-267da054824d", "parent": "5ac2629e-5592-4764-8111-56637af21e67"}}, {"head": {"id": "4a78c9d3-148e-4846-9f20-e26fedf6cb47", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523786616100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8620e1f-9545-49e6-859f-4cc0459e5fdf", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523788192600, "endTime": 29523841570500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2f2fe8c9-cedc-438f-a930-58db122fb95d", "logId": "3bf7c915-b044-414e-9e7f-94987cbc50f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b73710e-5b8f-4c49-890f-a22817becbe9", "name": "current process  memoryUsage: {\n  rss: 123973632,\n  heapTotal: 125657088,\n  heapUsed: 106728680,\n  external: 3075129,\n  arrayBuffers: 69030\n} os memoryUsage :13.067100524902344", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523789756800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "578e6008-9f1f-4c79-a363-503d0757337a", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523836983000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bf7c915-b044-414e-9e7f-94987cbc50f0", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523788192600, "endTime": 29523841570500}, "additional": {"logType": "info", "children": [], "durationId": "f8620e1f-9545-49e6-859f-4cc0459e5fdf", "parent": "5ac2629e-5592-4764-8111-56637af21e67"}}, {"head": {"id": "cdb7f1c1-43ba-46ea-8853-d904808a3b95", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523841751900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "819b10f1-d9dd-4a90-95d3-11eb174951ac", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523843617800, "endTime": 29523902925100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2f2fe8c9-cedc-438f-a930-58db122fb95d", "logId": "2c33aa9b-2a14-403e-9021-f17a91218f12"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a9cd9ad-6361-4b89-9156-c3b01e43d3ec", "name": "current process  memoryUsage: {\n  rss: 123973632,\n  heapTotal: 125657088,\n  heapUsed: 106990528,\n  external: 3075255,\n  arrayBuffers: 69171\n} os memoryUsage :13.075607299804688", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523845273000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "148fd528-c5db-44ff-a1f2-e1c6a05b51d2", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523900609800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c33aa9b-2a14-403e-9021-f17a91218f12", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523843617800, "endTime": 29523902925100}, "additional": {"logType": "info", "children": [], "durationId": "819b10f1-d9dd-4a90-95d3-11eb174951ac", "parent": "5ac2629e-5592-4764-8111-56637af21e67"}}, {"head": {"id": "835ce60b-cba6-47e6-bc01-7dd58b3412cc", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523903097100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98e89069-d15f-4646-b722-dfd1ec72715c", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523903988000, "endTime": 29523968108600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2f2fe8c9-cedc-438f-a930-58db122fb95d", "logId": "7aa0ef2b-8572-461e-8a81-0837d42ea00d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b873847-325b-4c49-9b82-f03abca6a9e4", "name": "current process  memoryUsage: {\n  rss: 123973632,\n  heapTotal: 125657088,\n  heapUsed: 107273720,\n  external: 3075381,\n  arrayBuffers: 70181\n} os memoryUsage :13.067047119140625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523904815800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbe7c731-3a43-493e-ab3c-c199e1fe4cf2", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523964684800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7aa0ef2b-8572-461e-8a81-0837d42ea00d", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523903988000, "endTime": 29523968108600}, "additional": {"logType": "info", "children": [], "durationId": "98e89069-d15f-4646-b722-dfd1ec72715c", "parent": "5ac2629e-5592-4764-8111-56637af21e67"}}, {"head": {"id": "2ab29587-bf18-43e4-bd01-656f4024dfa7", "name": "entry : default@PreviewCompileResource cost memory -10.335968017578125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523969829300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0737587b-ce29-488f-b228-bc7e4d96aac7", "name": "runTaskFromQueue task cost before running: 779 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523970116400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ac2629e-5592-4764-8111-56637af21e67", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523665919300, "endTime": 29523970224300, "totalTime": 304106800}, "additional": {"logType": "info", "children": ["332f2294-1dc3-4d91-bff7-ed8b692272e7", "3bf7c915-b044-414e-9e7f-94987cbc50f0", "2c33aa9b-2a14-403e-9021-f17a91218f12", "7aa0ef2b-8572-461e-8a81-0837d42ea00d"], "durationId": "2f2fe8c9-cedc-438f-a930-58db122fb95d"}}, {"head": {"id": "5d7b7d28-c12b-401d-bfed-43a58f4a00df", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523976493900, "endTime": 29523977035400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "9c988f4c-ce17-4ee2-8aa8-73a7e3007c0c", "logId": "6ddf0acf-de9f-4ed4-9c0c-ba6d2dd9adee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c988f4c-ce17-4ee2-8aa8-73a7e3007c0c", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523975136700}, "additional": {"logType": "detail", "children": [], "durationId": "5d7b7d28-c12b-401d-bfed-43a58f4a00df"}}, {"head": {"id": "3ed97063-6144-4b82-a686-eec0221b6d39", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523976190600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d97f708-b905-4842-aed8-08a9d0047bd6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523976347800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49f82bd0-07a6-40d9-a5ce-ee7dc5a0b369", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523976504900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b07750c-ae79-4aca-b8d6-cb4bbb5b1d7c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523976635700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87d351c3-8871-445f-a8d5-e8b0410ca3e5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523976713500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d597681-b569-4439-b84b-e57053f93253", "name": "entry : default@PreviewHookCompileResource cost memory 0.03803253173828125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523976814400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06ca13c0-fa01-487f-8ef3-2f76518c20a7", "name": "runTaskFromQueue task cost before running: 785 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523976945800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ddf0acf-de9f-4ed4-9c0c-ba6d2dd9adee", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523976493900, "endTime": 29523977035400, "totalTime": 423300}, "additional": {"logType": "info", "children": [], "durationId": "5d7b7d28-c12b-401d-bfed-43a58f4a00df"}}, {"head": {"id": "0507ecc4-efc7-4081-bd98-cccae5030258", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523982779200, "endTime": 29523997597600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "780dfb4a-4a4a-4571-af4b-a0a14fac006f", "logId": "92c9561b-8a4a-4845-aa95-1d26762593bb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "780dfb4a-4a4a-4571-af4b-a0a14fac006f", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523980124200}, "additional": {"logType": "detail", "children": [], "durationId": "0507ecc4-efc7-4081-bd98-cccae5030258"}}, {"head": {"id": "cd046cb7-a1e2-49c0-86f0-0f1d613c3025", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523981344600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4316fbe6-31b8-4101-99e3-63c171817fe4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523981496400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa1df227-9ba9-4622-b5d6-39309ef886fc", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523982821200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d83ec95-bcd6-4ff7-b3e3-acab41502af6", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523985828800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5fdfc3a-747c-41db-add3-fd5c867fb143", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523986071500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c7f0434-7043-4c84-8065-e82d243424a5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523986196600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a46c5afa-d02e-4f61-9ff2-2e01b3f0672c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523986272300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71275c80-a210-422d-bc66-dad444c47ab1", "name": "entry : default@CopyPreviewProfile cost memory 0.22457122802734375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523997218300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db15c7b8-6595-49d4-9419-ea2ce229bd4f", "name": "runTaskFromQueue task cost before running: 806 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523997479400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92c9561b-8a4a-4845-aa95-1d26762593bb", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523982779200, "endTime": 29523997597600, "totalTime": 14658100}, "additional": {"logType": "info", "children": [], "durationId": "0507ecc4-efc7-4081-bd98-cccae5030258"}}, {"head": {"id": "be2dce85-cf1a-431f-8e1c-da22faf12adc", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524003599800, "endTime": 29524004261900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "3ed8360a-99b2-47bb-9a71-addf02ad3d45", "logId": "c3dca9c2-b12a-415c-921a-c5e892a1a81d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ed8360a-99b2-47bb-9a71-addf02ad3d45", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524000818900}, "additional": {"logType": "detail", "children": [], "durationId": "be2dce85-cf1a-431f-8e1c-da22faf12adc"}}, {"head": {"id": "b9db7dd5-68bb-4df7-a0bc-956f54ae4881", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524001986100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8091d79a-9989-4f03-88e3-db2534c27a7d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524002136700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4d07b44-ebbe-4916-bdb8-ff11f725ef7b", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524003613400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91a3b1c1-b772-42fa-9a9d-be623c0c8404", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524003790800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3e05300-2d76-4efe-b21c-fa7372e4c25f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524003877300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a3c0cbf-ca11-4acf-a9c6-fbceecd0c23b", "name": "entry : default@ReplacePreviewerPage cost memory 0.03798675537109375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524004049900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae833bcd-7559-42d4-9875-7a04c7241b95", "name": "runTaskFromQueue task cost before running: 813 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524004177500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3dca9c2-b12a-415c-921a-c5e892a1a81d", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524003599800, "endTime": 29524004261900, "totalTime": 549200}, "additional": {"logType": "info", "children": [], "durationId": "be2dce85-cf1a-431f-8e1c-da22faf12adc"}}, {"head": {"id": "72b3f1d6-c70b-4e46-b46c-41251fc2e0f5", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524008653600, "endTime": 29524009093100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "acaed8af-f2bb-4afa-b842-0ac616fdb225", "logId": "60e44080-51cf-4afa-8cbf-5a1170fe5f0d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "acaed8af-f2bb-4afa-b842-0ac616fdb225", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524008558900}, "additional": {"logType": "detail", "children": [], "durationId": "72b3f1d6-c70b-4e46-b46c-41251fc2e0f5"}}, {"head": {"id": "f16dbd1e-0bab-418e-b303-dbc504856096", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524008663800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fd43d1d-a123-47bf-9e4a-29324827d75f", "name": "entry : buildPreviewerResource cost memory 0.01168060302734375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524008875100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6f79006-f3ce-449e-8dea-50b237f30522", "name": "runTaskFromQueue task cost before running: 817 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524009017300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60e44080-51cf-4afa-8cbf-5a1170fe5f0d", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524008653600, "endTime": 29524009093100, "totalTime": 336500}, "additional": {"logType": "info", "children": [], "durationId": "72b3f1d6-c70b-4e46-b46c-41251fc2e0f5"}}, {"head": {"id": "283e1c66-8d97-4b77-8315-6054d30ca2f4", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524015760000, "endTime": 29524023871500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "6b77d866-b2eb-49a0-bd32-ba3f455a3f2d", "logId": "157e0e56-d7db-48f3-93c0-03b8550c1d94"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b77d866-b2eb-49a0-bd32-ba3f455a3f2d", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524012858400}, "additional": {"logType": "detail", "children": [], "durationId": "283e1c66-8d97-4b77-8315-6054d30ca2f4"}}, {"head": {"id": "b188ed82-6333-4e58-82dd-4062c2c02235", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524013964800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54725c32-ded7-43fd-8df6-43fa4afdcf57", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524014117700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "056c4db0-dc58-4940-a665-328988a1f436", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524015776400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c824080b-ab92-4202-8540-15e36d29d11e", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524019517900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0ce6076-aa60-4715-bdb0-9a55d84f90eb", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524019681700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da7ac815-d989-4659-b693-b2de82601670", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524019789800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75b13f22-baa9-4fae-97cb-975281da0280", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524020056800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1b65f10-4de1-4a62-8448-bbf54b1e75f0", "name": "entry : default@PreviewUpdateAssets cost memory 0.14754486083984375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524023499600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f46d27cd-7df9-462e-ac9e-4a03edc8cb6f", "name": "runTaskFromQueue task cost before running: 832 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524023750900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "157e0e56-d7db-48f3-93c0-03b8550c1d94", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524015760000, "endTime": 29524023871500, "totalTime": 7948900}, "additional": {"logType": "info", "children": [], "durationId": "283e1c66-8d97-4b77-8315-6054d30ca2f4"}}, {"head": {"id": "84753481-dc21-4fe5-b03d-68189ddd97ef", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524040404700, "endTime": 29536940035100}, "additional": {"children": ["72ca0af1-1931-480c-9f4c-40845287e49e"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8189e863-10bb-410f-8b3d-89d2600fccad", "logId": "2875a077-aa3e-4ac5-90ad-59dad5c42460"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8189e863-10bb-410f-8b3d-89d2600fccad", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524028244000}, "additional": {"logType": "detail", "children": [], "durationId": "84753481-dc21-4fe5-b03d-68189ddd97ef"}}, {"head": {"id": "6fe38c5c-2114-448b-b851-a0809f1ead14", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524029246900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c133a2e3-6d7e-44de-9282-ac58dab3e1f8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524029386000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4aa1a2e2-6118-43d8-afc9-a81b2eae0e8a", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524040421300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72ca0af1-1931-480c-9f4c-40845287e49e", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker10", "startTime": 29524089381600, "endTime": 29536939781200}, "additional": {"children": ["390df9fb-4668-4da0-926a-0745fe9be4da", "62c103c2-30de-4f9f-9f88-1974848055dd", "232dc136-8db2-4a7c-908d-b6cf5c4e0b4b"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "84753481-dc21-4fe5-b03d-68189ddd97ef", "logId": "72a6c212-f71f-4aa1-b1a4-b8a47fa05d7a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef994e61-33aa-4201-905a-b7dec4f5eb3b", "name": "entry : default@PreviewArkTS cost memory -0.5043106079101562", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524093955900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddb0410f-5bab-45c0-8e4c-b83c01b73a81", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29528302805400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "390df9fb-4668-4da0-926a-0745fe9be4da", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29528303662800, "endTime": 29528303678500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "72ca0af1-1931-480c-9f4c-40845287e49e", "logId": "03c84b42-dbd2-447f-ab7f-cc76adb2fe4a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03c84b42-dbd2-447f-ab7f-cc76adb2fe4a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29528303662800, "endTime": 29528303678500}, "additional": {"logType": "info", "children": [], "durationId": "390df9fb-4668-4da0-926a-0745fe9be4da", "parent": "72a6c212-f71f-4aa1-b1a4-b8a47fa05d7a"}}, {"head": {"id": "23bc7b15-d57e-4dba-af51-ddf6f55a2043", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29534093843600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62c103c2-30de-4f9f-9f88-1974848055dd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29534094813900, "endTime": 29534094833500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "72ca0af1-1931-480c-9f4c-40845287e49e", "logId": "cf19076e-f43e-473c-bdaf-ac21398facd5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf19076e-f43e-473c-bdaf-ac21398facd5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29534094813900, "endTime": 29534094833500}, "additional": {"logType": "info", "children": [], "durationId": "62c103c2-30de-4f9f-9f88-1974848055dd", "parent": "72a6c212-f71f-4aa1-b1a4-b8a47fa05d7a"}}, {"head": {"id": "fa1deb31-9e06-42b2-811b-82df528918f8", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536938212200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "232dc136-8db2-4a7c-908d-b6cf5c4e0b4b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536939679600, "endTime": 29536939698000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "72ca0af1-1931-480c-9f4c-40845287e49e", "logId": "d49c355c-220c-45e8-8e6e-5620eb7b0456"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d49c355c-220c-45e8-8e6e-5620eb7b0456", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536939679600, "endTime": 29536939698000}, "additional": {"logType": "info", "children": [], "durationId": "232dc136-8db2-4a7c-908d-b6cf5c4e0b4b", "parent": "72a6c212-f71f-4aa1-b1a4-b8a47fa05d7a"}}, {"head": {"id": "72a6c212-f71f-4aa1-b1a4-b8a47fa05d7a", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Worker10", "startTime": 29524089381600, "endTime": 29536939781200}, "additional": {"logType": "error", "children": ["03c84b42-dbd2-447f-ab7f-cc76adb2fe4a", "cf19076e-f43e-473c-bdaf-ac21398facd5", "d49c355c-220c-45e8-8e6e-5620eb7b0456"], "durationId": "72ca0af1-1931-480c-9f4c-40845287e49e", "parent": "2875a077-aa3e-4ac5-90ad-59dad5c42460"}}, {"head": {"id": "2012a5fd-3a5a-4a38-8270-71869cec92fc", "name": "default@PreviewArkTS watch work[10] failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536939826900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2875a077-aa3e-4ac5-90ad-59dad5c42460", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29524040404700, "endTime": 29536940035100}, "additional": {"logType": "error", "children": ["72a6c212-f71f-4aa1-b1a4-b8a47fa05d7a"], "durationId": "84753481-dc21-4fe5-b03d-68189ddd97ef"}}, {"head": {"id": "cd9dba32-7b95-46b5-a1f9-47126e135f1d", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536940163300}, "additional": {"logType": "debug", "children": [], "durationId": "84753481-dc21-4fe5-b03d-68189ddd97ef"}}, {"head": {"id": "3d658b86-bb81-40e0-97c3-3152ac37aa02", "name": "ERROR: stacktrace = Error: \u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/PaymentPage.ets:521:13\n Type 'ApiResponse<ApiResponse<PaymentResponse>>' is not assignable to type 'ApiResponse<PaymentResponse>'.\n  Type 'ApiResponse<PaymentResponse>' is missing the following properties from type 'PaymentResponse': transactionId, transactionNo, status, message\n\n    at handleResponse (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536941108400}, "additional": {"logType": "debug", "children": [], "durationId": "84753481-dc21-4fe5-b03d-68189ddd97ef"}}, {"head": {"id": "087a3a49-7b23-417f-a782-144aa5d1da37", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536951371700, "endTime": 29536951444100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "483239d5-1cf4-4b15-974d-99c0178eca60", "logId": "211f350a-50f9-40d7-af57-bedd4c0fb449"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "211f350a-50f9-40d7-af57-bedd4c0fb449", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536951371700, "endTime": 29536951444100}, "additional": {"logType": "info", "children": [], "durationId": "087a3a49-7b23-417f-a782-144aa5d1da37"}}, {"head": {"id": "f89b3369-ce5c-46ed-912e-2c1d02138b23", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29523192037800, "endTime": 29536951566700}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 18}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "756aabd4-6e39-408c-a531-3360df60f3ec", "name": "BUILD FAILED in 13 s 760 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536951690600}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "ffde36dd-0c64-43c8-bc7f-2cccf7399b6e", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536951835900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7698ae2-0bbf-4213-b7f4-dfa98de1e752", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536951872300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85536cf3-ecca-4adb-8757-94be60d07447", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536951900000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28992e34-fa8a-4987-b322-31e1918a9334", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536951923800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de6bef66-1168-4d8a-95ab-b7ea21fd8a0c", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536951945600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03e95c8c-8a63-4d14-9ceb-c7b1350f6566", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536951969000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30c4a165-08f9-4e6f-b286-082819e78c00", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536951993500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1597f20-318e-4d8e-899e-d9094a47c7a2", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536953318100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44b6436c-2c37-4be6-89d9-9a7ccbdc557a", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536958993100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee50a512-9761-40e8-9497-0d753818a576", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536959298900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "734520c8-7206-4d41-801e-6c691f708aff", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536966902700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "467abc4a-c215-4c0a-84ac-dd71292ebf9b", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:16 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536967487200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c3e1b40-0ba6-4b38-91c0-546c2bc45637", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536967790000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae468d5b-e5b8-481a-bfa0-838462a276d5", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536968423200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07273d12-1ae2-4794-b8f2-9d32757bb1e2", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536969125300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1194dc4f-c493-4551-9b74-458bc9f83896", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536969460600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b4af912-1ebc-471c-b26f-19fa504e5b98", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536969743600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "315d4de1-d2f7-489d-9f69-4f19009a4a38", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536970673700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a3625d7-997d-4dc3-8169-92f41adaafb6", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536973111300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "988d6db2-fa84-4c3a-b3e3-5fd3d6c66966", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536973750300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49916dd6-126f-4286-8832-0ded5fbf32f1", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536973968900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "835b530f-0302-409c-998c-4f36ca315f03", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536974225300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e5527ac-b859-4499-9990-f7764ed5d737", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536974904800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b711021-63df-49cb-bcbf-2677c46a370c", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536982517600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a57734a1-39eb-4c11-8719-dde60da83760", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536982765200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20d4c927-7def-4fe4-8507-3ad3664a2e05", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536982970100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a0f6d6f-d9ca-4761-b389-872be50e90ce", "name": "Update task entry:default@PreviewArkTS output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536983186200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95129d1e-5248-41d2-9ae3-b405ae595d81", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:13 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29536983502600}, "additional": {"logType": "debug", "children": []}}], "workLog": []}