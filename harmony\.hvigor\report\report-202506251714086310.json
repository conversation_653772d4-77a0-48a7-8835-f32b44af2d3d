{"version": "2.0", "ppid": 21780, "events": [{"head": {"id": "075c4cf8-ac64-45fa-9b3f-31506be7c119", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251664517400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e88aba03-7ca3-4373-bfff-fd69f7126ae8", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251670187900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23e01b32-1810-4a1f-af38-cb721f888585", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251670544600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91dadb4a-1479-462c-b970-a7a8fbc08922", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291375829700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f54838fe-2e69-48f6-a5c6-85aa9c7348a5", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291383269200, "endTime": 29291517095000}, "additional": {"children": ["8965c2fd-1d42-42da-8879-5e97114deaaf", "65d36653-d066-4940-8133-07f0fe052d38", "6200fce4-3e7a-41da-81bf-ae749a12f09b", "c04aacd4-aa2e-4e4f-b614-c3c5908b449a", "df96a921-e0e6-42e1-b840-d0dcfa7dac89", "1e3659db-3dbb-47f3-aa82-62b3e3ab2be5", "56615706-a488-40cc-bb5e-f2435b8e5cb9"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "4213bae0-b4e3-4ebc-9066-8f7bf3e16a25"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8965c2fd-1d42-42da-8879-5e97114deaaf", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291383270100, "endTime": 29291393663500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f54838fe-2e69-48f6-a5c6-85aa9c7348a5", "logId": "942f6511-a0e6-452f-b555-c0a02e2a1932"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65d36653-d066-4940-8133-07f0fe052d38", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291393672500, "endTime": 29291515821100}, "additional": {"children": ["bb14acb2-67a1-4d35-9617-64ed5856bdda", "3a3ac83d-cf2e-48e0-ba77-6d8c910463f2", "5b873b18-25be-4272-b083-fddd0ee26178", "cb196a81-3575-49ed-b115-8c36cd0cf14e", "a8aa12b3-0679-44c2-9b5d-04436804434c", "4c8fd26a-9be8-4519-88d0-5e4466808366", "0e4a3407-f598-480e-98b0-57998603acf4", "52b3b23c-b694-4e32-8624-a11facfe7947", "1e3dc711-f1c2-4157-a4bd-f512cdce78c8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f54838fe-2e69-48f6-a5c6-85aa9c7348a5", "logId": "2b7d0b98-c2ed-4e6c-9efb-a5da74e659dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6200fce4-3e7a-41da-81bf-ae749a12f09b", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291515833700, "endTime": 29291517085900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f54838fe-2e69-48f6-a5c6-85aa9c7348a5", "logId": "27c4afa2-5543-48db-b212-9aa6f185d7cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c04aacd4-aa2e-4e4f-b614-c3c5908b449a", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291517091900, "endTime": 29291517093200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f54838fe-2e69-48f6-a5c6-85aa9c7348a5", "logId": "1ddd36c4-7208-4b9f-8ac8-a664cb218c56"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df96a921-e0e6-42e1-b840-d0dcfa7dac89", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291385899600, "endTime": 29291385944500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f54838fe-2e69-48f6-a5c6-85aa9c7348a5", "logId": "3a2876ac-98ea-4218-85b5-f98d89fd5802"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a2876ac-98ea-4218-85b5-f98d89fd5802", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291385899600, "endTime": 29291385944500}, "additional": {"logType": "info", "children": [], "durationId": "df96a921-e0e6-42e1-b840-d0dcfa7dac89", "parent": "4213bae0-b4e3-4ebc-9066-8f7bf3e16a25"}}, {"head": {"id": "1e3659db-3dbb-47f3-aa82-62b3e3ab2be5", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291389841200, "endTime": 29291389858500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f54838fe-2e69-48f6-a5c6-85aa9c7348a5", "logId": "c3ebe181-3210-4184-81c3-875afccf223c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3ebe181-3210-4184-81c3-875afccf223c", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291389841200, "endTime": 29291389858500}, "additional": {"logType": "info", "children": [], "durationId": "1e3659db-3dbb-47f3-aa82-62b3e3ab2be5", "parent": "4213bae0-b4e3-4ebc-9066-8f7bf3e16a25"}}, {"head": {"id": "1366cf83-d466-462f-b1a3-5be7a373ef3f", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291389891400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1debe02f-990c-4706-b11a-1f21cd6c77ce", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291393549100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "942f6511-a0e6-452f-b555-c0a02e2a1932", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291383270100, "endTime": 29291393663500}, "additional": {"logType": "info", "children": [], "durationId": "8965c2fd-1d42-42da-8879-5e97114deaaf", "parent": "4213bae0-b4e3-4ebc-9066-8f7bf3e16a25"}}, {"head": {"id": "bb14acb2-67a1-4d35-9617-64ed5856bdda", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291397577400, "endTime": 29291397584100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "65d36653-d066-4940-8133-07f0fe052d38", "logId": "6a5efc6d-d045-45db-a690-475043009230"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a3ac83d-cf2e-48e0-ba77-6d8c910463f2", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291397593800, "endTime": 29291401580500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "65d36653-d066-4940-8133-07f0fe052d38", "logId": "66264d4c-c8bc-4a2d-ba12-518e7aeab1b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5b873b18-25be-4272-b083-fddd0ee26178", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291401589500, "endTime": 29291466444900}, "additional": {"children": ["b215df85-9d31-44b2-b7c4-6702fa8947ef", "51c6f67f-701e-4b24-a934-f0d5a0ed2884", "d09084ef-56b5-4b74-b1a8-c67ece47f241"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "65d36653-d066-4940-8133-07f0fe052d38", "logId": "5f20c60d-5744-4ec5-bb52-05b1a4150a32"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cb196a81-3575-49ed-b115-8c36cd0cf14e", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291466455800, "endTime": 29291486416300}, "additional": {"children": ["e69817d6-298d-4b1a-8774-88b8a9551fb7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "65d36653-d066-4940-8133-07f0fe052d38", "logId": "65426d2a-de7a-45d1-a8da-255ac4ea9ceb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8aa12b3-0679-44c2-9b5d-04436804434c", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291486420900, "endTime": 29291497545700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "65d36653-d066-4940-8133-07f0fe052d38", "logId": "4ba09305-72e2-412d-83d7-183596984699"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c8fd26a-9be8-4519-88d0-5e4466808366", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291498426300, "endTime": 29291506028800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "65d36653-d066-4940-8133-07f0fe052d38", "logId": "52e25886-b7c1-4c37-bf73-0832ef4a4af1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e4a3407-f598-480e-98b0-57998603acf4", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291506044700, "endTime": 29291515653700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "65d36653-d066-4940-8133-07f0fe052d38", "logId": "2d66bead-3cb0-49e2-8ead-924fd85cbea5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52b3b23c-b694-4e32-8624-a11facfe7947", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291515710500, "endTime": 29291515813800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "65d36653-d066-4940-8133-07f0fe052d38", "logId": "cd708631-0b3a-45c5-8603-2a9b61755f7b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a5efc6d-d045-45db-a690-475043009230", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291397577400, "endTime": 29291397584100}, "additional": {"logType": "info", "children": [], "durationId": "bb14acb2-67a1-4d35-9617-64ed5856bdda", "parent": "2b7d0b98-c2ed-4e6c-9efb-a5da74e659dc"}}, {"head": {"id": "66264d4c-c8bc-4a2d-ba12-518e7aeab1b3", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291397593800, "endTime": 29291401580500}, "additional": {"logType": "info", "children": [], "durationId": "3a3ac83d-cf2e-48e0-ba77-6d8c910463f2", "parent": "2b7d0b98-c2ed-4e6c-9efb-a5da74e659dc"}}, {"head": {"id": "b215df85-9d31-44b2-b7c4-6702fa8947ef", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291402122400, "endTime": 29291402137700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5b873b18-25be-4272-b083-fddd0ee26178", "logId": "8284dddc-9653-43c7-9996-18f0af790170"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8284dddc-9653-43c7-9996-18f0af790170", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291402122400, "endTime": 29291402137700}, "additional": {"logType": "info", "children": [], "durationId": "b215df85-9d31-44b2-b7c4-6702fa8947ef", "parent": "5f20c60d-5744-4ec5-bb52-05b1a4150a32"}}, {"head": {"id": "51c6f67f-701e-4b24-a934-f0d5a0ed2884", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291403612900, "endTime": 29291465826200}, "additional": {"children": ["f1399cc4-9d12-4130-9022-3a95d72be012", "6d436b2e-e2fb-47bc-8f3d-83a024d3d6b2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5b873b18-25be-4272-b083-fddd0ee26178", "logId": "7b1f8ea4-d573-49b7-b442-d636202fd3d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1399cc4-9d12-4130-9022-3a95d72be012", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291403613500, "endTime": 29291407672000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "51c6f67f-701e-4b24-a934-f0d5a0ed2884", "logId": "8f7a2ce2-0366-447e-b680-d05da9fa6afe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6d436b2e-e2fb-47bc-8f3d-83a024d3d6b2", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291407682700, "endTime": 29291465819300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "51c6f67f-701e-4b24-a934-f0d5a0ed2884", "logId": "03b51625-dccf-4bdd-897d-f1c3142c9dbd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65b9210b-cde1-4c0b-b0ae-6b750dd76304", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291403617500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc63467d-8fbd-4c04-94b1-7416ee62fe4f", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291407563000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f7a2ce2-0366-447e-b680-d05da9fa6afe", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291403613500, "endTime": 29291407672000}, "additional": {"logType": "info", "children": [], "durationId": "f1399cc4-9d12-4130-9022-3a95d72be012", "parent": "7b1f8ea4-d573-49b7-b442-d636202fd3d2"}}, {"head": {"id": "367af3b4-e08b-428c-9aae-53d581a72fb0", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291407692200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70c870af-bbae-4e15-b9ae-ee8cb7727aba", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291413301400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49611a12-f2d0-4a51-a96e-7023156c69d4", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291413395600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8443b91a-d9aa-440a-98d5-cb61ba8c9c9c", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291413479600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02e051b5-3fa0-4da3-bf33-1ded86b201b5", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291413537900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0cb4b03-b668-4ab7-ac41-4b397d66ee82", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291414736100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e524363d-6483-43e9-8ea9-2dd52e11aa89", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291419103300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d201fbb-e562-42f9-88e9-b1159ea3f252", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291427730800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af816dc1-55ec-43f3-9518-c882cc877a2e", "name": "Sdk init in 27 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291446923600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "118fff86-dfc5-4f0a-99e5-a033ec528001", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291447098400}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 14}, "markType": "other"}}, {"head": {"id": "c956ccee-3102-46bb-8db5-2e33c1f3be13", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291447113400}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 14}, "markType": "other"}}, {"head": {"id": "6fdcb6f6-d05b-4c0c-a737-a9b1bcd6e943", "name": "Project task initialization takes 18 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291465626900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d3497ca-d487-4cf4-b260-27521a4d8c69", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291465724000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "499f09b6-a7cb-42a8-a9d6-40d5d91378d5", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291465759000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2f44fa0-444a-401c-a9d0-e388d818c46a", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291465784800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03b51625-dccf-4bdd-897d-f1c3142c9dbd", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291407682700, "endTime": 29291465819300}, "additional": {"logType": "info", "children": [], "durationId": "6d436b2e-e2fb-47bc-8f3d-83a024d3d6b2", "parent": "7b1f8ea4-d573-49b7-b442-d636202fd3d2"}}, {"head": {"id": "7b1f8ea4-d573-49b7-b442-d636202fd3d2", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291403612900, "endTime": 29291465826200}, "additional": {"logType": "info", "children": ["8f7a2ce2-0366-447e-b680-d05da9fa6afe", "03b51625-dccf-4bdd-897d-f1c3142c9dbd"], "durationId": "51c6f67f-701e-4b24-a934-f0d5a0ed2884", "parent": "5f20c60d-5744-4ec5-bb52-05b1a4150a32"}}, {"head": {"id": "d09084ef-56b5-4b74-b1a8-c67ece47f241", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291466424600, "endTime": 29291466436500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5b873b18-25be-4272-b083-fddd0ee26178", "logId": "c4e8a008-844b-4337-93b1-9baf6e94e7f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c4e8a008-844b-4337-93b1-9baf6e94e7f6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291466424600, "endTime": 29291466436500}, "additional": {"logType": "info", "children": [], "durationId": "d09084ef-56b5-4b74-b1a8-c67ece47f241", "parent": "5f20c60d-5744-4ec5-bb52-05b1a4150a32"}}, {"head": {"id": "5f20c60d-5744-4ec5-bb52-05b1a4150a32", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291401589500, "endTime": 29291466444900}, "additional": {"logType": "info", "children": ["8284dddc-9653-43c7-9996-18f0af790170", "7b1f8ea4-d573-49b7-b442-d636202fd3d2", "c4e8a008-844b-4337-93b1-9baf6e94e7f6"], "durationId": "5b873b18-25be-4272-b083-fddd0ee26178", "parent": "2b7d0b98-c2ed-4e6c-9efb-a5da74e659dc"}}, {"head": {"id": "e69817d6-298d-4b1a-8774-88b8a9551fb7", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291466958100, "endTime": 29291486404100}, "additional": {"children": ["a2154cde-4794-49e8-ae41-0ff71a147568", "f2191e8f-dfb3-4d9a-a8e0-5d56c18bcf9d", "058b232b-7a93-4dc8-9ecd-0a078cf9f01b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cb196a81-3575-49ed-b115-8c36cd0cf14e", "logId": "e8522422-e16f-4fa8-9090-72af90b0e794"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a2154cde-4794-49e8-ae41-0ff71a147568", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291469468300, "endTime": 29291469479500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e69817d6-298d-4b1a-8774-88b8a9551fb7", "logId": "641bf9b1-f33c-4fe6-90cc-ef024f132519"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "641bf9b1-f33c-4fe6-90cc-ef024f132519", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291469468300, "endTime": 29291469479500}, "additional": {"logType": "info", "children": [], "durationId": "a2154cde-4794-49e8-ae41-0ff71a147568", "parent": "e8522422-e16f-4fa8-9090-72af90b0e794"}}, {"head": {"id": "f2191e8f-dfb3-4d9a-a8e0-5d56c18bcf9d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291471063000, "endTime": 29291485239200}, "additional": {"children": ["20810439-b1af-48e7-bfbd-fd7371a1586a", "60611c6f-cb12-44b7-941c-04152cef433e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e69817d6-298d-4b1a-8774-88b8a9551fb7", "logId": "16504fd5-d6da-4a8e-84dc-7be087342c09"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "20810439-b1af-48e7-bfbd-fd7371a1586a", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291471063900, "endTime": 29291474453500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2191e8f-dfb3-4d9a-a8e0-5d56c18bcf9d", "logId": "ea6ec5b2-2f73-4ae4-b307-a717da81e979"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "60611c6f-cb12-44b7-941c-04152cef433e", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291474463400, "endTime": 29291485230700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2191e8f-dfb3-4d9a-a8e0-5d56c18bcf9d", "logId": "d1a47b72-cab6-4642-9079-80afb9328bf0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66dabb9f-fc56-456b-a350-0a3f332a1574", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291471069500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0aa6fb6-3331-435a-a06d-dc9eca980689", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291474338700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea6ec5b2-2f73-4ae4-b307-a717da81e979", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291471063900, "endTime": 29291474453500}, "additional": {"logType": "info", "children": [], "durationId": "20810439-b1af-48e7-bfbd-fd7371a1586a", "parent": "16504fd5-d6da-4a8e-84dc-7be087342c09"}}, {"head": {"id": "81e437de-168e-471d-821d-4b65d738be64", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291474471600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14b904d6-5794-4583-b521-559ba1419720", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291481051600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "864e52c4-1030-4d5b-a84f-2dd32f8bab53", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291481169700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efec96b5-bc5d-4577-8ae0-34acdba114c3", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291481848500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7421204-31e5-4f50-a42c-c3303bb93fa5", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291481992600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab0b7c93-339f-48b3-afb8-60f30aacd25d", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291482041300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd6263cc-bf49-4d64-aeca-cc731838274c", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291482071500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "347c5c1d-432d-44f1-884e-ed0db5499d6c", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291482105500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a4384f7-ba11-4713-83ad-3e208dda0288", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291484994900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bd2b1da-baf5-4345-aa68-495b88458a27", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291485129200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35d90b2a-5f5d-4304-b6f1-5504bc5b8c5e", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291485178600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e795f444-6688-488a-a2cc-e6e3e4822be2", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291485207300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1a47b72-cab6-4642-9079-80afb9328bf0", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291474463400, "endTime": 29291485230700}, "additional": {"logType": "info", "children": [], "durationId": "60611c6f-cb12-44b7-941c-04152cef433e", "parent": "16504fd5-d6da-4a8e-84dc-7be087342c09"}}, {"head": {"id": "16504fd5-d6da-4a8e-84dc-7be087342c09", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291471063000, "endTime": 29291485239200}, "additional": {"logType": "info", "children": ["ea6ec5b2-2f73-4ae4-b307-a717da81e979", "d1a47b72-cab6-4642-9079-80afb9328bf0"], "durationId": "f2191e8f-dfb3-4d9a-a8e0-5d56c18bcf9d", "parent": "e8522422-e16f-4fa8-9090-72af90b0e794"}}, {"head": {"id": "058b232b-7a93-4dc8-9ecd-0a078cf9f01b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291486384900, "endTime": 29291486396300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e69817d6-298d-4b1a-8774-88b8a9551fb7", "logId": "dd7c46d3-8df4-4455-ba5c-105ac7607e6d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd7c46d3-8df4-4455-ba5c-105ac7607e6d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291486384900, "endTime": 29291486396300}, "additional": {"logType": "info", "children": [], "durationId": "058b232b-7a93-4dc8-9ecd-0a078cf9f01b", "parent": "e8522422-e16f-4fa8-9090-72af90b0e794"}}, {"head": {"id": "e8522422-e16f-4fa8-9090-72af90b0e794", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291466958100, "endTime": 29291486404100}, "additional": {"logType": "info", "children": ["641bf9b1-f33c-4fe6-90cc-ef024f132519", "16504fd5-d6da-4a8e-84dc-7be087342c09", "dd7c46d3-8df4-4455-ba5c-105ac7607e6d"], "durationId": "e69817d6-298d-4b1a-8774-88b8a9551fb7", "parent": "65426d2a-de7a-45d1-a8da-255ac4ea9ceb"}}, {"head": {"id": "65426d2a-de7a-45d1-a8da-255ac4ea9ceb", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291466455800, "endTime": 29291486416300}, "additional": {"logType": "info", "children": ["e8522422-e16f-4fa8-9090-72af90b0e794"], "durationId": "cb196a81-3575-49ed-b115-8c36cd0cf14e", "parent": "2b7d0b98-c2ed-4e6c-9efb-a5da74e659dc"}}, {"head": {"id": "02eb2fbe-b4c2-4e6b-ab23-bb661ed3b2e6", "name": "watch files: [\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\hvigorfile.ts',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291497240400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34bd1bda-3a24-4ffa-9fda-9f7e6191d0d9", "name": "hvigorfile, resolve hvigorfile dependencies in 12 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291497495700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ba09305-72e2-412d-83d7-183596984699", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291486420900, "endTime": 29291497545700}, "additional": {"logType": "info", "children": [], "durationId": "a8aa12b3-0679-44c2-9b5d-04436804434c", "parent": "2b7d0b98-c2ed-4e6c-9efb-a5da74e659dc"}}, {"head": {"id": "1e3dc711-f1c2-4157-a4bd-f512cdce78c8", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291498275100, "endTime": 29291498419500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "65d36653-d066-4940-8133-07f0fe052d38", "logId": "4f0bf3be-e388-422d-b89b-98c57eb19f5c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b2804cd3-697c-4c1b-89fb-68ac4ffd5a18", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291498295400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f0bf3be-e388-422d-b89b-98c57eb19f5c", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291498275100, "endTime": 29291498419500}, "additional": {"logType": "info", "children": [], "durationId": "1e3dc711-f1c2-4157-a4bd-f512cdce78c8", "parent": "2b7d0b98-c2ed-4e6c-9efb-a5da74e659dc"}}, {"head": {"id": "c1d8207d-325b-49b1-96c1-66237db9c989", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291499607900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37f0172b-974b-4bee-be67-aa966e4922be", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291505275000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52e25886-b7c1-4c37-bf73-0832ef4a4af1", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291498426300, "endTime": 29291506028800}, "additional": {"logType": "info", "children": [], "durationId": "4c8fd26a-9be8-4519-88d0-5e4466808366", "parent": "2b7d0b98-c2ed-4e6c-9efb-a5da74e659dc"}}, {"head": {"id": "d91fa512-b91a-457b-ab3b-e3e3eb3ad2c0", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291506056200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "546053b2-fe39-4899-9459-f478f16b09ac", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291510857700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "163f2bda-8292-41ab-b4f7-80856f3501ce", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291510967700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "326f7d16-1701-48bc-a6c7-fb883da97b21", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291511222000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "347a9ae4-d511-4370-9b40-ab6a517ccaf6", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291513222100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b807264e-1e7f-48f5-a895-c2c373d14e50", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291513306100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d66bead-3cb0-49e2-8ead-924fd85cbea5", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291506044700, "endTime": 29291515653700}, "additional": {"logType": "info", "children": [], "durationId": "0e4a3407-f598-480e-98b0-57998603acf4", "parent": "2b7d0b98-c2ed-4e6c-9efb-a5da74e659dc"}}, {"head": {"id": "c6002f95-0b1d-41fc-a836-d78606f2f7f1", "name": "Configuration phase cost:119 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291515734600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd708631-0b3a-45c5-8603-2a9b61755f7b", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291515710500, "endTime": 29291515813800}, "additional": {"logType": "info", "children": [], "durationId": "52b3b23c-b694-4e32-8624-a11facfe7947", "parent": "2b7d0b98-c2ed-4e6c-9efb-a5da74e659dc"}}, {"head": {"id": "2b7d0b98-c2ed-4e6c-9efb-a5da74e659dc", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291393672500, "endTime": 29291515821100}, "additional": {"logType": "info", "children": ["6a5efc6d-d045-45db-a690-475043009230", "66264d4c-c8bc-4a2d-ba12-518e7aeab1b3", "5f20c60d-5744-4ec5-bb52-05b1a4150a32", "65426d2a-de7a-45d1-a8da-255ac4ea9ceb", "4ba09305-72e2-412d-83d7-183596984699", "52e25886-b7c1-4c37-bf73-0832ef4a4af1", "2d66bead-3cb0-49e2-8ead-924fd85cbea5", "cd708631-0b3a-45c5-8603-2a9b61755f7b", "4f0bf3be-e388-422d-b89b-98c57eb19f5c"], "durationId": "65d36653-d066-4940-8133-07f0fe052d38", "parent": "4213bae0-b4e3-4ebc-9066-8f7bf3e16a25"}}, {"head": {"id": "56615706-a488-40cc-bb5e-f2435b8e5cb9", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291517052600, "endTime": 29291517078400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f54838fe-2e69-48f6-a5c6-85aa9c7348a5", "logId": "3305a52a-1ca7-4625-8fbc-7dc4374e90c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3305a52a-1ca7-4625-8fbc-7dc4374e90c0", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291517052600, "endTime": 29291517078400}, "additional": {"logType": "info", "children": [], "durationId": "56615706-a488-40cc-bb5e-f2435b8e5cb9", "parent": "4213bae0-b4e3-4ebc-9066-8f7bf3e16a25"}}, {"head": {"id": "27c4afa2-5543-48db-b212-9aa6f185d7cb", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291515833700, "endTime": 29291517085900}, "additional": {"logType": "info", "children": [], "durationId": "6200fce4-3e7a-41da-81bf-ae749a12f09b", "parent": "4213bae0-b4e3-4ebc-9066-8f7bf3e16a25"}}, {"head": {"id": "1ddd36c4-7208-4b9f-8ac8-a664cb218c56", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291517091900, "endTime": 29291517093200}, "additional": {"logType": "info", "children": [], "durationId": "c04aacd4-aa2e-4e4f-b614-c3c5908b449a", "parent": "4213bae0-b4e3-4ebc-9066-8f7bf3e16a25"}}, {"head": {"id": "4213bae0-b4e3-4ebc-9066-8f7bf3e16a25", "name": "init", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291383269200, "endTime": 29291517095000}, "additional": {"logType": "info", "children": ["942f6511-a0e6-452f-b555-c0a02e2a1932", "2b7d0b98-c2ed-4e6c-9efb-a5da74e659dc", "27c4afa2-5543-48db-b212-9aa6f185d7cb", "1ddd36c4-7208-4b9f-8ac8-a664cb218c56", "3a2876ac-98ea-4218-85b5-f98d89fd5802", "c3ebe181-3210-4184-81c3-875afccf223c", "3305a52a-1ca7-4625-8fbc-7dc4374e90c0"], "durationId": "f54838fe-2e69-48f6-a5c6-85aa9c7348a5"}}, {"head": {"id": "acfc73e1-5d5b-4cff-afc3-adccd3f72cdb", "name": "Configuration task cost before running: 137 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291517202000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69df988f-aa24-4815-8136-fe2214c4bdc2", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291522263400, "endTime": 29291529914200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "2ee1fd82-88bf-4942-a46a-1de2b38846c3", "logId": "9016ab93-7166-454b-8a29-e8432deb2598"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ee1fd82-88bf-4942-a46a-1de2b38846c3", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291518370500}, "additional": {"logType": "detail", "children": [], "durationId": "69df988f-aa24-4815-8136-fe2214c4bdc2"}}, {"head": {"id": "c05ea53f-485d-4031-8731-46da4ae60546", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291518813400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60164b3e-de4c-4985-8559-8c89345436d1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291518887000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "037d6f76-7564-4128-b981-f49c94fc93d9", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291522274300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cca9a21-9d98-401c-8ff9-db66225a57ec", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291529553800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dba2bc7-f941-41d0-bf83-535bfd3d41d6", "name": "entry : default@PreBuild cost memory 0.29357147216796875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291529697900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9016ab93-7166-454b-8a29-e8432deb2598", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291522263400, "endTime": 29291529914200}, "additional": {"logType": "info", "children": [], "durationId": "69df988f-aa24-4815-8136-fe2214c4bdc2"}}, {"head": {"id": "5874a11e-ee6a-459e-9ff8-5b4b58852531", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291533967900, "endTime": 29291535945400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "1dad782c-3acb-4532-839c-f4137d5826cc", "logId": "efc11f19-3785-4895-b602-83a08b4986a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1dad782c-3acb-4532-839c-f4137d5826cc", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291532875100}, "additional": {"logType": "detail", "children": [], "durationId": "5874a11e-ee6a-459e-9ff8-5b4b58852531"}}, {"head": {"id": "82ac8611-d53b-4dbc-8fa2-0bd30db1a497", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291533308200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38d4385a-6453-4f3c-b56f-42fc6a4abdf4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291533373800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1de20a61-a43d-4601-94c5-c740a69e65c8", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291533974300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7b0e97d-a076-4731-9059-331746e6e080", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291535440300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "535c27ae-5855-489f-a5a9-deff0a57545c", "name": "entry : default@MergeProfile cost memory 0.11212921142578125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291535519000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efc11f19-3785-4895-b602-83a08b4986a6", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291533967900, "endTime": 29291535945400}, "additional": {"logType": "info", "children": [], "durationId": "5874a11e-ee6a-459e-9ff8-5b4b58852531"}}, {"head": {"id": "e9910485-c52b-4226-a83b-571c65fdc991", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291538501100, "endTime": 29291540363100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9dfaec46-d513-4957-bbd5-e373d62a09db", "logId": "a3530a65-95c3-4187-9ed5-2f5096fb78c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9dfaec46-d513-4957-bbd5-e373d62a09db", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291537310400}, "additional": {"logType": "detail", "children": [], "durationId": "e9910485-c52b-4226-a83b-571c65fdc991"}}, {"head": {"id": "4298e41e-9ebf-4617-86e6-b1c109f5edff", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291537720000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "809a81d2-ecf1-41cd-8eb0-da6fb114455a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291537787500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7ccf09a-9f75-455c-bfdd-3cd0d3692f24", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291538507300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bd4dc0d-fa01-46c1-8154-e1af0d152e5b", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291539248400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ce82850-ccc1-4766-9a2a-08fb37377263", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291540243600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78d27480-ab15-4b0b-bdcb-d81c08ac5c5c", "name": "entry : default@CreateBuildProfile cost memory 0.10039520263671875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291540317700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3530a65-95c3-4187-9ed5-2f5096fb78c2", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291538501100, "endTime": 29291540363100}, "additional": {"logType": "info", "children": [], "durationId": "e9910485-c52b-4226-a83b-571c65fdc991"}}, {"head": {"id": "c7fbff5c-c724-43e9-9bd2-0f7705289b3f", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291542758600, "endTime": 29291542999200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "020531d1-9255-41a0-8a67-4054fed9c2f2", "logId": "8ca8b663-b70d-4127-983c-c2c7635076ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "020531d1-9255-41a0-8a67-4054fed9c2f2", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291541648200}, "additional": {"logType": "detail", "children": [], "durationId": "c7fbff5c-c724-43e9-9bd2-0f7705289b3f"}}, {"head": {"id": "8568aa15-f76a-4b01-b2ef-4ff66e231e92", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291542066200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "154b2154-14e7-46f0-8dcf-4c56e72acafc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291542134800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa5d9f5a-7cb6-4a11-8c53-3f0bb278faad", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291542764600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45b76e29-2eb5-41bc-972e-9c213dc31270", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291542846000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f0c698c-7800-4112-9970-1843bb61e6ac", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291542876400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04a0bc9c-0ded-4ee8-bfe6-5aac2b69b400", "name": "entry : default@PreCheckSyscap cost memory 0.03899383544921875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291542926300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8d2ae0a-288b-4e3e-9f04-ab13b2e11646", "name": "runTaskFromQueue task cost before running: 163 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291542973400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ca8b663-b70d-4127-983c-c2c7635076ef", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291542758600, "endTime": 29291542999200, "totalTime": 200100}, "additional": {"logType": "info", "children": [], "durationId": "c7fbff5c-c724-43e9-9bd2-0f7705289b3f"}}, {"head": {"id": "8ebd6b0f-72c6-4820-bb28-bbd838310d08", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291550945900, "endTime": 29291551820900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a0941138-aaac-485f-9964-615b2c7a1867", "logId": "da98a2d9-d8bb-4fb9-8e02-b15c9785b308"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0941138-aaac-485f-9964-615b2c7a1867", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291544723000}, "additional": {"logType": "detail", "children": [], "durationId": "8ebd6b0f-72c6-4820-bb28-bbd838310d08"}}, {"head": {"id": "f26940d0-8c01-446b-a5fc-25690b2f9ab5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291545310800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a31deed-7839-49e2-b6a7-9431879ca611", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291545419500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d62ec32-16e0-4773-9fb9-1a7b124c1558", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291550958700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57a78fe6-16e1-4519-aa5b-e6fabf9d797f", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291551134400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee3c00a4-570e-43b0-a85c-f5d59c1ad217", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291551677600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8609240a-e3c3-4d58-aebc-77276a1099c3", "name": "entry : default@GeneratePkgContextInfo cost memory 0.071380615234375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291551773600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da98a2d9-d8bb-4fb9-8e02-b15c9785b308", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291550945900, "endTime": 29291551820900}, "additional": {"logType": "info", "children": [], "durationId": "8ebd6b0f-72c6-4820-bb28-bbd838310d08"}}, {"head": {"id": "e2660bbe-5fb1-47de-b925-aa525e3e635a", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291554632300, "endTime": 29291555567100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "c416c9f3-59f0-454b-b586-cd20dac68c64", "logId": "97075da3-6074-4a02-8dcd-4e89150b84dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c416c9f3-59f0-454b-b586-cd20dac68c64", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291553158100}, "additional": {"logType": "detail", "children": [], "durationId": "e2660bbe-5fb1-47de-b925-aa525e3e635a"}}, {"head": {"id": "08ae0fa0-0993-4c37-9009-78e342f847e2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291553583500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66382915-cdc4-4da0-9179-caa7aef7127c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291553658200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0473cd7f-6925-43e4-93c8-38f9b3b69aa4", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291554639000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6cf6000-700b-4fcc-91c1-133140c5d882", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291555436600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b61ba638-9812-4ddc-9b2a-a837b8a13949", "name": "entry : default@ProcessProfile cost memory 0.056884765625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291555522600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97075da3-6074-4a02-8dcd-4e89150b84dd", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291554632300, "endTime": 29291555567100}, "additional": {"logType": "info", "children": [], "durationId": "e2660bbe-5fb1-47de-b925-aa525e3e635a"}}, {"head": {"id": "5e76ab38-07d7-4156-a2ef-9d79c3bd7257", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291558722100, "endTime": 29291564106900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "18b9caad-db71-454f-aaff-80d157e5299a", "logId": "7a97e2b9-41a4-4998-a243-4d3fcd834778"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18b9caad-db71-454f-aaff-80d157e5299a", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291556849200}, "additional": {"logType": "detail", "children": [], "durationId": "5e76ab38-07d7-4156-a2ef-9d79c3bd7257"}}, {"head": {"id": "d6ad50b3-c59d-4970-b89e-5665b1c066c3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291557270000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03a9f4f8-0140-4f98-b121-0b15eec2a52a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291557341200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38e23baa-ab5d-4f24-95fd-644da43bcd9a", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291558728300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbd1a009-05b6-4c9c-b4d6-3caaad6460ea", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291563969100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "905ab146-882b-4a92-bf44-38ff1bc48014", "name": "entry : default@ProcessRouterMap cost memory -1.4000930786132812", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291564058000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a97e2b9-41a4-4998-a243-4d3fcd834778", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291558722100, "endTime": 29291564106900}, "additional": {"logType": "info", "children": [], "durationId": "5e76ab38-07d7-4156-a2ef-9d79c3bd7257"}}, {"head": {"id": "02318f85-d35e-4101-a031-c6d9f13abe03", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291569796700, "endTime": 29291571947400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "1e0c84f1-8ec5-4ae8-8353-50c8e4286fb1", "logId": "71cf2745-e748-47ac-911f-2028d8c57d03"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e0c84f1-8ec5-4ae8-8353-50c8e4286fb1", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291566565700}, "additional": {"logType": "detail", "children": [], "durationId": "02318f85-d35e-4101-a031-c6d9f13abe03"}}, {"head": {"id": "339092b0-04d9-4a90-bf2b-d6836ee67e02", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291567003000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38057f8c-32bc-4714-8297-0f8784063621", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291567078900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17d5d1f5-c6ea-4be3-a2ca-2ef10feed20b", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291567962400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ed9ed50-e025-47df-9cda-d4f5c4592151", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291570730700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5fd37af-10b0-4d39-bd2d-e364ec3e9699", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291570854600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee48cdb9-5449-46f5-b982-a366<PERSON><PERSON><PERSON>f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291570893700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58b09171-209d-419e-9ed4-ab1fabdbaa25", "name": "entry : default@PreviewProcessResource cost memory 0.07207489013671875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291570949100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fc21d71-5ef1-4e12-a3f2-dc51e8da12e1", "name": "runTaskFromQueue task cost before running: 192 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291571867200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71cf2745-e748-47ac-911f-2028d8c57d03", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291569796700, "endTime": 29291571947400, "totalTime": 1190300}, "additional": {"logType": "info", "children": [], "durationId": "02318f85-d35e-4101-a031-c6d9f13abe03"}}, {"head": {"id": "5c6139b4-db30-4239-8c49-3b949c2ec314", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291577460100, "endTime": 29291594041300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ac7892b2-1d17-47e4-9163-e2c8a12793e4", "logId": "edfb6832-3723-4e9e-95a0-f4cba75fc800"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac7892b2-1d17-47e4-9163-e2c8a12793e4", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291574136000}, "additional": {"logType": "detail", "children": [], "durationId": "5c6139b4-db30-4239-8c49-3b949c2ec314"}}, {"head": {"id": "b1f4514b-7747-49a7-9a00-7004efc4d7ab", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291574566700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60c51ccd-0f11-4b44-b8a0-f1476283c71e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291574653100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78487d4b-2783-46f3-b5a0-e8904e5f8f4b", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291577469200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00752075-6bca-4b57-94ba-a69eaaf382c5", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291593855900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0b66f92-261a-4b1e-9426-3e7830842182", "name": "entry : default@GenerateLoaderJson cost memory 0.7666473388671875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291593977700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edfb6832-3723-4e9e-95a0-f4cba75fc800", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291577460100, "endTime": 29291594041300}, "additional": {"logType": "info", "children": [], "durationId": "5c6139b4-db30-4239-8c49-3b949c2ec314"}}, {"head": {"id": "1fbccca9-0efe-448a-ab82-448ccdd1259d", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291603464000, "endTime": 29291758827900}, "additional": {"children": ["55c38219-a1b2-418b-815f-b97a2e0f6020", "0b8c82db-50d2-432c-ac2d-dc6803cc510d", "fe8293a0-e9f5-40cc-a7ff-0f24f6d30bc3", "158b1525-7d8e-4072-850a-4f207e52cd2c"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "6bd9304f-97b4-4a92-a4ac-646e639e1305", "logId": "91817f91-5246-4d6f-aa2a-2a015abb4b0b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6bd9304f-97b4-4a92-a4ac-646e639e1305", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291600524600}, "additional": {"logType": "detail", "children": [], "durationId": "1fbccca9-0efe-448a-ab82-448ccdd1259d"}}, {"head": {"id": "860aa131-8ce8-4438-b901-44cd4854bf3a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291600929800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56c04319-ad0c-4f14-9aed-a443986a9d8b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291601007000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b74a627e-7fa7-4c0e-b533-7068b3f3b1ab", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291601728800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53bed251-5d04-4b9d-aa98-8667b46203a6", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291603482700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55c38219-a1b2-418b-815f-b97a2e0f6020", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291605142800, "endTime": 29291616076300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1fbccca9-0efe-448a-ab82-448ccdd1259d", "logId": "2eeb24cd-b582-4957-ab04-b1df4bfa8276"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2eeb24cd-b582-4957-ab04-b1df4bfa8276", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291605142800, "endTime": 29291616076300}, "additional": {"logType": "info", "children": [], "durationId": "55c38219-a1b2-418b-815f-b97a2e0f6020", "parent": "91817f91-5246-4d6f-aa2a-2a015abb4b0b"}}, {"head": {"id": "2d91e57b-552f-4cd7-903d-8fe88bc88e05", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291616182600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b8c82db-50d2-432c-ac2d-dc6803cc510d", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291617023700, "endTime": 29291647959900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1fbccca9-0efe-448a-ab82-448ccdd1259d", "logId": "0894a2f1-815b-44c8-a856-1f5af367ec8b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6126313-4360-49ce-a065-7dc40a54996e", "name": "current process  memoryUsage: {\n  rss: 122404864,\n  heapTotal: 125132800,\n  heapUsed: 111025120,\n  external: 3133360,\n  arrayBuffers: 127261\n} os memoryUsage :12.397624969482422", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291617786700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "821ea4fb-e88e-47ff-821a-00e8612b8297", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291645629800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0894a2f1-815b-44c8-a856-1f5af367ec8b", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291617023700, "endTime": 29291647959900}, "additional": {"logType": "info", "children": [], "durationId": "0b8c82db-50d2-432c-ac2d-dc6803cc510d", "parent": "91817f91-5246-4d6f-aa2a-2a015abb4b0b"}}, {"head": {"id": "0c8543f1-e43b-4783-98ab-741e76ccc4d8", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291648132300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe8293a0-e9f5-40cc-a7ff-0f24f6d30bc3", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291649161000, "endTime": 29291686033700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1fbccca9-0efe-448a-ab82-448ccdd1259d", "logId": "1e2f7fae-4ca2-4175-85bc-a6358db01039"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a2cb3f6-2b41-4aa5-85a7-bfdc96fc0929", "name": "current process  memoryUsage: {\n  rss: 122417152,\n  heapTotal: 125132800,\n  heapUsed: 111284016,\n  external: 3133486,\n  arrayBuffers: 127402\n} os memoryUsage :12.406974792480469", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291649999400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f92adf7-9e4a-436d-a2fb-a1989d7f4074", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291683959000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e2f7fae-4ca2-4175-85bc-a6358db01039", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291649161000, "endTime": 29291686033700}, "additional": {"logType": "info", "children": [], "durationId": "fe8293a0-e9f5-40cc-a7ff-0f24f6d30bc3", "parent": "91817f91-5246-4d6f-aa2a-2a015abb4b0b"}}, {"head": {"id": "21eb3a68-2299-4393-a434-74edc86d5a72", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291686206600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "158b1525-7d8e-4072-850a-4f207e52cd2c", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291687050100, "endTime": 29291756615300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1fbccca9-0efe-448a-ab82-448ccdd1259d", "logId": "599f24ae-25dc-4a1a-a704-b8306be55d1d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49f66adf-6d37-4b2c-be0e-43e9c47138ed", "name": "current process  memoryUsage: {\n  rss: 122425344,\n  heapTotal: 125132800,\n  heapUsed: 111577088,\n  external: 3141804,\n  arrayBuffers: 136604\n} os memoryUsage :12.39907455444336", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291687797800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18843bfe-2b9e-4ff4-9a13-48489e1b7477", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291753151800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "599f24ae-25dc-4a1a-a704-b8306be55d1d", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291687050100, "endTime": 29291756615300}, "additional": {"logType": "info", "children": [], "durationId": "158b1525-7d8e-4072-850a-4f207e52cd2c", "parent": "91817f91-5246-4d6f-aa2a-2a015abb4b0b"}}, {"head": {"id": "4dec1f91-d7ec-487d-8c94-8392f7e9583e", "name": "entry : default@PreviewCompileResource cost memory 1.02490234375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291758571800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7154c5e8-c591-49ea-b294-30bbcaedbf7e", "name": "runTaskFromQueue task cost before running: 379 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291758771200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91817f91-5246-4d6f-aa2a-2a015abb4b0b", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291603464000, "endTime": 29291758827900, "totalTime": 155244800}, "additional": {"logType": "info", "children": ["2eeb24cd-b582-4957-ab04-b1df4bfa8276", "0894a2f1-815b-44c8-a856-1f5af367ec8b", "1e2f7fae-4ca2-4175-85bc-a6358db01039", "599f24ae-25dc-4a1a-a704-b8306be55d1d"], "durationId": "1fbccca9-0efe-448a-ab82-448ccdd1259d"}}, {"head": {"id": "f75e1906-6f91-4987-8164-bcf64e30af5c", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291762273800, "endTime": 29291762511500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "6bdb74bd-d106-40cc-9740-83e8c28baf28", "logId": "6a6166c1-59e5-444b-839c-aaeaa3dc20c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6bdb74bd-d106-40cc-9740-83e8c28baf28", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291761485500}, "additional": {"logType": "detail", "children": [], "durationId": "f75e1906-6f91-4987-8164-bcf64e30af5c"}}, {"head": {"id": "7fb3fe15-4e24-4df4-860b-f1508fc52393", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291762060600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcd2fbcd-2783-40a8-b992-de61ceb4d449", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291762173100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0cc24ad-d4ac-4096-b203-9bf9975432cd", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291762280500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1628d670-5444-48b4-85e8-c72c2d218342", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291762344600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69e7c9ec-27e6-4261-9ed4-482b4bedd1e3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291762368500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5ccc7d8-63e2-4d43-bccb-62fafd5c8021", "name": "entry : default@PreviewHookCompileResource cost memory 0.03803253173828125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291762415900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1deccda-3371-44b1-b962-c6f6882c2972", "name": "runTaskFromQueue task cost before running: 382 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291762481800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a6166c1-59e5-444b-839c-aaeaa3dc20c3", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291762273800, "endTime": 29291762511500, "totalTime": 187700}, "additional": {"logType": "info", "children": [], "durationId": "f75e1906-6f91-4987-8164-bcf64e30af5c"}}, {"head": {"id": "140f94e4-feee-4e3a-8f79-9718cac3bae0", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291765769100, "endTime": 29291772285800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "9625c8dc-7183-4563-8c90-aa9691771b0c", "logId": "656f5c38-8a69-47f9-be2a-e626eb2dc696"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9625c8dc-7183-4563-8c90-aa9691771b0c", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291764544400}, "additional": {"logType": "detail", "children": [], "durationId": "140f94e4-feee-4e3a-8f79-9718cac3bae0"}}, {"head": {"id": "3c586b7d-8b9a-4d03-ac5e-394c38a44c73", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291765090500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "065e0295-1625-4d1f-955a-bf384b6052e9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291765183000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a3560e3-effa-498e-99e1-f4b96643f659", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291765778300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77fb5970-0cb6-45e0-a24c-e405790d67d7", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291767086600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2501443-172c-46fb-9a68-a5c91a3530f9", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291767226700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c5939d6-d490-43bf-9cf8-d72a2fac5860", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291767295600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00a632c9-d1d4-4dc1-b27e-2bc0169d84ce", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291767322600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7e23f07-bb81-48a1-8e6c-cc52c2448489", "name": "entry : default@CopyPreviewProfile cost memory 0.22301483154296875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291772079400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a16d05f2-92cf-44dd-b686-05620bd5d0aa", "name": "runTaskFromQueue task cost before running: 392 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291772236500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "656f5c38-8a69-47f9-be2a-e626eb2dc696", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291765769100, "endTime": 29291772285800, "totalTime": 6438000}, "additional": {"logType": "info", "children": [], "durationId": "140f94e4-feee-4e3a-8f79-9718cac3bae0"}}, {"head": {"id": "722e2c51-18b0-4616-81af-5214ea87f024", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291775404500, "endTime": 29291775718600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "d89c81dd-bacd-42b8-9011-2db46d93f666", "logId": "540eb18f-ff18-4468-99bc-a532d4ecf1b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d89c81dd-bacd-42b8-9011-2db46d93f666", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291774068900}, "additional": {"logType": "detail", "children": [], "durationId": "722e2c51-18b0-4616-81af-5214ea87f024"}}, {"head": {"id": "a91b982b-519d-4792-9441-293e5fdc6cf2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291774553300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acd66dca-bd96-4eda-bd00-e77e75dab97a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291774658100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01279d51-1b29-4a04-8b84-02ed7f8e67a7", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291775413200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec190b06-6569-48d3-8548-f310727451e4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291775532100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d05e99c-2122-4183-8b47-44c6cf317cf6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291775566700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "600acf8f-c66b-42b8-bcb7-d38505648705", "name": "entry : default@ReplacePreviewerPage cost memory 0.0414581298828125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291775637600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4303efa7-79d8-4dd2-8169-6797bbddad84", "name": "runTaskFromQueue task cost before running: 396 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291775692100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "540eb18f-ff18-4468-99bc-a532d4ecf1b8", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291775404500, "endTime": 29291775718600, "totalTime": 270200}, "additional": {"logType": "info", "children": [], "durationId": "722e2c51-18b0-4616-81af-5214ea87f024"}}, {"head": {"id": "51cdf288-239b-4538-bd28-9ce088e58bf5", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291777218800, "endTime": 29291777434700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "882cbe4b-b145-4a1c-a769-2fc56ec61c11", "logId": "0f4a2f13-9436-4058-b379-694e1140ce5c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "882cbe4b-b145-4a1c-a769-2fc56ec61c11", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291777161800}, "additional": {"logType": "detail", "children": [], "durationId": "51cdf288-239b-4538-bd28-9ce088e58bf5"}}, {"head": {"id": "a148b293-b98e-40aa-9b0f-3e1ba59e9790", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291777224800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b1671cd-ad97-45b8-8b76-99b33af25ba7", "name": "entry : buildPreviewerResource cost memory 0.01168060302734375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291777337400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8331fa4-11c6-4b59-a07d-0c5f0f85a2b8", "name": "runTaskFromQueue task cost before running: 397 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291777400600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f4a2f13-9436-4058-b379-694e1140ce5c", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291777218800, "endTime": 29291777434700, "totalTime": 163800}, "additional": {"logType": "info", "children": [], "durationId": "51cdf288-239b-4538-bd28-9ce088e58bf5"}}, {"head": {"id": "23e0a242-448e-4be3-9e39-312f128561da", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291780289600, "endTime": 29291783892000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "0dca51aa-1396-4cd6-8468-62775854a1a9", "logId": "45ca0d6f-7035-40e2-9af7-fdd5ee7b805a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0dca51aa-1396-4cd6-8468-62775854a1a9", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291778805700}, "additional": {"logType": "detail", "children": [], "durationId": "23e0a242-448e-4be3-9e39-312f128561da"}}, {"head": {"id": "deabb62e-cf0c-4f01-8dd5-fddc2e425150", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291779442500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93fdf242-8bb1-47e8-9188-df3de7217e76", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291779547700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a342380f-65f0-49a6-ade9-c01e9f782273", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291780298000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb335b1f-1662-4f15-bdcb-fbc22cd229e9", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291781792000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26db20af-b024-4c14-9564-34ca92b38be0", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291781885800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6082eef6-d0af-4388-a831-87a6370783c9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291781940700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eec4a18b-3c8a-4150-8b7e-4bf7c78ed9ec", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291781967600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6cbbfa8-2474-49eb-8bd3-b739921fadac", "name": "entry : default@PreviewUpdateAssets cost memory 0.1458740234375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291783714400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b65949e7-42f6-4aab-ba17-a90aba928bda", "name": "runTaskFromQueue task cost before running: 404 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291783845100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45ca0d6f-7035-40e2-9af7-fdd5ee7b805a", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291780289600, "endTime": 29291783892000, "totalTime": 3530600}, "additional": {"logType": "info", "children": [], "durationId": "23e0a242-448e-4be3-9e39-312f128561da"}}, {"head": {"id": "50e2af6d-95a5-4ff2-af14-497f9ea522df", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291792778400, "endTime": 29303923040300}, "additional": {"children": ["e49ecafb-ac65-40ef-917a-b20c409735b1"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7e75ca1d-3c0b-4ad3-a581-8da8a86ecbf6", "logId": "3e3f1e09-4098-43c7-93ec-f69d197c0ff2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e75ca1d-3c0b-4ad3-a581-8da8a86ecbf6", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291785890300}, "additional": {"logType": "detail", "children": [], "durationId": "50e2af6d-95a5-4ff2-af14-497f9ea522df"}}, {"head": {"id": "38f0d765-c852-462d-8920-48841f10353f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291786362100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ffd3730-4f72-44fb-b8b9-e658e2b1228b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291786453100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53082a4b-e788-45dd-915c-70a01f20d78c", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291792788900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e49ecafb-ac65-40ef-917a-b20c409735b1", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker8", "startTime": 29291810956400, "endTime": 29303922637300}, "additional": {"children": ["ddaece53-3de6-4cc4-94c1-4fd1167b4874", "64bbdf2a-a4ad-43e9-b542-e6d8caaac6d2", "be3d4ce5-9276-44d4-9ab3-ccb77da98976", "dd4a1b36-cc96-4d65-a662-cb51c08f0576", "1d8982ec-7a8a-4902-951b-e9c661351e14", "c25f6aa9-9fc3-4bc9-a194-543de81b08e6", "2fd337bf-6ac0-45e9-9481-7d1fedac3f54", "5c1053f7-e769-48e7-8719-98d8cc1668eb", "68cdc719-0d11-4e07-aff4-441f9a44d360"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "50e2af6d-95a5-4ff2-af14-497f9ea522df", "logId": "e7e4ad4e-c0b2-4562-9b04-b18c24dcfbee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a09f0be7-605a-4e48-b2a2-07ebb113ce12", "name": "entry : default@PreviewArkTS cost memory -0.724700927734375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291812931200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b496d836-8c70-4be8-a499-bba30393d65a", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29295772003000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddaece53-3de6-4cc4-94c1-4fd1167b4874", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29295773174900, "endTime": 29295773190200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e49ecafb-ac65-40ef-917a-b20c409735b1", "logId": "8bb25dc3-ccf1-4a8d-b9a8-6d9fa1f3c1c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8bb25dc3-ccf1-4a8d-b9a8-6d9fa1f3c1c6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29295773174900, "endTime": 29295773190200}, "additional": {"logType": "info", "children": [], "durationId": "ddaece53-3de6-4cc4-94c1-4fd1167b4874", "parent": "e7e4ad4e-c0b2-4562-9b04-b18c24dcfbee"}}, {"head": {"id": "2ca94678-f8e5-434e-871e-c3295753595d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29301197273300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64bbdf2a-a4ad-43e9-b542-e6d8caaac6d2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29301198218700, "endTime": 29301198235400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e49ecafb-ac65-40ef-917a-b20c409735b1", "logId": "89a78827-edc8-4e3e-91e0-b4a9c21183d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "89a78827-edc8-4e3e-91e0-b4a9c21183d1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29301198218700, "endTime": 29301198235400}, "additional": {"logType": "info", "children": [], "durationId": "64bbdf2a-a4ad-43e9-b542-e6d8caaac6d2", "parent": "e7e4ad4e-c0b2-4562-9b04-b18c24dcfbee"}}, {"head": {"id": "8f8ebdbc-0a55-4f82-993a-a1bbda9b1b5f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29301198399900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be3d4ce5-9276-44d4-9ab3-ccb77da98976", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29301199693300, "endTime": 29301199725000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e49ecafb-ac65-40ef-917a-b20c409735b1", "logId": "fde23d99-acbf-45b8-83e6-22f5d877dfe5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fde23d99-acbf-45b8-83e6-22f5d877dfe5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29301199693300, "endTime": 29301199725000}, "additional": {"logType": "info", "children": [], "durationId": "be3d4ce5-9276-44d4-9ab3-ccb77da98976", "parent": "e7e4ad4e-c0b2-4562-9b04-b18c24dcfbee"}}, {"head": {"id": "8ee3b8bf-104e-4456-810a-762b84d9415d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29301199884200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd4a1b36-cc96-4d65-a662-cb51c08f0576", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29301202729600, "endTime": 29301202755000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e49ecafb-ac65-40ef-917a-b20c409735b1", "logId": "3fad0840-677e-400d-9f63-9816c2b2c145"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3fad0840-677e-400d-9f63-9816c2b2c145", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29301202729600, "endTime": 29301202755000}, "additional": {"logType": "info", "children": [], "durationId": "dd4a1b36-cc96-4d65-a662-cb51c08f0576", "parent": "e7e4ad4e-c0b2-4562-9b04-b18c24dcfbee"}}, {"head": {"id": "159c9892-b188-4d30-8c4d-732bee01ab20", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29301202904800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d8982ec-7a8a-4902-951b-e9c661351e14", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29301204434400, "endTime": 29301204465200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e49ecafb-ac65-40ef-917a-b20c409735b1", "logId": "a65dc7a8-4305-4288-a3d6-f8af480e7589"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a65dc7a8-4305-4288-a3d6-f8af480e7589", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29301204434400, "endTime": 29301204465200}, "additional": {"logType": "info", "children": [], "durationId": "1d8982ec-7a8a-4902-951b-e9c661351e14", "parent": "e7e4ad4e-c0b2-4562-9b04-b18c24dcfbee"}}, {"head": {"id": "f14f5456-e481-4e01-a4d9-3b7bdc701605", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29301204607800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c25f6aa9-9fc3-4bc9-a194-543de81b08e6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29301207327500, "endTime": 29301207452100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e49ecafb-ac65-40ef-917a-b20c409735b1", "logId": "9163f8e7-c586-44f7-9e8c-a252226a9fa5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9163f8e7-c586-44f7-9e8c-a252226a9fa5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29301207327500, "endTime": 29301207452100}, "additional": {"logType": "info", "children": [], "durationId": "c25f6aa9-9fc3-4bc9-a194-543de81b08e6", "parent": "e7e4ad4e-c0b2-4562-9b04-b18c24dcfbee"}}, {"head": {"id": "508b577c-23a5-485a-b078-a0c61bfcfdb5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29301207669700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fd337bf-6ac0-45e9-9481-7d1fedac3f54", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29301210275600, "endTime": 29301210345500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e49ecafb-ac65-40ef-917a-b20c409735b1", "logId": "3628d253-7cbe-44c0-85b4-b5b033a96a99"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3628d253-7cbe-44c0-85b4-b5b033a96a99", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29301210275600, "endTime": 29301210345500}, "additional": {"logType": "info", "children": [], "durationId": "2fd337bf-6ac0-45e9-9481-7d1fedac3f54", "parent": "e7e4ad4e-c0b2-4562-9b04-b18c24dcfbee"}}, {"head": {"id": "5fd250fd-d22c-4c1e-a76c-ac032f83548a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29301786588800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c1053f7-e769-48e7-8719-98d8cc1668eb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29301787531300, "endTime": 29301787546300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e49ecafb-ac65-40ef-917a-b20c409735b1", "logId": "012a6754-aa6e-4eae-b70e-f0392f0b509a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "012a6754-aa6e-4eae-b70e-f0392f0b509a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29301787531300, "endTime": 29301787546300}, "additional": {"logType": "info", "children": [], "durationId": "5c1053f7-e769-48e7-8719-98d8cc1668eb", "parent": "e7e4ad4e-c0b2-4562-9b04-b18c24dcfbee"}}, {"head": {"id": "00dbb4f0-dee3-44c8-a74d-ee14ceeace55", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303919471100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68cdc719-0d11-4e07-aff4-441f9a44d360", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303921732800, "endTime": 29303921750700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e49ecafb-ac65-40ef-917a-b20c409735b1", "logId": "44b99e4a-f0e6-479c-98a4-4ea37fa34e19"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44b99e4a-f0e6-479c-98a4-4ea37fa34e19", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303921732800, "endTime": 29303921750700}, "additional": {"logType": "info", "children": [], "durationId": "68cdc719-0d11-4e07-aff4-441f9a44d360", "parent": "e7e4ad4e-c0b2-4562-9b04-b18c24dcfbee"}}, {"head": {"id": "e7e4ad4e-c0b2-4562-9b04-b18c24dcfbee", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Worker8", "startTime": 29291810956400, "endTime": 29303922637300}, "additional": {"logType": "error", "children": ["8bb25dc3-ccf1-4a8d-b9a8-6d9fa1f3c1c6", "89a78827-edc8-4e3e-91e0-b4a9c21183d1", "fde23d99-acbf-45b8-83e6-22f5d877dfe5", "3fad0840-677e-400d-9f63-9816c2b2c145", "a65dc7a8-4305-4288-a3d6-f8af480e7589", "9163f8e7-c586-44f7-9e8c-a252226a9fa5", "3628d253-7cbe-44c0-85b4-b5b033a96a99", "012a6754-aa6e-4eae-b70e-f0392f0b509a", "44b99e4a-f0e6-479c-98a4-4ea37fa34e19"], "durationId": "e49ecafb-ac65-40ef-917a-b20c409735b1", "parent": "3e3f1e09-4098-43c7-93ec-f69d197c0ff2"}}, {"head": {"id": "4d44a0c8-ead6-4a4e-87ec-62408a1a3d0c", "name": "default@PreviewArkTS watch work[8] failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303922771600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e3f1e09-4098-43c7-93ec-f69d197c0ff2", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291792778400, "endTime": 29303923040300}, "additional": {"logType": "error", "children": ["e7e4ad4e-c0b2-4562-9b04-b18c24dcfbee"], "durationId": "50e2af6d-95a5-4ff2-af14-497f9ea522df"}}, {"head": {"id": "59c4301f-5db7-4696-962c-93000034c3d5", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303923213800}, "additional": {"logType": "debug", "children": [], "durationId": "50e2af6d-95a5-4ff2-af14-497f9ea522df"}}, {"head": {"id": "79bfc4d0-6db2-4157-96ba-8b78cf72eff1", "name": "ERROR: stacktrace = Error: \u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:709:46\n Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/PaymentPage.ets:521:35\n Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown)\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/PaymentPage.ets:521:76\n Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown)\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:53:25\n Cannot find name 'WalletInfo'.\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:710:9\n Type '{ userId: number; toPhone: string; amount: number; payPassword: string; description: string; }' is not assignable to type 'WalletTransferRequest'.\n  Object literal may only specify known properties, and 'userId' does not exist in type 'WalletTransferRequest'.\n\u001b[31mArkTS:ERROR File: D:/vue/daxiangmuwallet/harmony/entry/src/main/ets/pages/WalletOperationPage.ets:165:7\n Only UI component syntax can be written in build method.\n    at handleResponse (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303924954300}, "additional": {"logType": "debug", "children": [], "durationId": "50e2af6d-95a5-4ff2-af14-497f9ea522df"}}, {"head": {"id": "b9ada60a-dd76-4341-a981-0cb73b8ca390", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303937702100, "endTime": 29303937803900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "25f71538-d104-4d2c-bd8e-6d3a186eb2df", "logId": "025f4949-c660-4e22-af8d-384661f38549"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "025f4949-c660-4e22-af8d-384661f38549", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303937702100, "endTime": 29303937803900}, "additional": {"logType": "info", "children": [], "durationId": "b9ada60a-dd76-4341-a981-0cb73b8ca390"}}, {"head": {"id": "8772aa57-53a8-4e4d-b46e-e4050a7e6ba9", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29291380534300, "endTime": 29303938022700}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 14}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "a80aa492-9eda-4f53-a5fe-f96d39953f90", "name": "BUILD FAILED in 12 s 558 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303938091100}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "681881fa-d24b-437a-82e3-ed5c5dd66321", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303938378000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aba122bd-1e41-474f-bdf0-a585c0183300", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303938488900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43dc4ce2-bff2-4a29-a382-00f6653fdda1", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303938535600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7f9cb13-f21b-45fc-bae3-16b17b3632db", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303938577300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afaac019-9ecc-4dc5-a662-bc99b1ca17dd", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303938613700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f18ad86a-1f5a-4895-bdfd-f347cb6270df", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303938649000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94eb3048-bcf1-4b17-8b2a-e874e674534b", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303938684800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6e9b297-845d-498f-ba06-f4160fd44b9f", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303941074800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4165d049-1f58-409a-80ac-db6cfab2fd98", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303948427200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "037763f9-ea7f-4215-a4b7-0bd1d93988aa", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303948733000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9177ba0c-fade-499e-93eb-166a3d490980", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303956271600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60094503-718a-4dbd-bfb4-9c6d95b4264b", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:19 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303957143200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a092c19b-c6ba-4913-93a9-059efa64bc5b", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303957357600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "287ddf10-5eff-4d7f-a2e6-739c4b0653cf", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303958062600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c212c0bd-a71a-47e4-b5ed-ed34e1b02fe3", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303958721400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67903b19-f7d7-4218-8696-b65a00c753e8", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303959106600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9005950c-0e5e-47bf-be9c-5a60417fcb75", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303959423500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a64aea9d-650b-4ceb-bdf3-d26ddf0a6514", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303959696700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ab15f53-6f45-412a-b444-94b245fec254", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303961946500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e669877-9d31-475d-bd59-1955b993fb50", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303962552800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81d11dcc-4b7a-4ed4-8958-92b5575e18ce", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303962782500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b55f49d-3815-40a6-baaf-1ef522030332", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303963003900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53e5de5c-b28c-4f60-ab6c-4afe0af9f82d", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303963558200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17727fe9-dd5f-4cf8-ae87-d2ee6c1368dc", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303971093600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae866142-e927-4b15-9c5a-488a9a19d987", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303971335200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5775e082-f5ec-4cf1-9fff-bd66d023a48c", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303971537100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9e025b1-38c4-43b1-9399-06361d83fd29", "name": "Update task entry:default@PreviewArkTS output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303971760600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "962a08b9-b933-4ae0-bfe4-9b84698c5f95", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:13 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29303971966400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}