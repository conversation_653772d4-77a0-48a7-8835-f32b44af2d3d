import preferences from "@ohos:data.preferences";
import type Context from "@ohos:app.ability.common";
// 类型定义已移至 common/types/index.ets
// 保留本地接口定义以兼容现有代码
export interface LocalUserInfo {
    userId: number;
    phone: string;
    realName: string;
    idCard: string;
    walletNo: string;
    balance: number;
    payLimit: number;
    status: number;
    createTime: string;
    updateTime: string;
}
export interface LocalWalletInfo {
    walletNo: string;
    balance: number;
    status: number;
}
/**
 * 存储键名接口
 */
interface StorageKeys {
    USER_TOKEN: string;
    USER_INFO: string;
    WALLET_INFO: string;
    LOGIN_PHONE: string;
}
/**
 * 本地存储管理器
 * 用于管理用户token、用户信息等本地数据
 */
export class StorageManager {
    private static instance: StorageManager;
    private preferences: preferences.Preferences | null = null;
    private readonly STORE_NAME = 'ewallet_storage';
    // 存储键名常量
    private static readonly KEYS: StorageKeys = {
        USER_TOKEN: 'user_token',
        USER_INFO: 'user_info',
        WALLET_INFO: 'wallet_info',
        LOGIN_PHONE: 'login_phone'
    };
    private constructor() { }
    public static getInstance(): StorageManager {
        if (!StorageManager.instance) {
            StorageManager.instance = new StorageManager();
        }
        return StorageManager.instance;
    }
    /**
     * 初始化存储
     */
    public async init(context: Context) {
        // 如果已经初始化过，直接返回
        if (this.preferences) {
            console.log('StorageManager 已经初始化过了');
            return;
        }
        try {
            this.preferences = await preferences.getPreferences(context, this.STORE_NAME);
            console.log('StorageManager 初始化成功');
        }
        catch (error) {
            console.error('初始化存储失败:', error);
            throw new Error('初始化存储失败');
        }
    }
    /**
     * 保存用户Token
     */
    public async saveUserToken(token: string) {
        if (!this.preferences) {
            throw new Error('存储未初始化');
        }
        try {
            await this.preferences.put(StorageManager.KEYS.USER_TOKEN, token);
            await this.preferences.flush();
        }
        catch (error) {
            console.error('保存用户Token失败:', error);
            throw new Error('保存用户Token失败');
        }
    }
    /**
     * 获取用户Token
     */
    public async getUserToken(): Promise<string | null> {
        if (!this.preferences) {
            throw new Error('存储未初始化');
        }
        try {
            const token = await this.preferences.get(StorageManager.KEYS.USER_TOKEN, '');
            return token as string || null;
        }
        catch (error) {
            console.error('获取用户Token失败:', error);
            return null;
        }
    }
    /**
     * 保存用户信息
     */
    public async saveUserInfo(userInfo: LocalUserInfo) {
        if (!this.preferences) {
            throw new Error('存储未初始化');
        }
        try {
            await this.preferences.put(StorageManager.KEYS.USER_INFO, JSON.stringify(userInfo));
            await this.preferences.flush();
        }
        catch (error) {
            console.error('保存用户信息失败:', error);
            throw new Error('保存用户信息失败');
        }
    }
    /**
     * 获取用户信息
     */
    public async getUserInfo(): Promise<LocalUserInfo | null> {
        if (!this.preferences) {
            throw new Error('存储未初始化');
        }
        try {
            const userInfoStr = await this.preferences.get(StorageManager.KEYS.USER_INFO, '');
            if (userInfoStr) {
                return JSON.parse(userInfoStr as string) as LocalUserInfo;
            }
            return null;
        }
        catch (error) {
            console.error('获取用户信息失败:', error);
            return null;
        }
    }
    /**
     * 保存钱包信息
     */
    public async saveWalletInfo(walletInfo: LocalWalletInfo) {
        if (!this.preferences) {
            throw new Error('存储未初始化');
        }
        try {
            await this.preferences.put(StorageManager.KEYS.WALLET_INFO, JSON.stringify(walletInfo));
            await this.preferences.flush();
        }
        catch (error) {
            console.error('保存钱包信息失败:', error);
            throw new Error('保存钱包信息失败');
        }
    }
    /**
     * 获取钱包信息
     */
    public async getWalletInfo(): Promise<LocalWalletInfo | null> {
        if (!this.preferences) {
            throw new Error('存储未初始化');
        }
        try {
            const walletInfoStr = await this.preferences.get(StorageManager.KEYS.WALLET_INFO, '');
            if (walletInfoStr) {
                return JSON.parse(walletInfoStr as string) as LocalWalletInfo;
            }
            return null;
        }
        catch (error) {
            console.error('获取钱包信息失败:', error);
            return null;
        }
    }
    /**
     * 保存登录手机号（记住登录状态）
     */
    public async saveLoginPhone(phone: string) {
        if (!this.preferences) {
            throw new Error('存储未初始化');
        }
        try {
            await this.preferences.put(StorageManager.KEYS.LOGIN_PHONE, phone);
            await this.preferences.flush();
        }
        catch (error) {
            console.error('保存登录手机号失败:', error);
            throw new Error('保存登录手机号失败');
        }
    }
    /**
     * 获取登录手机号
     */
    public async getLoginPhone(): Promise<string | null> {
        if (!this.preferences) {
            throw new Error('存储未初始化');
        }
        try {
            const phone = await this.preferences.get(StorageManager.KEYS.LOGIN_PHONE, '');
            return phone as string || null;
        }
        catch (error) {
            console.error('获取登录手机号失败:', error);
            return null;
        }
    }
    /**
     * 清除所有用户数据（退出登录）
     */
    public async clearUserData() {
        if (!this.preferences) {
            throw new Error('存储未初始化');
        }
        try {
            await this.preferences.delete(StorageManager.KEYS.USER_TOKEN);
            await this.preferences.delete(StorageManager.KEYS.USER_INFO);
            await this.preferences.delete(StorageManager.KEYS.WALLET_INFO);
            await this.preferences.flush();
        }
        catch (error) {
            console.error('清除用户数据失败:', error);
            throw new Error('清除用户数据失败');
        }
    }
    /**
     * 检查是否已登录
     */
    public async isLoggedIn(): Promise<boolean> {
        const token = await this.getUserToken();
        return token !== null && token.length > 0;
    }
}
/**
 * 导出单例实例
 */
export const storageManager = StorageManager.getInstance();
