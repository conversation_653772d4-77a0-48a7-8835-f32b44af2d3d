{"version": "2.0", "ppid": 21780, "events": [{"head": {"id": "de3b72f2-ad08-4067-902c-94a73fb9fa23", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29046154138100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fea5fc4d-666a-42f3-9047-ac32d91e30dd", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29046160842900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "292fc325-ff49-4c27-a6f5-71cdf6c04eda", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29046161935900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b699037c-132d-4b13-8d43-bc70b0820548", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251285315800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b39c34a3-a750-43c9-9f88-37152a0a7bd5", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251295298900, "endTime": 29251427843700}, "additional": {"children": ["f83e26c2-4c2f-49ed-8a69-cf541d151967", "ebeac01c-b69d-4dee-b82e-b2c8a2dfffd7", "54f6b672-7a85-424d-9c3b-8df4e5ea0a98", "58849170-6c12-448f-98a6-ad35b67ad628", "7c8d716e-c3d0-4ba1-8363-fac2c54a27f1", "b8afb55a-5398-4842-9532-8b8ac2cdb8e5", "40426141-e113-45b8-98b8-cfccc43032ab"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "4e3811a5-0596-4243-ab34-3f71afe7c760"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f83e26c2-4c2f-49ed-8a69-cf541d151967", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251295300500, "endTime": 29251307872700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b39c34a3-a750-43c9-9f88-37152a0a7bd5", "logId": "1d441096-fd44-4491-a604-6e5df4f0114f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ebeac01c-b69d-4dee-b82e-b2c8a2dfffd7", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251307883200, "endTime": 29251426826100}, "additional": {"children": ["1330340b-49be-4ecc-b954-ad32f63f8575", "00530a27-abae-4d0f-97c9-fc44d4635c9b", "63b23400-fa26-42b0-8feb-85ed7f032052", "467d4d55-403f-43ce-9399-4b469f82e5db", "5302de14-9360-4a88-b041-668bab65abcc", "b438c05d-2477-4307-b55b-63f4988fd3ed", "ee715174-412a-42c2-833e-cf29d68f55c9", "8f8f78dc-0fdb-4d79-98fb-46e646b37328", "16d5c266-aeda-4aa7-a684-63cc492b5b37"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b39c34a3-a750-43c9-9f88-37152a0a7bd5", "logId": "266a4751-f8e0-4fe4-b000-318b695c39b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54f6b672-7a85-424d-9c3b-8df4e5ea0a98", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251426838400, "endTime": 29251427834800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b39c34a3-a750-43c9-9f88-37152a0a7bd5", "logId": "0804eca4-3203-42c1-b614-21b4cc053535"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58849170-6c12-448f-98a6-ad35b67ad628", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251427841200, "endTime": 29251427842100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b39c34a3-a750-43c9-9f88-37152a0a7bd5", "logId": "de3eb248-3ec6-45fb-8a92-7e6a4f6cc6c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c8d716e-c3d0-4ba1-8363-fac2c54a27f1", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251298807600, "endTime": 29251298963500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b39c34a3-a750-43c9-9f88-37152a0a7bd5", "logId": "79b878fa-f1c1-4aec-a4ab-ce7f8e1eb7fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "79b878fa-f1c1-4aec-a4ab-ce7f8e1eb7fd", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251298807600, "endTime": 29251298963500}, "additional": {"logType": "info", "children": [], "durationId": "7c8d716e-c3d0-4ba1-8363-fac2c54a27f1", "parent": "4e3811a5-0596-4243-ab34-3f71afe7c760"}}, {"head": {"id": "b8afb55a-5398-4842-9532-8b8ac2cdb8e5", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251302764800, "endTime": 29251302783800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b39c34a3-a750-43c9-9f88-37152a0a7bd5", "logId": "967a7d5a-2e47-456f-b3e3-f8bc51de0851"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "967a7d5a-2e47-456f-b3e3-f8bc51de0851", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251302764800, "endTime": 29251302783800}, "additional": {"logType": "info", "children": [], "durationId": "b8afb55a-5398-4842-9532-8b8ac2cdb8e5", "parent": "4e3811a5-0596-4243-ab34-3f71afe7c760"}}, {"head": {"id": "90a13bdd-3186-4afe-92ea-55cb5f42aaa4", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251302817100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3268844-936d-448c-bfda-42fcb948febf", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251307777100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d441096-fd44-4491-a604-6e5df4f0114f", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251295300500, "endTime": 29251307872700}, "additional": {"logType": "info", "children": [], "durationId": "f83e26c2-4c2f-49ed-8a69-cf541d151967", "parent": "4e3811a5-0596-4243-ab34-3f71afe7c760"}}, {"head": {"id": "1330340b-49be-4ecc-b954-ad32f63f8575", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251312118400, "endTime": 29251312124300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ebeac01c-b69d-4dee-b82e-b2c8a2dfffd7", "logId": "9ed03559-0029-47b2-b858-79645122f50e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "00530a27-abae-4d0f-97c9-fc44d4635c9b", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251312133200, "endTime": 29251315738400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ebeac01c-b69d-4dee-b82e-b2c8a2dfffd7", "logId": "e3cf4936-bbca-4ce8-bd3a-e5c6b5a9f655"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "63b23400-fa26-42b0-8feb-85ed7f032052", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251315747500, "endTime": 29251377299800}, "additional": {"children": ["b1156829-c48b-4e42-a7e6-223a028b4b6b", "aff54523-fe59-45b4-bfda-ce026292d822", "29894e47-de66-4bf5-a468-e14f5775c592"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ebeac01c-b69d-4dee-b82e-b2c8a2dfffd7", "logId": "5676bc32-83cc-49c8-a3c0-192a6e3494b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "467d4d55-403f-43ce-9399-4b469f82e5db", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251377314000, "endTime": 29251395945100}, "additional": {"children": ["96328467-792f-49ef-9213-636f2f52e0cf"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ebeac01c-b69d-4dee-b82e-b2c8a2dfffd7", "logId": "a65602a4-c1fe-4446-80c5-b742b8448b98"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5302de14-9360-4a88-b041-668bab65abcc", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251395950800, "endTime": 29251407486600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ebeac01c-b69d-4dee-b82e-b2c8a2dfffd7", "logId": "b17f5c15-d7dd-488d-867e-d54fb21f9960"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b438c05d-2477-4307-b55b-63f4988fd3ed", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251408341500, "endTime": 29251416139000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ebeac01c-b69d-4dee-b82e-b2c8a2dfffd7", "logId": "1080842a-3b32-42e5-8353-c7b814472c20"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee715174-412a-42c2-833e-cf29d68f55c9", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251416153700, "endTime": 29251426674000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ebeac01c-b69d-4dee-b82e-b2c8a2dfffd7", "logId": "b973296d-aeb2-4e51-92bc-80628dfd3d5a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f8f78dc-0fdb-4d79-98fb-46e646b37328", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251426693900, "endTime": 29251426818100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ebeac01c-b69d-4dee-b82e-b2c8a2dfffd7", "logId": "c172b0de-43a0-41fc-8235-7a7010ba5976"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ed03559-0029-47b2-b858-79645122f50e", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251312118400, "endTime": 29251312124300}, "additional": {"logType": "info", "children": [], "durationId": "1330340b-49be-4ecc-b954-ad32f63f8575", "parent": "266a4751-f8e0-4fe4-b000-318b695c39b6"}}, {"head": {"id": "e3cf4936-bbca-4ce8-bd3a-e5c6b5a9f655", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251312133200, "endTime": 29251315738400}, "additional": {"logType": "info", "children": [], "durationId": "00530a27-abae-4d0f-97c9-fc44d4635c9b", "parent": "266a4751-f8e0-4fe4-b000-318b695c39b6"}}, {"head": {"id": "b1156829-c48b-4e42-a7e6-223a028b4b6b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251316263800, "endTime": 29251316280200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "63b23400-fa26-42b0-8feb-85ed7f032052", "logId": "968e41ee-c6a2-4a83-a960-8482f77a189e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "968e41ee-c6a2-4a83-a960-8482f77a189e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251316263800, "endTime": 29251316280200}, "additional": {"logType": "info", "children": [], "durationId": "b1156829-c48b-4e42-a7e6-223a028b4b6b", "parent": "5676bc32-83cc-49c8-a3c0-192a6e3494b7"}}, {"head": {"id": "aff54523-fe59-45b4-bfda-ce026292d822", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251317819400, "endTime": 29251376534400}, "additional": {"children": ["45eb7f8b-9478-491d-a57a-0a44c5f1ba94", "13990488-3fd1-4205-94f4-055974aa60a9"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "63b23400-fa26-42b0-8feb-85ed7f032052", "logId": "d2d7fb4a-b3ce-43ef-91f8-34b689fef7ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "45eb7f8b-9478-491d-a57a-0a44c5f1ba94", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251317820100, "endTime": 29251320645000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aff54523-fe59-45b4-bfda-ce026292d822", "logId": "e96d33bf-a773-4112-b4d6-494dfbf47ea5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13990488-3fd1-4205-94f4-055974aa60a9", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251320654600, "endTime": 29251376528400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aff54523-fe59-45b4-bfda-ce026292d822", "logId": "a6ef573f-edd2-4602-8ce2-a47e7a4ce19b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bddb7864-3332-4340-98c5-b2f9168174a8", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251317824300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49357dc5-56ea-4a43-b9a5-cc4117ac2eb9", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251320540600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e96d33bf-a773-4112-b4d6-494dfbf47ea5", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251317820100, "endTime": 29251320645000}, "additional": {"logType": "info", "children": [], "durationId": "45eb7f8b-9478-491d-a57a-0a44c5f1ba94", "parent": "d2d7fb4a-b3ce-43ef-91f8-34b689fef7ac"}}, {"head": {"id": "66a1cb1d-236d-4606-a63e-33ba003e66cf", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251320668600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47aa5862-2734-4793-86d8-6834495f6c1c", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251326596600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0eaf70be-c0e8-46d5-8e8a-0b04af2f09cd", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251326685800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ca32a4e-c1b8-4f84-a9f2-505288405111", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251326769000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cce91982-77c6-48bf-88da-6b7ef3c25753", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251326827700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39a4c140-46b3-4fa3-9897-d27f17c8005e", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251328041300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d257e2d-f157-4053-8597-2ab8d760842b", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251332164200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37168fcb-58f8-4b40-bd4f-50af7c7b1b8d", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251340798600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "712afd34-0feb-4896-acd1-68e26e457fdd", "name": "Sdk init in 26 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251359164100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95174c39-143f-459c-9e64-8597bcc96305", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251359277800}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 13}, "markType": "other"}}, {"head": {"id": "e81b75e4-e011-4cd8-b937-47856b54f4b7", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251359290100}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 13}, "markType": "other"}}, {"head": {"id": "e7bedfef-556c-4168-be95-574b19e50569", "name": "Project task initialization takes 16 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251376337500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15747eaf-92ca-4331-ac9e-c12e2c19b710", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251376440300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea5f33d7-6c2f-49a5-9191-cbb8e862c7eb", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251376474900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83e396a1-d886-4df3-ba25-9d8a870aa0d2", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251376504000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6ef573f-edd2-4602-8ce2-a47e7a4ce19b", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251320654600, "endTime": 29251376528400}, "additional": {"logType": "info", "children": [], "durationId": "13990488-3fd1-4205-94f4-055974aa60a9", "parent": "d2d7fb4a-b3ce-43ef-91f8-34b689fef7ac"}}, {"head": {"id": "d2d7fb4a-b3ce-43ef-91f8-34b689fef7ac", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251317819400, "endTime": 29251376534400}, "additional": {"logType": "info", "children": ["e96d33bf-a773-4112-b4d6-494dfbf47ea5", "a6ef573f-edd2-4602-8ce2-a47e7a4ce19b"], "durationId": "aff54523-fe59-45b4-bfda-ce026292d822", "parent": "5676bc32-83cc-49c8-a3c0-192a6e3494b7"}}, {"head": {"id": "29894e47-de66-4bf5-a468-e14f5775c592", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251377170500, "endTime": 29251377184700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "63b23400-fa26-42b0-8feb-85ed7f032052", "logId": "235fc6bc-3db0-4846-afa5-4ce9a5d8c4a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "235fc6bc-3db0-4846-afa5-4ce9a5d8c4a5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251377170500, "endTime": 29251377184700}, "additional": {"logType": "info", "children": [], "durationId": "29894e47-de66-4bf5-a468-e14f5775c592", "parent": "5676bc32-83cc-49c8-a3c0-192a6e3494b7"}}, {"head": {"id": "5676bc32-83cc-49c8-a3c0-192a6e3494b7", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251315747500, "endTime": 29251377299800}, "additional": {"logType": "info", "children": ["968e41ee-c6a2-4a83-a960-8482f77a189e", "d2d7fb4a-b3ce-43ef-91f8-34b689fef7ac", "235fc6bc-3db0-4846-afa5-4ce9a5d8c4a5"], "durationId": "63b23400-fa26-42b0-8feb-85ed7f032052", "parent": "266a4751-f8e0-4fe4-b000-318b695c39b6"}}, {"head": {"id": "96328467-792f-49ef-9213-636f2f52e0cf", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251377860400, "endTime": 29251395930400}, "additional": {"children": ["aa89dfc8-fe8c-4de6-8b5a-7fbc694eec68", "0e0c8470-d6e3-4375-add1-f5214627c3ad", "1513c156-570c-4454-aaed-707b637baf91"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "467d4d55-403f-43ce-9399-4b469f82e5db", "logId": "47865c11-86c4-4857-9e4c-d16b122f049a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa89dfc8-fe8c-4de6-8b5a-7fbc694eec68", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251381047200, "endTime": 29251381060400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "96328467-792f-49ef-9213-636f2f52e0cf", "logId": "18d6dff3-8b13-48f4-916b-b78ba515edd6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18d6dff3-8b13-48f4-916b-b78ba515edd6", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251381047200, "endTime": 29251381060400}, "additional": {"logType": "info", "children": [], "durationId": "aa89dfc8-fe8c-4de6-8b5a-7fbc694eec68", "parent": "47865c11-86c4-4857-9e4c-d16b122f049a"}}, {"head": {"id": "0e0c8470-d6e3-4375-add1-f5214627c3ad", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251382599400, "endTime": 29251394554400}, "additional": {"children": ["754fb38d-40c2-4541-9223-77f1fcc13d70", "fc7041a5-790b-424c-bf82-c84732578118"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "96328467-792f-49ef-9213-636f2f52e0cf", "logId": "97d716c4-3bbb-49bb-a481-eb79cd77ff58"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "754fb38d-40c2-4541-9223-77f1fcc13d70", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251382600100, "endTime": 29251385566700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0e0c8470-d6e3-4375-add1-f5214627c3ad", "logId": "b4353b1e-2365-4451-973a-4a18499772f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc7041a5-790b-424c-bf82-c84732578118", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251385575000, "endTime": 29251394546700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0e0c8470-d6e3-4375-add1-f5214627c3ad", "logId": "893ed4bf-c49a-4ec7-881c-4866c06a0ce5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78ae9e54-8991-48cc-90f4-48737f64046e", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251382604000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa006dd8-d3eb-49de-9305-9c94d0664a44", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251385476400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4353b1e-2365-4451-973a-4a18499772f0", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251382600100, "endTime": 29251385566700}, "additional": {"logType": "info", "children": [], "durationId": "754fb38d-40c2-4541-9223-77f1fcc13d70", "parent": "97d716c4-3bbb-49bb-a481-eb79cd77ff58"}}, {"head": {"id": "d5a3a675-796d-406b-b899-e80bba2a3af2", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251385585200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a900164e-b6d3-4d7d-bff8-e253a31dfa81", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251390851000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1597a48b-0bc5-48ce-ae3b-7db874c7659f", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251390950200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a5e3d3f-94c0-4b32-ac31-8e2af5ea466c", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251391078300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df511727-8077-4d48-a954-f8ba360954dd", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251391163400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "674cf3e7-edb1-4e5d-b82e-324aa70a10b1", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251391197100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9323617c-ca2e-4675-a431-053f41f18783", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251391223500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f53dc0f9-278b-44b3-8094-bb6517f4e456", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251391249400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77eb2e6e-a476-4c79-a454-9eb84a1f3ac2", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251394326100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bee82f94-13f2-49cb-a98a-86d65ef0e789", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251394455800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6991b7e4-5e1c-4a4a-b71a-cc9842c0e394", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251394494700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3aaa5cec-4f5d-4efa-aa8a-b6ee1347372c", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251394523500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "893ed4bf-c49a-4ec7-881c-4866c06a0ce5", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251385575000, "endTime": 29251394546700}, "additional": {"logType": "info", "children": [], "durationId": "fc7041a5-790b-424c-bf82-c84732578118", "parent": "97d716c4-3bbb-49bb-a481-eb79cd77ff58"}}, {"head": {"id": "97d716c4-3bbb-49bb-a481-eb79cd77ff58", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251382599400, "endTime": 29251394554400}, "additional": {"logType": "info", "children": ["b4353b1e-2365-4451-973a-4a18499772f0", "893ed4bf-c49a-4ec7-881c-4866c06a0ce5"], "durationId": "0e0c8470-d6e3-4375-add1-f5214627c3ad", "parent": "47865c11-86c4-4857-9e4c-d16b122f049a"}}, {"head": {"id": "1513c156-570c-4454-aaed-707b637baf91", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251395893700, "endTime": 29251395918200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "96328467-792f-49ef-9213-636f2f52e0cf", "logId": "2aabdbeb-14d8-4a68-afd4-8ddb102f8e48"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2aabdbeb-14d8-4a68-afd4-8ddb102f8e48", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251395893700, "endTime": 29251395918200}, "additional": {"logType": "info", "children": [], "durationId": "1513c156-570c-4454-aaed-707b637baf91", "parent": "47865c11-86c4-4857-9e4c-d16b122f049a"}}, {"head": {"id": "47865c11-86c4-4857-9e4c-d16b122f049a", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251377860400, "endTime": 29251395930400}, "additional": {"logType": "info", "children": ["18d6dff3-8b13-48f4-916b-b78ba515edd6", "97d716c4-3bbb-49bb-a481-eb79cd77ff58", "2aabdbeb-14d8-4a68-afd4-8ddb102f8e48"], "durationId": "96328467-792f-49ef-9213-636f2f52e0cf", "parent": "a65602a4-c1fe-4446-80c5-b742b8448b98"}}, {"head": {"id": "a65602a4-c1fe-4446-80c5-b742b8448b98", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251377314000, "endTime": 29251395945100}, "additional": {"logType": "info", "children": ["47865c11-86c4-4857-9e4c-d16b122f049a"], "durationId": "467d4d55-403f-43ce-9399-4b469f82e5db", "parent": "266a4751-f8e0-4fe4-b000-318b695c39b6"}}, {"head": {"id": "959bafb9-e351-4a44-83b1-43d1211624f6", "name": "watch files: [\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\hvigorfile.ts',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251407089500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd25c5c9-4e9c-4220-96a0-641e59e45224", "name": "hvigorfile, resolve hvigorfile dependencies in 12 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251407431800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b17f5c15-d7dd-488d-867e-d54fb21f9960", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251395950800, "endTime": 29251407486600}, "additional": {"logType": "info", "children": [], "durationId": "5302de14-9360-4a88-b041-668bab65abcc", "parent": "266a4751-f8e0-4fe4-b000-318b695c39b6"}}, {"head": {"id": "16d5c266-aeda-4aa7-a684-63cc492b5b37", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251408190300, "endTime": 29251408335800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ebeac01c-b69d-4dee-b82e-b2c8a2dfffd7", "logId": "71d52e53-ad37-427b-b08f-89d7f181f8a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa300c77-b9fd-443b-836a-227bf651136d", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251408210200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71d52e53-ad37-427b-b08f-89d7f181f8a7", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251408190300, "endTime": 29251408335800}, "additional": {"logType": "info", "children": [], "durationId": "16d5c266-aeda-4aa7-a684-63cc492b5b37", "parent": "266a4751-f8e0-4fe4-b000-318b695c39b6"}}, {"head": {"id": "ff63c4d3-7f5c-4f46-bbec-91890a2dfd50", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251409736600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da886925-3443-46e5-bd58-66929c2554a3", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251415404800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1080842a-3b32-42e5-8353-c7b814472c20", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251408341500, "endTime": 29251416139000}, "additional": {"logType": "info", "children": [], "durationId": "b438c05d-2477-4307-b55b-63f4988fd3ed", "parent": "266a4751-f8e0-4fe4-b000-318b695c39b6"}}, {"head": {"id": "69ca6838-3b7f-4030-a2a3-eb90fe099dde", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251416164900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0750e18-f3c0-4606-a90d-41d38eb82aae", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251421007800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5982a82-37ae-4520-b1ee-3b08b5b593ab", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251421150700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03ee2f3e-e0bf-4cd8-b4eb-302d9d71091d", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251421436200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d37d8c93-bc83-41b5-afe6-0bfd6c0c9574", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251423484100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b6c129b-a5f8-4a78-a829-2d0bc47eecd7", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251423576800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b973296d-aeb2-4e51-92bc-80628dfd3d5a", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251416153700, "endTime": 29251426674000}, "additional": {"logType": "info", "children": [], "durationId": "ee715174-412a-42c2-833e-cf29d68f55c9", "parent": "266a4751-f8e0-4fe4-b000-318b695c39b6"}}, {"head": {"id": "d39db7e6-9a21-41f5-b891-d4ec06c711dd", "name": "Configuration phase cost:115 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251426727600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c172b0de-43a0-41fc-8235-7a7010ba5976", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251426693900, "endTime": 29251426818100}, "additional": {"logType": "info", "children": [], "durationId": "8f8f78dc-0fdb-4d79-98fb-46e646b37328", "parent": "266a4751-f8e0-4fe4-b000-318b695c39b6"}}, {"head": {"id": "266a4751-f8e0-4fe4-b000-318b695c39b6", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251307883200, "endTime": 29251426826100}, "additional": {"logType": "info", "children": ["9ed03559-0029-47b2-b858-79645122f50e", "e3cf4936-bbca-4ce8-bd3a-e5c6b5a9f655", "5676bc32-83cc-49c8-a3c0-192a6e3494b7", "a65602a4-c1fe-4446-80c5-b742b8448b98", "b17f5c15-d7dd-488d-867e-d54fb21f9960", "1080842a-3b32-42e5-8353-c7b814472c20", "b973296d-aeb2-4e51-92bc-80628dfd3d5a", "c172b0de-43a0-41fc-8235-7a7010ba5976", "71d52e53-ad37-427b-b08f-89d7f181f8a7"], "durationId": "ebeac01c-b69d-4dee-b82e-b2c8a2dfffd7", "parent": "4e3811a5-0596-4243-ab34-3f71afe7c760"}}, {"head": {"id": "40426141-e113-45b8-98b8-cfccc43032ab", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251427810100, "endTime": 29251427828100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b39c34a3-a750-43c9-9f88-37152a0a7bd5", "logId": "da5b8453-c244-4c88-8538-feaabee1c2f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da5b8453-c244-4c88-8538-feaabee1c2f7", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251427810100, "endTime": 29251427828100}, "additional": {"logType": "info", "children": [], "durationId": "40426141-e113-45b8-98b8-cfccc43032ab", "parent": "4e3811a5-0596-4243-ab34-3f71afe7c760"}}, {"head": {"id": "0804eca4-3203-42c1-b614-21b4cc053535", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251426838400, "endTime": 29251427834800}, "additional": {"logType": "info", "children": [], "durationId": "54f6b672-7a85-424d-9c3b-8df4e5ea0a98", "parent": "4e3811a5-0596-4243-ab34-3f71afe7c760"}}, {"head": {"id": "de3eb248-3ec6-45fb-8a92-7e6a4f6cc6c6", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251427841200, "endTime": 29251427842100}, "additional": {"logType": "info", "children": [], "durationId": "58849170-6c12-448f-98a6-ad35b67ad628", "parent": "4e3811a5-0596-4243-ab34-3f71afe7c760"}}, {"head": {"id": "4e3811a5-0596-4243-ab34-3f71afe7c760", "name": "init", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251295298900, "endTime": 29251427843700}, "additional": {"logType": "info", "children": ["1d441096-fd44-4491-a604-6e5df4f0114f", "266a4751-f8e0-4fe4-b000-318b695c39b6", "0804eca4-3203-42c1-b614-21b4cc053535", "de3eb248-3ec6-45fb-8a92-7e6a4f6cc6c6", "79b878fa-f1c1-4aec-a4ab-ce7f8e1eb7fd", "967a7d5a-2e47-456f-b3e3-f8bc51de0851", "da5b8453-c244-4c88-8538-feaabee1c2f7"], "durationId": "b39c34a3-a750-43c9-9f88-37152a0a7bd5"}}, {"head": {"id": "98b70dda-a5aa-41e6-baa6-73ac03da5fd0", "name": "Configuration task cost before running: 137 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251427963200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a1b9b2c-79b0-4555-bfc8-67b18a221164", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251433194900, "endTime": 29251440768600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "14f5a982-58b4-4421-95f1-8cbef066d9cb", "logId": "57b434b3-a324-4d23-8e3b-42c481021991"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14f5a982-58b4-4421-95f1-8cbef066d9cb", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251429247000}, "additional": {"logType": "detail", "children": [], "durationId": "4a1b9b2c-79b0-4555-bfc8-67b18a221164"}}, {"head": {"id": "12011931-250b-4460-8242-74528849f98a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251429888600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01113bc9-8d1f-4eb3-bbdd-d3cdfcc24006", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251429979500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63ff0ee0-8606-4ab6-8a51-682a1eeb1c94", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251433206000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "841693f4-8f9c-40d2-b10d-e7055ebf88e1", "name": "Incremental task entry:default@PreBuild pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251440538600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78626839-35d3-487f-ae39-527f7d77579b", "name": "entry : default@PreBuild cost memory 0.3710784912109375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251440692100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57b434b3-a324-4d23-8e3b-42c481021991", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251433194900, "endTime": 29251440768600}, "additional": {"logType": "info", "children": [], "durationId": "4a1b9b2c-79b0-4555-bfc8-67b18a221164"}}, {"head": {"id": "82263e9f-66d5-40b2-b5f6-6d2f2028aff5", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251445379300, "endTime": 29251447121900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "65f772bc-21ca-4b1e-945f-8257f36994a2", "logId": "81b91c5b-e331-4461-b432-e1f94b4b2cb0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65f772bc-21ca-4b1e-945f-8257f36994a2", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251444086200}, "additional": {"logType": "detail", "children": [], "durationId": "82263e9f-66d5-40b2-b5f6-6d2f2028aff5"}}, {"head": {"id": "c1e4f797-9844-44f1-bee7-f548725eee9a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251444537100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f9b2c91-ec5d-4637-a90c-f88b9b46947b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251444611200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4fc8e30-53b1-4789-add2-fdd178a04b40", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251445386500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d68e5e7-202a-4fea-ad85-0d9c00aa8543", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251446989600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85f7a57a-13d4-4fbe-83d3-f0a6ffd15a7d", "name": "entry : default@MergeProfile cost memory 0.11283111572265625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251447077900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81b91c5b-e331-4461-b432-e1f94b4b2cb0", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251445379300, "endTime": 29251447121900}, "additional": {"logType": "info", "children": [], "durationId": "82263e9f-66d5-40b2-b5f6-6d2f2028aff5"}}, {"head": {"id": "31947992-d7e8-4b3b-af4f-924dedfc470a", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251449612100, "endTime": 29251451977400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "40cf3b34-2974-4389-91fd-459b4b46e3f5", "logId": "0cd455a3-3255-42aa-aff5-2dd6913f3be8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40cf3b34-2974-4389-91fd-459b4b46e3f5", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251448481100}, "additional": {"logType": "detail", "children": [], "durationId": "31947992-d7e8-4b3b-af4f-924dedfc470a"}}, {"head": {"id": "48781046-6938-41ae-b35a-155a974ed53c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251448874700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98403056-5423-41f1-82c5-6981163ae48f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251448946000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d14cfbd5-677e-4bac-b627-1dfa99145810", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251449618400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5dd0649-501b-4342-a42d-d21f92fe52c6", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251450778600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41b5c8a6-d944-4004-ba5b-a38504f6071c", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251451843500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a819f0c-8b4b-4d8b-9820-2edd0edc89c3", "name": "entry : default@CreateBuildProfile cost memory 0.098785400390625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251451927500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cd455a3-3255-42aa-aff5-2dd6913f3be8", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251449612100, "endTime": 29251451977400}, "additional": {"logType": "info", "children": [], "durationId": "31947992-d7e8-4b3b-af4f-924dedfc470a"}}, {"head": {"id": "cc44ffb7-c9be-48ae-b63e-841642164dfe", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251454928600, "endTime": 29251455212400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "24a75903-21ec-47eb-b089-c0d0c60228b6", "logId": "f4799e0a-adf6-4904-b6b7-c005992939ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24a75903-21ec-47eb-b089-c0d0c60228b6", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251453297600}, "additional": {"logType": "detail", "children": [], "durationId": "cc44ffb7-c9be-48ae-b63e-841642164dfe"}}, {"head": {"id": "3734905c-1ca7-4412-a847-e096a9a94160", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251453789100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d37c481e-2ff3-4a15-b52a-3be9dc081707", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251454003500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "493c66b9-8205-4c2d-8bad-31584b2e5040", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251454939300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8fb3ebc-d5a1-4dc7-81db-0269b39a25b6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251455032400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05d3c5fa-2568-4424-8a19-ed501a66717b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251455065900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e554710-40b2-4cf8-988c-497973ce8807", "name": "entry : default@PreCheckSyscap cost memory 0.03691864013671875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251455116000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96e9f15f-322a-4330-94aa-fa7ee993b4b5", "name": "runTaskFromQueue task cost before running: 164 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251455171000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4799e0a-adf6-4904-b6b7-c005992939ef", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251454928600, "endTime": 29251455212400, "totalTime": 225200}, "additional": {"logType": "info", "children": [], "durationId": "cc44ffb7-c9be-48ae-b63e-841642164dfe"}}, {"head": {"id": "6b81fbaa-6a03-4512-a6ed-11c51610fb5d", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251462929700, "endTime": 29251464585300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "588b5b41-7d83-45e3-bd4a-7fc7ac9074a2", "logId": "08d3fd3b-0a3f-469f-ab56-f7b52023889e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "588b5b41-7d83-45e3-bd4a-7fc7ac9074a2", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251456648300}, "additional": {"logType": "detail", "children": [], "durationId": "6b81fbaa-6a03-4512-a6ed-11c51610fb5d"}}, {"head": {"id": "a5f2bb5a-e7a2-462b-9c04-d5594a8552d9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251457262800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bc8ac4c-fd6c-4e37-a701-498349746844", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251457340000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca1f0a79-ca63-405f-a854-94f37e129aec", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251462940000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bb145bc-8c9f-4afa-ac07-e8a4f12d2887", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251463107600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a6727bb-2f2d-4695-a17a-c66bfc51f5ce", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251464373400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f2cc2c2-5d67-4fd1-a24f-9e419610924e", "name": "entry : default@GeneratePkgContextInfo cost memory -1.6241073608398438", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251464527200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08d3fd3b-0a3f-469f-ab56-f7b52023889e", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251462929700, "endTime": 29251464585300}, "additional": {"logType": "info", "children": [], "durationId": "6b81fbaa-6a03-4512-a6ed-11c51610fb5d"}}, {"head": {"id": "0899e3ef-2f2c-40d3-bd72-2fbd0a4a1512", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251467993300, "endTime": 29251468929900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "dd0472b8-acbd-495f-ad3d-0d03133d53d5", "logId": "ec6f5e5b-b490-4e38-acb6-ed507851ae64"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd0472b8-acbd-495f-ad3d-0d03133d53d5", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251466443600}, "additional": {"logType": "detail", "children": [], "durationId": "0899e3ef-2f2c-40d3-bd72-2fbd0a4a1512"}}, {"head": {"id": "8de9828f-69f7-44cb-a550-0fe4514a0c86", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251466949800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "363c0bf7-febc-41f8-97fb-eb08631fe678", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251467033700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00ac2940-379e-44c7-a85b-05fb1b3e22f2", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251468001000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10bf39ae-e4c8-43c7-9740-1e7faa5d0743", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251468809500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ca6bf19-e3d1-468e-904a-383b45af5d99", "name": "entry : default@ProcessProfile cost memory 0.05686187744140625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251468886800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec6f5e5b-b490-4e38-acb6-ed507851ae64", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251467993300, "endTime": 29251468929900}, "additional": {"logType": "info", "children": [], "durationId": "0899e3ef-2f2c-40d3-bd72-2fbd0a4a1512"}}, {"head": {"id": "7148f351-35fa-42ae-86b1-5b9e09cef453", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251472103900, "endTime": 29251476596800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3cd9cf8e-b64c-4266-ab03-0fbcde404734", "logId": "6a73c7bf-4048-4bc6-af97-7c63db3e6767"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3cd9cf8e-b64c-4266-ab03-0fbcde404734", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251470138500}, "additional": {"logType": "detail", "children": [], "durationId": "7148f351-35fa-42ae-86b1-5b9e09cef453"}}, {"head": {"id": "b897ef57-ca17-45f4-b758-b19ce4acec04", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251470545800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a5f068c-e7df-4ffb-98dc-5fa1e8f15acb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251470627700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a42feb7-9a39-44dd-a9f9-447c8240e351", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251472110300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89963c70-e615-4dfc-add3-bec0fbff1e40", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251476426500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b72cda0-dbee-4242-bf87-cfcd4ee7fab8", "name": "entry : default@ProcessRouterMap cost memory 0.19751739501953125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251476546600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a73c7bf-4048-4bc6-af97-7c63db3e6767", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251472103900, "endTime": 29251476596800}, "additional": {"logType": "info", "children": [], "durationId": "7148f351-35fa-42ae-86b1-5b9e09cef453"}}, {"head": {"id": "fb75d763-c7f4-47f3-8151-c5d0156090eb", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251481783400, "endTime": 29251483908800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "888b06fa-d86c-4720-b126-25e8498e25a4", "logId": "b3946fd4-2daf-4718-a6c4-f7d064166117"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "888b06fa-d86c-4720-b126-25e8498e25a4", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251479038600}, "additional": {"logType": "detail", "children": [], "durationId": "fb75d763-c7f4-47f3-8151-c5d0156090eb"}}, {"head": {"id": "91a4b5e4-a554-4a59-983c-a97b27b02474", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251479465800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c23d1964-0235-42cf-8fca-aedd7bfabfb4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251479550800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "807a8899-1821-45f8-a065-3dce529e5e80", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251480291700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "372dbb5b-6c84-4ce9-974a-7a0f4c1dfe92", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251482654200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87ba9cfa-ebad-445a-bb5f-7d323ced8de3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251482764400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "164ee18b-101c-4d7b-8d1c-6ebf02f576a1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251482799300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e45f9599-d1c1-4d4b-afad-d7df072926aa", "name": "entry : default@PreviewProcessResource cost memory 0.07025146484375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251482855300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f21c12e1-65e6-46cf-be33-e214ea1da643", "name": "runTaskFromQueue task cost before running: 192 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251483812200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3946fd4-2daf-4718-a6c4-f7d064166117", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251481783400, "endTime": 29251483908800, "totalTime": 1104900}, "additional": {"logType": "info", "children": [], "durationId": "fb75d763-c7f4-47f3-8151-c5d0156090eb"}}, {"head": {"id": "713349f3-6198-4c55-acc8-53a30c8da5ab", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251489599800, "endTime": 29251507610600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "42649ac5-aeee-4519-8c8e-985255ab8613", "logId": "eeea4b0f-94c8-4992-bf85-de82d1961d4a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42649ac5-aeee-4519-8c8e-985255ab8613", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251486084700}, "additional": {"logType": "detail", "children": [], "durationId": "713349f3-6198-4c55-acc8-53a30c8da5ab"}}, {"head": {"id": "808ab132-f9f0-4be3-a973-56e78148b5a8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251486523900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfe19ed5-01f6-4343-8341-5d9b85442602", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251486611600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "306ac014-3978-4613-af1b-a5b886da0101", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251489608500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c3ca57d-bcf3-4eb6-9fc4-e10edd9e49b0", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251507384400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ba4fd43-f032-45a1-8f76-f75b93afeb0a", "name": "entry : default@GenerateLoaderJson cost memory -0.7599563598632812", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251507512900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eeea4b0f-94c8-4992-bf85-de82d1961d4a", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251489599800, "endTime": 29251507610600}, "additional": {"logType": "info", "children": [], "durationId": "713349f3-6198-4c55-acc8-53a30c8da5ab"}}, {"head": {"id": "a52cae75-6b8f-42af-80be-6a7a26855afa", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251516934800, "endTime": 29251620106200}, "additional": {"children": ["b9eb80a3-d1a6-4267-8d51-b68f1d4b3e9a", "57fdfe6b-b47f-4f1d-8da5-92c421f40dbd", "a777ff07-5681-4729-adde-98b031f74d4c"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "aa839b0f-f10d-49a9-9dd3-e01bdfe2e4c1", "logId": "1b623639-45b2-4b9b-9612-adb14750ed4d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa839b0f-f10d-49a9-9dd3-e01bdfe2e4c1", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251513793100}, "additional": {"logType": "detail", "children": [], "durationId": "a52cae75-6b8f-42af-80be-6a7a26855afa"}}, {"head": {"id": "ad7316a2-7e06-413b-808c-31b7c348bea9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251514221700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2161ea9a-3101-4402-a58e-fe2b7cdb09ab", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251514322900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bc760dc-68ac-4353-818d-595b146c5fa5", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251515051700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc14935a-67a5-4363-8e1c-be78dccbd89f", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251516967600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9eb80a3-d1a6-4267-8d51-b68f1d4b3e9a", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251518818400, "endTime": 29251540979500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a52cae75-6b8f-42af-80be-6a7a26855afa", "logId": "8b8ea60e-9d53-403e-b452-33950c5a618d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b8ea60e-9d53-403e-b452-33950c5a618d", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251518818400, "endTime": 29251540979500}, "additional": {"logType": "info", "children": [], "durationId": "b9eb80a3-d1a6-4267-8d51-b68f1d4b3e9a", "parent": "1b623639-45b2-4b9b-9612-adb14750ed4d"}}, {"head": {"id": "9c9f843e-28de-461b-a07f-0ba3063ef6e0", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251541158900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57fdfe6b-b47f-4f1d-8da5-92c421f40dbd", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251542362600, "endTime": 29251579645800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a52cae75-6b8f-42af-80be-6a7a26855afa", "logId": "f7b757c8-4229-45b6-aac0-d95c25ec52ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "437b28ad-3f05-422c-9dc2-2c49ec056c34", "name": "current process  memoryUsage: {\n  rss: 122138624,\n  heapTotal: 125657088,\n  heapUsed: 118721264,\n  external: 3191202,\n  arrayBuffers: 185103\n} os memoryUsage :12.61865234375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251543189300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "104c1e3c-dfbb-42aa-936a-3e85f86fa39e", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251577477600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7b757c8-4229-45b6-aac0-d95c25ec52ee", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251542362600, "endTime": 29251579645800}, "additional": {"logType": "info", "children": [], "durationId": "57fdfe6b-b47f-4f1d-8da5-92c421f40dbd", "parent": "1b623639-45b2-4b9b-9612-adb14750ed4d"}}, {"head": {"id": "de34f34f-1a12-4c83-9360-328ad2c71127", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251579760700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a777ff07-5681-4729-adde-98b031f74d4c", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251580674500, "endTime": 29251620111500}, "additional": {"children": [], "state": "failed", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a52cae75-6b8f-42af-80be-6a7a26855afa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a52bdf61-5ffa-4652-8d02-09a11d16b372", "name": "current process  memoryUsage: {\n  rss: 122073088,\n  heapTotal: 124870656,\n  heapUsed: 106971576,\n  external: 3083302,\n  arrayBuffers: 77218\n} os memoryUsage :12.638317108154297", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251581524200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a145439-724b-4bb5-b8ec-e4efc40b1835", "name": "ERROR: command = D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe -x D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources -o D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251619667300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b623639-45b2-4b9b-9612-adb14750ed4d", "name": "Failed :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251516934800, "endTime": 29251620106200}, "additional": {"logType": "error", "children": ["8b8ea60e-9d53-403e-b452-33950c5a618d", "f7b757c8-4229-45b6-aac0-d95c25ec52ee"], "durationId": "a52cae75-6b8f-42af-80be-6a7a26855afa"}}, {"head": {"id": "8ef5ecd9-6c69-4eb8-90f5-f92cbc45543a", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251620361600}, "additional": {"logType": "debug", "children": [], "durationId": "a52cae75-6b8f-42af-80be-6a7a26855afa"}}, {"head": {"id": "30e9a668-28f0-40f4-80cc-f0323e828c78", "name": "ERROR: stacktrace = Error: Tools execution failed.\r\nError: the name '钱包' can only contain [a-zA-Z0-9_].\r\nError: invalid idName '钱包'.\r\nat D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources\\base\\media\\钱包.png\r\nSolutions:\r\n> Modify the name '钱包' to match [a-zA-Z0-9_].\r\n\t Detail: Please check the message from tools.\n    at OhosLogger.errorMessageExit (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\log\\hvigor-log.js:1:3224)\n    at OhosLogger._printErrorAndExit (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor-ohos-plugin\\src\\utils\\log\\ohos-logger.js:1:1978)\n    at ProcessUtils.handleException (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor-ohos-plugin\\src\\utils\\process-utils.js:1:5395)\n    at ProcessUtils.execute (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor-ohos-plugin\\src\\utils\\process-utils.js:1:3613)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PreviewCompileResource.executeCommand (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor-ohos-plugin\\src\\tasks\\abstract\\abstract-compile-resource.js:1:3730)\n    at async PreviewCompileResource.compileLink (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor-ohos-plugin\\src\\tasks\\abstract\\abstract-compile-resource.js:1:5074)\n    at async PreviewCompileResource.previewCompileLink (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor-ohos-plugin\\src\\tasks\\abstract\\abstract-compile-resource.js:1:5585)\n    at async PreviewCompileResource.invokeRestool (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor-ohos-plugin\\src\\tasks\\preview-compile-resource.js:1:1518)\n    at async PreviewCompileResource.compilationProcess (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor-ohos-plugin\\src\\tasks\\abstract\\abstract-compile-resource.js:1:3025)", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251624219400}, "additional": {"logType": "debug", "children": [], "durationId": "a52cae75-6b8f-42af-80be-6a7a26855afa"}}, {"head": {"id": "de17231a-013f-4eec-b255-85afca1b4814", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251633954600, "endTime": 29251633986600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "63744823-0fd0-4d19-8956-186fa18413f1", "logId": "3209d925-e1fa-4a56-a9fb-c209e56ffb83"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3209d925-e1fa-4a56-a9fb-c209e56ffb83", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251633954600, "endTime": 29251633986600}, "additional": {"logType": "info", "children": [], "durationId": "de17231a-013f-4eec-b255-85afca1b4814"}}, {"head": {"id": "79a9b117-25e2-4f7d-aeab-fd2f90a6f58e", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251291838700, "endTime": 29251634120200}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 13}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "2c7cd209-31fa-4108-a658-31862b4f543e", "name": "BUILD FAILED in 343 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251634144600}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "92e2aba5-9053-4b7c-9fe9-e2b737327d1d", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251634289300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f712b91-2623-4800-83e9-2dad057883d3", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251634323300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7087eed-23a7-4ef3-bc50-3b43548e5fa5", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251634347800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d5f3667-302e-4c8f-9bb8-6b1f42aac35c", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251634371300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5af6e4af-66e0-4691-b5bf-bff93a9b7ca1", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251634394100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62c1db4f-0d20-400c-9b57-cf1227d4f01e", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251634416900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0a0159c-259e-4b66-a820-ebd617370f42", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251634439700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78e01fa6-cb61-4c5b-a36f-89d43c0a578d", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251635092700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cd3643e-7bf5-4436-b7b3-6c2717539550", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251640985100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e36bf998-39f7-4989-9950-c9a66d61f515", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251641325100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36b09a3b-88b2-486e-8cb9-f7c367dbc278", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251646929000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba18ef60-3d79-4340-8344-123d10d7a2bd", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:14 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29251647586300}, "additional": {"logType": "debug", "children": []}}], "workLog": []}