if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface ChangePayPasswordPage_Params {
    oldPassword?: string;
    newPassword?: string;
    confirmPassword?: string;
    isSubmitting?: boolean;
    isFirstTimeSetup?: boolean;
    isLoading?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { UserApi } from "@normalized:N&&&entry/src/main/ets/api/UserApi&";
import type { UpdatePayPasswordRequest } from '../common/types/index';
class ChangePayPasswordPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__oldPassword = new ObservedPropertySimplePU('', this, "oldPassword");
        this.__newPassword = new ObservedPropertySimplePU('', this, "newPassword");
        this.__confirmPassword = new ObservedPropertySimplePU('', this, "confirmPassword");
        this.__isSubmitting = new ObservedPropertySimplePU(false, this, "isSubmitting");
        this.__isFirstTimeSetup = new ObservedPropertySimplePU(false, this, "isFirstTimeSetup");
        this.__isLoading = new ObservedPropertySimplePU(true, this, "isLoading");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: ChangePayPasswordPage_Params) {
        if (params.oldPassword !== undefined) {
            this.oldPassword = params.oldPassword;
        }
        if (params.newPassword !== undefined) {
            this.newPassword = params.newPassword;
        }
        if (params.confirmPassword !== undefined) {
            this.confirmPassword = params.confirmPassword;
        }
        if (params.isSubmitting !== undefined) {
            this.isSubmitting = params.isSubmitting;
        }
        if (params.isFirstTimeSetup !== undefined) {
            this.isFirstTimeSetup = params.isFirstTimeSetup;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
    }
    updateStateVars(params: ChangePayPasswordPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__oldPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__newPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__confirmPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__isSubmitting.purgeDependencyOnElmtId(rmElmtId);
        this.__isFirstTimeSetup.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__oldPassword.aboutToBeDeleted();
        this.__newPassword.aboutToBeDeleted();
        this.__confirmPassword.aboutToBeDeleted();
        this.__isSubmitting.aboutToBeDeleted();
        this.__isFirstTimeSetup.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __oldPassword: ObservedPropertySimplePU<string>;
    get oldPassword() {
        return this.__oldPassword.get();
    }
    set oldPassword(newValue: string) {
        this.__oldPassword.set(newValue);
    }
    private __newPassword: ObservedPropertySimplePU<string>;
    get newPassword() {
        return this.__newPassword.get();
    }
    set newPassword(newValue: string) {
        this.__newPassword.set(newValue);
    }
    private __confirmPassword: ObservedPropertySimplePU<string>;
    get confirmPassword() {
        return this.__confirmPassword.get();
    }
    set confirmPassword(newValue: string) {
        this.__confirmPassword.set(newValue);
    }
    private __isSubmitting: ObservedPropertySimplePU<boolean>;
    get isSubmitting() {
        return this.__isSubmitting.get();
    }
    set isSubmitting(newValue: boolean) {
        this.__isSubmitting.set(newValue);
    }
    private __isFirstTimeSetup: ObservedPropertySimplePU<boolean>; // 是否为首次设置
    get isFirstTimeSetup() {
        return this.__isFirstTimeSetup.get();
    }
    set isFirstTimeSetup(newValue: boolean) {
        this.__isFirstTimeSetup.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>; // 是否正在加载用户信息
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    aboutToAppear() {
        this.checkPayPasswordStatus();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(22:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(24:7)", "entry");
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(56);
            // 顶部导航栏
            Row.padding({ left: 16, right: 16 });
            // 顶部导航栏
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('返回');
            Button.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(25:9)", "entry");
            Button.fontSize(16);
            Button.fontColor('#1976D2');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.isFirstTimeSetup ? '设置支付密码' : '修改支付密码');
            Text.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(33:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(39:9)", "entry");
            Text.width(60);
        }, Text);
        Text.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(47:7)", "entry");
            Scroll.layoutWeight(1);
            Scroll.backgroundColor('#F5F5F5');
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(48:9)", "entry");
            Column.padding({ left: 16, right: 16, bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 说明信息
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(50:11)", "entry");
            // 说明信息
            Column.width('100%');
            // 说明信息
            Column.padding(20);
            // 说明信息
            Column.borderRadius(12);
            // 说明信息
            Column.backgroundColor('#FFF3E0');
            // 说明信息
            Column.margin({ top: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('安全提示');
            Text.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(51:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('• 支付密码用于转账、提现等资金操作');
            Text.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(58:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('• 请设置6位数字密码，避免使用生日等简单密码');
            Text.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(64:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('• 请妥善保管您的支付密码，不要告诉他人');
            Text.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(70:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        // 说明信息
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 密码修改表单
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(82:11)", "entry");
            // 密码修改表单
            Column.width('100%');
            // 密码修改表单
            Column.padding(20);
            // 密码修改表单
            Column.borderRadius(12);
            // 密码修改表单
            Column.backgroundColor('#FFFFFF');
            // 密码修改表单
            Column.margin({ top: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('密码信息');
            Text.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(83:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 原支付密码（仅修改时显示）
            if (!this.isFirstTimeSetup) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(92:15)", "entry");
                        Column.width('100%');
                        Column.alignItems(HorizontalAlign.Start);
                        Column.margin({ bottom: 20 });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('原支付密码');
                        Text.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(93:17)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#333333');
                        Text.alignSelf(ItemAlign.Start);
                        Text.margin({ bottom: 8 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        TextInput.create({ placeholder: '请输入原支付密码' });
                        TextInput.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(99:17)", "entry");
                        TextInput.type(InputType.Password);
                        TextInput.maxLength(6);
                        TextInput.fontSize(16);
                        TextInput.height(48);
                        TextInput.borderRadius(8);
                        TextInput.backgroundColor('#F8F9FA');
                        TextInput.border({ width: 1, color: this.oldPassword.length > 0 ? '#1976D2' : '#E0E0E0' });
                        TextInput.onChange((value: string) => {
                            this.oldPassword = value;
                        });
                    }, TextInput);
                    Column.pop();
                });
            }
            // 新支付密码
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 新支付密码
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(117:13)", "entry");
            // 新支付密码
            Column.width('100%');
            // 新支付密码
            Column.alignItems(HorizontalAlign.Start);
            // 新支付密码
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('新支付密码');
            Text.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(118:15)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入6位数字密码' });
            TextInput.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(124:15)", "entry");
            TextInput.type(InputType.Password);
            TextInput.maxLength(6);
            TextInput.fontSize(16);
            TextInput.height(48);
            TextInput.borderRadius(8);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: this.newPassword.length > 0 ? '#1976D2' : '#E0E0E0' });
            TextInput.onChange((value: string) => {
                this.newPassword = value;
            });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.newPassword.length > 0 && this.newPassword.length < 6) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('密码长度必须为6位');
                        Text.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(137:17)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#F44336');
                        Text.alignSelf(ItemAlign.Start);
                        Text.margin({ top: 4 });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        // 新支付密码
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 确认新密码
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(149:13)", "entry");
            // 确认新密码
            Column.width('100%');
            // 确认新密码
            Column.alignItems(HorizontalAlign.Start);
            // 确认新密码
            Column.margin({ bottom: 30 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('确认新密码');
            Text.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(150:15)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请再次输入新密码' });
            TextInput.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(156:15)", "entry");
            TextInput.type(InputType.Password);
            TextInput.maxLength(6);
            TextInput.fontSize(16);
            TextInput.height(48);
            TextInput.borderRadius(8);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: this.confirmPassword.length > 0 ? '#1976D2' : '#E0E0E0' });
            TextInput.onChange((value: string) => {
                this.confirmPassword = value;
            });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.confirmPassword.length > 0 && this.newPassword !== this.confirmPassword) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('两次输入的密码不一致');
                        Text.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(169:17)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#F44336');
                        Text.alignSelf(ItemAlign.Start);
                        Text.margin({ top: 4 });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        // 确认新密码
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 提交按钮
            Button.createWithLabel(this.isSubmitting ? '修改中...' : '确认修改');
            Button.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(181:13)", "entry");
            // 提交按钮
            Button.width('100%');
            // 提交按钮
            Button.height(48);
            // 提交按钮
            Button.fontSize(16);
            // 提交按钮
            Button.fontColor(Color.White);
            // 提交按钮
            Button.backgroundColor(this.isFormValid() && !this.isSubmitting ? '#1976D2' : '#CCCCCC');
            // 提交按钮
            Button.borderRadius(8);
            // 提交按钮
            Button.enabled(this.isFormValid() && !this.isSubmitting);
            // 提交按钮
            Button.onClick(() => {
                this.submitForm();
            });
        }, Button);
        // 提交按钮
        Button.pop();
        // 密码修改表单
        Column.pop();
        Column.pop();
        Scroll.pop();
        Column.pop();
    }
    // 表单验证
    isFormValid(): boolean {
        const newPasswordValid = this.newPassword.length === 6 &&
            this.confirmPassword.length === 6 &&
            this.newPassword === this.confirmPassword;
        if (this.isFirstTimeSetup) {
            // 首次设置，只需要验证新密码
            return newPasswordValid;
        }
        else {
            // 修改密码，需要验证原密码和新密码
            return this.oldPassword.length === 6 && newPasswordValid;
        }
    }
    // 提交表单
    async submitForm() {
        if (!this.isFormValid() || this.isSubmitting)
            return;
        this.isSubmitting = true;
        try {
            const data: UpdatePayPasswordRequest = {
                oldPassword: this.isFirstTimeSetup ? undefined : this.oldPassword,
                newPassword: this.newPassword
            };
            await UserApi.updatePayPassword(data);
            const successMessage = this.isFirstTimeSetup ? '支付密码设置成功' : '支付密码修改成功';
            promptAction.showToast({ message: successMessage });
            // 如果是首次设置，更新状态
            if (this.isFirstTimeSetup) {
                this.isFirstTimeSetup = false;
            }
            // 返回设置页面
            router.back();
        }
        catch (error) {
            console.error('修改支付密码失败:', error);
            const errorMessage = this.isFirstTimeSetup ? '设置失败，请重试' : '修改失败，请检查原密码是否正确';
            promptAction.showToast({ message: errorMessage });
        }
        finally {
            this.isSubmitting = false;
        }
    }
    /**
     * 检查支付密码状态
     */
    async checkPayPasswordStatus() {
        try {
            // 获取用户信息来判断是否已设置支付密码
            const userInfo = await UserApi.getUserInfo();
            console.log('用户信息:', userInfo);
            console.log('hasPayPassword:', userInfo.hasPayPassword);
            // 根据后端返回的hasPayPassword字段判断
            this.isFirstTimeSetup = !userInfo.hasPayPassword;
            console.log('isFirstTimeSetup:', this.isFirstTimeSetup);
        }
        catch (error) {
            console.error('获取用户信息失败:', error);
            // 默认为修改模式
            this.isFirstTimeSetup = false;
        }
        finally {
            this.isLoading = false;
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "ChangePayPasswordPage";
    }
}
registerNamedRoute(() => new ChangePayPasswordPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/ChangePayPasswordPage", pageFullPath: "entry/src/main/ets/pages/ChangePayPasswordPage", integratedHsp: "false", moduleType: "followWithHap" });
