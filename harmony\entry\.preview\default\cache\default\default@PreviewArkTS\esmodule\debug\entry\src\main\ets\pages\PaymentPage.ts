if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface PaymentPage_Params {
    selectedCard?: BankCard | null;
    bankCards?: BankCard[];
    payPassword?: string;
    amount?: string;
    description?: string;
    isLoading?: boolean;
    showCardSelector?: boolean;
    showPasswordInput?: boolean;
    paymentMethod?: string;
    paymentChannel?: string;
    walletBalance?: number;
    merchantName?: string;
    showPaymentDialog?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { BankCardType, BankCardStatus } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import type { BankCard, SpringBootBankCardResponse } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import { httpClient } from "@normalized:N&&&entry/src/main/ets/common/http/HttpClient&";
import { storageManager } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
class PaymentPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__selectedCard = new ObservedPropertyObjectPU(null, this, "selectedCard");
        this.__bankCards = new ObservedPropertyObjectPU([], this, "bankCards");
        this.__payPassword = new ObservedPropertySimplePU("", this, "payPassword");
        this.__amount = new ObservedPropertySimplePU("", this, "amount");
        this.__description = new ObservedPropertySimplePU("", this, "description");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__showCardSelector = new ObservedPropertySimplePU(false, this, "showCardSelector");
        this.__showPasswordInput = new ObservedPropertySimplePU(false, this, "showPasswordInput");
        this.__paymentMethod = new ObservedPropertySimplePU("wallet", this, "paymentMethod");
        this.__paymentChannel = new ObservedPropertySimplePU("merchant", this, "paymentChannel");
        this.__walletBalance = new ObservedPropertySimplePU(0, this, "walletBalance");
        this.__merchantName = new ObservedPropertySimplePU("", this, "merchantName");
        this.__showPaymentDialog = new ObservedPropertySimplePU(false, this, "showPaymentDialog");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: PaymentPage_Params) {
        if (params.selectedCard !== undefined) {
            this.selectedCard = params.selectedCard;
        }
        if (params.bankCards !== undefined) {
            this.bankCards = params.bankCards;
        }
        if (params.payPassword !== undefined) {
            this.payPassword = params.payPassword;
        }
        if (params.amount !== undefined) {
            this.amount = params.amount;
        }
        if (params.description !== undefined) {
            this.description = params.description;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.showCardSelector !== undefined) {
            this.showCardSelector = params.showCardSelector;
        }
        if (params.showPasswordInput !== undefined) {
            this.showPasswordInput = params.showPasswordInput;
        }
        if (params.paymentMethod !== undefined) {
            this.paymentMethod = params.paymentMethod;
        }
        if (params.paymentChannel !== undefined) {
            this.paymentChannel = params.paymentChannel;
        }
        if (params.walletBalance !== undefined) {
            this.walletBalance = params.walletBalance;
        }
        if (params.merchantName !== undefined) {
            this.merchantName = params.merchantName;
        }
        if (params.showPaymentDialog !== undefined) {
            this.showPaymentDialog = params.showPaymentDialog;
        }
    }
    updateStateVars(params: PaymentPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__selectedCard.purgeDependencyOnElmtId(rmElmtId);
        this.__bankCards.purgeDependencyOnElmtId(rmElmtId);
        this.__payPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__amount.purgeDependencyOnElmtId(rmElmtId);
        this.__description.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__showCardSelector.purgeDependencyOnElmtId(rmElmtId);
        this.__showPasswordInput.purgeDependencyOnElmtId(rmElmtId);
        this.__paymentMethod.purgeDependencyOnElmtId(rmElmtId);
        this.__paymentChannel.purgeDependencyOnElmtId(rmElmtId);
        this.__walletBalance.purgeDependencyOnElmtId(rmElmtId);
        this.__merchantName.purgeDependencyOnElmtId(rmElmtId);
        this.__showPaymentDialog.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__selectedCard.aboutToBeDeleted();
        this.__bankCards.aboutToBeDeleted();
        this.__payPassword.aboutToBeDeleted();
        this.__amount.aboutToBeDeleted();
        this.__description.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__showCardSelector.aboutToBeDeleted();
        this.__showPasswordInput.aboutToBeDeleted();
        this.__paymentMethod.aboutToBeDeleted();
        this.__paymentChannel.aboutToBeDeleted();
        this.__walletBalance.aboutToBeDeleted();
        this.__merchantName.aboutToBeDeleted();
        this.__showPaymentDialog.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __selectedCard: ObservedPropertyObjectPU<BankCard | null>;
    get selectedCard() {
        return this.__selectedCard.get();
    }
    set selectedCard(newValue: BankCard | null) {
        this.__selectedCard.set(newValue);
    }
    private __bankCards: ObservedPropertyObjectPU<BankCard[]>;
    get bankCards() {
        return this.__bankCards.get();
    }
    set bankCards(newValue: BankCard[]) {
        this.__bankCards.set(newValue);
    }
    private __payPassword: ObservedPropertySimplePU<string>;
    get payPassword() {
        return this.__payPassword.get();
    }
    set payPassword(newValue: string) {
        this.__payPassword.set(newValue);
    }
    private __amount: ObservedPropertySimplePU<string>;
    get amount() {
        return this.__amount.get();
    }
    set amount(newValue: string) {
        this.__amount.set(newValue);
    }
    private __description: ObservedPropertySimplePU<string>;
    get description() {
        return this.__description.get();
    }
    set description(newValue: string) {
        this.__description.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __showCardSelector: ObservedPropertySimplePU<boolean>;
    get showCardSelector() {
        return this.__showCardSelector.get();
    }
    set showCardSelector(newValue: boolean) {
        this.__showCardSelector.set(newValue);
    }
    private __showPasswordInput: ObservedPropertySimplePU<boolean>;
    get showPasswordInput() {
        return this.__showPasswordInput.get();
    }
    set showPasswordInput(newValue: boolean) {
        this.__showPasswordInput.set(newValue);
    }
    private __paymentMethod: ObservedPropertySimplePU<string>; // "wallet" | "bankcard"
    get paymentMethod() {
        return this.__paymentMethod.get();
    }
    set paymentMethod(newValue: string) {
        this.__paymentMethod.set(newValue);
    }
    private __paymentChannel: ObservedPropertySimplePU<string>; // "merchant" | "qr" | "nfc"
    get paymentChannel() {
        return this.__paymentChannel.get();
    }
    set paymentChannel(newValue: string) {
        this.__paymentChannel.set(newValue);
    }
    private __walletBalance: ObservedPropertySimplePU<number>;
    get walletBalance() {
        return this.__walletBalance.get();
    }
    set walletBalance(newValue: number) {
        this.__walletBalance.set(newValue);
    }
    private __merchantName: ObservedPropertySimplePU<string>;
    get merchantName() {
        return this.__merchantName.get();
    }
    set merchantName(newValue: string) {
        this.__merchantName.set(newValue);
    }
    private __showPaymentDialog: ObservedPropertySimplePU<boolean>;
    get showPaymentDialog() {
        return this.__showPaymentDialog.get();
    }
    set showPaymentDialog(newValue: boolean) {
        this.__showPaymentDialog.set(newValue);
    }
    aboutToAppear() {
        // 检查路由参数
        const params = router.getParams() as Record<string, Object>;
        if (params?.selectedCard) {
            this.selectedCard = params.selectedCard as BankCard;
        }
        if (params?.amount) {
            this.amount = params.amount as string;
        }
        if (params?.description) {
            this.description = params.description as string;
        }
        this.loadBankCards();
    }
    async loadBankCards() {
        try {
            // 从本地存储获取当前用户ID
            const cachedUserInfo = await storageManager.getUserInfo();
            if (!cachedUserInfo || !cachedUserInfo.userId) {
                console.error("无法获取用户ID");
                return;
            }
            // 调用SpringBoot3后端API获取银行卡列表
            const response = await httpClient.get<SpringBootBankCardResponse[]>(`/bank-card/user/${cachedUserInfo.userId}`);
            const bankCardList: SpringBootBankCardResponse[] = response.data;
            // 转换为本地格式，只显示已绑定的银行卡
            this.bankCards = this.convertSpringBootBankCardsToLocal(bankCardList)
                .filter(card => card.isBound === BankCardStatus.BOUND);
            // 如果没有选中的银行卡，默认选择第一张或默认卡
            if (!this.selectedCard && this.bankCards.length > 0) {
                this.selectedCard = this.bankCards.find(card => card.isDefault) || this.bankCards[0];
            }
            console.log("银行卡列表加载完成，数量:", this.bankCards.length);
        }
        catch (error) {
            console.error("加载银行卡列表失败:", error);
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(69:5)", "entry");
            Column.width("100%");
            Column.height("100%");
            Column.backgroundColor("#F5F5F5");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentPage.ets(71:7)", "entry");
            // 顶部导航
            Row.width("100%");
            // 顶部导航
            Row.padding({ left: 16, right: 16, top: 12, bottom: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/PaymentPage.ets(72:9)", "entry");
            Button.width(40);
            Button.height(40);
            Button.borderRadius(20);
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777238, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/PaymentPage.ets(73:11)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor("#333333");
        }, Image);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create("支付");
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(86:9)", "entry");
            Text.fontSize(18);
            Text.fontColor("#333333");
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create("");
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(93:9)", "entry");
            Text.width(40);
        }, Text);
        Text.pop();
        // 顶部导航
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/PaymentPage.ets(99:7)", "entry");
            Scroll.layoutWeight(1);
            Scroll.scrollable(ScrollDirection.Vertical);
            Scroll.scrollBar(BarState.Off);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(100:9)", "entry");
            Column.padding({ left: 16, right: 16, top: 16, bottom: 20 });
        }, Column);
        // 支付途径选择
        this.PaymentChannelSection.bind(this)();
        // 支付方式选择
        this.PaymentMethodSection.bind(this)();
        // 支付金额
        this.PaymentAmountSection.bind(this)();
        // 支付密码
        this.PaymentPasswordSection.bind(this)();
        // 确认支付按钮
        this.ConfirmPaymentButton.bind(this)();
        Column.pop();
        Scroll.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 银行卡选择器弹窗
            if (this.showCardSelector) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.BankCardSelectorDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    BankCardSection(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(134:5)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ bottom: 24 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create("银行卡信息");
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(135:7)", "entry");
            Text.fontSize(16);
            Text.fontColor("#333333");
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.selectedCard) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 显示选中的银行卡
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(144:9)", "entry");
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/PaymentPage.ets(145:11)", "entry");
                        Row.width("100%");
                        Row.padding(16);
                        Row.backgroundColor("#FFFFFF");
                        Row.borderRadius(8);
                        Row.border({ width: 1, color: "#E0E0E0" });
                        Row.onClick(() => {
                            if (this.bankCards.length > 1) {
                                this.showCardSelector = true;
                            }
                        });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 银行卡缩略图
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(147:13)", "entry");
                        // 银行卡缩略图
                        Column.width(48);
                        // 银行卡缩略图
                        Column.height(32);
                        // 银行卡缩略图
                        Column.borderRadius(4);
                        // 银行卡缩略图
                        Column.justifyContent(FlexAlign.Center);
                        // 银行卡缩略图
                        Column.linearGradient({
                            direction: GradientDirection.Right,
                            colors: this.getBankCardGradient(this.selectedCard.bankName)
                        });
                        // 银行卡缩略图
                        Column.margin({ right: 12 });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.selectedCard.bankName.substring(0, 2));
                        Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(148:15)", "entry");
                        Text.fontSize(16);
                        Text.fontColor("#FFFFFF");
                        Text.fontWeight(FontWeight.Bold);
                    }, Text);
                    Text.pop();
                    // 银行卡缩略图
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(163:13)", "entry");
                        Column.alignItems(HorizontalAlign.Start);
                        Column.layoutWeight(1);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.selectedCard.bankName);
                        Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(164:15)", "entry");
                        Text.fontSize(16);
                        Text.fontColor("#333333");
                        Text.fontWeight(FontWeight.Medium);
                        Text.alignSelf(ItemAlign.Start);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`${this.getCardTypeText(this.selectedCard.cardType)} • ${this.formatCardNumber(this.selectedCard.cardNo)}`);
                        Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(170:15)", "entry");
                        Text.fontSize(14);
                        Text.fontColor("#666666");
                        Text.margin({ top: 2 });
                        Text.alignSelf(ItemAlign.Start);
                    }, Text);
                    Text.pop();
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.bankCards.length > 1) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create("更换");
                                    Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(180:15)", "entry");
                                    Text.fontSize(14);
                                    Text.fontColor("#1976D2");
                                    Text.onClick(() => {
                                        this.showCardSelector = true;
                                    });
                                }, Text);
                                Text.pop();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    Row.pop();
                    // 显示选中的银行卡
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 选择银行卡
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/PaymentPage.ets(201:9)", "entry");
                        // 选择银行卡
                        Row.width("100%");
                        // 选择银行卡
                        Row.height(48);
                        // 选择银行卡
                        Row.padding({ left: 12, right: 12 });
                        // 选择银行卡
                        Row.backgroundColor("#FFFFFF");
                        // 选择银行卡
                        Row.borderRadius(8);
                        // 选择银行卡
                        Row.border({ width: 1, color: "#E0E0E0" });
                        // 选择银行卡
                        Row.onClick(() => {
                            this.showCardSelector = true;
                        });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create("请选择银行卡");
                        Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(202:11)", "entry");
                        Text.fontSize(16);
                        Text.fontColor("#999999");
                        Text.layoutWeight(1);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Image.create({ "id": 16777224, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
                        Image.debugLine("entry/src/main/ets/pages/PaymentPage.ets(207:11)", "entry");
                        Image.width(16);
                        Image.height(16);
                        Image.fillColor("#999999");
                    }, Image);
                    // 选择银行卡
                    Row.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    PaymentAmountSection(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(229:5)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ bottom: 24 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create("支付金额");
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(230:7)", "entry");
            Text.fontSize(16);
            Text.fontColor("#333333");
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentPage.ets(237:7)", "entry");
            Row.width("100%");
            Row.padding(16);
            Row.backgroundColor("#FFFFFF");
            Row.borderRadius(8);
            Row.border({ width: 1, color: "#E0E0E0" });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create("¥");
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(238:9)", "entry");
            Text.fontSize(24);
            Text.fontColor("#333333");
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ right: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({
                placeholder: "0.00",
                text: this.amount
            });
            TextInput.debugLine("entry/src/main/ets/pages/PaymentPage.ets(244:9)", "entry");
            TextInput.type(InputType.Number);
            TextInput.fontSize(24);
            TextInput.fontWeight(FontWeight.Bold);
            TextInput.backgroundColor(Color.Transparent);
            TextInput.border({ width: 0 });
            TextInput.layoutWeight(1);
            TextInput.onChange((value: string) => {
                this.amount = value;
            });
        }, TextInput);
        Row.pop();
        Column.pop();
    }
    PaymentPasswordSection(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(270:5)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ bottom: 24 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create("支付密码");
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(271:7)", "entry");
            Text.fontSize(16);
            Text.fontColor("#333333");
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentPage.ets(278:7)", "entry");
            Row.width("100%");
            Row.padding(16);
            Row.backgroundColor("#FFFFFF");
            Row.borderRadius(8);
            Row.border({ width: 1, color: "#E0E0E0" });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({
                placeholder: "请输入6位支付密码"
            });
            TextInput.debugLine("entry/src/main/ets/pages/PaymentPage.ets(279:9)", "entry");
            TextInput.type(InputType.Password);
            TextInput.maxLength(6);
            TextInput.fontSize(16);
            TextInput.backgroundColor(Color.Transparent);
            TextInput.border({ width: 0 });
            TextInput.layoutWeight(1);
            TextInput.onChange((value: string) => {
                this.payPassword = value;
            });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/PaymentPage.ets(292:9)", "entry");
            Button.width(40);
            Button.height(40);
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                this.showPasswordInput = !this.showPasswordInput;
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create($r("app.media.ic_eye_off"));
            Image.debugLine("entry/src/main/ets/pages/PaymentPage.ets(293:11)", "entry");
            Image.width(20);
            Image.height(20);
            Image.fillColor("#999999");
        }, Image);
        Button.pop();
        Row.pop();
        Column.pop();
    }
    ConfirmPaymentButton(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel("确认支付");
            Button.debugLine("entry/src/main/ets/pages/PaymentPage.ets(317:5)", "entry");
            Button.fontSize(16);
            Button.fontColor("#FFFFFF");
            Button.backgroundColor(this.canPay() ? "#1976D2" : "#CCCCCC");
            Button.borderRadius(8);
            Button.width("100%");
            Button.height(48);
            Button.enabled(this.canPay());
            Button.onClick(() => {
                this.confirmPayment();
            });
        }, Button);
        Button.pop();
    }
    BankCardSelectorDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create({ alignContent: Alignment.Bottom });
            Stack.debugLine("entry/src/main/ets/pages/PaymentPage.ets(332:5)", "entry");
            Stack.width('100%');
            Stack.height('100%');
            Stack.position({ x: 0, y: 0 });
            Stack.zIndex(1000);
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 遮罩层
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(334:7)", "entry");
            // 遮罩层
            Column.width('100%');
            // 遮罩层
            Column.height('100%');
            // 遮罩层
            Column.backgroundColor('rgba(0,0,0,0.5)');
            // 遮罩层
            Column.onClick(() => {
                this.showCardSelector = false;
            });
        }, Column);
        // 遮罩层
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡选择面板
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(343:7)", "entry");
            Context.animation({
                duration: 300,
                curve: Curve.EaseInOut
            });
            // 银行卡选择面板
            Column.width('100%');
            // 银行卡选择面板
            Column.backgroundColor('#FFFFFF');
            // 银行卡选择面板
            Column.borderRadius({ topLeft: 16, topRight: 16 });
            Context.animation(null);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部标题栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentPage.ets(345:9)", "entry");
            // 顶部标题栏
            Row.width('100%');
            // 顶部标题栏
            Row.height(56);
            // 顶部标题栏
            Row.padding({ left: 16, right: 16 });
            // 顶部标题栏
            Row.border({
                width: { bottom: 1 },
                color: '#E0E0E0'
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/PaymentPage.ets(346:11)", "entry");
            Button.fontSize(16);
            Button.fontColor('#666666');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                this.showCardSelector = false;
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('选择银行卡');
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(354:11)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(361:11)", "entry");
            Text.width(60);
        }, Text);
        Text.pop();
        // 顶部标题栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 银行卡列表
            if (this.bankCards.length > 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        List.create();
                        List.debugLine("entry/src/main/ets/pages/PaymentPage.ets(374:11)", "entry");
                        List.height(300);
                        List.scrollBar(BarState.Off);
                        List.padding({ left: 16, right: 16, top: 16, bottom: 16 });
                    }, List);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const card = _item;
                            {
                                const itemCreation = (elmtId, isInitialRender) => {
                                    ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                    itemCreation2(elmtId, isInitialRender);
                                    if (!isInitialRender) {
                                        ListItem.pop();
                                    }
                                    ViewStackProcessor.StopGetAccessRecording();
                                };
                                const itemCreation2 = (elmtId, isInitialRender) => {
                                    ListItem.create(deepRenderFunction, true);
                                    ListItem.debugLine("entry/src/main/ets/pages/PaymentPage.ets(376:15)", "entry");
                                };
                                const deepRenderFunction = (elmtId, isInitialRender) => {
                                    itemCreation(elmtId, isInitialRender);
                                    this.BankCardItem.bind(this)(card);
                                    ListItem.pop();
                                };
                                this.observeComponentCreation2(itemCreation2, ListItem);
                                ListItem.pop();
                            }
                        };
                        this.forEachUpdateFunction(elmtId, this.bankCards, forEachItemGenFunction);
                    }, ForEach);
                    ForEach.pop();
                    List.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(385:11)", "entry");
                        Column.width('100%');
                        Column.justifyContent(FlexAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无可用银行卡');
                        Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(386:13)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#999999');
                        Text.margin({ top: 40, bottom: 40 });
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        // 银行卡选择面板
        Column.pop();
        Stack.pop();
    }
    BankCardItem(card: BankCard, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentPage.ets(411:5)", "entry");
            Row.width('100%');
            Row.padding(16);
            Row.backgroundColor(this.selectedCard?.cardId === card.cardId ? '#E8F5E8' : '#FFFFFF');
            Row.borderRadius(8);
            Row.border({
                width: this.selectedCard?.cardId === card.cardId ? 2 : 1,
                color: this.selectedCard?.cardId === card.cardId ? '#4CAF50' : '#E0E0E0'
            });
            Row.margin({ bottom: 12 });
            Row.onClick(() => {
                this.selectedCard = card;
                this.showCardSelector = false;
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡缩略图
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(413:7)", "entry");
            // 银行卡缩略图
            Column.width(48);
            // 银行卡缩略图
            Column.height(32);
            // 银行卡缩略图
            Column.borderRadius(4);
            // 银行卡缩略图
            Column.justifyContent(FlexAlign.Center);
            // 银行卡缩略图
            Column.linearGradient({
                direction: GradientDirection.Right,
                colors: this.getBankCardGradient(card.bankName)
            });
            // 银行卡缩略图
            Column.margin({ right: 12 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.bankName.substring(0, 2));
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(414:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Bold);
        }, Text);
        Text.pop();
        // 银行卡缩略图
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡信息
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(430:7)", "entry");
            // 银行卡信息
            Column.alignItems(HorizontalAlign.Start);
            // 银行卡信息
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentPage.ets(431:9)", "entry");
            Row.width('100%');
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.bankName);
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(432:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (card.isDefault) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('默认');
                        Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(439:13)", "entry");
                        Text.fontSize(10);
                        Text.fontColor('#FFFFFF');
                        Text.backgroundColor('#FF9800');
                        Text.borderRadius(8);
                        Text.padding({ left: 6, right: 6, top: 2, bottom: 2 });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`${this.getCardTypeText(card.cardType)} • ${this.formatCardNumber(card.cardNo)}`);
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(450:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 银行卡信息
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 选择状态
            if (this.selectedCard?.cardId === card.cardId) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('✓');
                        Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(460:9)", "entry");
                        Text.fontSize(20);
                        Text.fontColor('#4CAF50');
                        Text.fontWeight(FontWeight.Bold);
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Row.pop();
    }
    // 辅助方法
    private canPay(): boolean {
        return this.selectedCard !== null &&
            this.amount !== "" &&
            parseFloat(this.amount) > 0 &&
            this.payPassword.length === 6 &&
            !this.isLoading;
    }
    private async confirmPayment() {
        if (!this.canPay())
            return;
        try {
            this.isLoading = true;
            // 获取当前用户信息
            const cachedUserInfo = await storageManager.getUserInfo();
            if (!cachedUserInfo || !cachedUserInfo.userId) {
                promptAction.showToast({ message: '用户信息获取失败' });
                return;
            }
            // 构建支付请求
            const paymentRequest = {
                userId: cachedUserInfo.userId,
                amount: parseFloat(this.amount),
                paymentMethod: this.paymentMethod,
                paymentChannel: this.paymentChannel,
                merchantName: this.merchantName || '商户支付',
                description: `${this.paymentChannel}支付 - ${this.merchantName || '商户'}`,
                payPassword: this.payPassword
            };
            // 如果是银行卡支付，添加银行卡信息
            if (this.paymentMethod === "card" && this.selectedCard) {
                paymentRequest['cardId'] = this.selectedCard.cardId;
                paymentRequest['cardNo'] = this.selectedCard.cardNo;
            }
            // 调用SpringBoot3后端支付API
            const response = await httpClient.post('/payment/pay', paymentRequest);
            if (response.data) {
                promptAction.showToast({ message: '支付成功' });
                // 支付成功后，可以跳转到支付结果页面或返回
                setTimeout(() => {
                    router.back();
                }, 1500);
            }
            else {
                promptAction.showToast({ message: '支付失败，请重试' });
            }
        }
        catch (error) {
            console.error('支付失败:', error);
            let errorMessage = '支付失败，请重试';
            if (error instanceof Error) {
                errorMessage = `支付失败: ${error.message}`;
            }
            promptAction.showToast({ message: errorMessage });
        }
        finally {
            this.isLoading = false;
        }
    }
    private convertSpringBootBankCardsToLocal(bankCardList: SpringBootBankCardResponse[]): BankCard[] {
        return bankCardList.map((card: SpringBootBankCardResponse): BankCard => ({
            cardId: card.cardId || 0,
            userId: card.userId || 0,
            cardNo: card.cardNumber || '',
            cardType: this.mapBankCardType(card.cardType),
            bankName: card.bankName || '',
            holderName: card.cardHolder || '',
            isBound: card.status === 1 ? BankCardStatus.BOUND : BankCardStatus.UNBOUND,
            createTime: card.createdAt || card.createTime || '',
            updateTime: card.updatedAt || card.updateTime || '',
            maskedCardNo: card.cardNumber ? this.maskCardNo(card.cardNumber) : undefined,
            isDefault: card.isDefault === 1 || card.isDefault === true
        }));
    }
    private mapBankCardType(cardType: string | number | undefined): BankCardType {
        if (typeof cardType === "number") {
            return cardType === 2 ? BankCardType.CREDIT : BankCardType.DEBIT;
        }
        else if (typeof cardType === "string") {
            if (cardType === "信用卡" || cardType === "CREDIT") {
                return BankCardType.CREDIT;
            }
        }
        return BankCardType.DEBIT;
    }
    private maskCardNo(cardNo: string): string {
        if (!cardNo || cardNo.length < 8)
            return cardNo;
        return cardNo.replace(/(\d{4})\d*(\d{4})/, "$1****$2");
    }
    private getCardTypeText(cardType: BankCardType): string {
        switch (cardType) {
            case BankCardType.CREDIT:
                return "信用卡";
            case BankCardType.DEBIT:
                return "储蓄卡";
            default:
                return "储蓄卡";
        }
    }
    private formatCardNumber(cardNo: string): string {
        if (!cardNo)
            return "";
        return `**** **** **** ${cardNo.slice(-4)}`;
    }
    private getBankCardGradient(bankName: string): Array<[
        string,
        number
    ]> {
        const gradients: Record<string, Array<[
            string,
            number
        ]>> = {
            '中国工商银行': [['#C41E3A', 0], ['#8B0000', 1]],
            '中国建设银行': [['#003DA5', 0], ['#001F5C', 1]],
            '中国农业银行': [['#00A651', 0], ['#006B35', 1]],
            '中国银行': [['#B8860B', 0], ['#8B6914', 1]],
            '招商银行': [['#DC143C', 0], ['#8B0000', 1]],
            '交通银行': [['#0066CC', 0], ['#003D7A', 1]],
            '中信银行': [['#FF6B35', 0], ['#CC4125', 1]],
            '光大银行': [['#9932CC', 0], ['#6A1B9A', 1]],
            '华夏银行': [['#FF1744', 0], ['#C62828', 1]],
            '民生银行': [['#00BCD4', 0], ['#0097A7', 1]],
            '广发银行': [['#FF5722', 0], ['#D84315', 1]],
            '平安银行': [['#FF9800', 0], ['#F57C00', 1]]
        };
        return gradients[bankName] || [['#1976D2', 0], ['#0D47A1', 1]];
    }
    PaymentChannelSection(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(614:5)", "entry");
            Column.width('100%');
            Column.padding(20);
            Column.margin({ bottom: 16 });
            Column.borderRadius(12);
            Column.backgroundColor('#FFFFFF');
            Column.shadow({
                radius: 4,
                color: 'rgba(0,0,0,0.1)',
                offsetX: 0,
                offsetY: 2
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('选择支付途径');
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(615:7)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Bold);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('请选择您的付款方式');
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(622:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支付途径选项
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(629:7)", "entry");
            // 支付途径选项
            Column.width('100%');
        }, Column);
        this.ChannelOptionCard.bind(this)('merchant', '商户付款', '🏪', '线下商户扫码收款');
        this.ChannelOptionCard.bind(this)('qr', '扫码付款', '📱', '扫描二维码完成支付');
        this.ChannelOptionCard.bind(this)('nfc', 'NFC支付', '📡', '近场通信快速支付');
        // 支付途径选项
        Column.pop();
        Column.pop();
    }
    ChannelOptionCard(channel: string, title: string, icon: string, description: string, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentPage.ets(651:5)", "entry");
            Row.width('100%');
            Row.height(80);
            Row.padding(16);
            Row.margin({ bottom: 12 });
            Row.borderRadius(12);
            Row.backgroundColor(this.paymentChannel === channel ? "#E3F2FD" : "#F8F9FA");
            Row.border({
                width: 2,
                color: this.paymentChannel === channel ? "#1976D2" : "transparent"
            });
            Row.onClick(() => {
                this.paymentChannel = channel;
                if (channel === "merchant") {
                    this.merchantName = "示例商户";
                    promptAction.showToast({ message: "已选择商户付款" });
                }
                else if (channel === "qr") {
                    this.merchantName = "扫码商户";
                    this.handleQRPayment();
                }
                else if (channel === "nfc") {
                    this.merchantName = "NFC商户";
                    this.handleNFCPayment();
                }
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 图标区域
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(653:7)", "entry");
            // 图标区域
            Column.width(60);
            // 图标区域
            Column.height(60);
            // 图标区域
            Column.justifyContent(FlexAlign.Center);
            // 图标区域
            Column.borderRadius(30);
            // 图标区域
            Column.backgroundColor(this.paymentChannel === channel ? '#1976D2' : '#F0F0F0');
            // 图标区域
            Column.margin({ right: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(icon);
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(654:9)", "entry");
            Text.fontSize(32);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        // 图标区域
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 内容区域
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(666:7)", "entry");
            // 内容区域
            Column.layoutWeight(1);
            // 内容区域
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(667:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Bold);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(description);
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(674:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.maxLines(1);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
        }, Text);
        Text.pop();
        // 内容区域
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 选择指示器
            if (this.paymentChannel === channel) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('✓');
                        Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(686:9)", "entry");
                        Text.fontSize(20);
                        Text.fontColor('#1976D2');
                        Text.fontWeight(FontWeight.Bold);
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Row.pop();
    }
    PaymentMethodSection(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(719:5)", "entry");
            Column.width('100%');
            Column.padding(20);
            Column.margin({ bottom: 16 });
            Column.borderRadius(12);
            Column.backgroundColor('#FFFFFF');
            Column.shadow({
                radius: 4,
                color: 'rgba(0,0,0,0.1)',
                offsetX: 0,
                offsetY: 2
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('选择支付方式');
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(720:7)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Bold);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('请选择您的资金来源');
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(727:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        // 钱包支付选项
        this.PaymentMethodCard.bind(this)('wallet', '钱包余额', '💰', `可用余额：¥${this.walletBalance.toFixed(2)}`);
        // 银行卡支付选项
        this.PaymentMethodCard.bind(this)('bankcard', '银行卡支付', '💳', this.getBankCardDescription());
        Column.pop();
    }
    PaymentMethodCard(method: string, title: string, icon: string, description: string, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentPage.ets(754:5)", "entry");
            Row.width('100%');
            Row.height(80);
            Row.padding(16);
            Row.margin({ bottom: 12 });
            Row.borderRadius(12);
            Row.backgroundColor(this.paymentMethod === method ? '#E3F2FD' : '#F8F9FA');
            Row.border({
                width: 2,
                color: this.paymentMethod === method ? '#1976D2' : 'transparent'
            });
            Row.onClick(() => {
                this.paymentMethod = method;
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 选择指示器
            Radio.create({ value: method, group: 'paymentMethod' });
            Radio.debugLine("entry/src/main/ets/pages/PaymentPage.ets(756:7)", "entry");
            // 选择指示器
            Radio.checked(this.paymentMethod === method);
            // 选择指示器
            Radio.onChange((isChecked: boolean) => {
                if (isChecked) {
                    this.paymentMethod = method;
                }
            });
            // 选择指示器
            Radio.margin({ right: 16 });
        }, Radio);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 图标区域
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(766:7)", "entry");
            // 图标区域
            Column.width(50);
            // 图标区域
            Column.height(50);
            // 图标区域
            Column.justifyContent(FlexAlign.Center);
            // 图标区域
            Column.borderRadius(25);
            // 图标区域
            Column.backgroundColor(this.paymentMethod === method ? '#1976D2' : '#F0F0F0');
            // 图标区域
            Column.margin({ right: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(icon);
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(767:9)", "entry");
            Text.fontSize(28);
        }, Text);
        Text.pop();
        // 图标区域
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 内容区域
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentPage.ets(778:7)", "entry");
            // 内容区域
            Column.layoutWeight(1);
            // 内容区域
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(779:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Bold);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(description);
            Text.debugLine("entry/src/main/ets/pages/PaymentPage.ets(786:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.maxLines(1);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
        }, Text);
        Text.pop();
        // 内容区域
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 操作按钮
            if (method === "bankcard") {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('选择');
                        Button.debugLine("entry/src/main/ets/pages/PaymentPage.ets(798:9)", "entry");
                        Button.fontSize(12);
                        Button.fontColor('#1976D2');
                        Button.backgroundColor('#E3F2FD');
                        Button.borderRadius(6);
                        Button.padding({ left: 12, right: 12, top: 6, bottom: 6 });
                        Button.onClick(() => {
                            this.showCardSelector = true;
                        });
                    }, Button);
                    Button.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Row.pop();
    }
    /**
     * 处理扫码支付
     */
    private handleQRPayment() {
        try {
            // 模拟扫码功能
            promptAction.showToast({ message: "正在启动扫码功能..." });
            // 这里可以调用相机API进行二维码扫描
            // 现在模拟扫码结果
            setTimeout(() => {
                this.merchantName = "扫码商户 - 星巴克咖啡";
                this.amount = "35.00";
                promptAction.showToast({ message: "扫码成功，已获取商户信息" });
            }, 1500);
        }
        catch (error) {
            console.error("扫码支付失败:", error);
            promptAction.showToast({ message: "扫码失败，请重试" });
        }
    }
    /**
     * 处理NFC支付
     */
    private handleNFCPayment() {
        try {
            // 模拟NFC功能
            promptAction.showToast({ message: "请将设备靠近NFC标签..." });
            // 这里可以调用NFC API
            // 现在模拟NFC读取结果
            setTimeout(() => {
                this.merchantName = "NFC商户 - 便利店";
                this.amount = "12.50";
                promptAction.showToast({ message: "NFC读取成功，已获取商户信息" });
            }, 2000);
        }
        catch (error) {
            console.error("NFC支付失败:", error);
            promptAction.showToast({ message: "NFC读取失败，请重试" });
        }
    }
    /**
     * 获取支付途径显示文本
     */
    private getPaymentChannelText(channel: string): string {
        switch (channel) {
            case "merchant": return "商户付款";
            case "qr": return "扫码付款";
            case "nfc": return "NFC支付";
            default: return "未知途径";
        }
    }
    /**
     * 获取支付方式显示文本
     */
    private getPaymentMethodText(method: string): string {
        switch (method) {
            case "wallet": return "钱包支付";
            case "card": return "银行卡支付";
            default: return "未知方式";
        }
    }
    /**
     * 获取银行卡描述文本
     */
    getBankCardDescription(): string {
        if (this.selectedCard) {
            return `${this.selectedCard.bankName} **** ${this.selectedCard.cardNo.slice(-4)}`;
        }
        else if (this.bankCards.length > 0) {
            return `已绑定 ${this.bankCards.length} 张银行卡`;
        }
        else {
            return "请先添加银行卡";
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "PaymentPage";
    }
}
registerNamedRoute(() => new PaymentPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/PaymentPage", pageFullPath: "entry/src/main/ets/pages/PaymentPage", integratedHsp: "false", moduleType: "followWithHap" });
