if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface MainPage_Params {
    currentTabIndex?: number;
    userInfo?: LocalUserInfo | null;
    walletBalance?: LocalWalletInfo | null;
    pageLoadingState?: LoadingState;
    userLoadingState?: LoadingState;
    walletLoadingState?: LoadingState;
    transactions?: Transaction[];
    bankCards?: BankCard[];
    transactionLoading?: boolean;
    bankCardLoading?: boolean;
    bankCardRefreshFlag?: number;
}
import router from "@ohos:router";
import { storageManager } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
import type { LocalUserInfo, LocalWalletInfo } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
import { httpClient } from "@normalized:N&&&entry/src/main/ets/common/http/HttpClient&";
import { errorHandler } from "@normalized:N&&&entry/src/main/ets/common/utils/ErrorHandler&";
import { globalStateManager, RefreshTypes } from "@normalized:N&&&entry/src/main/ets/common/utils/EventManager&";
import { LoadingState, TransactionType, PaymentMethod, TransactionStatus, BankCardType, BankCardStatus } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import type { UserInfo, WalletInfo, Transaction, BankCard, SpringBootUserResponse, SpringBootAccountResponse, SpringBootTransactionResponse, SpringBootBankCardResponse } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import { tempDataManager } from "@normalized:N&&&entry/src/main/ets/common/storage/TempDataManager&";
class MainPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__currentTabIndex = new ObservedPropertySimplePU(0, this, "currentTabIndex");
        this.__userInfo = new ObservedPropertyObjectPU(null, this, "userInfo");
        this.__walletBalance = new ObservedPropertyObjectPU(null, this, "walletBalance");
        this.__pageLoadingState = new ObservedPropertySimplePU(LoadingState.IDLE, this, "pageLoadingState");
        this.__userLoadingState = new ObservedPropertySimplePU(LoadingState.IDLE, this, "userLoadingState");
        this.__walletLoadingState = new ObservedPropertySimplePU(LoadingState.IDLE, this, "walletLoadingState");
        this.__transactions = new ObservedPropertyObjectPU([], this, "transactions");
        this.__bankCards = new ObservedPropertyObjectPU([], this, "bankCards");
        this.__transactionLoading = new ObservedPropertySimplePU(false, this, "transactionLoading");
        this.__bankCardLoading = new ObservedPropertySimplePU(false, this, "bankCardLoading");
        this.__bankCardRefreshFlag = new ObservedPropertySimplePU(0, this, "bankCardRefreshFlag");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: MainPage_Params) {
        if (params.currentTabIndex !== undefined) {
            this.currentTabIndex = params.currentTabIndex;
        }
        if (params.userInfo !== undefined) {
            this.userInfo = params.userInfo;
        }
        if (params.walletBalance !== undefined) {
            this.walletBalance = params.walletBalance;
        }
        if (params.pageLoadingState !== undefined) {
            this.pageLoadingState = params.pageLoadingState;
        }
        if (params.userLoadingState !== undefined) {
            this.userLoadingState = params.userLoadingState;
        }
        if (params.walletLoadingState !== undefined) {
            this.walletLoadingState = params.walletLoadingState;
        }
        if (params.transactions !== undefined) {
            this.transactions = params.transactions;
        }
        if (params.bankCards !== undefined) {
            this.bankCards = params.bankCards;
        }
        if (params.transactionLoading !== undefined) {
            this.transactionLoading = params.transactionLoading;
        }
        if (params.bankCardLoading !== undefined) {
            this.bankCardLoading = params.bankCardLoading;
        }
        if (params.bankCardRefreshFlag !== undefined) {
            this.bankCardRefreshFlag = params.bankCardRefreshFlag;
        }
    }
    updateStateVars(params: MainPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__currentTabIndex.purgeDependencyOnElmtId(rmElmtId);
        this.__userInfo.purgeDependencyOnElmtId(rmElmtId);
        this.__walletBalance.purgeDependencyOnElmtId(rmElmtId);
        this.__pageLoadingState.purgeDependencyOnElmtId(rmElmtId);
        this.__userLoadingState.purgeDependencyOnElmtId(rmElmtId);
        this.__walletLoadingState.purgeDependencyOnElmtId(rmElmtId);
        this.__transactions.purgeDependencyOnElmtId(rmElmtId);
        this.__bankCards.purgeDependencyOnElmtId(rmElmtId);
        this.__transactionLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__bankCardLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__bankCardRefreshFlag.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__currentTabIndex.aboutToBeDeleted();
        this.__userInfo.aboutToBeDeleted();
        this.__walletBalance.aboutToBeDeleted();
        this.__pageLoadingState.aboutToBeDeleted();
        this.__userLoadingState.aboutToBeDeleted();
        this.__walletLoadingState.aboutToBeDeleted();
        this.__transactions.aboutToBeDeleted();
        this.__bankCards.aboutToBeDeleted();
        this.__transactionLoading.aboutToBeDeleted();
        this.__bankCardLoading.aboutToBeDeleted();
        this.__bankCardRefreshFlag.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __currentTabIndex: ObservedPropertySimplePU<number>;
    get currentTabIndex() {
        return this.__currentTabIndex.get();
    }
    set currentTabIndex(newValue: number) {
        this.__currentTabIndex.set(newValue);
    }
    private __userInfo: ObservedPropertyObjectPU<LocalUserInfo | null>;
    get userInfo() {
        return this.__userInfo.get();
    }
    set userInfo(newValue: LocalUserInfo | null) {
        this.__userInfo.set(newValue);
    }
    private __walletBalance: ObservedPropertyObjectPU<LocalWalletInfo | null>;
    get walletBalance() {
        return this.__walletBalance.get();
    }
    set walletBalance(newValue: LocalWalletInfo | null) {
        this.__walletBalance.set(newValue);
    }
    private __pageLoadingState: ObservedPropertySimplePU<LoadingState>;
    get pageLoadingState() {
        return this.__pageLoadingState.get();
    }
    set pageLoadingState(newValue: LoadingState) {
        this.__pageLoadingState.set(newValue);
    }
    private __userLoadingState: ObservedPropertySimplePU<LoadingState>;
    get userLoadingState() {
        return this.__userLoadingState.get();
    }
    set userLoadingState(newValue: LoadingState) {
        this.__userLoadingState.set(newValue);
    }
    private __walletLoadingState: ObservedPropertySimplePU<LoadingState>;
    get walletLoadingState() {
        return this.__walletLoadingState.get();
    }
    set walletLoadingState(newValue: LoadingState) {
        this.__walletLoadingState.set(newValue);
    }
    private __transactions: ObservedPropertyObjectPU<Transaction[]>;
    get transactions() {
        return this.__transactions.get();
    }
    set transactions(newValue: Transaction[]) {
        this.__transactions.set(newValue);
    }
    private __bankCards: ObservedPropertyObjectPU<BankCard[]>;
    get bankCards() {
        return this.__bankCards.get();
    }
    set bankCards(newValue: BankCard[]) {
        this.__bankCards.set(newValue);
    }
    private __transactionLoading: ObservedPropertySimplePU<boolean>;
    get transactionLoading() {
        return this.__transactionLoading.get();
    }
    set transactionLoading(newValue: boolean) {
        this.__transactionLoading.set(newValue);
    }
    private __bankCardLoading: ObservedPropertySimplePU<boolean>;
    get bankCardLoading() {
        return this.__bankCardLoading.get();
    }
    set bankCardLoading(newValue: boolean) {
        this.__bankCardLoading.set(newValue);
    }
    private __bankCardRefreshFlag: ObservedPropertySimplePU<number>; // 用于强制刷新银行卡列表
    get bankCardRefreshFlag() {
        return this.__bankCardRefreshFlag.get();
    }
    set bankCardRefreshFlag(newValue: number) {
        this.__bankCardRefreshFlag.set(newValue);
    }
    aboutToAppear() {
        this.loadUserData();
    }
    onPageShow() {
        // 检查全局状态是否需要刷新
        const refreshInfo = globalStateManager.checkNeedsRefresh();
        if (refreshInfo.needsRefresh) {
            console.log('页面显示，检测到需要刷新:', refreshInfo.refreshType);
            if (refreshInfo.refreshType === RefreshTypes.WALLET || refreshInfo.refreshType === RefreshTypes.TRANSACTION) {
                this.refreshWalletData();
            }
            else {
                this.loadUserData();
            }
        }
        // 检查是否有银行卡添加事件
        const cardAdded = tempDataManager.getData('BANK_CARD_ADDED');
        console.log('MainPage onPageShow - 检查银行卡添加事件:', cardAdded);
        if (cardAdded) {
            console.log('MainPage - 检测到银行卡添加，重新加载银行卡列表');
            // 重新加载银行卡列表
            this.loadBankCards();
            // 设置标志表示MainPage已处理
            tempDataManager.setData('MAIN_PAGE_PROCESSED', true);
        }
        // 检查是否有银行卡解绑事件
        const cardUnbound = tempDataManager.getData('BANK_CARD_UNBOUND');
        console.log('MainPage onPageShow - 检查银行卡解绑事件:', cardUnbound);
        if (cardUnbound) {
            console.log('MainPage - 检测到银行卡解绑，重新加载银行卡列表');
            // 重新加载银行卡列表
            this.loadBankCards();
            // 设置标志表示MainPage已处理解绑事件
            tempDataManager.setData('MAIN_PAGE_UNBOUND_PROCESSED', true);
        }
    }
    async loadUserData() {
        this.pageLoadingState = LoadingState.LOADING;
        try {
            // 检查登录状态
            const isLoggedIn = await storageManager.isLoggedIn();
            if (!isLoggedIn) {
                router.replaceUrl({
                    url: 'pages/LoginPage'
                });
                return;
            }
            // 获取并设置token
            const token = await storageManager.getUserToken();
            if (token) {
                httpClient.setAuthToken(token);
            }
            // 尝试从本地存储获取用户信息
            const cachedUserInfo = await storageManager.getUserInfo();
            const cachedWalletInfo = await storageManager.getWalletInfo();
            if (cachedUserInfo) {
                this.userInfo = cachedUserInfo;
            }
            if (cachedWalletInfo) {
                this.walletBalance = cachedWalletInfo;
            }
            // 使用加载管理器并行加载最新数据
            await Promise.all([
                this.loadUserInfo(),
                this.loadWalletInfo(),
                this.loadTransactions(),
                this.loadBankCards()
            ]);
            this.pageLoadingState = LoadingState.SUCCESS;
        }
        catch (error) {
            console.error('加载用户数据失败:', error);
            this.pageLoadingState = LoadingState.ERROR;
            // 处理认证错误
            await errorHandler.handleError(error as Error, '加载用户数据');
            // 如果是认证错误，清除本地数据并跳转到登录页
            if (error && typeof error === 'object' && this.isAuthError(error)) {
                await storageManager.clearUserData();
                httpClient.clearAuthToken();
                router.replaceUrl({
                    url: 'pages/LoginPage'
                });
            }
        }
    }
    async loadUserInfo() {
        try {
            // 从本地存储获取当前用户ID
            const cachedUserInfo = await storageManager.getUserInfo();
            if (!cachedUserInfo || !cachedUserInfo.userId) {
                console.error('无法获取用户ID');
                return;
            }
            // 调用SpringBoot3后端API获取用户信息
            const response = await httpClient.get<SpringBootUserResponse>(`/user/${cachedUserInfo.userId}`);
            const userInfo: SpringBootUserResponse = response.data;
            this.userInfo = this.convertSpringBootUserToLocal(userInfo);
            await storageManager.saveUserInfo(this.userInfo);
        }
        catch (error) {
            console.error('获取用户信息失败:', error);
        }
    }
    async loadWalletInfo() {
        try {
            // 从本地存储获取当前用户ID
            const cachedUserInfo = await storageManager.getUserInfo();
            if (!cachedUserInfo || !cachedUserInfo.userId) {
                console.error('无法获取用户ID');
                return;
            }
            // 调用SpringBoot3后端API获取账户信息
            const response = await httpClient.get<SpringBootAccountResponse>(`/account/${cachedUserInfo.userId}`);
            const accountInfo: SpringBootAccountResponse = response.data;
            this.walletBalance = this.convertSpringBootAccountToLocal(accountInfo);
            await storageManager.saveWalletInfo(this.walletBalance);
        }
        catch (error) {
            console.error('获取钱包余额失败:', error);
        }
    }
    private convertToLocalUserInfo(userInfo: UserInfo): LocalUserInfo {
        return {
            userId: userInfo.userId,
            phone: userInfo.phone,
            realName: userInfo.realName,
            idCard: userInfo.idCard,
            walletNo: '',
            balance: 0,
            payLimit: userInfo.payLimit,
            status: 1,
            createTime: userInfo.createTime,
            updateTime: userInfo.updateTime
        };
    }
    private convertToLocalWalletInfo(walletInfo: WalletInfo): LocalWalletInfo {
        return {
            walletNo: walletInfo.walletNo,
            balance: walletInfo.balance,
            status: 1
        };
    }
    /**
     * 转换SpringBoot3用户信息到本地格式
     */
    private convertSpringBootUserToLocal(userInfo: SpringBootUserResponse): LocalUserInfo {
        return {
            userId: userInfo.userId || 0,
            phone: userInfo.phone || '',
            realName: userInfo.realName || userInfo.username || '用户',
            idCard: userInfo.idCard || '',
            walletNo: '',
            balance: 0,
            payLimit: userInfo.payLimit || 1000,
            status: userInfo.status || 1,
            createTime: userInfo.createdAt || userInfo.createTime || '',
            updateTime: userInfo.updatedAt || userInfo.updateTime || ''
        };
    }
    /**
     * 转换SpringBoot3账户信息到本地钱包格式
     */
    private convertSpringBootAccountToLocal(accountInfo: SpringBootAccountResponse): LocalWalletInfo {
        return {
            walletNo: accountInfo.accountNo || '',
            balance: accountInfo.balance || 0,
            status: accountInfo.status || 1
        };
    }
    /**
     * 转换SpringBoot3交易记录到本地格式
     */
    private convertSpringBootTransactionsToLocal(transactionList: SpringBootTransactionResponse[]): Transaction[] {
        return transactionList.map((transaction: SpringBootTransactionResponse): Transaction => ({
            transactionId: transaction.transactionId || 0,
            transactionNo: transaction.transactionNo || '',
            fromUserId: transaction.fromUserId || transaction.userId || 0,
            toUserId: transaction.toUserId || 0,
            amount: transaction.amount || 0,
            transactionType: this.mapTransactionTypeToEnum(transaction.type),
            paymentMethod: this.mapPaymentMethod(transaction.paymentMethod || 1),
            description: transaction.description || '',
            status: this.mapTransactionStatusToEnum(transaction.status),
            createTime: transaction.createdAt || transaction.createTime || '',
            relatedCardId: transaction.relatedCardId,
            cardNo: transaction.cardNo,
            statusDesc: this.mapTransactionStatus(transaction.status),
            maskedCardNo: transaction.cardNo ? this.maskCardNo(transaction.cardNo) : undefined,
            fromUserName: transaction.fromUserName,
            toUserName: transaction.toUserName
        }));
    }
    /**
     * 转换SpringBoot3银行卡到本地格式
     */
    private convertSpringBootBankCardsToLocal(bankCardList: SpringBootBankCardResponse[]): BankCard[] {
        return bankCardList.map((card: SpringBootBankCardResponse): BankCard => ({
            cardId: card.cardId || 0,
            userId: card.userId || 0,
            cardNo: card.cardNumber || '',
            cardType: this.mapBankCardType(card.cardType),
            bankName: card.bankName || '',
            holderName: card.cardHolder || '',
            isBound: card.status === 1 ? BankCardStatus.BOUND : BankCardStatus.UNBOUND,
            createTime: card.createdAt || card.createTime || '',
            updateTime: card.updatedAt || card.updateTime || '',
            maskedCardNo: card.cardNumber ? this.maskCardNo(card.cardNumber) : undefined
        }));
    }
    /**
     * 映射银行卡类型
     */
    private mapBankCardType(cardType: string | number | undefined): BankCardType {
        if (typeof cardType === 'number') {
            // 数字类型：1=储蓄卡，2=信用卡
            return cardType === 2 ? BankCardType.CREDIT : BankCardType.DEBIT;
        }
        else if (typeof cardType === 'string') {
            // 字符串类型
            if (cardType === '信用卡' || cardType === 'CREDIT') {
                return BankCardType.CREDIT;
            }
        }
        // 默认返回储蓄卡
        return BankCardType.DEBIT;
    }
    /**
     * 映射交易类型到枚举
     */
    private mapTransactionTypeToEnum(type: number): TransactionType {
        switch (type) {
            case 1: return TransactionType.RECHARGE;
            case 2: return TransactionType.WITHDRAW;
            case 3: return TransactionType.TRANSFER;
            case 4: return TransactionType.RECEIVE;
            case 5: return TransactionType.PAYMENT;
            case 6: return TransactionType.REFUND;
            default: return TransactionType.PAYMENT;
        }
    }
    /**
     * 映射支付方式
     */
    private mapPaymentMethod(method: number): PaymentMethod {
        switch (method) {
            case 1: return PaymentMethod.WALLET;
            case 2: return PaymentMethod.BANK_CARD;
            case 3: return PaymentMethod.THIRD_PARTY;
            case 4: return PaymentMethod.THIRD_PARTY;
            default: return PaymentMethod.WALLET;
        }
    }
    /**
     * 映射交易状态到枚举
     */
    private mapTransactionStatusToEnum(status: number): TransactionStatus {
        switch (status) {
            case 1: return TransactionStatus.SUCCESS;
            case 0: return TransactionStatus.FAILED;
            case 2: return TransactionStatus.PENDING;
            default: return TransactionStatus.PENDING;
        }
    }
    /**
     * 映射交易状态到字符串
     */
    private mapTransactionStatus(status: number): string {
        switch (status) {
            case 1: return '成功';
            case 0: return '失败';
            case 2: return '处理中';
            default: return '未知';
        }
    }
    /**
     * 脱敏银行卡号
     */
    private maskCardNo(cardNo: string): string {
        if (!cardNo || cardNo.length < 8)
            return cardNo;
        return cardNo.replace(/(\d{4})\d*(\d{4})/, '$1****$2');
    }
    private isAuthError(error: object): boolean {
        return typeof (error as AuthErrorType).code === 'number' &&
            (error as AuthErrorType).code === 401;
    }
    async loadTransactions() {
        try {
            this.transactionLoading = true;
            // 从本地存储获取当前用户ID
            const cachedUserInfo = await storageManager.getUserInfo();
            if (!cachedUserInfo || !cachedUserInfo.userId) {
                console.error('无法获取用户ID');
                return;
            }
            // 调用SpringBoot3后端API获取交易记录
            const response = await httpClient.get<SpringBootTransactionResponse[]>(`/transaction/query?userId=${cachedUserInfo.userId}`);
            const transactionList: SpringBootTransactionResponse[] = response.data;
            this.transactions = this.convertSpringBootTransactionsToLocal(transactionList);
        }
        catch (error) {
            console.error('获取交易记录失败:', error);
        }
        finally {
            this.transactionLoading = false;
        }
    }
    async loadBankCards() {
        try {
            console.log('MainPage - 开始加载银行卡列表');
            this.bankCardLoading = true;
            // 从本地存储获取当前用户ID
            const cachedUserInfo = await storageManager.getUserInfo();
            if (!cachedUserInfo || !cachedUserInfo.userId) {
                console.error('无法获取用户ID');
                return;
            }
            // 调用SpringBoot3后端API获取银行卡列表
            const response = await httpClient.get<SpringBootBankCardResponse[]>(`/bank-card/user/${cachedUserInfo.userId}`);
            const bankCardList: SpringBootBankCardResponse[] = response.data;
            this.bankCards = this.convertSpringBootBankCardsToLocal(bankCardList);
            this.bankCardRefreshFlag++; // 强制界面刷新
            console.log('MainPage - 银行卡列表加载完成，数量:', this.bankCards.length, 'refreshFlag:', this.bankCardRefreshFlag);
        }
        catch (error) {
            console.error('获取银行卡列表失败:', error);
        }
        finally {
            this.bankCardLoading = false;
        }
    }
    async refreshWalletData() {
        try {
            console.log('刷新钱包数据...');
            // 并行刷新钱包余额和交易记录
            await Promise.all([
                this.loadWalletInfo(),
                this.loadTransactions()
            ]);
            console.log('钱包数据刷新完成');
        }
        catch (error) {
            console.error('刷新钱包数据失败:', error);
        }
    }
    WalletHomeTab(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(425:5)", "entry");
            Column.width('100%');
            Column.height('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.pageLoadingState === LoadingState.LOADING) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.LoadingView.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.WalletContent.bind(this)();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    LoadingView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(438:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            LoadingProgress.create();
            LoadingProgress.debugLine("entry/src/main/ets/pages/MainPage.ets(439:7)", "entry");
            LoadingProgress.width(40);
            LoadingProgress.height(40);
            LoadingProgress.color('#1976D2');
        }, LoadingProgress);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('加载中...');
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(444:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#999999');
            Text.margin({ top: 16 });
        }, Text);
        Text.pop();
        Column.pop();
    }
    WalletContent(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/MainPage.ets(457:5)", "entry");
            Scroll.scrollable(ScrollDirection.Vertical);
            Scroll.scrollBar(BarState.Off);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(458:7)", "entry");
            Column.padding({ left: 16, right: 16, bottom: 20 });
        }, Column);
        // 钱包卡片
        this.WalletCard.bind(this)();
        // 快捷操作
        this.QuickActions.bind(this)();
        // 最近交易
        this.RecentTransactions.bind(this)();
        Column.pop();
        Scroll.pop();
    }
    WalletCard(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(476:5)", "entry");
            Column.width('100%');
            Column.padding(20);
            Column.borderRadius(16);
            Column.backgroundColor('#1976D2');
            Column.margin({ top: 20, bottom: 24 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部用户信息
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(478:7)", "entry");
            // 顶部用户信息
            Row.width('100%');
            // 顶部用户信息
            Row.margin({ bottom: 24 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(479:9)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`你好，${this.userInfo?.realName || '用户'}`);
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(480:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Medium);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.userInfo?.phone || '');
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(485:11)", "entry");
            Text.fontSize(12);
            Text.fontColor('#E3F2FD');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(493:9)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('⚙️');
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(496:11)", "entry");
            Text.fontSize(24);
            Text.fontColor('#FFFFFF');
            Text.onClick(() => {
                router.pushUrl({
                    url: 'pages/SettingsPage'
                });
            });
        }, Text);
        Text.pop();
        Row.pop();
        // 顶部用户信息
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 余额显示
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(510:7)", "entry");
            // 余额显示
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包余额');
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(511:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#E3F2FD');
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`¥${(this.walletBalance?.balance ?? 0).toFixed(2)}`);
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(516:9)", "entry");
            Text.fontSize(32);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Bold);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`钱包号：${this.walletBalance?.walletNo || ''}`);
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(521:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#E3F2FD');
            Text.margin({ top: 8 });
        }, Text);
        Text.pop();
        // 余额显示
        Column.pop();
        Column.pop();
    }
    QuickActions(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(537:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 24 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('快捷操作');
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(538:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(545:7)", "entry");
            Row.width('100%');
            Row.justifyContent(FlexAlign.SpaceEvenly);
        }, Row);
        this.ActionButton.bind(this)('充值', { "id": 16777237, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" }, () => {
            router.pushUrl({
                url: 'pages/WalletOperationPage',
                params: { operationType: 'recharge' }
            });
        });
        this.ActionButton.bind(this)('提现', { "id": 16777234, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" }, () => {
            router.pushUrl({
                url: 'pages/WalletOperationPage',
                params: { operationType: 'withdraw' }
            });
        });
        this.ActionButton.bind(this)('转账', { "id": ********, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" }, () => {
            router.pushUrl({
                url: 'pages/WalletOperationPage',
                params: { operationType: 'transfer' }
            });
        });
        this.ActionButton.bind(this)('银行卡', { "id": ********, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" }, () => {
            router.pushUrl({ url: 'pages/BankCardPage' });
        });
        Row.pop();
        Column.pop();
    }
    ActionButton(title: string, icon: Resource, onClick: () => void, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(580:5)", "entry");
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/MainPage.ets(581:7)", "entry");
            Button.width(56);
            Button.height(56);
            Button.borderRadius(28);
            Button.backgroundColor('#E3F2FD');
            Button.onClick(onClick);
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create(icon);
            Image.debugLine("entry/src/main/ets/pages/MainPage.ets(582:9)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor('#1976D2');
        }, Image);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(593:7)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 8 });
        }, Text);
        Text.pop();
        Column.pop();
    }
    RecentTransactions(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(603:5)", "entry");
            Column.width('100%');
            Column.padding(20);
            Column.borderRadius(12);
            Column.backgroundColor('#FFFFFF');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(604:7)", "entry");
            Row.width('100%');
            Row.margin({ bottom: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('最近交易');
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(605:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('查看全部');
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(611:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#1976D2');
            Text.onClick(() => {
                this.currentTabIndex = 1; // 切换到交易记录tab
            });
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 显示最近的交易记录
            if (this.transactionLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/MainPage.ets(623:9)", "entry");
                        Row.width('100%');
                        Row.height(80);
                        Row.justifyContent(FlexAlign.Center);
                        Row.backgroundColor('#F8F9FA');
                        Row.borderRadius(8);
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.debugLine("entry/src/main/ets/pages/MainPage.ets(624:11)", "entry");
                        LoadingProgress.width(20);
                        LoadingProgress.height(20);
                        LoadingProgress.color('#1976D2');
                        LoadingProgress.margin({ right: 8 });
                    }, LoadingProgress);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('加载中...');
                        Text.debugLine("entry/src/main/ets/pages/MainPage.ets(630:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                    }, Text);
                    Text.pop();
                    Row.pop();
                });
            }
            else if (this.transactions.length === 0) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/MainPage.ets(640:9)", "entry");
                        Column.width('100%');
                        Column.height(100);
                        Column.justifyContent(FlexAlign.Center);
                        Column.alignItems(HorizontalAlign.Center);
                        Column.backgroundColor('#F8F9FA');
                        Column.borderRadius(8);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无交易记录');
                        Text.debugLine("entry/src/main/ets/pages/MainPage.ets(641:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                        Text.margin({ bottom: 12 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('去充值');
                        Button.debugLine("entry/src/main/ets/pages/MainPage.ets(646:11)", "entry");
                        Button.fontSize(12);
                        Button.fontColor('#1976D2');
                        Button.backgroundColor('#E3F2FD');
                        Button.borderRadius(6);
                        Button.height(32);
                        Button.onClick(() => {
                            router.pushUrl({ url: 'pages/RechargePage' });
                        });
                    }, Button);
                    Button.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/MainPage.ets(663:9)", "entry");
                        Column.width('100%');
                        Column.backgroundColor('#F8F9FA');
                        Column.borderRadius(8);
                        Column.padding(12);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = (_item, index: number) => {
                            const transaction = _item;
                            this.RecentTransactionItem.bind(this)(transaction);
                        };
                        this.forEachUpdateFunction(elmtId, this.transactions.slice(0, 3), forEachItemGenFunction, undefined, true, false);
                    }, ForEach);
                    ForEach.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.transactions.length > 3) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create(`还有${this.transactions.length - 3}条交易记录`);
                                    Text.debugLine("entry/src/main/ets/pages/MainPage.ets(669:13)", "entry");
                                    Text.fontSize(12);
                                    Text.fontColor('#999999');
                                    Text.textAlign(TextAlign.Center);
                                    Text.width('100%');
                                    Text.margin({ top: 8 });
                                    Text.onClick(() => {
                                        this.currentTabIndex = 1; // 切换到交易记录tab
                                    });
                                }, Text);
                                Text.pop();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(693:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Tabs.create({ barPosition: BarPosition.End, index: this.currentTabIndex });
            Tabs.debugLine("entry/src/main/ets/pages/MainPage.ets(694:7)", "entry");
            Tabs.onChange((index: number) => {
                console.log('Tab切换到:', index);
                this.currentTabIndex = index;
                // 如果切换到银行卡tab，检查是否需要刷新
                if (index === 2) {
                    // 检查银行卡添加事件
                    const cardAdded = tempDataManager.getData('BANK_CARD_ADDED');
                    const mainPageProcessed = tempDataManager.getData('MAIN_PAGE_PROCESSED');
                    console.log('切换到银行卡tab，检查银行卡添加事件:', cardAdded, 'MainPage已处理:', mainPageProcessed);
                    if (cardAdded && !mainPageProcessed) {
                        console.log('检测到银行卡添加且MainPage未处理，重新加载银行卡列表');
                        this.loadBankCards();
                        // 设置标志表示MainPage已处理
                        tempDataManager.setData('MAIN_PAGE_PROCESSED', true);
                    }
                    // 检查银行卡解绑事件
                    const cardUnbound = tempDataManager.getData('BANK_CARD_UNBOUND');
                    const mainPageUnboundProcessed = tempDataManager.getData('MAIN_PAGE_UNBOUND_PROCESSED');
                    console.log('切换到银行卡tab，检查银行卡解绑事件:', cardUnbound, 'MainPage已处理解绑:', mainPageUnboundProcessed);
                    if (cardUnbound && !mainPageUnboundProcessed) {
                        console.log('检测到银行卡解绑且MainPage未处理，重新加载银行卡列表');
                        this.loadBankCards();
                        // 设置标志表示MainPage已处理解绑事件
                        tempDataManager.setData('MAIN_PAGE_UNBOUND_PROCESSED', true);
                    }
                    // 如果MainPage已处理添加事件，清理相关标志
                    if (cardAdded && mainPageProcessed) {
                        console.log('MainPage已处理添加事件，清理相关标志');
                        tempDataManager.removeData('BANK_CARD_ADDED');
                        tempDataManager.removeData('MAIN_PAGE_PROCESSED');
                    }
                    // 如果MainPage已处理解绑事件，清理相关标志
                    if (cardUnbound && mainPageUnboundProcessed) {
                        console.log('MainPage已处理解绑事件，清理相关标志');
                        tempDataManager.removeData('BANK_CARD_UNBOUND');
                        tempDataManager.removeData('MAIN_PAGE_UNBOUND_PROCESSED');
                    }
                }
            });
        }, Tabs);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TabContent.create(() => {
                this.WalletHomeTab.bind(this)();
            });
            TabContent.tabBar({ builder: () => {
                    this.TabBuilder.call(this, '钱包', { "id": ********, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" }, 0);
                } });
            TabContent.debugLine("entry/src/main/ets/pages/MainPage.ets(695:9)", "entry");
        }, TabContent);
        TabContent.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TabContent.create(() => {
                this.TransactionTab.bind(this)();
            });
            TabContent.tabBar({ builder: () => {
                    this.TabBuilder.call(this, '交易', { "id": ********, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" }, 1);
                } });
            TabContent.debugLine("entry/src/main/ets/pages/MainPage.ets(700:9)", "entry");
        }, TabContent);
        TabContent.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TabContent.create(() => {
                this.BankCardTab.bind(this)();
            });
            TabContent.tabBar({ builder: () => {
                    this.TabBuilder.call(this, '银行卡', { "id": ********, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" }, 2);
                } });
            TabContent.debugLine("entry/src/main/ets/pages/MainPage.ets(705:9)", "entry");
        }, TabContent);
        TabContent.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TabContent.create(() => {
                this.ProfileTab.bind(this)();
            });
            TabContent.tabBar({ builder: () => {
                    this.TabBuilder.call(this, '我的', { "id": ********, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" }, 3);
                } });
            TabContent.debugLine("entry/src/main/ets/pages/MainPage.ets(710:9)", "entry");
        }, TabContent);
        TabContent.pop();
        Tabs.pop();
        Column.pop();
    }
    TransactionTab(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(768:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易记录标题
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(770:7)", "entry");
            // 交易记录标题
            Row.width('100%');
            // 交易记录标题
            Row.padding({ left: 16, right: 16, top: 16, bottom: 8 });
            // 交易记录标题
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易记录');
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(771:9)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('查看全部');
            Button.debugLine("entry/src/main/ets/pages/MainPage.ets(777:9)", "entry");
            Button.fontSize(14);
            Button.fontColor('#1976D2');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.pushUrl({ url: 'pages/TransactionListPage' });
            });
        }, Button);
        Button.pop();
        // 交易记录标题
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 交易记录列表
            if (this.transactionLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/MainPage.ets(791:9)", "entry");
                        Column.width('100%');
                        Column.height(200);
                        Column.justifyContent(FlexAlign.Center);
                        Column.alignItems(HorizontalAlign.Center);
                        Column.backgroundColor('#FFFFFF');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.debugLine("entry/src/main/ets/pages/MainPage.ets(792:11)", "entry");
                        LoadingProgress.width(40);
                        LoadingProgress.height(40);
                        LoadingProgress.color('#1976D2');
                    }, LoadingProgress);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('加载中...');
                        Text.debugLine("entry/src/main/ets/pages/MainPage.ets(797:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                        Text.margin({ top: 16 });
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
            else if (this.transactions.length === 0) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/MainPage.ets(808:9)", "entry");
                        Column.width('100%');
                        Column.height(200);
                        Column.justifyContent(FlexAlign.Center);
                        Column.alignItems(HorizontalAlign.Center);
                        Column.backgroundColor('#FFFFFF');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无交易记录');
                        Text.debugLine("entry/src/main/ets/pages/MainPage.ets(809:11)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#999999');
                        Text.margin({ bottom: 20 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('去充值');
                        Button.debugLine("entry/src/main/ets/pages/MainPage.ets(814:11)", "entry");
                        Button.fontSize(14);
                        Button.fontColor('#1976D2');
                        Button.backgroundColor('#E3F2FD');
                        Button.borderRadius(8);
                        Button.onClick(() => {
                            router.pushUrl({ url: 'pages/RechargePage' });
                        });
                    }, Button);
                    Button.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        List.create();
                        List.debugLine("entry/src/main/ets/pages/MainPage.ets(829:9)", "entry");
                        List.layoutWeight(1);
                        List.backgroundColor('#FFFFFF');
                        List.padding({ left: 16, right: 16, bottom: 16 });
                    }, List);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = (_item, index: number) => {
                            const transaction = _item;
                            {
                                const itemCreation = (elmtId, isInitialRender) => {
                                    ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                    itemCreation2(elmtId, isInitialRender);
                                    if (!isInitialRender) {
                                        ListItem.pop();
                                    }
                                    ViewStackProcessor.StopGetAccessRecording();
                                };
                                const itemCreation2 = (elmtId, isInitialRender) => {
                                    ListItem.create(deepRenderFunction, true);
                                    ListItem.onClick(() => {
                                        router.pushUrl({
                                            url: 'pages/TransactionDetailPage',
                                            params: {
                                                transactionId: transaction.transactionId
                                            }
                                        });
                                    });
                                    ListItem.debugLine("entry/src/main/ets/pages/MainPage.ets(831:13)", "entry");
                                };
                                const deepRenderFunction = (elmtId, isInitialRender) => {
                                    itemCreation(elmtId, isInitialRender);
                                    this.TransactionItem.bind(this)(transaction);
                                    ListItem.pop();
                                };
                                this.observeComponentCreation2(itemCreation2, ListItem);
                                ListItem.pop();
                            }
                        };
                        this.forEachUpdateFunction(elmtId, this.transactions, forEachItemGenFunction, undefined, true, false);
                    }, ForEach);
                    ForEach.pop();
                    List.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    BankCardTab(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(856:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡标题
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(858:7)", "entry");
            // 银行卡标题
            Row.width('100%');
            // 银行卡标题
            Row.padding({ left: 16, right: 16, top: 16, bottom: 8 });
            // 银行卡标题
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我的银行卡');
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(859:9)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('管理');
            Button.debugLine("entry/src/main/ets/pages/MainPage.ets(865:9)", "entry");
            Button.fontSize(14);
            Button.fontColor('#1976D2');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.pushUrl({ url: 'pages/BankCardPage' });
            });
        }, Button);
        Button.pop();
        // 银行卡标题
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 银行卡列表
            if (this.bankCardLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/MainPage.ets(879:9)", "entry");
                        Column.width('100%');
                        Column.height(200);
                        Column.justifyContent(FlexAlign.Center);
                        Column.alignItems(HorizontalAlign.Center);
                        Column.backgroundColor('#FFFFFF');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.debugLine("entry/src/main/ets/pages/MainPage.ets(880:11)", "entry");
                        LoadingProgress.width(40);
                        LoadingProgress.height(40);
                        LoadingProgress.color('#1976D2');
                    }, LoadingProgress);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('加载中...');
                        Text.debugLine("entry/src/main/ets/pages/MainPage.ets(885:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                        Text.margin({ top: 16 });
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
            else if (this.bankCards.length === 0) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/MainPage.ets(896:9)", "entry");
                        Column.width('100%');
                        Column.height(200);
                        Column.justifyContent(FlexAlign.Center);
                        Column.alignItems(HorizontalAlign.Center);
                        Column.backgroundColor('#FFFFFF');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无银行卡');
                        Text.debugLine("entry/src/main/ets/pages/MainPage.ets(897:11)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#999999');
                        Text.margin({ bottom: 20 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('添加银行卡');
                        Button.debugLine("entry/src/main/ets/pages/MainPage.ets(902:11)", "entry");
                        Button.fontSize(14);
                        Button.fontColor('#1976D2');
                        Button.backgroundColor('#E3F2FD');
                        Button.borderRadius(8);
                        Button.onClick(() => {
                            router.pushUrl({ url: 'pages/AddBankCardPage' });
                        });
                    }, Button);
                    Button.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        List.create();
                        List.debugLine("entry/src/main/ets/pages/MainPage.ets(917:9)", "entry");
                        List.layoutWeight(1);
                        List.backgroundColor('#FFFFFF');
                        List.padding({ left: 16, right: 16, bottom: 16 });
                    }, List);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = (_item, index: number) => {
                            const card = _item;
                            {
                                const itemCreation = (elmtId, isInitialRender) => {
                                    ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                    itemCreation2(elmtId, isInitialRender);
                                    if (!isInitialRender) {
                                        ListItem.pop();
                                    }
                                    ViewStackProcessor.StopGetAccessRecording();
                                };
                                const itemCreation2 = (elmtId, isInitialRender) => {
                                    ListItem.create(deepRenderFunction, true);
                                    ListItem.onClick(() => {
                                        router.pushUrl({ url: 'pages/BankCardPage' });
                                    });
                                    ListItem.debugLine("entry/src/main/ets/pages/MainPage.ets(919:13)", "entry");
                                };
                                const deepRenderFunction = (elmtId, isInitialRender) => {
                                    itemCreation(elmtId, isInitialRender);
                                    this.BankCardItem.bind(this)(card, this.bankCardRefreshFlag);
                                    ListItem.pop();
                                };
                                this.observeComponentCreation2(itemCreation2, ListItem);
                                ListItem.pop();
                            }
                        };
                        this.forEachUpdateFunction(elmtId, this.bankCards, forEachItemGenFunction, (card: BankCard, index: number) => `${card.cardId}_${this.bankCardRefreshFlag}`, true, true);
                    }, ForEach);
                    ForEach.pop();
                    List.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    ProfileTab(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/MainPage.ets(939:5)", "entry");
            Scroll.width('100%');
            Scroll.height('100%');
            Scroll.backgroundColor('#F5F5F5');
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(940:7)", "entry");
            Column.padding({ left: 16, right: 16, bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 用户信息卡片
            if (this.userInfo) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/MainPage.ets(943:11)", "entry");
                        Column.width('100%');
                        Column.padding(20);
                        Column.borderRadius(12);
                        Column.backgroundColor('#FFFFFF');
                        Column.margin({ top: 16 });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/MainPage.ets(944:13)", "entry");
                        Row.width('100%');
                        Row.onClick(() => {
                            router.pushUrl({ url: 'pages/SettingsPage' });
                        });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 头像
                        Text.create(this.userInfo.realName?.charAt(0) || '用');
                        Text.debugLine("entry/src/main/ets/pages/MainPage.ets(946:15)", "entry");
                        // 头像
                        Text.fontSize(24);
                        // 头像
                        Text.fontColor('#FFFFFF');
                        // 头像
                        Text.fontWeight(FontWeight.Bold);
                        // 头像
                        Text.width(60);
                        // 头像
                        Text.height(60);
                        // 头像
                        Text.textAlign(TextAlign.Center);
                        // 头像
                        Text.backgroundColor('#1976D2');
                        // 头像
                        Text.borderRadius(30);
                    }, Text);
                    // 头像
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/MainPage.ets(956:15)", "entry");
                        Column.layoutWeight(1);
                        Column.alignItems(HorizontalAlign.Start);
                        Column.margin({ left: 16 });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.userInfo.realName || '用户');
                        Text.debugLine("entry/src/main/ets/pages/MainPage.ets(957:17)", "entry");
                        Text.fontSize(18);
                        Text.fontColor('#333333');
                        Text.fontWeight(FontWeight.Medium);
                        Text.alignSelf(ItemAlign.Start);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.maskPhone(this.userInfo.phone));
                        Text.debugLine("entry/src/main/ets/pages/MainPage.ets(963:17)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#666666');
                        Text.alignSelf(ItemAlign.Start);
                        Text.margin({ top: 4 });
                    }, Text);
                    Text.pop();
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('>');
                        Text.debugLine("entry/src/main/ets/pages/MainPage.ets(973:15)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#CCCCCC');
                    }, Text);
                    Text.pop();
                    Row.pop();
                    Column.pop();
                });
            }
            // 银行卡管理
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡管理
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(990:9)", "entry");
            // 银行卡管理
            Column.width('100%');
            // 银行卡管理
            Column.padding(20);
            // 银行卡管理
            Column.borderRadius(12);
            // 银行卡管理
            Column.backgroundColor('#FFFFFF');
            // 银行卡管理
            Column.margin({ top: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(991:11)", "entry");
            Row.width('100%');
            Row.margin({ bottom: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我的银行卡');
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(992:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('管理');
            Button.debugLine("entry/src/main/ets/pages/MainPage.ets(998:13)", "entry");
            Button.fontSize(14);
            Button.fontColor('#1976D2');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.pushUrl({ url: 'pages/BankCardPage' });
            });
        }, Button);
        Button.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.bankCardLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/MainPage.ets(1010:13)", "entry");
                        Row.width('100%');
                        Row.height(60);
                        Row.justifyContent(FlexAlign.Center);
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.debugLine("entry/src/main/ets/pages/MainPage.ets(1011:15)", "entry");
                        LoadingProgress.width(20);
                        LoadingProgress.height(20);
                        LoadingProgress.color('#1976D2');
                        LoadingProgress.margin({ right: 8 });
                    }, LoadingProgress);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('加载中...');
                        Text.debugLine("entry/src/main/ets/pages/MainPage.ets(1017:15)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                    }, Text);
                    Text.pop();
                    Row.pop();
                });
            }
            else if (this.bankCards.length === 0) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/MainPage.ets(1025:13)", "entry");
                        Column.width('100%');
                        Column.height(80);
                        Column.justifyContent(FlexAlign.Center);
                        Column.alignItems(HorizontalAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无银行卡');
                        Text.debugLine("entry/src/main/ets/pages/MainPage.ets(1026:15)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                        Text.margin({ bottom: 12 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('添加银行卡');
                        Button.debugLine("entry/src/main/ets/pages/MainPage.ets(1031:15)", "entry");
                        Button.fontSize(14);
                        Button.fontColor('#1976D2');
                        Button.backgroundColor('#E3F2FD');
                        Button.borderRadius(8);
                        Button.height(36);
                        Button.onClick(() => {
                            router.pushUrl({ url: 'pages/AddBankCardPage' });
                        });
                    }, Button);
                    Button.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/MainPage.ets(1046:13)", "entry");
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = (_item, index: number) => {
                            const card = _item;
                            this.BankCardItem.bind(this)(card, this.bankCardRefreshFlag);
                        };
                        this.forEachUpdateFunction(elmtId, this.bankCards.slice(0, 3), forEachItemGenFunction, (card: BankCard, index: number) => `${card.cardId}_${this.bankCardRefreshFlag}`, true, true);
                    }, ForEach);
                    ForEach.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.bankCards.length > 3) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create(`还有${this.bankCards.length - 3}张银行卡`);
                                    Text.debugLine("entry/src/main/ets/pages/MainPage.ets(1052:17)", "entry");
                                    Text.fontSize(12);
                                    Text.fontColor('#999999');
                                    Text.textAlign(TextAlign.Center);
                                    Text.width('100%');
                                    Text.margin({ top: 8 });
                                }, Text);
                                Text.pop();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        // 银行卡管理
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 功能菜单
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(1069:9)", "entry");
            // 功能菜单
            Column.width('100%');
            // 功能菜单
            Column.padding(20);
            // 功能菜单
            Column.borderRadius(12);
            // 功能菜单
            Column.backgroundColor('#FFFFFF');
            // 功能菜单
            Column.margin({ top: 16 });
        }, Column);
        this.MenuItemBuilder.bind(this)('交易记录', '查看所有交易记录', '📊', () => {
            router.pushUrl({ url: 'pages/TransactionListPage' });
        });
        this.MenuItemBuilder.bind(this)('设置', '账户设置和安全', '⚙️', () => {
            router.pushUrl({ url: 'pages/SettingsPage' });
        });
        this.MenuItemBuilder.bind(this)('帮助中心', '常见问题和使用指南', '❓', () => {
            router.pushUrl({ url: 'pages/HelpCenterPage' });
        });
        this.MenuItemBuilder.bind(this)('关于我们', 'E-Wallet v1.2.0', 'ℹ️', () => {
            router.pushUrl({ url: 'pages/AboutPage' });
        });
        // 功能菜单
        Column.pop();
        Column.pop();
        Scroll.pop();
    }
    RecentTransactionItem(transaction: Transaction, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(1101:5)", "entry");
            Row.width('100%');
            Row.padding(8);
            Row.margin({ bottom: 4 });
            Row.onClick(() => {
                router.pushUrl({
                    url: 'pages/TransactionDetailPage',
                    params: {
                        transactionId: transaction.transactionId
                    }
                });
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易类型图标
            Text.create(this.getTransactionIcon(transaction.transactionType));
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(1103:7)", "entry");
            // 交易类型图标
            Text.fontSize(20);
            // 交易类型图标
            Text.width(32);
            // 交易类型图标
            Text.height(32);
            // 交易类型图标
            Text.textAlign(TextAlign.Center);
            // 交易类型图标
            Text.backgroundColor('#FFFFFF');
            // 交易类型图标
            Text.borderRadius(16);
        }, Text);
        // 交易类型图标
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(1111:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ left: 12 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(1112:9)", "entry");
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(transaction.transactionType);
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(1113:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.formatAmount(transaction));
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(1119:11)", "entry");
            Text.fontSize(14);
            Text.fontColor(this.getAmountColor(transaction));
            Text.fontWeight(FontWeight.Medium);
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.formatDateTime(transaction.createTime));
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(1126:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ top: 2 });
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
    }
    TransactionItem(transaction: Transaction, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(1151:5)", "entry");
            Row.width('100%');
            Row.padding(12);
            Row.margin({ bottom: 8 });
            Row.borderRadius(8);
            Row.backgroundColor('#F8F9FA');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易类型图标
            Text.create(this.getTransactionIcon(transaction.transactionType));
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(1153:7)", "entry");
            // 交易类型图标
            Text.fontSize(24);
            // 交易类型图标
            Text.width(40);
            // 交易类型图标
            Text.height(40);
            // 交易类型图标
            Text.textAlign(TextAlign.Center);
            // 交易类型图标
            Text.backgroundColor('#F0F0F0');
            // 交易类型图标
            Text.borderRadius(20);
        }, Text);
        // 交易类型图标
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(1161:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ left: 12 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(1162:9)", "entry");
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(transaction.transactionType);
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(1163:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.formatAmount(transaction));
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(1169:11)", "entry");
            Text.fontSize(16);
            Text.fontColor(this.getAmountColor(transaction));
            Text.fontWeight(FontWeight.Medium);
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(1176:9)", "entry");
            Row.width('100%');
            Row.margin({ top: 4 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.formatDateTime(transaction.createTime));
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(1177:11)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(transaction.status);
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(1182:11)", "entry");
            Text.fontSize(12);
            Text.fontColor(this.getStatusColor(transaction.status));
        }, Text);
        Text.pop();
        Row.pop();
        Column.pop();
        Row.pop();
    }
    BankCardItem(card: BankCard, refreshFlag?: number, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(1202:5)", "entry");
            Row.width('100%');
            Row.padding(12);
            Row.margin({ bottom: 8 });
            Row.borderRadius(8);
            Row.backgroundColor('#F8F9FA');
            Row.onClick(() => {
                router.pushUrl({ url: 'pages/BankCardPage' });
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💳');
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(1203:7)", "entry");
            Text.fontSize(24);
            Text.width(40);
            Text.height(40);
            Text.textAlign(TextAlign.Center);
            Text.backgroundColor('#E3F2FD');
            Text.borderRadius(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(1211:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ left: 12 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.bankName);
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(1212:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`${card.cardType} **** ${card.cardNo.slice(-4)}`);
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(1218:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('>');
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(1228:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#CCCCCC');
        }, Text);
        Text.pop();
        Row.pop();
    }
    MenuItemBuilder(title: string, subtitle: string, icon: string, onClick: () => void, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MainPage.ets(1244:5)", "entry");
            Row.width('100%');
            Row.height(60);
            Row.padding({ top: 8, bottom: 8 });
            Row.onClick(onClick);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(icon);
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(1245:7)", "entry");
            Text.fontSize(20);
            Text.width(40);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(1250:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ left: 12 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(1251:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(subtitle);
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(1256:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('>');
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(1266:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#CCCCCC');
        }, Text);
        Text.pop();
        Row.pop();
    }
    TabBuilder(title: string, icon: Resource, index: number, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MainPage.ets(1278:5)", "entry");
            Column.alignItems(HorizontalAlign.Center);
            Column.padding({ top: 8, bottom: 8 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create(icon);
            Image.debugLine("entry/src/main/ets/pages/MainPage.ets(1279:7)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor(this.currentTabIndex === index ? '#1976D2' : '#999999');
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.debugLine("entry/src/main/ets/pages/MainPage.ets(1284:7)", "entry");
            Text.fontSize(12);
            Text.fontColor(this.currentTabIndex === index ? '#1976D2' : '#999999');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
    }
    // 工具方法
    maskPhone(phone: string): string {
        if (!phone || phone.length !== 11)
            return phone;
        return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    }
    getTransactionIcon(type: string): string {
        switch (type) {
            case '充值': return '⬇️';
            case '提现': return '⬆️';
            case '转账': return '💸';
            case '收钱': return '💰';
            case '支付': return '💳';
            case '退款': return '↩️';
            default: return '📄';
        }
    }
    formatAmount(transaction: Transaction): string {
        // 收入类型：充值、收钱、退款
        const isIncome = transaction.transactionType === TransactionType.RECHARGE ||
            transaction.transactionType === TransactionType.RECEIVE ||
            transaction.transactionType === TransactionType.REFUND;
        const prefix = isIncome ? '+' : '-';
        return `${prefix}¥${transaction.amount.toFixed(2)}`;
    }
    getAmountColor(transaction: Transaction): string {
        // 收入类型显示绿色，支出类型显示黑色
        const isIncome = transaction.transactionType === TransactionType.RECHARGE ||
            transaction.transactionType === TransactionType.RECEIVE ||
            transaction.transactionType === TransactionType.REFUND;
        return isIncome ? '#4CAF50' : '#333333';
    }
    getStatusColor(status: string): string {
        switch (status) {
            case '成功': return '#4CAF50';
            case '失败': return '#F44336';
            case '处理中': return '#FF9800';
            default: return '#666666';
        }
    }
    formatDateTime(dateTime: string): string {
        const date = new Date(dateTime);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        if (diffDays === 0) {
            return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', hour12: false });
        }
        else if (diffDays === 1) {
            return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', hour12: false });
        }
        else {
            return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', hour12: false });
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "MainPage";
    }
}
/**
 * 认证错误类型
 */
interface AuthErrorType {
    code: number;
}
registerNamedRoute(() => new MainPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/MainPage", pageFullPath: "entry/src/main/ets/pages/MainPage", integratedHsp: "false", moduleType: "followWithHap" });
