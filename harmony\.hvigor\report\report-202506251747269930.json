{"version": "2.0", "ppid": 21780, "events": [{"head": {"id": "c1282458-4399-4dc3-8d04-373493f3fa81", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289160482700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fdd4f6b-75f9-4536-9506-cc38e59a000f", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289736744700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d059b28-4e60-4fd4-8ed8-9bd56988208c", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289746938000, "endTime": 31289911070300}, "additional": {"children": ["d8f59f72-0f10-4ee6-8066-2e58300a93f9", "d652eedf-be4c-4055-920d-6e28c654d1e5", "65cfad57-051a-454e-823d-04fef01c4927", "e85e37cc-9fe8-4801-9653-74d7bcef42f0", "4946c40b-ce92-47d3-8754-59e219b906b2", "ff82b2d8-0e81-4f99-a9fe-3afde8fe6c05", "59b2678b-10dc-4038-b59f-a2adcf67bcec"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "3d3bb75b-ea04-4df2-8954-d4a7cd4d362a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8f59f72-0f10-4ee6-8066-2e58300a93f9", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289746939700, "endTime": 31289759748900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d059b28-4e60-4fd4-8ed8-9bd56988208c", "logId": "0fabcf3d-384d-46b4-9ab5-6a331da3e5d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d652eedf-be4c-4055-920d-6e28c654d1e5", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289759769100, "endTime": 31289910023800}, "additional": {"children": ["fa966a52-f1c9-4dda-bbcc-706fdd52638b", "5b328819-adc6-414b-9b7f-99345997c306", "a8bc6953-1b90-4750-8553-a5def411d4ac", "ccccc1b1-bc6f-4825-8ad9-64ed4b043fa6", "5b505401-f9ae-4706-8a25-7178f07ddbdb", "042940a9-b7f7-4263-a7c9-f4f4500993f8", "d591e502-293d-4930-b548-a250bbe11f2e", "8231cde2-92f4-4d91-946e-11d361ac43d9", "e9855014-d361-42b6-a7a7-9701e7601fe9"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d059b28-4e60-4fd4-8ed8-9bd56988208c", "logId": "b081d63d-f4ec-491e-aa6a-4e41275f2a51"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65cfad57-051a-454e-823d-04fef01c4927", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289910039100, "endTime": 31289911063400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d059b28-4e60-4fd4-8ed8-9bd56988208c", "logId": "31bec089-bc7c-4387-9b69-0b96a583173e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e85e37cc-9fe8-4801-9653-74d7bcef42f0", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289911066700, "endTime": 31289911068200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d059b28-4e60-4fd4-8ed8-9bd56988208c", "logId": "84cb1051-7d7b-4b5f-b988-b5b28e8894f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4946c40b-ce92-47d3-8754-59e219b906b2", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289750314600, "endTime": 31289750355300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d059b28-4e60-4fd4-8ed8-9bd56988208c", "logId": "37a91b23-d20a-429b-9f49-2eb6d1e2a3ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "37a91b23-d20a-429b-9f49-2eb6d1e2a3ee", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289750314600, "endTime": 31289750355300}, "additional": {"logType": "info", "children": [], "durationId": "4946c40b-ce92-47d3-8754-59e219b906b2", "parent": "3d3bb75b-ea04-4df2-8954-d4a7cd4d362a"}}, {"head": {"id": "ff82b2d8-0e81-4f99-a9fe-3afde8fe6c05", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289755521900, "endTime": 31289755543000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d059b28-4e60-4fd4-8ed8-9bd56988208c", "logId": "1534694e-4d77-4980-aed8-cab6fd96244a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1534694e-4d77-4980-aed8-cab6fd96244a", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289755521900, "endTime": 31289755543000}, "additional": {"logType": "info", "children": [], "durationId": "ff82b2d8-0e81-4f99-a9fe-3afde8fe6c05", "parent": "3d3bb75b-ea04-4df2-8954-d4a7cd4d362a"}}, {"head": {"id": "5f82785b-7353-4b9d-8ce9-c15cfb2fd3e0", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289755586800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fd86dc1-6a0f-41e3-931f-2f6fa24c349a", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289759636400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fabcf3d-384d-46b4-9ab5-6a331da3e5d3", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289746939700, "endTime": 31289759748900}, "additional": {"logType": "info", "children": [], "durationId": "d8f59f72-0f10-4ee6-8066-2e58300a93f9", "parent": "3d3bb75b-ea04-4df2-8954-d4a7cd4d362a"}}, {"head": {"id": "fa966a52-f1c9-4dda-bbcc-706fdd52638b", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289764854500, "endTime": 31289764862200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d652eedf-be4c-4055-920d-6e28c654d1e5", "logId": "35c0b80c-b6ed-41be-ae6d-f1663ef090f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5b328819-adc6-414b-9b7f-99345997c306", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289764873600, "endTime": 31289769642200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d652eedf-be4c-4055-920d-6e28c654d1e5", "logId": "cdc56375-beda-4e22-aca4-e62fbc891cc5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8bc6953-1b90-4750-8553-a5def411d4ac", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289769661000, "endTime": 31289840388700}, "additional": {"children": ["7750a532-cbba-4c3f-8234-1d1005a42a49", "0ec17940-dda7-49e0-97c8-a0e9e047f32b", "6924e7d5-346e-4da1-974d-61f87afb544b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d652eedf-be4c-4055-920d-6e28c654d1e5", "logId": "11420ada-08cc-474c-a44b-0a34b3c547a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ccccc1b1-bc6f-4825-8ad9-64ed4b043fa6", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289840400900, "endTime": 31289862341900}, "additional": {"children": ["f3d242f6-731a-4f7f-93bc-e0e736b4f536"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d652eedf-be4c-4055-920d-6e28c654d1e5", "logId": "66853e45-ed2b-42e8-bf17-9ab6db19f3df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5b505401-f9ae-4706-8a25-7178f07ddbdb", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289862350800, "endTime": 31289880217900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d652eedf-be4c-4055-920d-6e28c654d1e5", "logId": "68b120a4-387d-45a2-9f5c-833135d75eef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "042940a9-b7f7-4263-a7c9-f4f4500993f8", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289881752100, "endTime": 31289896883700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d652eedf-be4c-4055-920d-6e28c654d1e5", "logId": "e5c52686-88ac-414f-998a-ab0f15174a46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d591e502-293d-4930-b548-a250bbe11f2e", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289896915600, "endTime": 31289909880000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d652eedf-be4c-4055-920d-6e28c654d1e5", "logId": "e0766beb-0735-42ba-8a46-6c5121e65061"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8231cde2-92f4-4d91-946e-11d361ac43d9", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289909899300, "endTime": 31289910004000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d652eedf-be4c-4055-920d-6e28c654d1e5", "logId": "ce2a9ede-aff4-4fbb-ae43-2977a5b92619"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35c0b80c-b6ed-41be-ae6d-f1663ef090f9", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289764854500, "endTime": 31289764862200}, "additional": {"logType": "info", "children": [], "durationId": "fa966a52-f1c9-4dda-bbcc-706fdd52638b", "parent": "b081d63d-f4ec-491e-aa6a-4e41275f2a51"}}, {"head": {"id": "cdc56375-beda-4e22-aca4-e62fbc891cc5", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289764873600, "endTime": 31289769642200}, "additional": {"logType": "info", "children": [], "durationId": "5b328819-adc6-414b-9b7f-99345997c306", "parent": "b081d63d-f4ec-491e-aa6a-4e41275f2a51"}}, {"head": {"id": "7750a532-cbba-4c3f-8234-1d1005a42a49", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289770831400, "endTime": 31289770853600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a8bc6953-1b90-4750-8553-a5def411d4ac", "logId": "4d554688-5d37-4024-b625-a26851e8288f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4d554688-5d37-4024-b625-a26851e8288f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289770831400, "endTime": 31289770853600}, "additional": {"logType": "info", "children": [], "durationId": "7750a532-cbba-4c3f-8234-1d1005a42a49", "parent": "11420ada-08cc-474c-a44b-0a34b3c547a3"}}, {"head": {"id": "0ec17940-dda7-49e0-97c8-a0e9e047f32b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289774696700, "endTime": 31289839759300}, "additional": {"children": ["073ed4c3-34f9-4c71-93b5-b18da3b903cd", "da9c33a9-d33d-4a96-a765-724a1f027252"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a8bc6953-1b90-4750-8553-a5def411d4ac", "logId": "33c99bd6-96bd-4da6-8444-958d79761677"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "073ed4c3-34f9-4c71-93b5-b18da3b903cd", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289774717900, "endTime": 31289778080000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0ec17940-dda7-49e0-97c8-a0e9e047f32b", "logId": "538d7385-0063-4576-b7eb-08887bea08f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da9c33a9-d33d-4a96-a765-724a1f027252", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289778104500, "endTime": 31289839747000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0ec17940-dda7-49e0-97c8-a0e9e047f32b", "logId": "a5dd6881-e435-4023-b332-605e8d8c18b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "125a9431-ca83-44e6-a8f1-e4432d321c7a", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289774723300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9bbc2f0-a0c4-422b-bdfc-0dc08bd61008", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289777932800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "538d7385-0063-4576-b7eb-08887bea08f1", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289774717900, "endTime": 31289778080000}, "additional": {"logType": "info", "children": [], "durationId": "073ed4c3-34f9-4c71-93b5-b18da3b903cd", "parent": "33c99bd6-96bd-4da6-8444-958d79761677"}}, {"head": {"id": "5eee2b5b-d581-496e-bdab-3dd886d9cfb8", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289778117800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78755323-cdda-4f3a-b382-5aad460aabfb", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289786258200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81f32b8e-7b12-46a9-96f0-aea0fc733b1f", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289786405700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e0aa675-f76b-4ebe-8160-99716acf3a32", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289786538200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d67e93d-d86b-45d8-90db-63d2bf7fd8ad", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289787563500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe411151-0747-40c1-a63c-9accf50753a7", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289790224400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fae74bd-3935-4851-a0c8-6d520c33e7f9", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289794844000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0d4e0f6-d533-4dc0-b03e-daea5699e2fb", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289805143400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52a30d56-7c47-42d7-839a-4c8a3a88ac3c", "name": "Sdk init in 26 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289822068700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "123f11bb-1a59-47a5-94f8-07f72e058148", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289822182900}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 47}, "markType": "other"}}, {"head": {"id": "603659a1-9427-4799-80f0-31921ae075bf", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289822195800}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 47}, "markType": "other"}}, {"head": {"id": "8610619e-acdf-4dcf-85ed-c96b2354c6e0", "name": "Project task initialization takes 17 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289839562400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "432484ff-9867-4a31-b9c2-a8dff29069bd", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289839659500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a50f325a-fc3a-4a07-8708-ed8ad59dbc91", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289839691400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e32f45f8-7623-4291-94cd-c25b12e898e2", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289839716500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5dd6881-e435-4023-b332-605e8d8c18b4", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289778104500, "endTime": 31289839747000}, "additional": {"logType": "info", "children": [], "durationId": "da9c33a9-d33d-4a96-a765-724a1f027252", "parent": "33c99bd6-96bd-4da6-8444-958d79761677"}}, {"head": {"id": "33c99bd6-96bd-4da6-8444-958d79761677", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289774696700, "endTime": 31289839759300}, "additional": {"logType": "info", "children": ["538d7385-0063-4576-b7eb-08887bea08f1", "a5dd6881-e435-4023-b332-605e8d8c18b4"], "durationId": "0ec17940-dda7-49e0-97c8-a0e9e047f32b", "parent": "11420ada-08cc-474c-a44b-0a34b3c547a3"}}, {"head": {"id": "6924e7d5-346e-4da1-974d-61f87afb544b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289840364500, "endTime": 31289840377000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a8bc6953-1b90-4750-8553-a5def411d4ac", "logId": "11bc6584-e919-4be7-843f-900b406e0679"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11bc6584-e919-4be7-843f-900b406e0679", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289840364500, "endTime": 31289840377000}, "additional": {"logType": "info", "children": [], "durationId": "6924e7d5-346e-4da1-974d-61f87afb544b", "parent": "11420ada-08cc-474c-a44b-0a34b3c547a3"}}, {"head": {"id": "11420ada-08cc-474c-a44b-0a34b3c547a3", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289769661000, "endTime": 31289840388700}, "additional": {"logType": "info", "children": ["4d554688-5d37-4024-b625-a26851e8288f", "33c99bd6-96bd-4da6-8444-958d79761677", "11bc6584-e919-4be7-843f-900b406e0679"], "durationId": "a8bc6953-1b90-4750-8553-a5def411d4ac", "parent": "b081d63d-f4ec-491e-aa6a-4e41275f2a51"}}, {"head": {"id": "f3d242f6-731a-4f7f-93bc-e0e736b4f536", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289840959600, "endTime": 31289862330500}, "additional": {"children": ["afa7db43-d59e-48e1-baf6-4972656891be", "5b4cc54e-18dc-4e14-aa6f-01e9d76a4620", "09cff1e1-c5fc-4b02-a8de-be42bac41a3e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ccccc1b1-bc6f-4825-8ad9-64ed4b043fa6", "logId": "5fbf7d0b-758e-4f15-acc8-8dded85d374b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "afa7db43-d59e-48e1-baf6-4972656891be", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289844560700, "endTime": 31289844577700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f3d242f6-731a-4f7f-93bc-e0e736b4f536", "logId": "09325075-3c8b-411c-bb79-d9a2c19a14b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "09325075-3c8b-411c-bb79-d9a2c19a14b9", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289844560700, "endTime": 31289844577700}, "additional": {"logType": "info", "children": [], "durationId": "afa7db43-d59e-48e1-baf6-4972656891be", "parent": "5fbf7d0b-758e-4f15-acc8-8dded85d374b"}}, {"head": {"id": "5b4cc54e-18dc-4e14-aa6f-01e9d76a4620", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289847525400, "endTime": 31289860551300}, "additional": {"children": ["a0bd738c-b820-48d4-9e34-531184d00247", "19cd4654-4486-4097-8fd1-b0cde3e5381d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f3d242f6-731a-4f7f-93bc-e0e736b4f536", "logId": "4b2e9a5b-7244-48b7-a4a0-731a6b59998b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0bd738c-b820-48d4-9e34-531184d00247", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289847526900, "endTime": 31289850051100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5b4cc54e-18dc-4e14-aa6f-01e9d76a4620", "logId": "c373ffef-216e-47a1-836f-320470233c33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19cd4654-4486-4097-8fd1-b0cde3e5381d", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289850070500, "endTime": 31289860489300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5b4cc54e-18dc-4e14-aa6f-01e9d76a4620", "logId": "ef4af085-8089-47d1-8e70-b23db779018f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "acff6f9a-fd69-47ad-94cd-fdced09dad90", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289847530900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48107a6a-38db-4fbb-90ef-95c81d544304", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289849936200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c373ffef-216e-47a1-836f-320470233c33", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289847526900, "endTime": 31289850051100}, "additional": {"logType": "info", "children": [], "durationId": "a0bd738c-b820-48d4-9e34-531184d00247", "parent": "4b2e9a5b-7244-48b7-a4a0-731a6b59998b"}}, {"head": {"id": "e98f33e0-c4d5-484c-a0df-c3ab90a320c7", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289850112300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0183d52-0663-420a-9513-68e448434ad4", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289855955100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aee90bb1-036b-4ae8-92ee-7789455e9d9d", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289856061100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ad4cd9c-8cab-4a19-b91f-5079ba7825cd", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289856187200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9017c7e6-adaa-43b3-9227-9afe668cfbb6", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289856259700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5a37cd3-c81e-4c7e-b1c7-f262c2212b3f", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289856299900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb69bc68-aeea-4fdf-a090-accc396a8f5f", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289856337900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1caedac9-e37b-4c8f-8ebc-98db36cd4b9c", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289856368200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca5c219b-b7c5-4942-bb61-126d9d08c8cf", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289860056100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89a32cc0-8a82-40f9-b253-efb1d6c28b72", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289860252900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b04dad6-be98-4965-b494-2de7dda5fd9a", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289860340700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "504e0d6a-c068-44b9-a7f8-772e2d131825", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289860425800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef4af085-8089-47d1-8e70-b23db779018f", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289850070500, "endTime": 31289860489300}, "additional": {"logType": "info", "children": [], "durationId": "19cd4654-4486-4097-8fd1-b0cde3e5381d", "parent": "4b2e9a5b-7244-48b7-a4a0-731a6b59998b"}}, {"head": {"id": "4b2e9a5b-7244-48b7-a4a0-731a6b59998b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289847525400, "endTime": 31289860551300}, "additional": {"logType": "info", "children": ["c373ffef-216e-47a1-836f-320470233c33", "ef4af085-8089-47d1-8e70-b23db779018f"], "durationId": "5b4cc54e-18dc-4e14-aa6f-01e9d76a4620", "parent": "5fbf7d0b-758e-4f15-acc8-8dded85d374b"}}, {"head": {"id": "09cff1e1-c5fc-4b02-a8de-be42bac41a3e", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289862296500, "endTime": 31289862314100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f3d242f6-731a-4f7f-93bc-e0e736b4f536", "logId": "1a3ad0bd-f12e-499c-b5fd-f22e08c99aae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a3ad0bd-f12e-499c-b5fd-f22e08c99aae", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289862296500, "endTime": 31289862314100}, "additional": {"logType": "info", "children": [], "durationId": "09cff1e1-c5fc-4b02-a8de-be42bac41a3e", "parent": "5fbf7d0b-758e-4f15-acc8-8dded85d374b"}}, {"head": {"id": "5fbf7d0b-758e-4f15-acc8-8dded85d374b", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289840959600, "endTime": 31289862330500}, "additional": {"logType": "info", "children": ["09325075-3c8b-411c-bb79-d9a2c19a14b9", "4b2e9a5b-7244-48b7-a4a0-731a6b59998b", "1a3ad0bd-f12e-499c-b5fd-f22e08c99aae"], "durationId": "f3d242f6-731a-4f7f-93bc-e0e736b4f536", "parent": "66853e45-ed2b-42e8-bf17-9ab6db19f3df"}}, {"head": {"id": "66853e45-ed2b-42e8-bf17-9ab6db19f3df", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289840400900, "endTime": 31289862341900}, "additional": {"logType": "info", "children": ["5fbf7d0b-758e-4f15-acc8-8dded85d374b"], "durationId": "ccccc1b1-bc6f-4825-8ad9-64ed4b043fa6", "parent": "b081d63d-f4ec-491e-aa6a-4e41275f2a51"}}, {"head": {"id": "c0e6d09d-f0ce-4b3e-956f-2debc100efe1", "name": "watch files: [\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\hvigorfile.ts',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289879801700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64434896-7865-484d-a7c9-26fa633f1f4d", "name": "hvigorfile, resolve hvigorfile dependencies in 18 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289880133300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68b120a4-387d-45a2-9f5c-833135d75eef", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289862350800, "endTime": 31289880217900}, "additional": {"logType": "info", "children": [], "durationId": "5b505401-f9ae-4706-8a25-7178f07ddbdb", "parent": "b081d63d-f4ec-491e-aa6a-4e41275f2a51"}}, {"head": {"id": "e9855014-d361-42b6-a7a7-9701e7601fe9", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289881514200, "endTime": 31289881733400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d652eedf-be4c-4055-920d-6e28c654d1e5", "logId": "0b5b1185-fe67-48da-bbdc-6777b9deae91"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6eabeebf-cbca-4630-b67a-c676918a6eca", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289881542100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b5b1185-fe67-48da-bbdc-6777b9deae91", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289881514200, "endTime": 31289881733400}, "additional": {"logType": "info", "children": [], "durationId": "e9855014-d361-42b6-a7a7-9701e7601fe9", "parent": "b081d63d-f4ec-491e-aa6a-4e41275f2a51"}}, {"head": {"id": "69fc4d35-7097-4a7e-8834-2464bac0a412", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289884518900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63b36965-e8c4-4734-baff-4eab344a4a01", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289895217700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5c52686-88ac-414f-998a-ab0f15174a46", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289881752100, "endTime": 31289896883700}, "additional": {"logType": "info", "children": [], "durationId": "042940a9-b7f7-4263-a7c9-f4f4500993f8", "parent": "b081d63d-f4ec-491e-aa6a-4e41275f2a51"}}, {"head": {"id": "7498697e-29cf-4af3-85a5-52faf0374b51", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289896931200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd1bfc29-f494-45cc-bca2-8f4cb32e214e", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289905517400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d10e65e-49b9-4eed-8e29-bb32d5affec6", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289905624000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffca0846-3827-407a-b9c8-34115f373ebd", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289905790500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87c60431-5026-4a51-9647-fd5bb0a33a35", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289907541300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d4769b9-dbb2-49ba-a814-66d4fb71ec10", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289907613500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0766beb-0735-42ba-8a46-6c5121e65061", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289896915600, "endTime": 31289909880000}, "additional": {"logType": "info", "children": [], "durationId": "d591e502-293d-4930-b548-a250bbe11f2e", "parent": "b081d63d-f4ec-491e-aa6a-4e41275f2a51"}}, {"head": {"id": "cdf751f6-c4da-4d2b-9090-9f23fb72cc9f", "name": "Configuration phase cost:146 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289909919600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce2a9ede-aff4-4fbb-ae43-2977a5b92619", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289909899300, "endTime": 31289910004000}, "additional": {"logType": "info", "children": [], "durationId": "8231cde2-92f4-4d91-946e-11d361ac43d9", "parent": "b081d63d-f4ec-491e-aa6a-4e41275f2a51"}}, {"head": {"id": "b081d63d-f4ec-491e-aa6a-4e41275f2a51", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289759769100, "endTime": 31289910023800}, "additional": {"logType": "info", "children": ["35c0b80c-b6ed-41be-ae6d-f1663ef090f9", "cdc56375-beda-4e22-aca4-e62fbc891cc5", "11420ada-08cc-474c-a44b-0a34b3c547a3", "66853e45-ed2b-42e8-bf17-9ab6db19f3df", "68b120a4-387d-45a2-9f5c-833135d75eef", "e5c52686-88ac-414f-998a-ab0f15174a46", "e0766beb-0735-42ba-8a46-6c5121e65061", "ce2a9ede-aff4-4fbb-ae43-2977a5b92619", "0b5b1185-fe67-48da-bbdc-6777b9deae91"], "durationId": "d652eedf-be4c-4055-920d-6e28c654d1e5", "parent": "3d3bb75b-ea04-4df2-8954-d4a7cd4d362a"}}, {"head": {"id": "59b2678b-10dc-4038-b59f-a2adcf67bcec", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289911041700, "endTime": 31289911053900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d059b28-4e60-4fd4-8ed8-9bd56988208c", "logId": "ba63000d-4ac8-42bd-b21c-4e1a953df782"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ba63000d-4ac8-42bd-b21c-4e1a953df782", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289911041700, "endTime": 31289911053900}, "additional": {"logType": "info", "children": [], "durationId": "59b2678b-10dc-4038-b59f-a2adcf67bcec", "parent": "3d3bb75b-ea04-4df2-8954-d4a7cd4d362a"}}, {"head": {"id": "31bec089-bc7c-4387-9b69-0b96a583173e", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289910039100, "endTime": 31289911063400}, "additional": {"logType": "info", "children": [], "durationId": "65cfad57-051a-454e-823d-04fef01c4927", "parent": "3d3bb75b-ea04-4df2-8954-d4a7cd4d362a"}}, {"head": {"id": "84cb1051-7d7b-4b5f-b988-b5b28e8894f5", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289911066700, "endTime": 31289911068200}, "additional": {"logType": "info", "children": [], "durationId": "e85e37cc-9fe8-4801-9653-74d7bcef42f0", "parent": "3d3bb75b-ea04-4df2-8954-d4a7cd4d362a"}}, {"head": {"id": "3d3bb75b-ea04-4df2-8954-d4a7cd4d362a", "name": "init", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289746938000, "endTime": 31289911070300}, "additional": {"logType": "info", "children": ["0fabcf3d-384d-46b4-9ab5-6a331da3e5d3", "b081d63d-f4ec-491e-aa6a-4e41275f2a51", "31bec089-bc7c-4387-9b69-0b96a583173e", "84cb1051-7d7b-4b5f-b988-b5b28e8894f5", "37a91b23-d20a-429b-9f49-2eb6d1e2a3ee", "1534694e-4d77-4980-aed8-cab6fd96244a", "ba63000d-4ac8-42bd-b21c-4e1a953df782"], "durationId": "3d059b28-4e60-4fd4-8ed8-9bd56988208c"}}, {"head": {"id": "86623aeb-81f9-4f51-952b-394b54a9fc9a", "name": "Configuration task cost before running: 169 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289911157700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c86efa1-b38b-49d7-80b1-b90b1b504974", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289915121500, "endTime": 31289921961600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "b6c1b6d5-f034-4407-9fbb-981a0d874a06", "logId": "250cfb8f-d8d3-43a2-8164-f5b821a4cce5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6c1b6d5-f034-4407-9fbb-981a0d874a06", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289912320900}, "additional": {"logType": "detail", "children": [], "durationId": "7c86efa1-b38b-49d7-80b1-b90b1b504974"}}, {"head": {"id": "38397a17-9276-45b2-8c47-c23c6db3a143", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289912751600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c668eda-a0e1-482d-95ae-84253d05af34", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289912823000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a347c0ba-72d6-40ac-be07-e12834575501", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289915133000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7985be0c-2d57-4cf5-97a5-bf50d4422df1", "name": "Incremental task entry:default@PreBuild pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289921803600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81874375-7f9f-40b1-aa35-76e2798f6362", "name": "entry : default@PreBuild cost memory 0.2739105224609375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289921916200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "250cfb8f-d8d3-43a2-8164-f5b821a4cce5", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289915121500, "endTime": 31289921961600}, "additional": {"logType": "info", "children": [], "durationId": "7c86efa1-b38b-49d7-80b1-b90b1b504974"}}, {"head": {"id": "7422fc1e-78f3-4135-85aa-dd20bd438c61", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289927108300, "endTime": 31289929004100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "2881bffc-b8ba-491a-8160-6c3cb0e3ccd6", "logId": "341b62a9-d58f-461f-9b5f-1fcc73ce5696"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2881bffc-b8ba-491a-8160-6c3cb0e3ccd6", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289925940000}, "additional": {"logType": "detail", "children": [], "durationId": "7422fc1e-78f3-4135-85aa-dd20bd438c61"}}, {"head": {"id": "e8db2cf6-b3d4-4575-b6bf-e9cc4fddbc08", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289926417700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d1cc8f0-9514-4a3a-9f80-29b76849ffd2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289926505300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46d4b714-406f-4d58-91c2-4965c3f1f3eb", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289927115800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e1f54de-1f53-47d4-b982-b85feca4b7b1", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289928880100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaf11919-2c88-46be-8692-1f1e6266a524", "name": "entry : default@MergeProfile cost memory 0.10770416259765625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289928963700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "341b62a9-d58f-461f-9b5f-1fcc73ce5696", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289927108300, "endTime": 31289929004100}, "additional": {"logType": "info", "children": [], "durationId": "7422fc1e-78f3-4135-85aa-dd20bd438c61"}}, {"head": {"id": "c864aa50-bcdc-4517-9113-6fad8f54dc9e", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289931498100, "endTime": 31289933423300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2dbb4172-e4de-42f3-85d7-2b5b6663ce8e", "logId": "67387a8c-ef25-4ff4-b3ae-cf57576075c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2dbb4172-e4de-42f3-85d7-2b5b6663ce8e", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289930358600}, "additional": {"logType": "detail", "children": [], "durationId": "c864aa50-bcdc-4517-9113-6fad8f54dc9e"}}, {"head": {"id": "ed12faab-a223-4657-8485-0b2ea5650671", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289930770700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0be088cf-d665-4f90-91af-e2d1eaa91ebc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289930836400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71259eb3-5db8-4564-86e8-5221f5a79b4c", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289931504100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2159884-e3dc-4d17-8d66-930a9b371795", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289932241200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fa3b370-903b-4d46-be3c-9062cefef4bf", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289933264100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a966d455-423e-4421-8bd5-f46ebc734b46", "name": "entry : default@CreateBuildProfile cost memory 0.09545135498046875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289933373700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67387a8c-ef25-4ff4-b3ae-cf57576075c5", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289931498100, "endTime": 31289933423300}, "additional": {"logType": "info", "children": [], "durationId": "c864aa50-bcdc-4517-9113-6fad8f54dc9e"}}, {"head": {"id": "1ec78893-9fb8-419c-80a8-cd9f46f853f1", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289936171500, "endTime": 31289936433000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "067ed471-322d-4b17-89bc-0343cbd9d76b", "logId": "082a2c7c-4dc9-4d68-98b3-3dc654872d36"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "067ed471-322d-4b17-89bc-0343cbd9d76b", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289934794700}, "additional": {"logType": "detail", "children": [], "durationId": "1ec78893-9fb8-419c-80a8-cd9f46f853f1"}}, {"head": {"id": "c4ffbaa1-3338-4d58-bfb0-ab2cceff96ea", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289935456300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "397e3911-0862-4042-be6c-4665437e8109", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289935537700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07c2ea27-2e37-4b1b-97b0-0e43f3954669", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289936182200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b007cf3-e762-4585-8fe1-1a27b82ec6b2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289936273900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7056cc9d-7408-4a38-afd9-a030f9da44ef", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289936305300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfb3d35e-f423-4546-b4ed-329ce07ed7df", "name": "entry : default@PreCheckSyscap cost memory 0.03681182861328125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289936356600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4c38968-35c2-4377-82f7-f2f519d6a797", "name": "runTaskFromQueue task cost before running: 194 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289936405400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "082a2c7c-4dc9-4d68-98b3-3dc654872d36", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289936171500, "endTime": 31289936433000, "totalTime": 221200}, "additional": {"logType": "info", "children": [], "durationId": "1ec78893-9fb8-419c-80a8-cd9f46f853f1"}}, {"head": {"id": "e35c7169-ac42-435e-bb5e-0b9c4583edae", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289945275100, "endTime": 31289946386700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "bc288c45-70fa-419e-87fc-665ae246e2f2", "logId": "2d89a9b1-3592-42d2-9894-590d6733848a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc288c45-70fa-419e-87fc-665ae246e2f2", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289937777900}, "additional": {"logType": "detail", "children": [], "durationId": "e35c7169-ac42-435e-bb5e-0b9c4583edae"}}, {"head": {"id": "eadb934d-173c-4ecb-9be0-20282942b4c3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289938239800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c3b70bc-e25f-4808-b1b2-16bf92a19b49", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289938325700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bc06bf1-c73f-4438-8793-3fda3ab7bdbc", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289945286700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1161836e-c63b-4e1a-b723-5f19d24abdc2", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289945475600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cbb3713-ab41-4299-85a4-b20aced5324b", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289946176700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc25faed-7cba-4a2b-9773-97a00784da31", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06366729736328125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289946310200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d89a9b1-3592-42d2-9894-590d6733848a", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289945275100, "endTime": 31289946386700}, "additional": {"logType": "info", "children": [], "durationId": "e35c7169-ac42-435e-bb5e-0b9c4583edae"}}, {"head": {"id": "698302bb-17a4-48b2-bc7b-eb19ca137329", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289950713100, "endTime": 31289951883600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "009b64f2-a35f-4600-bbe8-e4b18ffdb506", "logId": "51e3761c-1a46-4ac9-903c-b0ab8b1394b0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "009b64f2-a35f-4600-bbe8-e4b18ffdb506", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289948433600}, "additional": {"logType": "detail", "children": [], "durationId": "698302bb-17a4-48b2-bc7b-eb19ca137329"}}, {"head": {"id": "f1d33661-6920-4a76-b32d-c069806cef4e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289949164200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4265798-623d-4351-b6e1-e60acf6cff63", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289949287000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f8b03dc-32f6-433e-8fc5-de3f83561d0c", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289950730200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c535118e-81d4-4e8d-aa6f-1fea7c5b1284", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289951728400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59d3032f-e986-4a3f-824e-a4be51a20bae", "name": "entry : default@ProcessProfile cost memory 0.05466461181640625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289951836700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51e3761c-1a46-4ac9-903c-b0ab8b1394b0", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289950713100, "endTime": 31289951883600}, "additional": {"logType": "info", "children": [], "durationId": "698302bb-17a4-48b2-bc7b-eb19ca137329"}}, {"head": {"id": "319be6b9-5d0d-45d4-8fda-679dcaf6f8db", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289956335700, "endTime": 31289967455400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "361b951d-bdb4-4022-bcde-8342c170bb5b", "logId": "bec4b552-990a-440b-b298-f2b1e34917c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "361b951d-bdb4-4022-bcde-8342c170bb5b", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289953597500}, "additional": {"logType": "detail", "children": [], "durationId": "319be6b9-5d0d-45d4-8fda-679dcaf6f8db"}}, {"head": {"id": "a91edf6e-bb75-444b-91e8-785366e858a0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289954243300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a33eb987-6e1f-4f84-bc6f-2276c5c36b61", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289954353300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86280a63-9173-4045-a4c3-7803fd0f60b0", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289956347000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9c877c2-85b1-415c-8aef-2769ec50ae52", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289966901300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9aa650cb-3c84-4bb0-ac1a-487286d7ebf3", "name": "entry : default@ProcessRouterMap cost memory 0.1942596435546875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289967252700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bec4b552-990a-440b-b298-f2b1e34917c4", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289956335700, "endTime": 31289967455400}, "additional": {"logType": "info", "children": [], "durationId": "319be6b9-5d0d-45d4-8fda-679dcaf6f8db"}}, {"head": {"id": "bc8ad88d-1012-471d-8a64-e0d65c2ba680", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289976664200, "endTime": 31289979705600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "ebc9f0fc-6fae-46aa-9139-b6b43224ea81", "logId": "4fd35132-0dd3-4cd0-9e78-0563bfe5e4e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ebc9f0fc-6fae-46aa-9139-b6b43224ea81", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289971853000}, "additional": {"logType": "detail", "children": [], "durationId": "bc8ad88d-1012-471d-8a64-e0d65c2ba680"}}, {"head": {"id": "310651a8-0be9-4848-a520-e65d738d43e7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289972491900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dab38211-b034-4b2a-ae45-93b72203d605", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289972609100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "312d6af2-4a89-4162-8c6f-2be5d7f6134d", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289973745800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d315da5-766f-404c-b0c0-4cd02b29ce75", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289977800900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ff28363-025f-415d-ac64-9c9f62c44ca7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289977993400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b555f07-37ef-4aa8-9380-5af4a3d7ec7b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289978039200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2d3d42e-31df-4b2e-99cd-9b1cb98fd786", "name": "entry : default@PreviewProcessResource cost memory 0.0698699951171875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289978097800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "791a5fc2-415e-4eb9-bba6-29d69b76506c", "name": "runTaskFromQueue task cost before running: 237 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289979578800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fd35132-0dd3-4cd0-9e78-0563bfe5e4e3", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289976664200, "endTime": 31289979705600, "totalTime": 1481200}, "additional": {"logType": "info", "children": [], "durationId": "bc8ad88d-1012-471d-8a64-e0d65c2ba680"}}, {"head": {"id": "f83684a5-4d6e-4de0-8793-d887684d4c6a", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289990743500, "endTime": 31290026113300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "20ce72b3-3a66-4c11-905b-e1d4d9be7621", "logId": "670beed4-678c-491c-9f4b-9d6061825ccf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "20ce72b3-3a66-4c11-905b-e1d4d9be7621", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289983433100}, "additional": {"logType": "detail", "children": [], "durationId": "f83684a5-4d6e-4de0-8793-d887684d4c6a"}}, {"head": {"id": "65c4d375-d3e2-4c03-b708-4fb0f562a0e4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289984366500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "065c7f53-8e5b-4eba-948f-9fffcf0939c1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289984531800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b47b0fc-2e79-4486-b7ba-448c793837c1", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289990772700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fc67ee4-8841-4da0-95c7-660eb58d1b3f", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 21 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290025825900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c95240f4-9cc9-476b-9c41-666fd5ddaec2", "name": "entry : default@GenerateLoaderJson cost memory 0.7455520629882812", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290026026900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "670beed4-678c-491c-9f4b-9d6061825ccf", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289990743500, "endTime": 31290026113300}, "additional": {"logType": "info", "children": [], "durationId": "f83684a5-4d6e-4de0-8793-d887684d4c6a"}}, {"head": {"id": "9b5a4c61-55f4-4d93-9a66-01675aa0c7bf", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290043661800, "endTime": 31290277912000}, "additional": {"children": ["c6c15326-60bc-4f31-b7aa-7dfb2fd64bf2", "c5c918b3-424c-451b-bff5-94e7cf15c092", "6d5f3a2b-fce8-45d2-b7ee-937af1527f96", "6e771d0d-fee5-4744-8085-78b4498a310b"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "b7598731-0b38-47a2-9ed1-c0ce14c5bfe6", "logId": "79b236c6-2545-4deb-8427-7a17ed8c8148"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b7598731-0b38-47a2-9ed1-c0ce14c5bfe6", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290039665600}, "additional": {"logType": "detail", "children": [], "durationId": "9b5a4c61-55f4-4d93-9a66-01675aa0c7bf"}}, {"head": {"id": "1641986a-dc19-4b6a-a610-6068c8d37920", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290040272500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71a7509b-7bcd-4732-b3fc-48f91d72e6c8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290040380400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a93bc2a6-4854-4366-96be-a625e3f2b9f9", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290041370900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0343f83-91b5-42d1-995a-f116b5fb5988", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290043689400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cae3678-df4a-4518-920e-a7187bc2a00b", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290061210600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "946bc705-2b6e-448c-bb31-4c654cee6548", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 17 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290061398700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6c15326-60bc-4f31-b7aa-7dfb2fd64bf2", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290062834600, "endTime": 31290099563300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b5a4c61-55f4-4d93-9a66-01675aa0c7bf", "logId": "c7e11a10-bf5c-46a4-b3bb-6f0048b70f0b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7e11a10-bf5c-46a4-b3bb-6f0048b70f0b", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290062834600, "endTime": 31290099563300}, "additional": {"logType": "info", "children": [], "durationId": "c6c15326-60bc-4f31-b7aa-7dfb2fd64bf2", "parent": "79b236c6-2545-4deb-8427-7a17ed8c8148"}}, {"head": {"id": "0fd95874-58c9-441e-a4c6-7ddf4fa3f7bb", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290099862300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5c918b3-424c-451b-bff5-94e7cf15c092", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290100766100, "endTime": 31290146031500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b5a4c61-55f4-4d93-9a66-01675aa0c7bf", "logId": "4c1d44d0-18cc-4ba6-8833-6fb36b220a8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "899c2e88-0559-4939-9ac2-2eae30c06202", "name": "current process  memoryUsage: {\n  rss: 374296576,\n  heapTotal: 131686400,\n  heapUsed: 107506384,\n  external: 3075129,\n  arrayBuffers: 69030\n} os memoryUsage :13.748416900634766", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290101651400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f58e45f1-3ccf-4c32-825e-c173170a54a3", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290142438300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c1d44d0-18cc-4ba6-8833-6fb36b220a8d", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290100766100, "endTime": 31290146031500}, "additional": {"logType": "info", "children": [], "durationId": "c5c918b3-424c-451b-bff5-94e7cf15c092", "parent": "79b236c6-2545-4deb-8427-7a17ed8c8148"}}, {"head": {"id": "e88f5494-25f1-46fd-a6d0-60eff5310f33", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290146136300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d5f3a2b-fce8-45d2-b7ee-937af1527f96", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290147161900, "endTime": 31290198689600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b5a4c61-55f4-4d93-9a66-01675aa0c7bf", "logId": "a79b1c8f-a8f9-49c6-bc5b-8d26785c7932"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f1e8166-9b53-4a6d-9753-9842885bc22a", "name": "current process  memoryUsage: {\n  rss: 376492032,\n  heapTotal: 131686400,\n  heapUsed: 107763872,\n  external: 3075255,\n  arrayBuffers: 69171\n} os memoryUsage :13.743030548095703", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290148048500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faf93da1-e861-46bb-af7b-06995ff1030f", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290196226400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a79b1c8f-a8f9-49c6-bc5b-8d26785c7932", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290147161900, "endTime": 31290198689600}, "additional": {"logType": "info", "children": [], "durationId": "6d5f3a2b-fce8-45d2-b7ee-937af1527f96", "parent": "79b236c6-2545-4deb-8427-7a17ed8c8148"}}, {"head": {"id": "a43c9407-625c-4f72-8883-e3811b625f23", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290198850400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e771d0d-fee5-4744-8085-78b4498a310b", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290199855700, "endTime": 31290276027500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b5a4c61-55f4-4d93-9a66-01675aa0c7bf", "logId": "69b2260a-65a5-4e1c-ad6b-0834f7bc4b05"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf698a54-b1b4-490e-83f7-591372c80334", "name": "current process  memoryUsage: {\n  rss: 379887616,\n  heapTotal: 131686400,\n  heapUsed: 108042392,\n  external: 3075381,\n  arrayBuffers: 70181\n} os memoryUsage :13.77197265625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290200672700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51ee27eb-0c1b-4a1a-975f-486376f17cc9", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290272488600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69b2260a-65a5-4e1c-ad6b-0834f7bc4b05", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290199855700, "endTime": 31290276027500}, "additional": {"logType": "info", "children": [], "durationId": "6e771d0d-fee5-4744-8085-78b4498a310b", "parent": "79b236c6-2545-4deb-8427-7a17ed8c8148"}}, {"head": {"id": "41411f73-ac9c-4f88-bc81-73cd2538fe36", "name": "entry : default@PreviewCompileResource cost memory -8.910102844238281", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290277627300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4aa8d855-325e-4bbf-b8cf-20b843a928a6", "name": "runTaskFromQueue task cost before running: 535 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290277831800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79b236c6-2545-4deb-8427-7a17ed8c8148", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290043661800, "endTime": 31290277912000, "totalTime": 234108900}, "additional": {"logType": "info", "children": ["c7e11a10-bf5c-46a4-b3bb-6f0048b70f0b", "4c1d44d0-18cc-4ba6-8833-6fb36b220a8d", "a79b1c8f-a8f9-49c6-bc5b-8d26785c7932", "69b2260a-65a5-4e1c-ad6b-0834f7bc4b05"], "durationId": "9b5a4c61-55f4-4d93-9a66-01675aa0c7bf"}}, {"head": {"id": "975c868b-05fd-4bd6-8e32-5e1b1fbb75da", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290282486200, "endTime": 31290282821100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "eb935398-da2c-4173-9da6-90d478560ab1", "logId": "840c2d00-9107-4f07-a41b-d6a1a30e68c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb935398-da2c-4173-9da6-90d478560ab1", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290281411700}, "additional": {"logType": "detail", "children": [], "durationId": "975c868b-05fd-4bd6-8e32-5e1b1fbb75da"}}, {"head": {"id": "3c812e13-140c-4623-ad17-a7beba47066e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290282241500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b5abaf1-7b23-4543-b44a-ce0e7c800187", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290282365200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb69ce79-a315-4b1d-8c4e-db8230d3ac16", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290282494800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec5901d6-cdd5-42c4-9589-8e3c597f1051", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290282586500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "628d46d1-2976-429b-8152-38163ea68c6e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290282630900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0742f217-691a-4d27-bbe6-28420b02abe2", "name": "entry : default@PreviewHookCompileResource cost memory 0.0379180908203125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290282696800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed808818-bcba-44c4-aa9f-a8eb725e1d1c", "name": "runTaskFromQueue task cost before running: 540 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290282769700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "840c2d00-9107-4f07-a41b-d6a1a30e68c2", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290282486200, "endTime": 31290282821100, "totalTime": 266100}, "additional": {"logType": "info", "children": [], "durationId": "975c868b-05fd-4bd6-8e32-5e1b1fbb75da"}}, {"head": {"id": "ba2beb7e-312d-4fac-a8bc-cb187c5edb24", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290286796200, "endTime": 31290293689700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "85072bcc-78bb-4595-8f99-1f27a23d5e2b", "logId": "fb4a1466-51e8-44c6-ab3a-2b90bd74f404"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85072bcc-78bb-4595-8f99-1f27a23d5e2b", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290285031800}, "additional": {"logType": "detail", "children": [], "durationId": "ba2beb7e-312d-4fac-a8bc-cb187c5edb24"}}, {"head": {"id": "b3c2fdcb-449a-4a62-a199-e72527d388ef", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290285808900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d89fe98a-bfb0-4571-a870-bc23e94ce895", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290285924300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b01d706b-9313-4751-96e0-ab5b21029b26", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290286805000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0a213ee-2460-47b0-b5fd-544aa54afd92", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290288106400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e3a1022-3cf4-4db2-b992-768781dcbec4", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290288204100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd96b63f-d8bb-4c92-9b53-777bdd062ed8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290288261300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccc42ce2-2910-44f1-846a-a890fef2563c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290288289500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb583808-3a48-4291-aa4e-c9444d0ff78f", "name": "entry : default@CopyPreviewProfile cost memory 0.2228851318359375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290293485400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "057d986f-f0d3-407b-af5c-8029274b1d64", "name": "runTaskFromQueue task cost before running: 551 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290293638500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb4a1466-51e8-44c6-ab3a-2b90bd74f404", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290286796200, "endTime": 31290293689700, "totalTime": 6820900}, "additional": {"logType": "info", "children": [], "durationId": "ba2beb7e-312d-4fac-a8bc-cb187c5edb24"}}, {"head": {"id": "00a9a294-6b02-4bbf-a9a0-d9542d10ebdf", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290297154600, "endTime": 31290297442900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "b61963dc-3e24-4008-8396-0ae9aeb98c72", "logId": "e4c36b42-6d34-42d9-a179-0d7e5afc909d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b61963dc-3e24-4008-8396-0ae9aeb98c72", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290295875600}, "additional": {"logType": "detail", "children": [], "durationId": "00a9a294-6b02-4bbf-a9a0-d9542d10ebdf"}}, {"head": {"id": "526ee5c3-2bdc-4798-87b0-2db8370b9dd8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290296379700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9916d21d-908e-4299-83e2-2fadbc55142a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290296472100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91877125-c244-49ab-b4ab-eedcd759bae6", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290297164400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28be9279-fd34-49e6-b974-863d49399af2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290297266100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11327f5e-98ca-4218-8a76-47753f5e3e91", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290297299000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35b9d435-dcb8-4f7f-9f40-3cf6c8f483d6", "name": "entry : default@ReplacePreviewerPage cost memory 0.037872314453125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290297364600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8296fba8-daae-4f40-aa07-2d77da530b45", "name": "runTaskFromQueue task cost before running: 555 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290297415300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4c36b42-6d34-42d9-a179-0d7e5afc909d", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290297154600, "endTime": 31290297442900, "totalTime": 248900}, "additional": {"logType": "info", "children": [], "durationId": "00a9a294-6b02-4bbf-a9a0-d9542d10ebdf"}}, {"head": {"id": "d54aed81-a6f6-4479-aa08-2152adea7156", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290298812900, "endTime": 31290298999700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "e38a6404-db7e-4776-b80d-417ccec220cb", "logId": "b17c9ca7-2a67-47c1-b16b-05b58026f87f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e38a6404-db7e-4776-b80d-417ccec220cb", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290298762900}, "additional": {"logType": "detail", "children": [], "durationId": "d54aed81-a6f6-4479-aa08-2152adea7156"}}, {"head": {"id": "5ca5e9a2-9ba0-46f9-b380-e0eb9fba1b00", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290298818800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f87f02c9-9a37-4359-b199-633e4a0af99e", "name": "entry : buildPreviewerResource cost memory 0.0116424560546875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290298912600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ed5cc68-bf81-4880-9f65-f3404b2b2a96", "name": "runTaskFromQueue task cost before running: 556 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290298968500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b17c9ca7-2a67-47c1-b16b-05b58026f87f", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290298812900, "endTime": 31290298999700, "totalTime": 142300}, "additional": {"logType": "info", "children": [], "durationId": "d54aed81-a6f6-4479-aa08-2152adea7156"}}, {"head": {"id": "0dfe8d87-81bc-4ac1-91d6-72e9450cd3a9", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290301555100, "endTime": 31290305226500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "06580a8c-c0c1-4673-977e-0c4607e99191", "logId": "27b8f025-8ba4-4a12-99af-29c530715d05"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06580a8c-c0c1-4673-977e-0c4607e99191", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290300335800}, "additional": {"logType": "detail", "children": [], "durationId": "0dfe8d87-81bc-4ac1-91d6-72e9450cd3a9"}}, {"head": {"id": "7cda264c-903c-46e5-9223-5a7917f2e379", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290300805000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "400b4aa5-d9cb-46a3-96fd-18bb3547ce89", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290300889000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fca4dca-50f2-4124-b23f-7f9aca6339c7", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290301562300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3f760e0-1acb-4675-8d2a-c68c8ac0c965", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290303281300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "003336f1-e6e1-4a2a-afb3-56d54a3e4461", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290303375600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89169027-3461-4bc3-b22b-0a33a8403940", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290303430300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eca46b88-0f41-48a4-ae89-40082871e6e5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290303457700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97d0bc05-17d7-4008-8e58-1f6929ad430f", "name": "entry : default@PreviewUpdateAssets cost memory 0.14337921142578125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290305060100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a98fe461-4349-40f7-af82-61d9fa187710", "name": "runTaskFromQueue task cost before running: 563 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290305177000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27b8f025-8ba4-4a12-99af-29c530715d05", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290301555100, "endTime": 31290305226500, "totalTime": 3600000}, "additional": {"logType": "info", "children": [], "durationId": "0dfe8d87-81bc-4ac1-91d6-72e9450cd3a9"}}, {"head": {"id": "14466408-080e-457e-a1c8-7ef33c8c8918", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290312264800, "endTime": 31300734791800}, "additional": {"children": ["3a50c887-a83b-4f52-8cf8-b8dcde3ec39a"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "58f34cbc-8eb4-46ea-b6ab-0153ffb62e78", "logId": "9c988e0a-4efe-4b6b-93e4-6359fc0fa4ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58f34cbc-8eb4-46ea-b6ab-0153ffb62e78", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290307347600}, "additional": {"logType": "detail", "children": [], "durationId": "14466408-080e-457e-a1c8-7ef33c8c8918"}}, {"head": {"id": "77a639fb-19ff-4169-bde2-6abf2abb1422", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290307800400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae083ac3-7173-40ff-9bbd-e8d3cbc81106", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290307877600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac0e53b1-4527-40e6-b790-e71232ac576a", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290312275700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a50c887-a83b-4f52-8cf8-b8dcde3ec39a", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker19", "startTime": 31290329060500, "endTime": 31300731653500}, "additional": {"children": ["8d58add5-0ed6-4228-a9fe-d47330672e1e", "ec48462e-5a56-481e-9017-102979df3ee7", "fcf70d2f-d459-4066-b574-b7a916f5d7fb", "a4e050bb-3f14-4f33-bf2b-8cbc9253b9c6", "ff24b7a8-74d6-4335-a3b8-9c42a2fdd5fb"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "14466408-080e-457e-a1c8-7ef33c8c8918", "logId": "b76c840d-5846-411b-b805-c0f37be8c64c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "766b7ab0-c88f-4c7a-8c8f-47ea20534176", "name": "entry : default@PreviewArkTS cost memory 1.0124359130859375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290330883500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abf4e645-318f-415e-8b7e-218b93b16078", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31293056110500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d676faea-83ec-4e6a-b1c5-1b8f363e936e", "name": "Server currently has 1 watch-worker", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31293056438800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5075d86d-c16d-492d-98f2-ddf2f7ac7146", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31294042936900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d58add5-0ed6-4228-a9fe-d47330672e1e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker19", "startTime": 31294044024800, "endTime": 31294044042000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3a50c887-a83b-4f52-8cf8-b8dcde3ec39a", "logId": "e24ba66d-06d6-403a-892b-e50ad3b1b964"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e24ba66d-06d6-403a-892b-e50ad3b1b964", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31294044024800, "endTime": 31294044042000}, "additional": {"logType": "info", "children": [], "durationId": "8d58add5-0ed6-4228-a9fe-d47330672e1e", "parent": "b76c840d-5846-411b-b805-c0f37be8c64c"}}, {"head": {"id": "03498b52-5b7f-407e-bcd6-ae4c9c055af2", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300730490800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec48462e-5a56-481e-9017-102979df3ee7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker19", "startTime": 31300731532400, "endTime": 31300731548700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3a50c887-a83b-4f52-8cf8-b8dcde3ec39a", "logId": "c0b937a0-4c68-4174-989d-97a6eaab5b68"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0b937a0-4c68-4174-989d-97a6eaab5b68", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300731532400, "endTime": 31300731548700}, "additional": {"logType": "info", "children": [], "durationId": "ec48462e-5a56-481e-9017-102979df3ee7", "parent": "b76c840d-5846-411b-b805-c0f37be8c64c"}}, {"head": {"id": "b76c840d-5846-411b-b805-c0f37be8c64c", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Worker19", "startTime": 31290329060500, "endTime": 31300731653500}, "additional": {"logType": "info", "children": ["e24ba66d-06d6-403a-892b-e50ad3b1b964", "c0b937a0-4c68-4174-989d-97a6eaab5b68", "4cd42e75-be60-44c0-8ee3-963c57b8db15", "a6e9cc30-9bc6-4542-bac1-7bb3e8119708", "8a859952-e3a6-4e53-bd07-5434b2f6ef4e"], "durationId": "3a50c887-a83b-4f52-8cf8-b8dcde3ec39a", "parent": "9c988e0a-4efe-4b6b-93e4-6359fc0fa4ec"}}, {"head": {"id": "fcf70d2f-d459-4066-b574-b7a916f5d7fb", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker19", "startTime": 31293031135700, "endTime": 31294021485600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3a50c887-a83b-4f52-8cf8-b8dcde3ec39a", "logId": "4cd42e75-be60-44c0-8ee3-963c57b8db15"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4cd42e75-be60-44c0-8ee3-963c57b8db15", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31293031135700, "endTime": 31294021485600}, "additional": {"logType": "info", "children": [], "durationId": "fcf70d2f-d459-4066-b574-b7a916f5d7fb", "parent": "b76c840d-5846-411b-b805-c0f37be8c64c"}}, {"head": {"id": "a4e050bb-3f14-4f33-bf2b-8cbc9253b9c6", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker19", "startTime": 31294021634200, "endTime": 31294021739700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3a50c887-a83b-4f52-8cf8-b8dcde3ec39a", "logId": "a6e9cc30-9bc6-4542-bac1-7bb3e8119708"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6e9cc30-9bc6-4542-bac1-7bb3e8119708", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31294021634200, "endTime": 31294021739700}, "additional": {"logType": "info", "children": [], "durationId": "a4e050bb-3f14-4f33-bf2b-8cbc9253b9c6", "parent": "b76c840d-5846-411b-b805-c0f37be8c64c"}}, {"head": {"id": "ff24b7a8-74d6-4335-a3b8-9c42a2fdd5fb", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker19", "startTime": 31294021826300, "endTime": 31300730434400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3a50c887-a83b-4f52-8cf8-b8dcde3ec39a", "logId": "8a859952-e3a6-4e53-bd07-5434b2f6ef4e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a859952-e3a6-4e53-bd07-5434b2f6ef4e", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31294021826300, "endTime": 31300730434400}, "additional": {"logType": "info", "children": [], "durationId": "ff24b7a8-74d6-4335-a3b8-9c42a2fdd5fb", "parent": "b76c840d-5846-411b-b805-c0f37be8c64c"}}, {"head": {"id": "9c988e0a-4efe-4b6b-93e4-6359fc0fa4ec", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31290312264800, "endTime": 31300734791800, "totalTime": 10422511100}, "additional": {"logType": "info", "children": ["b76c840d-5846-411b-b805-c0f37be8c64c"], "durationId": "14466408-080e-457e-a1c8-7ef33c8c8918"}}, {"head": {"id": "064ae5ab-e7a1-40c2-8d1e-4ff051ac7df8", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300739695600, "endTime": 31300739884700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "baf482f7-817e-48a3-b1b0-0949b3018b26", "logId": "e6e7ec48-af62-4477-b27f-8cf943b8915a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "baf482f7-817e-48a3-b1b0-0949b3018b26", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300739652400}, "additional": {"logType": "detail", "children": [], "durationId": "064ae5ab-e7a1-40c2-8d1e-4ff051ac7df8"}}, {"head": {"id": "64df8109-79fe-410d-a227-2402bb07bc6c", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300739704200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84ad9645-7bc0-458d-adb7-cefd93ad4fd0", "name": "entry : PreviewBuild cost memory 0.0115203857421875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300739800500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "992a86e4-40e9-4ec7-b35f-6cb945e22848", "name": "runTaskFromQueue task cost before running: 10 s 997 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300739853900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6e7ec48-af62-4477-b27f-8cf943b8915a", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300739695600, "endTime": 31300739884700, "totalTime": 144300}, "additional": {"logType": "info", "children": [], "durationId": "064ae5ab-e7a1-40c2-8d1e-4ff051ac7df8"}}, {"head": {"id": "564e73fc-e4ff-49a5-991b-f325dde95aed", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300748753300, "endTime": 31300748777600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1d809f06-461c-4c83-835e-d9a22c59c638", "logId": "282d5a0e-7fe9-4b1c-a9ee-dd72dd23fb05"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "282d5a0e-7fe9-4b1c-a9ee-dd72dd23fb05", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300748753300, "endTime": 31300748777600}, "additional": {"logType": "info", "children": [], "durationId": "564e73fc-e4ff-49a5-991b-f325dde95aed"}}, {"head": {"id": "714d7958-3938-40fa-8d66-2affb1fb6f84", "name": "BUILD SUCCESSFUL in 11 s 6 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300748819400}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "407128de-c3a4-43b6-901d-e839f64db957", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31289743047200, "endTime": 31300749027600}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 47}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "f20f7927-7435-4715-b3f9-1889682f511c", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300749050600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c00a237-afeb-4d0f-a453-a2c409036151", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300749087900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4738e583-d764-4648-91bf-fca44177b96b", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300749113600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a67deec2-da92-4f5a-b969-9184ef15f09a", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300749136700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2cab0df-cfae-4f4a-b3a3-28a58606e505", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300749161800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63970f75-670e-4df1-a4c6-6d6c94628224", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300749182500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fbda162-36fd-42ce-8cad-9fe66b202bfe", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300749214000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f794658-088a-4c0e-86d9-42b77cb472a8", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300750097000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77eb5cb2-eca1-4474-9002-ba23b0ea3c1f", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300755912200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7425247a-a130-4d5c-863e-e3020918d938", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300756234000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "806c8f5d-7fd4-4915-bab2-6c477dec5152", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300765747300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b782a565-89bd-4565-812f-69b66af906e6", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:18 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300766323500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bae2340-a51b-46ce-9203-03957511e13e", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300766494500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3fd2e76-0751-4b4e-82d9-a52530949d19", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300767122700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c694c972-b7b3-4a93-a50c-dd86bcb23169", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300767723900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfb30162-ca8a-4722-8aa4-b2c5b3fcacb2", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300768052000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f08ce3a7-9a24-44d7-88c8-591dfe6e3725", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300768296200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0bced8e-0d85-4c6c-986e-8aa2ea200edd", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300768510300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7ebed8a-949c-4d1b-8f8f-e34d6949ed57", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300770623000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f941efe-a7fc-44bd-99d8-73f24ff457e4", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300771255900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2df99b93-c1b5-45fb-a384-835ce0071065", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300771479700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfe34fa5-257f-4353-94c0-2e20ba9c749d", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300771687500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9e0d084-5619-4e27-b704-c97e79a31992", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300772234600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "653993c8-7aab-4ac8-80c2-caf44d055943", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300779716200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04f83473-ea52-4119-b37e-979d1b73306c", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300779975300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99e76a7a-ec73-46e2-ad59-6e74ea76a75d", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300780203400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "724c15f8-d98d-41b2-8e27-9a9bf78929e3", "name": "Update task entry:default@PreviewArkTS output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300780412200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e640d91-b5d5-4564-b706-a0e4ebd01c87", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:13 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31300780638400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}