if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface PayLimitSettingPage_Params {
    userInfo?: LocalUserInfo | null;
    isUpdating?: boolean;
    selectedLimit?: number;
    limitOptions?: LimitOption[];
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { UserApi } from "@normalized:N&&&entry/src/main/ets/api/UserApi&";
import { storageManager } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
import type { LocalUserInfo } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
import type { UpdatePayLimitRequest } from '../common/types/index';
interface LimitOption {
    value: number;
    label: string;
    description: string;
}
class PayLimitSettingPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__userInfo = new ObservedPropertyObjectPU(null, this, "userInfo");
        this.__isUpdating = new ObservedPropertySimplePU(false, this, "isUpdating");
        this.__selectedLimit = new ObservedPropertySimplePU(0, this, "selectedLimit");
        this.limitOptions = [
            { value: 500, label: '¥500', description: '日常小额支付' },
            { value: 1000, label: '¥1,000', description: '一般消费支付' },
            { value: 2000, label: '¥2,000', description: '中等金额支付' },
            { value: 5000, label: '¥5,000', description: '大额消费支付' },
            { value: 10000, label: '¥10,000', description: '最高限额支付' }
        ];
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: PayLimitSettingPage_Params) {
        if (params.userInfo !== undefined) {
            this.userInfo = params.userInfo;
        }
        if (params.isUpdating !== undefined) {
            this.isUpdating = params.isUpdating;
        }
        if (params.selectedLimit !== undefined) {
            this.selectedLimit = params.selectedLimit;
        }
        if (params.limitOptions !== undefined) {
            this.limitOptions = params.limitOptions;
        }
    }
    updateStateVars(params: PayLimitSettingPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__userInfo.purgeDependencyOnElmtId(rmElmtId);
        this.__isUpdating.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedLimit.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__userInfo.aboutToBeDeleted();
        this.__isUpdating.aboutToBeDeleted();
        this.__selectedLimit.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __userInfo: ObservedPropertyObjectPU<LocalUserInfo | null>;
    get userInfo() {
        return this.__userInfo.get();
    }
    set userInfo(newValue: LocalUserInfo | null) {
        this.__userInfo.set(newValue);
    }
    private __isUpdating: ObservedPropertySimplePU<boolean>;
    get isUpdating() {
        return this.__isUpdating.get();
    }
    set isUpdating(newValue: boolean) {
        this.__isUpdating.set(newValue);
    }
    private __selectedLimit: ObservedPropertySimplePU<number>;
    get selectedLimit() {
        return this.__selectedLimit.get();
    }
    set selectedLimit(newValue: number) {
        this.__selectedLimit.set(newValue);
    }
    private limitOptions: LimitOption[];
    aboutToAppear() {
        this.loadUserInfo();
    }
    async loadUserInfo() {
        try {
            const cachedUserInfo = await storageManager.getUserInfo();
            if (cachedUserInfo) {
                this.userInfo = cachedUserInfo;
                this.selectedLimit = cachedUserInfo.payLimit || 0;
            }
        }
        catch (error) {
            console.error('获取用户信息失败:', error);
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(45:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(47:7)", "entry");
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(56);
            // 顶部导航栏
            Row.padding({ left: 16, right: 16 });
            // 顶部导航栏
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('返回');
            Button.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(48:9)", "entry");
            Button.fontSize(16);
            Button.fontColor('#1976D2');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付限额设置');
            Text.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(56:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(62:9)", "entry");
            Text.width(60);
        }, Text);
        Text.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(70:7)", "entry");
            Scroll.layoutWeight(1);
            Scroll.backgroundColor('#F5F5F5');
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(71:9)", "entry");
            Column.padding({ left: 16, right: 16, bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 当前限额显示
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(73:11)", "entry");
            // 当前限额显示
            Column.width('100%');
            // 当前限额显示
            Column.padding(20);
            // 当前限额显示
            Column.borderRadius(12);
            // 当前限额显示
            Column.backgroundColor('#FFFFFF');
            // 当前限额显示
            Column.margin({ top: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('当前支付限额');
            Text.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(74:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`¥${(this.userInfo?.payLimit ?? 0).toFixed(2)}`);
            Text.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(81:13)", "entry");
            Text.fontSize(32);
            Text.fontColor('#1976D2');
            Text.fontWeight(FontWeight.Bold);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('单笔支付限额，保护您的资金安全');
            Text.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(88:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        // 当前限额显示
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 安全提示
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(100:11)", "entry");
            // 安全提示
            Column.width('100%');
            // 安全提示
            Column.padding(20);
            // 安全提示
            Column.borderRadius(12);
            // 安全提示
            Column.backgroundColor('#FFF3E0');
            // 安全提示
            Column.margin({ top: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('安全提示');
            Text.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(101:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('• 支付限额用于控制单笔转账和提现的最大金额');
            Text.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(108:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('• 建议根据您的实际需求选择合适的限额');
            Text.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(114:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('• 较低的限额可以更好地保护您的资金安全');
            Text.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(120:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        // 安全提示
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 限额选择
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(132:11)", "entry");
            // 限额选择
            Column.width('100%');
            // 限额选择
            Column.padding(20);
            // 限额选择
            Column.borderRadius(12);
            // 限额选择
            Column.backgroundColor('#FFFFFF');
            // 限额选择
            Column.margin({ top: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('选择新的支付限额');
            Text.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(133:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const option = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create();
                    Row.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(141:15)", "entry");
                    Row.width('100%');
                    Row.height(70);
                    Row.padding({ left: 16, right: 16 });
                    Row.backgroundColor(this.selectedLimit === option.value ? '#E3F2FD' : '#F8F9FA');
                    Row.borderRadius(8);
                    Row.border({
                        width: this.selectedLimit === option.value ? 2 : 1,
                        color: this.selectedLimit === option.value ? '#1976D2' : '#E0E0E0'
                    });
                    Row.margin({ bottom: 12 });
                    Row.onClick(() => {
                        this.selectedLimit = option.value;
                    });
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(142:17)", "entry");
                    Column.layoutWeight(1);
                    Column.alignItems(HorizontalAlign.Start);
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(option.label);
                    Text.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(143:19)", "entry");
                    Text.fontSize(18);
                    Text.fontColor('#333333');
                    Text.fontWeight(FontWeight.Medium);
                    Text.alignSelf(ItemAlign.Start);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(option.description);
                    Text.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(149:19)", "entry");
                    Text.fontSize(14);
                    Text.fontColor('#666666');
                    Text.alignSelf(ItemAlign.Start);
                    Text.margin({ top: 4 });
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    If.create();
                    if (this.selectedLimit === option.value) {
                        this.ifElseBranchUpdateFunction(0, () => {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create('✓');
                                Text.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(159:19)", "entry");
                                Text.fontSize(20);
                                Text.fontColor('#1976D2');
                                Text.fontWeight(FontWeight.Bold);
                            }, Text);
                            Text.pop();
                        });
                    }
                    else {
                        this.ifElseBranchUpdateFunction(1, () => {
                        });
                    }
                }, If);
                If.pop();
                Row.pop();
            };
            this.forEachUpdateFunction(elmtId, this.limitOptions, forEachItemGenFunction, undefined, true, false);
        }, ForEach);
        ForEach.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 确认按钮
            Button.createWithLabel(this.isUpdating ? '设置中...' : '确认设置');
            Button.debugLine("entry/src/main/ets/pages/PayLimitSettingPage.ets(181:13)", "entry");
            // 确认按钮
            Button.width('100%');
            // 确认按钮
            Button.height(48);
            // 确认按钮
            Button.fontSize(16);
            // 确认按钮
            Button.fontColor(Color.White);
            // 确认按钮
            Button.backgroundColor(this.canSubmit() && !this.isUpdating ? '#1976D2' : '#CCCCCC');
            // 确认按钮
            Button.borderRadius(8);
            // 确认按钮
            Button.margin({ top: 20 });
            // 确认按钮
            Button.enabled(this.canSubmit() && !this.isUpdating);
            // 确认按钮
            Button.onClick(() => {
                this.updatePayLimit();
            });
        }, Button);
        // 确认按钮
        Button.pop();
        // 限额选择
        Column.pop();
        Column.pop();
        Scroll.pop();
        Column.pop();
    }
    // 检查是否可以提交
    canSubmit(): boolean {
        return this.selectedLimit > 0 && this.selectedLimit !== (this.userInfo?.payLimit || 0);
    }
    // 更新支付限额
    async updatePayLimit() {
        if (!this.canSubmit() || this.isUpdating)
            return;
        this.isUpdating = true;
        try {
            const data: UpdatePayLimitRequest = {
                payLimit: this.selectedLimit
            };
            console.log('正在设置支付限额:', this.selectedLimit);
            await UserApi.updatePayLimit(data);
            console.log('API调用成功');
            // 更新本地用户信息
            if (this.userInfo) {
                this.userInfo.payLimit = this.selectedLimit;
                await storageManager.saveUserInfo(this.userInfo);
                console.log('本地数据已保存，新限额:', this.userInfo.payLimit);
            }
            promptAction.showToast({ message: `支付限额已设置为 ¥${this.selectedLimit.toFixed(2)}` });
            // 返回设置页面
            router.back();
        }
        catch (error) {
            console.error('设置支付限额失败:', error);
            promptAction.showToast({ message: '设置支付限额失败，请重试' });
        }
        finally {
            this.isUpdating = false;
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "PayLimitSettingPage";
    }
}
registerNamedRoute(() => new PayLimitSettingPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/PayLimitSettingPage", pageFullPath: "entry/src/main/ets/pages/PayLimitSettingPage", integratedHsp: "false", moduleType: "followWithHap" });
