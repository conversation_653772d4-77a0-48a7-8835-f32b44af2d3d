{"version": "2.0", "ppid": 21780, "events": [{"head": {"id": "b74be08a-dbd9-4be2-a221-9e97d221fa10", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26019464520400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "061fd523-81f3-40ae-9e7b-97c65bc5c143", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26020084464500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c7fd67d-67b0-4dee-895e-eaeb7043477e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26020086193700, "endTime": 26020086225400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12784a9f-cf46-416c-b772-f3556d75af96", "logId": "446269b1-d4dc-4c72-8d35-dd7ed7a79a12"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "446269b1-d4dc-4c72-8d35-dd7ed7a79a12", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26020086193700, "endTime": 26020086225400}, "additional": {"logType": "info", "children": [], "durationId": "5c7fd67d-67b0-4dee-895e-eaeb7043477e"}}, {"head": {"id": "9d4cf36f-71eb-4fe7-86f3-2850c497bd02", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26020265799700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "899ac370-8fb0-40cd-bbba-3586720aa7c7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26020267472500, "endTime": 26020267501700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12784a9f-cf46-416c-b772-f3556d75af96", "logId": "17b4567e-8606-45ab-aad7-780c94c957e5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17b4567e-8606-45ab-aad7-780c94c957e5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26020267472500, "endTime": 26020267501700}, "additional": {"logType": "info", "children": [], "durationId": "899ac370-8fb0-40cd-bbba-3586720aa7c7"}}, {"head": {"id": "37be9d40-dabd-42f3-bed0-55b441278c8b", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26020392073100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7a116a6-4856-48e2-964e-ddbd42c2c6e8", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26020392339200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b55de143-4076-41e6-ae8f-d4209d29ef01", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202314189200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a13cf1c6-9c7f-404b-827f-f30ac0efa33b", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202330787500, "endTime": 26202659247700}, "additional": {"children": ["21a8d4e2-bd9f-4b06-8c2c-93bc6e373c3d", "deb53941-6614-4b6c-a82c-327d35abd454", "4c7e456a-1dfc-46f2-9068-02eb094b9c65", "7e83dfd3-dcd8-4853-9bcd-641dc5fa8222", "83872aac-edf9-4bfe-95ee-88a50dd61836", "1716eb2d-9323-41fe-a93f-87c2a8689430", "36c39cd2-cc40-45b9-81f8-afb671a452df"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "7c8192d3-f6a6-47d2-8049-583b90de824c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21a8d4e2-bd9f-4b06-8c2c-93bc6e373c3d", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202330789600, "endTime": 26202353964300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a13cf1c6-9c7f-404b-827f-f30ac0efa33b", "logId": "31fe5683-db79-4e17-ac25-38440c06c965"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "deb53941-6614-4b6c-a82c-327d35abd454", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202353984600, "endTime": 26202655670000}, "additional": {"children": ["7e7614f0-e6b0-4f1e-9cf1-6c299228a9ac", "93ea6344-c3a6-47da-8f18-e0735971d945", "93b8d044-5547-4ebd-8df4-432a3ab82ffb", "901ec4d6-5fbd-4b57-a65e-a67c917c7fe4", "ab07bb1f-b7a9-44dd-a83c-6ec525888073", "93c80fc1-3a0b-4be4-8e5b-42728d9dd888", "3899b315-54f6-438a-854a-10559a45e5d0", "b599f391-385c-4a3c-8b2b-00bc5e26d698", "9cbde986-c621-4133-bd37-5d73670a14ec"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a13cf1c6-9c7f-404b-827f-f30ac0efa33b", "logId": "75224333-4a49-4c4d-bfe9-7e56138acb33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c7e456a-1dfc-46f2-9068-02eb094b9c65", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202655695200, "endTime": 26202659182400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a13cf1c6-9c7f-404b-827f-f30ac0efa33b", "logId": "8b192d22-e0c2-4246-9380-a23e984f0c65"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e83dfd3-dcd8-4853-9bcd-641dc5fa8222", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202659211600, "endTime": 26202659234100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a13cf1c6-9c7f-404b-827f-f30ac0efa33b", "logId": "30fe9041-3351-4b15-8b9c-ada61a0bac58"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "83872aac-edf9-4bfe-95ee-88a50dd61836", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202336884600, "endTime": 26202336935100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a13cf1c6-9c7f-404b-827f-f30ac0efa33b", "logId": "2add61a3-586b-4017-a73d-87626812a57c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2add61a3-586b-4017-a73d-87626812a57c", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202336884600, "endTime": 26202336935100}, "additional": {"logType": "info", "children": [], "durationId": "83872aac-edf9-4bfe-95ee-88a50dd61836", "parent": "7c8192d3-f6a6-47d2-8049-583b90de824c"}}, {"head": {"id": "1716eb2d-9323-41fe-a93f-87c2a8689430", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202346929700, "endTime": 26202346957200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a13cf1c6-9c7f-404b-827f-f30ac0efa33b", "logId": "fd4566b6-a405-4ee3-98ca-f36890943eab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fd4566b6-a405-4ee3-98ca-f36890943eab", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202346929700, "endTime": 26202346957200}, "additional": {"logType": "info", "children": [], "durationId": "1716eb2d-9323-41fe-a93f-87c2a8689430", "parent": "7c8192d3-f6a6-47d2-8049-583b90de824c"}}, {"head": {"id": "de1b4b8d-f798-491d-8e69-2d11215f0bb2", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202347020400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6723df75-bf4d-41ad-8ed5-dfe653cd27fa", "name": "Cache service initialization finished in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202353804700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31fe5683-db79-4e17-ac25-38440c06c965", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202330789600, "endTime": 26202353964300}, "additional": {"logType": "info", "children": [], "durationId": "21a8d4e2-bd9f-4b06-8c2c-93bc6e373c3d", "parent": "7c8192d3-f6a6-47d2-8049-583b90de824c"}}, {"head": {"id": "7e7614f0-e6b0-4f1e-9cf1-6c299228a9ac", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202363626800, "endTime": 26202363639900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "deb53941-6614-4b6c-a82c-327d35abd454", "logId": "4b781ec0-14b3-440c-aca1-16e0fd3900ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93ea6344-c3a6-47da-8f18-e0735971d945", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202363656200, "endTime": 26202371096800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "deb53941-6614-4b6c-a82c-327d35abd454", "logId": "9b862299-a2d0-42e8-9710-6d654f7f3080"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93b8d044-5547-4ebd-8df4-432a3ab82ffb", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202371115700, "endTime": 26202509620900}, "additional": {"children": ["cb5f9bcf-bdfb-4209-b5b1-675c5664412f", "4e3715b9-6618-4e19-a7c4-24acec5d1e3e", "65006626-e66e-45f4-80f1-ff38485c50d7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "deb53941-6614-4b6c-a82c-327d35abd454", "logId": "3cedefc9-d81c-4b9f-a4ff-6ad93e0bce34"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "901ec4d6-5fbd-4b57-a65e-a67c917c7fe4", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202509650600, "endTime": 26202550510000}, "additional": {"children": ["b9ab4405-081a-4561-bf9f-c4aec58e8fc7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "deb53941-6614-4b6c-a82c-327d35abd454", "logId": "9d426b6a-f768-481f-8349-143d661afe99"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab07bb1f-b7a9-44dd-a83c-6ec525888073", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202550522600, "endTime": 26202588446000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "deb53941-6614-4b6c-a82c-327d35abd454", "logId": "e6861cf2-48fa-433c-9fb4-ad365fb26518"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93c80fc1-3a0b-4be4-8e5b-42728d9dd888", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202592055400, "endTime": 26202620254800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "deb53941-6614-4b6c-a82c-327d35abd454", "logId": "25bb543f-de87-4ca2-a36f-b76f5a717ba4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3899b315-54f6-438a-854a-10559a45e5d0", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202620283800, "endTime": 26202655419200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "deb53941-6614-4b6c-a82c-327d35abd454", "logId": "0ae2017c-b2e7-4354-86e4-c6fcab7b22b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b599f391-385c-4a3c-8b2b-00bc5e26d698", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202655447300, "endTime": 26202655650600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "deb53941-6614-4b6c-a82c-327d35abd454", "logId": "d32f4ba9-df2b-4ba8-b315-a898a3c7973c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b781ec0-14b3-440c-aca1-16e0fd3900ce", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202363626800, "endTime": 26202363639900}, "additional": {"logType": "info", "children": [], "durationId": "7e7614f0-e6b0-4f1e-9cf1-6c299228a9ac", "parent": "75224333-4a49-4c4d-bfe9-7e56138acb33"}}, {"head": {"id": "9b862299-a2d0-42e8-9710-6d654f7f3080", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202363656200, "endTime": 26202371096800}, "additional": {"logType": "info", "children": [], "durationId": "93ea6344-c3a6-47da-8f18-e0735971d945", "parent": "75224333-4a49-4c4d-bfe9-7e56138acb33"}}, {"head": {"id": "cb5f9bcf-bdfb-4209-b5b1-675c5664412f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202372504700, "endTime": 26202372542100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "93b8d044-5547-4ebd-8df4-432a3ab82ffb", "logId": "5145916b-d3cb-426f-a0b2-4b62aa82e689"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5145916b-d3cb-426f-a0b2-4b62aa82e689", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202372504700, "endTime": 26202372542100}, "additional": {"logType": "info", "children": [], "durationId": "cb5f9bcf-bdfb-4209-b5b1-675c5664412f", "parent": "3cedefc9-d81c-4b9f-a4ff-6ad93e0bce34"}}, {"head": {"id": "4e3715b9-6618-4e19-a7c4-24acec5d1e3e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202376999200, "endTime": 26202507964600}, "additional": {"children": ["30a7b4c3-2714-4964-88e1-90c5829e2a1b", "5d0dbe88-8284-45c9-bba1-415785a9d4f7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "93b8d044-5547-4ebd-8df4-432a3ab82ffb", "logId": "5fbc7a53-0697-4cad-b29b-a9852b4f2e71"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30a7b4c3-2714-4964-88e1-90c5829e2a1b", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202377001500, "endTime": 26202382560900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4e3715b9-6618-4e19-a7c4-24acec5d1e3e", "logId": "57aab139-fdf7-4447-ab9d-64e02c349866"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5d0dbe88-8284-45c9-bba1-415785a9d4f7", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202382578000, "endTime": 26202507951100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4e3715b9-6618-4e19-a7c4-24acec5d1e3e", "logId": "265e135f-6e08-45b2-adfd-93b923fbaeea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "651c7265-7a31-471e-ad44-b5ac6fcda553", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202377010900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa9aaa40-af5c-4598-ba7c-9aa2991358e9", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202382385200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57aab139-fdf7-4447-ab9d-64e02c349866", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202377001500, "endTime": 26202382560900}, "additional": {"logType": "info", "children": [], "durationId": "30a7b4c3-2714-4964-88e1-90c5829e2a1b", "parent": "5fbc7a53-0697-4cad-b29b-a9852b4f2e71"}}, {"head": {"id": "85f99ffe-9883-4279-993e-82607ff0e707", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202382595900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea04aa7c-53e1-41bf-bf3e-7510b96e5543", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202399811900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e2052bd-8b60-4d0c-ab70-391d10449fec", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202400010100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b957874-060c-4b63-b5e8-6353e155a49c", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202400233900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1da14eef-9ce6-4402-974d-6c26a10e2589", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202400376200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aafad352-d4ac-4694-a001-83dead41190d", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202403840500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f43cdbad-32bd-46b5-814f-207156f89c5b", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202410601700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfdd0ba4-4a53-466e-8cb5-84bf6d9bac35", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202427985200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b092d81-0cea-4037-b996-82cc15da287d", "name": "Sdk init in 52 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202464433300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b2bbb6b-9da0-45d6-bd6d-1d40c4f80583", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202464633900}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 16, "minute": 22}, "markType": "other"}}, {"head": {"id": "e1b42ee5-0b7d-4ba9-854d-158c1f9ba37d", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202464655000}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 16, "minute": 22}, "markType": "other"}}, {"head": {"id": "d387c05d-c785-4694-b0f2-8a833c4bf2e8", "name": "Project task initialization takes 41 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202507619900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "789a8aba-7472-4695-afee-096cec31eaaf", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202507764000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c49ff2e4-d5e1-49e1-9e0c-397cc49e6592", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202507834200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddc63b21-5895-4195-a70a-7d5ff0861650", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202507894100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "265e135f-6e08-45b2-adfd-93b923fbaeea", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202382578000, "endTime": 26202507951100}, "additional": {"logType": "info", "children": [], "durationId": "5d0dbe88-8284-45c9-bba1-415785a9d4f7", "parent": "5fbc7a53-0697-4cad-b29b-a9852b4f2e71"}}, {"head": {"id": "5fbc7a53-0697-4cad-b29b-a9852b4f2e71", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202376999200, "endTime": 26202507964600}, "additional": {"logType": "info", "children": ["57aab139-fdf7-4447-ab9d-64e02c349866", "265e135f-6e08-45b2-adfd-93b923fbaeea"], "durationId": "4e3715b9-6618-4e19-a7c4-24acec5d1e3e", "parent": "3cedefc9-d81c-4b9f-a4ff-6ad93e0bce34"}}, {"head": {"id": "65006626-e66e-45f4-80f1-ff38485c50d7", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202509551300, "endTime": 26202509598400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "93b8d044-5547-4ebd-8df4-432a3ab82ffb", "logId": "0c56320a-62d3-4b44-becf-90f4de9779f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c56320a-62d3-4b44-becf-90f4de9779f8", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202509551300, "endTime": 26202509598400}, "additional": {"logType": "info", "children": [], "durationId": "65006626-e66e-45f4-80f1-ff38485c50d7", "parent": "3cedefc9-d81c-4b9f-a4ff-6ad93e0bce34"}}, {"head": {"id": "3cedefc9-d81c-4b9f-a4ff-6ad93e0bce34", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202371115700, "endTime": 26202509620900}, "additional": {"logType": "info", "children": ["5145916b-d3cb-426f-a0b2-4b62aa82e689", "5fbc7a53-0697-4cad-b29b-a9852b4f2e71", "0c56320a-62d3-4b44-becf-90f4de9779f8"], "durationId": "93b8d044-5547-4ebd-8df4-432a3ab82ffb", "parent": "75224333-4a49-4c4d-bfe9-7e56138acb33"}}, {"head": {"id": "b9ab4405-081a-4561-bf9f-c4aec58e8fc7", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202510897300, "endTime": 26202550484800}, "additional": {"children": ["8b6d6b5b-2612-480e-aeb9-5cb6e2717426", "7e52e6a7-09f1-4eac-ac76-f94ba9e31c2f", "acbc568e-b26d-494d-bc8d-dadfc4a2099f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "901ec4d6-5fbd-4b57-a65e-a67c917c7fe4", "logId": "a229fdc9-b737-49a4-b16d-2b1b9cd571e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b6d6b5b-2612-480e-aeb9-5cb6e2717426", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202515872800, "endTime": 26202515898600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b9ab4405-081a-4561-bf9f-c4aec58e8fc7", "logId": "fd1cf250-28ab-48a4-8f27-d117da5610a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fd1cf250-28ab-48a4-8f27-d117da5610a2", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202515872800, "endTime": 26202515898600}, "additional": {"logType": "info", "children": [], "durationId": "8b6d6b5b-2612-480e-aeb9-5cb6e2717426", "parent": "a229fdc9-b737-49a4-b16d-2b1b9cd571e9"}}, {"head": {"id": "7e52e6a7-09f1-4eac-ac76-f94ba9e31c2f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202520159100, "endTime": 26202547272400}, "additional": {"children": ["62ef8bd6-cb97-4c15-b93b-4bb200cd8d3f", "83340d73-a456-48ca-9927-d9b346dd3491"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b9ab4405-081a-4561-bf9f-c4aec58e8fc7", "logId": "742a8421-d518-4870-aeca-a7fe9dd0240d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62ef8bd6-cb97-4c15-b93b-4bb200cd8d3f", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202520161200, "endTime": 26202525844700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7e52e6a7-09f1-4eac-ac76-f94ba9e31c2f", "logId": "89974be8-f335-474c-ad9d-867cbc4abccc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "83340d73-a456-48ca-9927-d9b346dd3491", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202525864100, "endTime": 26202547254100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7e52e6a7-09f1-4eac-ac76-f94ba9e31c2f", "logId": "16aa1a42-6549-4826-85c4-54fa71cb68e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03e00122-d57a-403a-ac2a-3e16f500e9ad", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202520171200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18ebb8c4-8fd5-463b-bb45-675835bd6db3", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202525678800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89974be8-f335-474c-ad9d-867cbc4abccc", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202520161200, "endTime": 26202525844700}, "additional": {"logType": "info", "children": [], "durationId": "62ef8bd6-cb97-4c15-b93b-4bb200cd8d3f", "parent": "742a8421-d518-4870-aeca-a7fe9dd0240d"}}, {"head": {"id": "a2cc1e81-9dfe-4078-b206-c06fe88aac4f", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202525891100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c77c223d-29a4-48c0-bb1a-c16229beaa92", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202538658800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2c887bd-574e-420f-ae65-5ee88eed1ef3", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202538851300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdbfc231-f0fa-418f-8df7-a26cc50a57e4", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202540121200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6359afc-dc79-42c5-b88b-a7a590245ac7", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202540396900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "353515eb-3b0a-43da-be8c-cfb6f9d395c6", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202540498200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6218ee6a-d6bb-4d9f-9a5b-e44e4dc85670", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202540572600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcaef7c2-528d-49ef-bf3f-63a015eafb75", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202540651300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d600ba6-027b-4073-906f-570dedbc2a65", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202546324900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b0fe10d-ef8c-419e-99c1-e3116cac3db0", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202546908200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b43a8ac7-e864-4b4c-b204-18a4ac55e879", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202547104500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31e64a7e-89c9-41d9-88ff-ec28187e82ce", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202547194000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16aa1a42-6549-4826-85c4-54fa71cb68e3", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202525864100, "endTime": 26202547254100}, "additional": {"logType": "info", "children": [], "durationId": "83340d73-a456-48ca-9927-d9b346dd3491", "parent": "742a8421-d518-4870-aeca-a7fe9dd0240d"}}, {"head": {"id": "742a8421-d518-4870-aeca-a7fe9dd0240d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202520159100, "endTime": 26202547272400}, "additional": {"logType": "info", "children": ["89974be8-f335-474c-ad9d-867cbc4abccc", "16aa1a42-6549-4826-85c4-54fa71cb68e3"], "durationId": "7e52e6a7-09f1-4eac-ac76-f94ba9e31c2f", "parent": "a229fdc9-b737-49a4-b16d-2b1b9cd571e9"}}, {"head": {"id": "acbc568e-b26d-494d-bc8d-dadfc4a2099f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202550423400, "endTime": 26202550459800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b9ab4405-081a-4561-bf9f-c4aec58e8fc7", "logId": "51aa6adc-865c-42de-8aa5-9d841ec284b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51aa6adc-865c-42de-8aa5-9d841ec284b6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202550423400, "endTime": 26202550459800}, "additional": {"logType": "info", "children": [], "durationId": "acbc568e-b26d-494d-bc8d-dadfc4a2099f", "parent": "a229fdc9-b737-49a4-b16d-2b1b9cd571e9"}}, {"head": {"id": "a229fdc9-b737-49a4-b16d-2b1b9cd571e9", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202510897300, "endTime": 26202550484800}, "additional": {"logType": "info", "children": ["fd1cf250-28ab-48a4-8f27-d117da5610a2", "742a8421-d518-4870-aeca-a7fe9dd0240d", "51aa6adc-865c-42de-8aa5-9d841ec284b6"], "durationId": "b9ab4405-081a-4561-bf9f-c4aec58e8fc7", "parent": "9d426b6a-f768-481f-8349-143d661afe99"}}, {"head": {"id": "9d426b6a-f768-481f-8349-143d661afe99", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202509650600, "endTime": 26202550510000}, "additional": {"logType": "info", "children": ["a229fdc9-b737-49a4-b16d-2b1b9cd571e9"], "durationId": "901ec4d6-5fbd-4b57-a65e-a67c917c7fe4", "parent": "75224333-4a49-4c4d-bfe9-7e56138acb33"}}, {"head": {"id": "43d709fa-7d63-413c-9657-22eacb3753bf", "name": "watch files: [\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\hvigorfile.ts',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202587531300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dc02bf7-af0d-4f59-9740-59f551349f43", "name": "hvigorfile, resolve hvigorfile dependencies in 38 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202588273000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6861cf2-48fa-433c-9fb4-ad365fb26518", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202550522600, "endTime": 26202588446000}, "additional": {"logType": "info", "children": [], "durationId": "ab07bb1f-b7a9-44dd-a83c-6ec525888073", "parent": "75224333-4a49-4c4d-bfe9-7e56138acb33"}}, {"head": {"id": "9cbde986-c621-4133-bd37-5d73670a14ec", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202591606900, "endTime": 26202592038300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "deb53941-6614-4b6c-a82c-327d35abd454", "logId": "abadfb16-aa0b-4b93-bf3a-0d0efe3213ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28a85496-6333-41a1-ac72-74ac48ee9a93", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202591662700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abadfb16-aa0b-4b93-bf3a-0d0efe3213ef", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202591606900, "endTime": 26202592038300}, "additional": {"logType": "info", "children": [], "durationId": "9cbde986-c621-4133-bd37-5d73670a14ec", "parent": "75224333-4a49-4c4d-bfe9-7e56138acb33"}}, {"head": {"id": "2dbdb94f-d154-48c9-9795-918f1f05049f", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202597334300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98078de3-e542-41cd-87b2-3b439abe2916", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202616967900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25bb543f-de87-4ca2-a36f-b76f5a717ba4", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202592055400, "endTime": 26202620254800}, "additional": {"logType": "info", "children": [], "durationId": "93c80fc1-3a0b-4be4-8e5b-42728d9dd888", "parent": "75224333-4a49-4c4d-bfe9-7e56138acb33"}}, {"head": {"id": "f78b905d-2e71-42d3-80eb-c3eedfd482dd", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202620315000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99b2580d-b6f2-429b-bf22-276915c93190", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202640056900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6221368-62a0-4ac6-862c-68f1a194cfda", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202640230800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ad9ee71-54fc-4fcd-aa92-7f7f3a08559f", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202640594400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7034208-97ef-4d56-ae90-8ee534d2787c", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202648531200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "caebeda3-3228-427f-849c-93cdbe323dcc", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202648669700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ae2017c-b2e7-4354-86e4-c6fcab7b22b4", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202620283800, "endTime": 26202655419200}, "additional": {"logType": "info", "children": [], "durationId": "3899b315-54f6-438a-854a-10559a45e5d0", "parent": "75224333-4a49-4c4d-bfe9-7e56138acb33"}}, {"head": {"id": "e87f65f4-4127-46b5-a157-d045146f0ccd", "name": "Configuration phase cost:292 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202655488900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d32f4ba9-df2b-4ba8-b315-a898a3c7973c", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202655447300, "endTime": 26202655650600}, "additional": {"logType": "info", "children": [], "durationId": "b599f391-385c-4a3c-8b2b-00bc5e26d698", "parent": "75224333-4a49-4c4d-bfe9-7e56138acb33"}}, {"head": {"id": "75224333-4a49-4c4d-bfe9-7e56138acb33", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202353984600, "endTime": 26202655670000}, "additional": {"logType": "info", "children": ["4b781ec0-14b3-440c-aca1-16e0fd3900ce", "9b862299-a2d0-42e8-9710-6d654f7f3080", "3cedefc9-d81c-4b9f-a4ff-6ad93e0bce34", "9d426b6a-f768-481f-8349-143d661afe99", "e6861cf2-48fa-433c-9fb4-ad365fb26518", "25bb543f-de87-4ca2-a36f-b76f5a717ba4", "0ae2017c-b2e7-4354-86e4-c6fcab7b22b4", "d32f4ba9-df2b-4ba8-b315-a898a3c7973c", "abadfb16-aa0b-4b93-bf3a-0d0efe3213ef"], "durationId": "deb53941-6614-4b6c-a82c-327d35abd454", "parent": "7c8192d3-f6a6-47d2-8049-583b90de824c"}}, {"head": {"id": "36c39cd2-cc40-45b9-81f8-afb671a452df", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202659105800, "endTime": 26202659156700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a13cf1c6-9c7f-404b-827f-f30ac0efa33b", "logId": "345dee9a-32bf-45e7-a07c-9f0e22f95c47"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "345dee9a-32bf-45e7-a07c-9f0e22f95c47", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202659105800, "endTime": 26202659156700}, "additional": {"logType": "info", "children": [], "durationId": "36c39cd2-cc40-45b9-81f8-afb671a452df", "parent": "7c8192d3-f6a6-47d2-8049-583b90de824c"}}, {"head": {"id": "8b192d22-e0c2-4246-9380-a23e984f0c65", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202655695200, "endTime": 26202659182400}, "additional": {"logType": "info", "children": [], "durationId": "4c7e456a-1dfc-46f2-9068-02eb094b9c65", "parent": "7c8192d3-f6a6-47d2-8049-583b90de824c"}}, {"head": {"id": "30fe9041-3351-4b15-8b9c-ada61a0bac58", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202659211600, "endTime": 26202659234100}, "additional": {"logType": "info", "children": [], "durationId": "7e83dfd3-dcd8-4853-9bcd-641dc5fa8222", "parent": "7c8192d3-f6a6-47d2-8049-583b90de824c"}}, {"head": {"id": "7c8192d3-f6a6-47d2-8049-583b90de824c", "name": "init", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202330787500, "endTime": 26202659247700}, "additional": {"logType": "info", "children": ["31fe5683-db79-4e17-ac25-38440c06c965", "75224333-4a49-4c4d-bfe9-7e56138acb33", "8b192d22-e0c2-4246-9380-a23e984f0c65", "30fe9041-3351-4b15-8b9c-ada61a0bac58", "2add61a3-586b-4017-a73d-87626812a57c", "fd4566b6-a405-4ee3-98ca-f36890943eab", "345dee9a-32bf-45e7-a07c-9f0e22f95c47"], "durationId": "a13cf1c6-9c7f-404b-827f-f30ac0efa33b"}}, {"head": {"id": "4ce3df31-a8ea-4027-a751-ddfa07b4ef50", "name": "Configuration task cost before running: 337 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202659885100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "440ba457-1752-4360-9f14-e746dd2bbf6b", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202674984300, "endTime": 26202703040400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "626159cb-7aca-4bbe-b31f-fc9e618205bf", "logId": "4402bdea-8cd0-4276-aed2-4bb93ad8acf6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "626159cb-7aca-4bbe-b31f-fc9e618205bf", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202666016600}, "additional": {"logType": "detail", "children": [], "durationId": "440ba457-1752-4360-9f14-e746dd2bbf6b"}}, {"head": {"id": "d46c9c54-53dd-459f-bf7f-dc2358754ee2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202667486900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6a3afed-a512-4d59-9fe0-f733847ba9c1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202667803000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be8a54d6-090a-4e10-acb1-87d4f3ee2bda", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202675014400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04604a06-eb45-45ed-9622-c140188a3c15", "name": "Incremental task entry:default@PreBuild pre-execution cost: 19 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202702618200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee4ebcd7-19b4-4437-83f3-40471731f72f", "name": "entry : default@PreBuild cost memory 0.2812957763671875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202702905900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4402bdea-8cd0-4276-aed2-4bb93ad8acf6", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202674984300, "endTime": 26202703040400}, "additional": {"logType": "info", "children": [], "durationId": "440ba457-1752-4360-9f14-e746dd2bbf6b"}}, {"head": {"id": "9f55c5ba-8121-42dc-a176-4a5803eaa62b", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202714279000, "endTime": 26202719426300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "6efe8e8f-aade-4460-8f02-73f40c16431b", "logId": "745e82ca-3ab9-460e-9a5a-86a68cd48df1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6efe8e8f-aade-4460-8f02-73f40c16431b", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202711837600}, "additional": {"logType": "detail", "children": [], "durationId": "9f55c5ba-8121-42dc-a176-4a5803eaa62b"}}, {"head": {"id": "4042b1c0-f9ca-41d8-87ac-7d59b3b2077f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202712892500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5252bdf-94d6-40c4-9ad4-8ea1a90a836b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202713032400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64cfe0c1-1299-40d9-a157-247b0be5ed70", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202714293700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1386825f-d74a-4d27-9c13-5837e67e9e31", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202719098100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9338d3cd-c117-42f4-8596-9f6c359a0329", "name": "entry : default@MergeProfile cost memory -0.6813201904296875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202719325900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "745e82ca-3ab9-460e-9a5a-86a68cd48df1", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202714279000, "endTime": 26202719426300}, "additional": {"logType": "info", "children": [], "durationId": "9f55c5ba-8121-42dc-a176-4a5803eaa62b"}}, {"head": {"id": "de363922-3a75-412c-a2aa-507a3d7f8b2c", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202725311200, "endTime": 26202730946000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "28f88e56-68e5-4016-8aa8-5a4ae1c539ac", "logId": "9a95c11d-b497-4531-8342-ad3f8dcb024c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28f88e56-68e5-4016-8aa8-5a4ae1c539ac", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202722442700}, "additional": {"logType": "detail", "children": [], "durationId": "de363922-3a75-412c-a2aa-507a3d7f8b2c"}}, {"head": {"id": "a7e8d45d-9362-4701-ba02-6ae48d2cccd3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202723582000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3373eecb-0381-4e19-9077-f065bd3a5527", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202723754700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ae9ef7c-4c4b-4463-a7e8-dcbb16483c62", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202725326300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2896f38a-46be-4573-8f48-bf9aac13b949", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 3 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202727703000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02346408-d1dc-4087-b10e-1bd21bcf2181", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202730667200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ff6cdf5-5fca-4b73-a43b-181354b540a1", "name": "entry : default@CreateBuildProfile cost memory 0.09955596923828125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202730851700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a95c11d-b497-4531-8342-ad3f8dcb024c", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202725311200, "endTime": 26202730946000}, "additional": {"logType": "info", "children": [], "durationId": "de363922-3a75-412c-a2aa-507a3d7f8b2c"}}, {"head": {"id": "2f6f9f29-dfa9-4ad1-922d-4d1637e5cc04", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202736756500, "endTime": 26202737473600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "77295943-d0ba-4d24-9364-d201280b7eca", "logId": "75732388-18ed-4575-8bc6-cf3ab577fa33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "77295943-d0ba-4d24-9364-d201280b7eca", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202733591200}, "additional": {"logType": "detail", "children": [], "durationId": "2f6f9f29-dfa9-4ad1-922d-4d1637e5cc04"}}, {"head": {"id": "54c3bcad-dea5-4b93-9fb6-7769728453db", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202734831500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "462e76b5-5029-4356-8bff-dc176e2693e4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202735027100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1680e83-c121-4db0-8fd3-6f07c174cd9b", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202736774700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "199a8545-077b-4974-93e3-423826e5b72f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202736978200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9346369e-5917-400b-aa83-6dac8d8aa642", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202737056300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0d98e40-1ffa-4748-940a-f917538eb0b7", "name": "entry : default@PreCheckSyscap cost memory 0.0367584228515625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202737167000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c588749-f947-4e49-85a1-d0b59939e351", "name": "runTaskFromQueue task cost before running: 415 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202737279700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75732388-18ed-4575-8bc6-cf3ab577fa33", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202736756500, "endTime": 26202737473600, "totalTime": 490400}, "additional": {"logType": "info", "children": [], "durationId": "2f6f9f29-dfa9-4ad1-922d-4d1637e5cc04"}}, {"head": {"id": "844b82af-8f68-4664-8dad-c05df2139ec2", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202756281800, "endTime": 26202758237000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0b55c43c-4bc8-42fc-ae8e-f84be0e7bed9", "logId": "2ca07a88-366b-48c6-b346-bc940bd9bcd2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b55c43c-4bc8-42fc-ae8e-f84be0e7bed9", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202740159000}, "additional": {"logType": "detail", "children": [], "durationId": "844b82af-8f68-4664-8dad-c05df2139ec2"}}, {"head": {"id": "61cbe656-da32-4f90-a086-87b2a4483c19", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202741479200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51b54dc6-d942-4a99-91d2-e5ca358af827", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202742323600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43d7d607-9c5e-49eb-bc2e-b0583321e764", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202756300500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07f28000-4872-43c6-979b-482c16201d82", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202756589800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6233be7b-961c-4245-a59d-feab083bc56f", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202757886800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c150af4e-9538-41ba-9abd-cd01229831ae", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0667572021484375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202758096400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ca07a88-366b-48c6-b346-bc940bd9bcd2", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202756281800, "endTime": 26202758237000}, "additional": {"logType": "info", "children": [], "durationId": "844b82af-8f68-4664-8dad-c05df2139ec2"}}, {"head": {"id": "13eb81d1-aa55-4d12-b062-638c0fa5d8e9", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202764570600, "endTime": 26202766853800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "e926ce58-d59c-41ef-98ed-e8b2e62942dd", "logId": "73ef25b2-d072-4cbf-b748-feb6774bcc21"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e926ce58-d59c-41ef-98ed-e8b2e62942dd", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202761244600}, "additional": {"logType": "detail", "children": [], "durationId": "13eb81d1-aa55-4d12-b062-638c0fa5d8e9"}}, {"head": {"id": "ff288bfa-2ec3-4706-acbb-75d679415a7c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202762260600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fce9814a-8085-47d8-9cd4-37ca0b98d5c6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202762400000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62aad98e-d4ce-4c76-9edf-e9e9cadfdc08", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202764584700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8698010e-3581-4874-9ded-a62efc7a53ec", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202766533500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "527cce54-f9f5-412f-b6a4-f2f75faf92ad", "name": "entry : default@ProcessProfile cost memory 0.0575714111328125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202766702700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73ef25b2-d072-4cbf-b748-feb6774bcc21", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202764570600, "endTime": 26202766853800}, "additional": {"logType": "info", "children": [], "durationId": "13eb81d1-aa55-4d12-b062-638c0fa5d8e9"}}, {"head": {"id": "932ff6e0-4f1c-4510-8a45-e94797d94b28", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202778183100, "endTime": 26202790939900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "64ced1f3-6a6b-465a-ae77-d22e9fbb1b22", "logId": "5553fd93-19e2-4b11-ae10-28051b5e4623"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "64ced1f3-6a6b-465a-ae77-d22e9fbb1b22", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202770943600}, "additional": {"logType": "detail", "children": [], "durationId": "932ff6e0-4f1c-4510-8a45-e94797d94b28"}}, {"head": {"id": "b6c772c9-79e6-457f-a551-11b155020534", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202772069400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74b00d9f-c552-4740-9cf2-9e9757cc8805", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202772254300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a8fc9c2-2836-4272-a782-58e99a0f2a42", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202778203800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80228336-77de-4940-b755-1c25668d1fad", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202790643300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31a27688-6569-4987-afdc-4db6b85d9d61", "name": "entry : default@ProcessRouterMap cost memory 0.19757843017578125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202790829500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5553fd93-19e2-4b11-ae10-28051b5e4623", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202778183100, "endTime": 26202790939900}, "additional": {"logType": "info", "children": [], "durationId": "932ff6e0-4f1c-4510-8a45-e94797d94b28"}}, {"head": {"id": "5f3ab410-3e39-4fe0-a582-a3de31d8fcd3", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202806445500, "endTime": 26202813321000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "932cde9b-ff97-444e-9b4d-4395241453d2", "logId": "ddb9c5a9-ec9e-4549-ade3-3beeb1b70225"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "932cde9b-ff97-444e-9b4d-4395241453d2", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202797748100}, "additional": {"logType": "detail", "children": [], "durationId": "5f3ab410-3e39-4fe0-a582-a3de31d8fcd3"}}, {"head": {"id": "1e703b53-0b84-4df1-b6d5-afd3721ee4fc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202798829600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a503a768-8c91-4227-b920-7d56fe9f6332", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202799017000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1f5ca96-4783-4261-a396-d6d2affad785", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202801703100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "177e905d-641a-42dc-81da-54bc52d91e4a", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202808904800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b5631d5-b294-424f-9ac9-e188c0f0274e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202809387000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "070839ac-e089-4d42-835b-bbdc73606bdb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202809518400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ca14546-25d5-4fb0-9d29-e16618df74b2", "name": "entry : default@PreviewProcessResource cost memory 0.06993865966796875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202809712700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60adcfa0-3867-4f7c-af43-510528a4d7c5", "name": "runTaskFromQueue task cost before running: 491 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202813103000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddb9c5a9-ec9e-4549-ade3-3beeb1b70225", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202806445500, "endTime": 26202813321000, "totalTime": 3396200}, "additional": {"logType": "info", "children": [], "durationId": "5f3ab410-3e39-4fe0-a582-a3de31d8fcd3"}}, {"head": {"id": "9630e695-9ec1-47d6-b0fb-8fb2ea46ad26", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202827435300, "endTime": 26202866060000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "815d39a0-739b-485b-937e-565ab1b3ea0f", "logId": "14cce2d7-3f17-4326-950f-1e29d71f4c8b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "815d39a0-739b-485b-937e-565ab1b3ea0f", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202819133600}, "additional": {"logType": "detail", "children": [], "durationId": "9630e695-9ec1-47d6-b0fb-8fb2ea46ad26"}}, {"head": {"id": "fcbac33c-f27b-4521-b0e2-04343746e40a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202820153300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1062bd0a-f1f3-43c7-8b84-8da2a9db07d6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202820294300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97acf0a6-f1d8-4e0e-976e-13a999ab1a78", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202827455800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a573460e-7bc1-4472-bcca-5fe2ef4dc414", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 20 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202865780000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e371360-aa12-41f9-b1f6-ccb42e4e974e", "name": "entry : default@GenerateLoaderJson cost memory 0.754364013671875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202865962200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14cce2d7-3f17-4326-950f-1e29d71f4c8b", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202827435300, "endTime": 26202866060000}, "additional": {"logType": "info", "children": [], "durationId": "9630e695-9ec1-47d6-b0fb-8fb2ea46ad26"}}, {"head": {"id": "fdf63fc5-8fad-4744-b1a3-939fe0fa69b6", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202893398200, "endTime": 26202955427900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "d3eab5ff-d0d4-47f0-a976-9490007947ee", "logId": "e7836949-ee9e-4995-9b5d-12977fbf8b49"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3eab5ff-d0d4-47f0-a976-9490007947ee", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202885708900}, "additional": {"logType": "detail", "children": [], "durationId": "fdf63fc5-8fad-4744-b1a3-939fe0fa69b6"}}, {"head": {"id": "072b2140-df4c-4f05-b4ca-08c3641f7738", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202886868400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "696de7f0-d1d6-46e2-a272-cee826eac43c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202887022400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1284edc-bf76-46d7-be0c-4c0ebb8105aa", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202888779300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18a6ed6d-c47a-4896-a3c8-bafda7d495e6", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202893465100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c01520b-72c0-4873-a618-499112ba66f9", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 60 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202955118500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d290fc2-ff3d-43e0-b1be-18d7f0371107", "name": "entry : default@PreviewCompileResource cost memory 0.7035598754882812", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202955300300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7836949-ee9e-4995-9b5d-12977fbf8b49", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202893398200, "endTime": 26202955427900}, "additional": {"logType": "info", "children": [], "durationId": "fdf63fc5-8fad-4744-b1a3-939fe0fa69b6"}}, {"head": {"id": "c2348fec-c042-4a5a-bb55-80f7a7fd46cb", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202960741800, "endTime": 26202961294400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "ce2b0925-bbde-4847-b42a-23d24c8f91e9", "logId": "8e72366f-20db-47f0-868b-51ca123433af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce2b0925-bbde-4847-b42a-23d24c8f91e9", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202959396200}, "additional": {"logType": "detail", "children": [], "durationId": "c2348fec-c042-4a5a-bb55-80f7a7fd46cb"}}, {"head": {"id": "314d9f3f-e997-48d1-b0eb-d06924e16094", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202960454500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adde7cf5-bf97-4988-bfa8-8d27a4e60dcc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202960619400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "056f40e8-44d8-4d28-b31a-66f15567baac", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202960751300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61d528bd-2067-4858-9447-3312eadec9db", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202960869800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46633cd0-fcf2-4723-9b85-a410bba837ab", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202960959500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bfbeb9e-1101-42bf-ab35-fada24f35d3e", "name": "entry : default@PreviewHookCompileResource cost memory 0.037872314453125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202961068800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efe93114-0a0d-4d98-963a-4b4038b49457", "name": "runTaskFromQueue task cost before running: 639 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202961201200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e72366f-20db-47f0-868b-51ca123433af", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202960741800, "endTime": 26202961294400, "totalTime": 424900}, "additional": {"logType": "info", "children": [], "durationId": "c2348fec-c042-4a5a-bb55-80f7a7fd46cb"}}, {"head": {"id": "636522ee-5938-485d-99c3-daca38c73984", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202966162900, "endTime": 26202971327700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "7cdc6802-7c55-4375-b0d4-a1b4b30c61c7", "logId": "a17e9e9f-79b6-4cfb-a1ef-3ab4b0dacd45"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7cdc6802-7c55-4375-b0d4-a1b4b30c61c7", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202964138300}, "additional": {"logType": "detail", "children": [], "durationId": "636522ee-5938-485d-99c3-daca38c73984"}}, {"head": {"id": "25d1faaf-9b16-44a7-b394-909149bf4b72", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202964997300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c874540-e983-48e8-bc93-d18ce23c3f03", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202965121100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5efab681-7d1c-4c1c-a58e-c3df90a1edbc", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202966177400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ec475d9-1ea0-4139-8989-f3e7d3d54ace", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202971054100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c97bcf3f-20a3-4036-ac8c-db0d578db8bc", "name": "entry : default@CopyPreviewProfile cost memory 0.09917449951171875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202971230800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a17e9e9f-79b6-4cfb-a1ef-3ab4b0dacd45", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202966162900, "endTime": 26202971327700}, "additional": {"logType": "info", "children": [], "durationId": "636522ee-5938-485d-99c3-daca38c73984"}}, {"head": {"id": "915c4ef4-4ff1-4c15-b8eb-f3da33374a5f", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202977117300, "endTime": 26202977724400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "25cd0e4c-1908-4a37-8c1e-f2105b7f4dcc", "logId": "da04d045-ecda-41ca-96a2-8c22da6c247b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "25cd0e4c-1908-4a37-8c1e-f2105b7f4dcc", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202973961200}, "additional": {"logType": "detail", "children": [], "durationId": "915c4ef4-4ff1-4c15-b8eb-f3da33374a5f"}}, {"head": {"id": "65eb6f0f-aa37-45f3-b707-2f556ef2fd93", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202974813500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e62fc7bf-857e-4c64-aa7a-3c4b1306b64c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202974953800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1828451-7d2e-4b2b-bc52-c17e2a723004", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202977133200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "670dfc91-7eba-4c6a-93d1-176e06e3a021", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202977311300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5099a086-b122-48bb-9409-498a885b5e2a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202977395700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25b23b4e-8aa3-4d1f-b7a1-43b421234095", "name": "entry : default@ReplacePreviewerPage cost memory 0.0378265380859375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202977540200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9664be3-e254-44ff-a406-148b2ffbebda", "name": "runTaskFromQueue task cost before running: 655 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202977653500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da04d045-ecda-41ca-96a2-8c22da6c247b", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202977117300, "endTime": 26202977724400, "totalTime": 504000}, "additional": {"logType": "info", "children": [], "durationId": "915c4ef4-4ff1-4c15-b8eb-f3da33374a5f"}}, {"head": {"id": "3d7e7771-7e85-41ac-9a6f-22ac440b109f", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202980307100, "endTime": 26202980655100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "81bea785-03aa-4639-9e9e-3b54a3d51a86", "logId": "5633bb43-62eb-4f81-80e8-d5d34396443f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "81bea785-03aa-4639-9e9e-3b54a3d51a86", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202980247300}, "additional": {"logType": "detail", "children": [], "durationId": "3d7e7771-7e85-41ac-9a6f-22ac440b109f"}}, {"head": {"id": "fbb762f9-4a19-4e72-8e57-a3465f3c1bf4", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202980315100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a03fce4b-4201-4992-9b8d-a5a0fa73ccbd", "name": "entry : buildPreviewerResource cost memory 0.011627197265625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202980466600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccf4e2e5-e0d2-4739-8847-eca8489e2380", "name": "runTaskFromQueue task cost before running: 658 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202980572600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5633bb43-62eb-4f81-80e8-d5d34396443f", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202980307100, "endTime": 26202980655100, "totalTime": 237400}, "additional": {"logType": "info", "children": [], "durationId": "3d7e7771-7e85-41ac-9a6f-22ac440b109f"}}, {"head": {"id": "bed8a9cb-3815-40ed-8a6f-6e3adfbb75cb", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202986443800, "endTime": 26202990973400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "8b02538a-62da-4436-930a-0da370c45c24", "logId": "9f45c8c6-c079-43a2-aa2a-ab73b104ba70"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b02538a-62da-4436-930a-0da370c45c24", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202983922700}, "additional": {"logType": "detail", "children": [], "durationId": "bed8a9cb-3815-40ed-8a6f-6e3adfbb75cb"}}, {"head": {"id": "224d05b5-2819-4f59-b3be-ebb1972600a8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202984995300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b564333-133b-45e9-a0d4-f3e8d08aff12", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202985176400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff76fbbf-c4f9-4a46-bd0a-dd6e1b2dabac", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202986455000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35cbc233-b703-4572-bdac-d413502f804e", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202990727700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7385d3b-6391-4d32-83ec-afef696baa15", "name": "entry : default@PreviewUpdateAssets cost memory 0.1153106689453125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202990877500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f45c8c6-c079-43a2-aa2a-ab73b104ba70", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202986443800, "endTime": 26202990973400}, "additional": {"logType": "info", "children": [], "durationId": "bed8a9cb-3815-40ed-8a6f-6e3adfbb75cb"}}, {"head": {"id": "a2e746d9-ffe0-4487-90c3-15124f62b7bf", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26203010715400, "endTime": 26213114366400}, "additional": {"children": ["811f12c1-19ed-495b-8deb-e8cd2deb6251"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4b7849fd-625e-41ad-adad-58007dd16778", "logId": "be65c15e-d264-4de3-ad37-9c1db591aa96"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b7849fd-625e-41ad-adad-58007dd16778", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202996611000}, "additional": {"logType": "detail", "children": [], "durationId": "a2e746d9-ffe0-4487-90c3-15124f62b7bf"}}, {"head": {"id": "15a617e3-bffa-464d-901a-2219dd69116b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202998226500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf146e9b-1ec2-4023-b518-7ad781bb1637", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202998365100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f99f44ee-1b1f-411b-9f8d-ef02c969f743", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26203010742300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker5", "startTime": 26203073187300, "endTime": 26213114102500}, "additional": {"children": ["bbf518ab-4aeb-4bc5-8c07-7ccc73536984", "64fc0648-454f-48ca-ae99-63f2b857ce5c", "708bbb11-91a5-48c3-8d39-afd460315b9f", "c84fd7ba-abc8-40ab-8fae-4d77340438ac", "1624e3b5-b779-4be1-9776-a590cd273fcf", "aa9504f1-496f-4eac-9f67-43a4b5c54d4c", "deb25408-f7a7-49a3-aaf5-3968db13bde9", "c1a24b1b-efea-4490-b6f5-4d58b442d0f0", "5270c5f2-2d01-4da6-b78a-52a32f348d6d", "a8bd0547-adca-4ae0-83b2-536a8decdf67", "250d3775-5688-49df-841b-fc7e0fd984ae", "75ddf882-9a94-4996-8118-8fef9c70cddc", "f837f8fe-a400-4ce2-baad-67867883227f", "b9ed8cae-32bd-49d9-bce9-ebf6c18fc104", "7e1c80c0-0d38-4cd8-9ff0-78c9c67f1eec", "89b736b8-253d-4997-9406-eb179203cdc5", "7231d9e3-97fd-4685-92d2-90df46ca684b", "926b2645-8d23-4758-bccb-3396b58f9305", "24c50990-486d-4458-889d-db94bb8778ce", "d74e81c0-92ce-4ff1-97cd-bb5198aaad7b", "d91d11d0-f09a-44ba-897d-dfd9302873d9", "00523112-ac90-4c4b-9567-62c773f66f4a", "94895051-4e68-448d-a307-1150b7e9ac98", "da97499a-88ba-4537-887a-bff95893119b", "688876e3-b6e7-456b-b620-8c5fe54f3037", "368a8c62-ccad-48cc-9383-32c929f91455", "3e5efedd-1d2c-46d6-a4e1-32d7ab88b861", "8656078e-d1ae-4dab-809b-3b1efab910a5", "a1b210f5-18c2-41bc-b43d-15cd0c86e387", "0084481a-5b54-42d6-8c93-b3bbd7a0f711", "f7f679e3-0ea1-4382-a038-baa55bda45f1", "024ef347-5b37-461a-af64-aef938afb401", "75636db8-6058-4fc4-97b0-452733556854", "eb26279e-3bde-42dd-be2e-1b16260822f9", "c51ef3cd-094e-4242-9a1e-910d170444cb", "61853317-4f79-43ba-add4-593ccd6269c2", "f5532899-6ada-4eef-b363-2859fa6eb102", "1a5e2db1-d45f-4477-b627-c996cc49cb35", "05b9c27c-91be-4135-bce0-43fdc9bf2870", "545ed40d-44a1-4891-a793-4c0743846aae", "d9ef1156-c047-43c3-ae6e-5c907df4e58e", "5a2ad967-3bcc-40a4-b50f-d48f0e75938a", "579e3cde-7c17-44b3-a748-16c77271a38d", "68766a21-12c2-4d17-a65b-aec64d6cab8c", "1b4ccde5-4e69-438e-999b-e244ff1117fd", "269ff311-1e5e-4f52-b307-d6e95605a910", "ae58e147-dda7-4c99-9054-21fa82dfa217", "f1d9142e-c1fa-423c-baff-73b2f31e0e0f", "2c7ba501-adc5-4732-8057-5da5b336dec1", "a2f570fb-6a6b-4960-abe5-1f6247fe3396", "11b32775-efe1-4fd0-bc48-9174caa232e5", "a22d20bb-3cfa-499b-bbd2-4fb33a219ce8", "aeb8a719-ea74-4b42-b13f-e320271c3032", "04638ee2-3060-4e45-81c3-71f14b1c6dcf", "328b38cf-f8d3-4fa9-b1c6-7dc151e19b83", "25fd8de9-fb1c-472a-ab1e-f5d4ac56cadb", "886bf1bc-5ba1-4984-8ab3-44ad31794563", "b2013169-3b08-4c4c-8dd6-b89941cccf27", "bc8a95d9-2769-46a7-94d3-ec4a60d7e219", "ed74d6cc-0696-405e-b549-a6d7a5198ba1", "f44fb1c9-b1fb-41cc-8b8c-e15e758d75fd", "6e09f4df-2fcc-4a43-a73a-691489df1513", "2a4684e3-92f4-4427-8b18-c0d0d68a4aac", "de351d32-1c11-4ee1-abac-739a5cb56c62", "15784e12-0863-40c8-bf24-12be0c391798", "7f5ba8d6-378f-43a4-98f3-aa883d006d94", "2f4599ef-f243-444b-a358-5da307819366", "6cab2bac-5a0d-437a-8662-9a4093298a66", "58ea24d9-36b4-438c-9193-52dc3709da41", "32177686-07bf-4b32-9cdc-5e21743db35d", "84265e13-853a-4e98-9045-3e08223c8c40", "a6c40784-879d-4d50-9a1f-ef07f11e8d2a", "cc710039-7823-4ab6-84e9-d8857f4b0b3e", "97f3ba59-4c1e-42b7-a677-788b65a2433d", "b8517197-b875-4b05-9f4c-d8f9d7b8a7f3", "8a6493a7-eebe-4059-a40a-01d02a8bd725", "66bb1da9-2808-4ae8-a75c-0ed37bb7c818", "5793c3d0-2905-4558-9688-94fb75c171c9", "6595f9d6-0caf-45db-b49a-88a7da3453bf", "5391bd7b-17bb-4383-8832-548918cd7e6d", "d119c152-5405-41f1-a490-3d0818f7f6e3", "7412d260-a6db-4bce-ad67-edbc43eb6633", "0dddfd63-667a-4f3b-b9a9-85249906e88d", "0decf41d-00a8-4d2d-9cfe-510daff69578", "6a3a9d08-6b8b-4d52-9b3c-e070cc153bd7", "b347bfc5-db56-4d47-9c8c-0b0b42df110b", "55458b69-05db-49d6-a54a-6951cba6cf74", "70e68002-48f6-441f-af9f-e1242e0a2d22", "fc6ba8b1-3161-4c80-982a-e06f52c2bc30", "6293d372-8db7-4997-ab3b-9bf151ee228a", "d524ca8d-3d3d-44d8-a014-2bff4e0de2fa", "eacde5d7-df88-4ec4-bc50-f28698196e87", "709d890e-a55a-498c-abe5-c40412067004", "26fe6d56-3b34-4eb0-a1fd-02c876496097", "f086998f-1402-4750-a798-93e3124f0854", "def24584-492c-420b-a6f6-48a00f56c30d", "b5e3aa2f-8792-4d7d-85be-586cbfcdb730", "d732aee8-9e77-4a5c-83be-53f985b9f01d", "0ab9b442-85ce-480e-914d-9218de5a7434", "1fe20c42-7959-4247-b618-6717c20da2ec", "a5f5a918-d496-418c-9d8b-d0bf2fe99a1b", "507eadbf-9943-42a0-a265-8bb152b016c2", "7d7b0085-e90a-4b98-b089-ea86838773ad", "a318daa1-fad3-4d19-b4e0-b058e76a36ad", "13f77dbc-e80e-4dab-860d-51420a3abdac", "6df0bf43-a6d8-4496-b008-e6f647ec1ad8", "b0cbc344-d93f-4722-858c-b7c43a0c0c77", "455ae4e5-f063-4a9e-af41-54b09ca66eb0", "d60afbd5-36b5-4d74-9d75-f6c1a9e0f071", "e21f1ee4-9211-4bea-bcc7-76676c48fa74", "05f44f07-9eb1-462f-8422-18b4f50359cd", "c6c5d518-6e9d-4b81-841c-5e1cda9178b4", "26f3bbb0-ccb0-4dba-a7aa-32f51eaf7e43", "84a6e30f-ce41-470b-b0df-66ab175c9447", "ae50fa86-0617-41dd-b821-26d78f7a8c62", "9de75822-fac8-453d-8bf3-ccb4ad365668", "aae4f162-e789-4e7e-943f-1d320fa8f05f"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "a2e746d9-ffe0-4487-90c3-15124f62b7bf", "logId": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59a1df39-d72f-409f-bc0a-065ef70a2d66", "name": "entry : default@PreviewArkTS cost memory -0.8189163208007812", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26203083155000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f36acabf-3fa2-46aa-a4a7-42279d074d1a", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26207953101800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbf518ab-4aeb-4bc5-8c07-7ccc73536984", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26207954029000, "endTime": 26207954067800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "5b969541-b3d1-4f16-a85b-43aef075b619"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5b969541-b3d1-4f16-a85b-43aef075b619", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26207954029000, "endTime": 26207954067800}, "additional": {"logType": "info", "children": [], "durationId": "bbf518ab-4aeb-4bc5-8c07-7ccc73536984", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "63a515d9-6797-4fb5-b556-d38131529ffa", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211797283100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64fc0648-454f-48ca-ae99-63f2b857ce5c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211798611700, "endTime": 26211798630800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "35c5bfee-d979-485c-b475-feba86ffa6c7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35c5bfee-d979-485c-b475-feba86ffa6c7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211798611700, "endTime": 26211798630800}, "additional": {"logType": "info", "children": [], "durationId": "64fc0648-454f-48ca-ae99-63f2b857ce5c", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "dd001437-e98f-4e4b-9e75-4139f13fa604", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211799025100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "708bbb11-91a5-48c3-8d39-afd460315b9f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211800565500, "endTime": 26211800594100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "2fd54b8f-7fa3-4537-b16e-c67378d95ead"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2fd54b8f-7fa3-4537-b16e-c67378d95ead", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211800565500, "endTime": 26211800594100}, "additional": {"logType": "info", "children": [], "durationId": "708bbb11-91a5-48c3-8d39-afd460315b9f", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "d7bbd90f-fb55-4789-b57a-540725dd286f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211800722500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c84fd7ba-abc8-40ab-8fae-4d77340438ac", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211803042800, "endTime": 26211803072200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "2160ca1a-b1c7-4c23-ad3c-402bc791608e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2160ca1a-b1c7-4c23-ad3c-402bc791608e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211803042800, "endTime": 26211803072200}, "additional": {"logType": "info", "children": [], "durationId": "c84fd7ba-abc8-40ab-8fae-4d77340438ac", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "ca331abf-0e3a-48bd-a5bf-c0e23c426655", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211803292000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1624e3b5-b779-4be1-9776-a590cd273fcf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211804949400, "endTime": 26211804973100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "0f791282-7940-46ea-af9d-0ab828313357"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f791282-7940-46ea-af9d-0ab828313357", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211804949400, "endTime": 26211804973100}, "additional": {"logType": "info", "children": [], "durationId": "1624e3b5-b779-4be1-9776-a590cd273fcf", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "8772244a-7e6a-44bd-bba2-a1724fe541ab", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211805065400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa9504f1-496f-4eac-9f67-43a4b5c54d4c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211806587900, "endTime": 26211806605300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "91c53b0c-d53e-465d-80a7-301c613c4edb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "91c53b0c-d53e-465d-80a7-301c613c4edb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211806587900, "endTime": 26211806605300}, "additional": {"logType": "info", "children": [], "durationId": "aa9504f1-496f-4eac-9f67-43a4b5c54d4c", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "232e6a30-3621-4026-933a-9868e7293de1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211807079900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "deb25408-f7a7-49a3-aaf5-3968db13bde9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211809110300, "endTime": 26211809135400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "dfed7524-ae56-42d3-a9cc-1506e84bed9a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dfed7524-ae56-42d3-a9cc-1506e84bed9a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211809110300, "endTime": 26211809135400}, "additional": {"logType": "info", "children": [], "durationId": "deb25408-f7a7-49a3-aaf5-3968db13bde9", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "f86efcab-4795-4155-8930-bf2b7860ad50", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211809240900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1a24b1b-efea-4490-b6f5-4d58b442d0f0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211810749800, "endTime": 26211810785000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "79ff752c-c964-404d-9b04-f269e52e2077"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "79ff752c-c964-404d-9b04-f269e52e2077", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211810749800, "endTime": 26211810785000}, "additional": {"logType": "info", "children": [], "durationId": "c1a24b1b-efea-4490-b6f5-4d58b442d0f0", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "216f219d-c832-4b7a-a0d3-12ec65f8af33", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211811619300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5270c5f2-2d01-4da6-b78a-52a32f348d6d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211813346000, "endTime": 26211813369700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "d8c9a09e-87d3-46e5-9ac2-f9be99cfeea5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8c9a09e-87d3-46e5-9ac2-f9be99cfeea5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211813346000, "endTime": 26211813369700}, "additional": {"logType": "info", "children": [], "durationId": "5270c5f2-2d01-4da6-b78a-52a32f348d6d", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "7b56ba20-16ca-45b5-9429-21c9bf41d0cf", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211813508600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8bd0547-adca-4ae0-83b2-536a8decdf67", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211815101700, "endTime": 26211815119700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "7552a734-9bfb-477b-b094-8a7383e3ea29"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7552a734-9bfb-477b-b094-8a7383e3ea29", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211815101700, "endTime": 26211815119700}, "additional": {"logType": "info", "children": [], "durationId": "a8bd0547-adca-4ae0-83b2-536a8decdf67", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "f9aebc61-9141-4040-84c5-56c302165750", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211815302700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "250d3775-5688-49df-841b-fc7e0fd984ae", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211817596700, "endTime": 26211817623500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "a3e4ad52-a6a9-4e72-b25c-9c7cd7994bf6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a3e4ad52-a6a9-4e72-b25c-9c7cd7994bf6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211817596700, "endTime": 26211817623500}, "additional": {"logType": "info", "children": [], "durationId": "250d3775-5688-49df-841b-fc7e0fd984ae", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "a85ec358-486d-4870-a10e-12e71a69252f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211817739400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75ddf882-9a94-4996-8118-8fef9c70cddc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211819580000, "endTime": 26211819614000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "ff2cd06c-aa89-409b-b393-b581b22f6c47"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff2cd06c-aa89-409b-b393-b581b22f6c47", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211819580000, "endTime": 26211819614000}, "additional": {"logType": "info", "children": [], "durationId": "75ddf882-9a94-4996-8118-8fef9c70cddc", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "d1b0cff2-0264-493d-bad5-ef4ad221190c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211819729800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f837f8fe-a400-4ce2-baad-67867883227f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211821330900, "endTime": 26211821357900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "c9aba78b-9f21-4dbd-bdef-4bb5a05fc537"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9aba78b-9f21-4dbd-bdef-4bb5a05fc537", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211821330900, "endTime": 26211821357900}, "additional": {"logType": "info", "children": [], "durationId": "f837f8fe-a400-4ce2-baad-67867883227f", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "5e33100e-d800-4346-a31b-1e4ed883ac69", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211821477300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9ed8cae-32bd-49d9-bce9-ebf6c18fc104", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211822486100, "endTime": 26211822506900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "ffe29cf4-e8e1-4ce7-a3fd-a80870ad75ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ffe29cf4-e8e1-4ce7-a3fd-a80870ad75ea", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211822486100, "endTime": 26211822506900}, "additional": {"logType": "info", "children": [], "durationId": "b9ed8cae-32bd-49d9-bce9-ebf6c18fc104", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "e12521c7-0cf8-49f7-a25d-2d4408dc0225", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211822579000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e1c80c0-0d38-4cd8-9ff0-78c9c67f1eec", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211823486900, "endTime": 26211823502200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "5b62bd2e-3d93-4062-a72d-691fe4fae04e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5b62bd2e-3d93-4062-a72d-691fe4fae04e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211823486900, "endTime": 26211823502200}, "additional": {"logType": "info", "children": [], "durationId": "7e1c80c0-0d38-4cd8-9ff0-78c9c67f1eec", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "8bdd7fdd-9f4b-48cb-b9a4-e239acc62b60", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211823622100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89b736b8-253d-4997-9406-eb179203cdc5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211824881800, "endTime": 26211824900900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "d907e0f7-d5cc-4bf8-9f05-84815e25bee9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d907e0f7-d5cc-4bf8-9f05-84815e25bee9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211824881800, "endTime": 26211824900900}, "additional": {"logType": "info", "children": [], "durationId": "89b736b8-253d-4997-9406-eb179203cdc5", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "db02850b-17ab-4e30-8bc2-ecb436da18b8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211824964800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7231d9e3-97fd-4685-92d2-90df46ca684b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211825996500, "endTime": 26211826016600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "65e904f8-9993-4d9a-9075-56c3e3d9f116"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65e904f8-9993-4d9a-9075-56c3e3d9f116", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211825996500, "endTime": 26211826016600}, "additional": {"logType": "info", "children": [], "durationId": "7231d9e3-97fd-4685-92d2-90df46ca684b", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "3a4d7760-9e32-4ac5-be66-8a98a5527478", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211826086300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "926b2645-8d23-4758-bccb-3396b58f9305", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211827487700, "endTime": 26211827510600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "24f821df-fd9d-43f8-a8fc-30289d5b09d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24f821df-fd9d-43f8-a8fc-30289d5b09d3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211827487700, "endTime": 26211827510600}, "additional": {"logType": "info", "children": [], "durationId": "926b2645-8d23-4758-bccb-3396b58f9305", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "2776e645-766c-4e86-b480-1fd362d94813", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211827607100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24c50990-486d-4458-889d-db94bb8778ce", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211828990100, "endTime": 26211829011600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "708642b3-30b6-4907-81b8-99d2c66318e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "708642b3-30b6-4907-81b8-99d2c66318e0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211828990100, "endTime": 26211829011600}, "additional": {"logType": "info", "children": [], "durationId": "24c50990-486d-4458-889d-db94bb8778ce", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "eb87296f-e490-4bf5-8687-f10433ce1978", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211829103900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d74e81c0-92ce-4ff1-97cd-bb5198aaad7b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211830482100, "endTime": 26211830499400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "7509fb98-19b8-481f-ac0b-57bd8650791c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7509fb98-19b8-481f-ac0b-57bd8650791c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211830482100, "endTime": 26211830499400}, "additional": {"logType": "info", "children": [], "durationId": "d74e81c0-92ce-4ff1-97cd-bb5198aaad7b", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "f0e12359-e31b-46db-9c47-2b7bf37565e9", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211830568200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d91d11d0-f09a-44ba-897d-dfd9302873d9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211832060800, "endTime": 26211832093400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "e202091c-e04b-4f0f-a7d5-f3f35450e278"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e202091c-e04b-4f0f-a7d5-f3f35450e278", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211832060800, "endTime": 26211832093400}, "additional": {"logType": "info", "children": [], "durationId": "d91d11d0-f09a-44ba-897d-dfd9302873d9", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "1deb8270-2966-4899-8e1a-1d6f2273a067", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211832213900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00523112-ac90-4c4b-9567-62c773f66f4a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211833208300, "endTime": 26211833221600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "a5c2fa1e-906d-43c2-99a3-2ca35c98f632"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5c2fa1e-906d-43c2-99a3-2ca35c98f632", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211833208300, "endTime": 26211833221600}, "additional": {"logType": "info", "children": [], "durationId": "00523112-ac90-4c4b-9567-62c773f66f4a", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "9d5030ad-6536-408c-b5e8-598696062048", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211833273900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94895051-4e68-448d-a307-1150b7e9ac98", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211834034000, "endTime": 26211834047100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "bc37fc55-bdb6-4f94-a960-22a5f8c0e9c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc37fc55-bdb6-4f94-a960-22a5f8c0e9c8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211834034000, "endTime": 26211834047100}, "additional": {"logType": "info", "children": [], "durationId": "94895051-4e68-448d-a307-1150b7e9ac98", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "8241b200-cb11-45cd-92ba-43931b9f7986", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211834098100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da97499a-88ba-4537-887a-bff95893119b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211834728300, "endTime": 26211834739800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "ec4e6f3b-4df1-4c20-aaa0-b6ee3e5ef429"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec4e6f3b-4df1-4c20-aaa0-b6ee3e5ef429", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211834728300, "endTime": 26211834739800}, "additional": {"logType": "info", "children": [], "durationId": "da97499a-88ba-4537-887a-bff95893119b", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "123ce5b8-93f3-444d-b510-a265e63f7874", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211834785000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "688876e3-b6e7-456b-b620-8c5fe54f3037", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211835541500, "endTime": 26211835559200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "66a0ff5f-4dfa-4cf5-9a6e-f6a84c576dad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66a0ff5f-4dfa-4cf5-9a6e-f6a84c576dad", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211835541500, "endTime": 26211835559200}, "additional": {"logType": "info", "children": [], "durationId": "688876e3-b6e7-456b-b620-8c5fe54f3037", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "c7d1642e-5f8d-4cb6-94ed-761a159bfc00", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211835633900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "368a8c62-ccad-48cc-9383-32c929f91455", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211836387900, "endTime": 26211836400000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "283919d5-2c99-4b1f-bd51-7683755a3241"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "283919d5-2c99-4b1f-bd51-7683755a3241", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211836387900, "endTime": 26211836400000}, "additional": {"logType": "info", "children": [], "durationId": "368a8c62-ccad-48cc-9383-32c929f91455", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "fc02aa5b-5939-4458-a2bd-779f17101fb1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211836447400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e5efedd-1d2c-46d6-a4e1-32d7ab88b861", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211837151900, "endTime": 26211837164000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "fe7fb5de-b814-465e-b0e4-9e5807827af6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe7fb5de-b814-465e-b0e4-9e5807827af6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211837151900, "endTime": 26211837164000}, "additional": {"logType": "info", "children": [], "durationId": "3e5efedd-1d2c-46d6-a4e1-32d7ab88b861", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "aef97156-3c88-4451-9097-3d8ed2a3f767", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211837213800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8656078e-d1ae-4dab-809b-3b1efab910a5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211838071600, "endTime": 26211838084400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "d06488f0-5a2a-4eee-8d08-7a1420becdce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d06488f0-5a2a-4eee-8d08-7a1420becdce", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211838071600, "endTime": 26211838084400}, "additional": {"logType": "info", "children": [], "durationId": "8656078e-d1ae-4dab-809b-3b1efab910a5", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "b1b6ff9b-1cec-441e-bf68-23c24c054b75", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211838136900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1b210f5-18c2-41bc-b43d-15cd0c86e387", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211838811100, "endTime": 26211838824000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "b9e65a1e-15fc-43c4-8f32-7e244a21f9b0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b9e65a1e-15fc-43c4-8f32-7e244a21f9b0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211838811100, "endTime": 26211838824000}, "additional": {"logType": "info", "children": [], "durationId": "a1b210f5-18c2-41bc-b43d-15cd0c86e387", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "1f99d0cf-8e27-4d6f-834e-062287ad2315", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211838874800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0084481a-5b54-42d6-8c93-b3bbd7a0f711", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211839611000, "endTime": 26211839623800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "a7f3dc0a-66b3-4933-9bfb-b981d5b37c6b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7f3dc0a-66b3-4933-9bfb-b981d5b37c6b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211839611000, "endTime": 26211839623800}, "additional": {"logType": "info", "children": [], "durationId": "0084481a-5b54-42d6-8c93-b3bbd7a0f711", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "9536ed66-a870-4217-a45c-a022116e6d0e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211839676500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7f679e3-0ea1-4382-a038-baa55bda45f1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211840650200, "endTime": 26211840665200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "f1f2b9c3-b5df-4dbc-b447-389fd8c1995c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1f2b9c3-b5df-4dbc-b447-389fd8c1995c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211840650200, "endTime": 26211840665200}, "additional": {"logType": "info", "children": [], "durationId": "f7f679e3-0ea1-4382-a038-baa55bda45f1", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "a825a336-2e46-4174-ad8b-56e17301f74e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211841411100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "024ef347-5b37-461a-af64-aef938afb401", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211842609200, "endTime": 26211842628200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "d0a7fb12-2317-46d5-a136-eefb485421b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0a7fb12-2317-46d5-a136-eefb485421b5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211842609200, "endTime": 26211842628200}, "additional": {"logType": "info", "children": [], "durationId": "024ef347-5b37-461a-af64-aef938afb401", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "c68852df-219c-4e9f-92b9-89e2b427b724", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211842689300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75636db8-6058-4fc4-97b0-452733556854", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211843349500, "endTime": 26211843362300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "c30070b1-477d-402d-9f18-6ec88b4ec9d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c30070b1-477d-402d-9f18-6ec88b4ec9d1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211843349500, "endTime": 26211843362300}, "additional": {"logType": "info", "children": [], "durationId": "75636db8-6058-4fc4-97b0-452733556854", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "aca5e7ba-6eea-4915-8e27-f9aef5003b86", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211843409100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb26279e-3bde-42dd-be2e-1b16260822f9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211844087200, "endTime": 26211844100700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "3c802585-06cd-4506-8ce1-d2b58918113e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c802585-06cd-4506-8ce1-d2b58918113e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211844087200, "endTime": 26211844100700}, "additional": {"logType": "info", "children": [], "durationId": "eb26279e-3bde-42dd-be2e-1b16260822f9", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "10c1981e-2071-4fc7-9ef1-50384cbeecc2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211844149600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c51ef3cd-094e-4242-9a1e-910d170444cb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211844796700, "endTime": 26211844808500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "21e36563-d5c9-450f-94ea-b92bff4881a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21e36563-d5c9-450f-94ea-b92bff4881a5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211844796700, "endTime": 26211844808500}, "additional": {"logType": "info", "children": [], "durationId": "c51ef3cd-094e-4242-9a1e-910d170444cb", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "b83c2fff-90ed-4e7b-988f-7d4858e59578", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211844851700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61853317-4f79-43ba-add4-593ccd6269c2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211845546900, "endTime": 26211845560100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "1c94f55c-1eae-4675-a18b-21440b366710"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c94f55c-1eae-4675-a18b-21440b366710", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211845546900, "endTime": 26211845560100}, "additional": {"logType": "info", "children": [], "durationId": "61853317-4f79-43ba-add4-593ccd6269c2", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "3af148b3-1de6-4f7c-98b5-d2c9cdb7c9f0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211845614800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5532899-6ada-4eef-b363-2859fa6eb102", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211846253100, "endTime": 26211846266800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "69eea447-845e-4604-8a79-018440e7136b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69eea447-845e-4604-8a79-018440e7136b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211846253100, "endTime": 26211846266800}, "additional": {"logType": "info", "children": [], "durationId": "f5532899-6ada-4eef-b363-2859fa6eb102", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "d8a812a1-7e82-42bf-9742-15191ed96fb9", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211846315700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a5e2db1-d45f-4477-b627-c996cc49cb35", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211847020400, "endTime": 26211847034000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "429d2ef0-b6ec-42e7-a1e1-5abb0ce4f181"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "429d2ef0-b6ec-42e7-a1e1-5abb0ce4f181", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211847020400, "endTime": 26211847034000}, "additional": {"logType": "info", "children": [], "durationId": "1a5e2db1-d45f-4477-b627-c996cc49cb35", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "27a1ddf2-66d6-4a88-b56e-a76b0926bf88", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211847086200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05b9c27c-91be-4135-bce0-43fdc9bf2870", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211847787600, "endTime": 26211847802400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "d6bb97f6-d388-4bb2-808b-2c85a7353dc7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6bb97f6-d388-4bb2-808b-2c85a7353dc7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211847787600, "endTime": 26211847802400}, "additional": {"logType": "info", "children": [], "durationId": "05b9c27c-91be-4135-bce0-43fdc9bf2870", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "680d9695-ab4d-4e5c-bcf2-747347e33ac7", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211847853300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "545ed40d-44a1-4891-a793-4c0743846aae", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211848557600, "endTime": 26211848572700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "366586db-ce09-40af-87c8-c68e85378cef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "366586db-ce09-40af-87c8-c68e85378cef", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211848557600, "endTime": 26211848572700}, "additional": {"logType": "info", "children": [], "durationId": "545ed40d-44a1-4891-a793-4c0743846aae", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "cc8de3d9-37f3-48c0-a159-6f07826b4717", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211848629200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9ef1156-c047-43c3-ae6e-5c907df4e58e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211849542300, "endTime": 26211849556100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "73b7c083-fe9b-4f68-9ebe-7759086f065f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73b7c083-fe9b-4f68-9ebe-7759086f065f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211849542300, "endTime": 26211849556100}, "additional": {"logType": "info", "children": [], "durationId": "d9ef1156-c047-43c3-ae6e-5c907df4e58e", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "3ad89217-bfc0-450c-806a-563903fec9d5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211849607700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a2ad967-3bcc-40a4-b50f-d48f0e75938a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211850610700, "endTime": 26211850631800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "7c2c5df2-d30b-4dbf-bb6b-d9d8a05dc985"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c2c5df2-d30b-4dbf-bb6b-d9d8a05dc985", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211850610700, "endTime": 26211850631800}, "additional": {"logType": "info", "children": [], "durationId": "5a2ad967-3bcc-40a4-b50f-d48f0e75938a", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "0bc29ace-1993-41da-bf0f-47560a85bb67", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211850708200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "579e3cde-7c17-44b3-a748-16c77271a38d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211852597800, "endTime": 26211852623100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "dffdaae1-7e41-4207-a187-a49687e263e8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dffdaae1-7e41-4207-a187-a49687e263e8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211852597800, "endTime": 26211852623100}, "additional": {"logType": "info", "children": [], "durationId": "579e3cde-7c17-44b3-a748-16c77271a38d", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "6e283958-eaf0-4ef6-924f-1c821bd9c18d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211852750900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68766a21-12c2-4d17-a65b-aec64d6cab8c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211853888700, "endTime": 26211853914100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "77fc07ea-c58d-429d-9d81-a15d3341e9f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "77fc07ea-c58d-429d-9d81-a15d3341e9f2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211853888700, "endTime": 26211853914100}, "additional": {"logType": "info", "children": [], "durationId": "68766a21-12c2-4d17-a65b-aec64d6cab8c", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "090a2014-1301-4e2e-b435-2b8a1215a94e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211854024300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b4ccde5-4e69-438e-999b-e244ff1117fd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211854964800, "endTime": 26211854990300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "bda01b7f-97ca-40a5-8fcf-ce730e68b32d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bda01b7f-97ca-40a5-8fcf-ce730e68b32d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211854964800, "endTime": 26211854990300}, "additional": {"logType": "info", "children": [], "durationId": "1b4ccde5-4e69-438e-999b-e244ff1117fd", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "a0088392-cfa0-4fbe-8c5c-b42298e4532f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211855051400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "269ff311-1e5e-4f52-b307-d6e95605a910", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211855725900, "endTime": 26211855740500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "bad969e2-ee0a-4bd7-a82c-1eb73e3b0869"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bad969e2-ee0a-4bd7-a82c-1eb73e3b0869", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211855725900, "endTime": 26211855740500}, "additional": {"logType": "info", "children": [], "durationId": "269ff311-1e5e-4f52-b307-d6e95605a910", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "264bbb33-1b25-434f-8fef-a54e8440f369", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211855798800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae58e147-dda7-4c99-9054-21fa82dfa217", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211856726300, "endTime": 26211856742800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "a2811a16-1e37-4c3e-a8ed-0532e9492894"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a2811a16-1e37-4c3e-a8ed-0532e9492894", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211856726300, "endTime": 26211856742800}, "additional": {"logType": "info", "children": [], "durationId": "ae58e147-dda7-4c99-9054-21fa82dfa217", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "6ac966fc-bc60-47f5-be41-09e77ee7dc84", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211856800800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1d9142e-c1fa-423c-baff-73b2f31e0e0f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211857853700, "endTime": 26211857874600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "a68c291b-1a2a-483b-83fc-79f78fcf2f53"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a68c291b-1a2a-483b-83fc-79f78fcf2f53", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211857853700, "endTime": 26211857874600}, "additional": {"logType": "info", "children": [], "durationId": "f1d9142e-c1fa-423c-baff-73b2f31e0e0f", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "f9a79440-9525-444c-8356-dfe5fc235038", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211857957900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c7ba501-adc5-4732-8057-5da5b336dec1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211858930900, "endTime": 26211858950700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "3f1b0273-2461-4b82-869b-23cb0f749cf7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f1b0273-2461-4b82-869b-23cb0f749cf7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211858930900, "endTime": 26211858950700}, "additional": {"logType": "info", "children": [], "durationId": "2c7ba501-adc5-4732-8057-5da5b336dec1", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "8b4c947a-e84f-418f-ab8f-e5908f4fd585", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211859023100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2f570fb-6a6b-4960-abe5-1f6247fe3396", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211859927300, "endTime": 26211859946300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "e30a2284-d138-4321-98b5-595f661480f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e30a2284-d138-4321-98b5-595f661480f1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211859927300, "endTime": 26211859946300}, "additional": {"logType": "info", "children": [], "durationId": "a2f570fb-6a6b-4960-abe5-1f6247fe3396", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "4a6f28cb-5f83-4aec-8471-6d8ca431478f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211860024900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11b32775-efe1-4fd0-bc48-9174caa232e5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211861060200, "endTime": 26211861084600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "4c4b02b3-760b-4e32-ab31-ad4f9c53717b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c4b02b3-760b-4e32-ab31-ad4f9c53717b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211861060200, "endTime": 26211861084600}, "additional": {"logType": "info", "children": [], "durationId": "11b32775-efe1-4fd0-bc48-9174caa232e5", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "985f8970-f0c0-4068-bce8-324179a646fd", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211861165500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a22d20bb-3cfa-499b-bbd2-4fb33a219ce8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211862218100, "endTime": 26211862240600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "0e47812d-6d90-4e5a-ae5f-37ce4e73cc46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e47812d-6d90-4e5a-ae5f-37ce4e73cc46", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211862218100, "endTime": 26211862240600}, "additional": {"logType": "info", "children": [], "durationId": "a22d20bb-3cfa-499b-bbd2-4fb33a219ce8", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "0bafcc27-0dbe-4429-9554-70be5d65caa7", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211862320500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aeb8a719-ea74-4b42-b13f-e320271c3032", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211863315400, "endTime": 26211863335200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "66420994-d6b0-4364-b488-d71b55f39400"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66420994-d6b0-4364-b488-d71b55f39400", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211863315400, "endTime": 26211863335200}, "additional": {"logType": "info", "children": [], "durationId": "aeb8a719-ea74-4b42-b13f-e320271c3032", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "a5893300-b85b-4a3e-994a-aa254b3c6e47", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211863411000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04638ee2-3060-4e45-81c3-71f14b1c6dcf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211865366500, "endTime": 26211865422600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "e24298c4-60a4-4b2b-963b-a4f381e2019f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e24298c4-60a4-4b2b-963b-a4f381e2019f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211865366500, "endTime": 26211865422600}, "additional": {"logType": "info", "children": [], "durationId": "04638ee2-3060-4e45-81c3-71f14b1c6dcf", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "4c114fd0-139a-4b7d-9bfa-6af4d747161f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211865572100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "328b38cf-f8d3-4fa9-b1c6-7dc151e19b83", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211867189100, "endTime": 26211867227700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "66e70d08-de11-41ce-ab25-0d17e38461b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66e70d08-de11-41ce-ab25-0d17e38461b8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211867189100, "endTime": 26211867227700}, "additional": {"logType": "info", "children": [], "durationId": "328b38cf-f8d3-4fa9-b1c6-7dc151e19b83", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "91533932-ac74-430d-a626-c092064e67a6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211867381500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25fd8de9-fb1c-472a-ab1e-f5d4ac56cadb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211868544100, "endTime": 26211868566600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "108ee235-fdb3-429c-9f0e-91ee9e986a29"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "108ee235-fdb3-429c-9f0e-91ee9e986a29", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211868544100, "endTime": 26211868566600}, "additional": {"logType": "info", "children": [], "durationId": "25fd8de9-fb1c-472a-ab1e-f5d4ac56cadb", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "7b97dadd-3311-4846-94be-cba8ec448bce", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211868650000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "886bf1bc-5ba1-4984-8ab3-44ad31794563", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211869444100, "endTime": 26211869467700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "8f77ae28-1b9c-467c-826b-6d186f7f68de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f77ae28-1b9c-467c-826b-6d186f7f68de", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211869444100, "endTime": 26211869467700}, "additional": {"logType": "info", "children": [], "durationId": "886bf1bc-5ba1-4984-8ab3-44ad31794563", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "5036ce9e-1879-4dfe-bc5d-68a29fa22119", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211869543500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2013169-3b08-4c4c-8dd6-b89941cccf27", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211870547100, "endTime": 26211870576400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "3bbfe7b4-9e3b-4d90-bba7-3d9420311ffd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3bbfe7b4-9e3b-4d90-bba7-3d9420311ffd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211870547100, "endTime": 26211870576400}, "additional": {"logType": "info", "children": [], "durationId": "b2013169-3b08-4c4c-8dd6-b89941cccf27", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "d6afc501-15e3-4b31-860a-318e2279b659", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211870677800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc8a95d9-2769-46a7-94d3-ec4a60d7e219", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211872157700, "endTime": 26211872182500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "59367388-e787-4cbd-8f5d-c197208cc300"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59367388-e787-4cbd-8f5d-c197208cc300", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211872157700, "endTime": 26211872182500}, "additional": {"logType": "info", "children": [], "durationId": "bc8a95d9-2769-46a7-94d3-ec4a60d7e219", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "da3b38ac-4607-492c-bd2d-bdd131039cca", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211872288000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed74d6cc-0696-405e-b549-a6d7a5198ba1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211873699200, "endTime": 26211873726700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "c9ce4ca3-f345-4565-8b47-8b6006049951"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9ce4ca3-f345-4565-8b47-8b6006049951", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211873699200, "endTime": 26211873726700}, "additional": {"logType": "info", "children": [], "durationId": "ed74d6cc-0696-405e-b549-a6d7a5198ba1", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "96d28f86-63dc-4beb-a987-4a36c409a516", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211873845200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f44fb1c9-b1fb-41cc-8b8c-e15e758d75fd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211875350200, "endTime": 26211875379700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "8bba4d0c-573b-43cb-8c0f-9ee843bca65e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8bba4d0c-573b-43cb-8c0f-9ee843bca65e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211875350200, "endTime": 26211875379700}, "additional": {"logType": "info", "children": [], "durationId": "f44fb1c9-b1fb-41cc-8b8c-e15e758d75fd", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "e5191a4e-c2c0-4f19-b686-1a3e3810f527", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211875505700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e09f4df-2fcc-4a43-a73a-691489df1513", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211876860100, "endTime": 26211876883000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "a4e86527-f79a-40ef-a84c-86f76aa3de60"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4e86527-f79a-40ef-a84c-86f76aa3de60", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211876860100, "endTime": 26211876883000}, "additional": {"logType": "info", "children": [], "durationId": "6e09f4df-2fcc-4a43-a73a-691489df1513", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "6e6590ab-dda2-4600-8db9-39968b938e35", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211876978500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a4684e3-92f4-4427-8b18-c0d0d68a4aac", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211878263600, "endTime": 26211878289300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "acae2052-f308-4534-a71c-f7af3f7b44a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "acae2052-f308-4534-a71c-f7af3f7b44a8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211878263600, "endTime": 26211878289300}, "additional": {"logType": "info", "children": [], "durationId": "2a4684e3-92f4-4427-8b18-c0d0d68a4aac", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "66a65753-c725-47d4-b516-fa37e88c5c24", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211878393700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de351d32-1c11-4ee1-abac-739a5cb56c62", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211880464700, "endTime": 26211880494600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "bdd5b282-d661-42c0-8a36-f9c41c633b2d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bdd5b282-d661-42c0-8a36-f9c41c633b2d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211880464700, "endTime": 26211880494600}, "additional": {"logType": "info", "children": [], "durationId": "de351d32-1c11-4ee1-abac-739a5cb56c62", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "5e27a311-4dff-4048-bf6f-1fdeef01d844", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211880600400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15784e12-0863-40c8-bf24-12be0c391798", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211881431500, "endTime": 26211881446300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "a03be638-abc8-4fff-91d3-ec07ae2c4b96"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a03be638-abc8-4fff-91d3-ec07ae2c4b96", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211881431500, "endTime": 26211881446300}, "additional": {"logType": "info", "children": [], "durationId": "15784e12-0863-40c8-bf24-12be0c391798", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "61727994-027d-4e9d-ad05-c8d864900caf", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211881504700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f5ba8d6-378f-43a4-98f3-aa883d006d94", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211882153900, "endTime": 26211882167700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "77ae02ae-abb6-4385-b764-a7a233642849"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "77ae02ae-abb6-4385-b764-a7a233642849", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211882153900, "endTime": 26211882167700}, "additional": {"logType": "info", "children": [], "durationId": "7f5ba8d6-378f-43a4-98f3-aa883d006d94", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "260bca79-d72e-4589-8d31-3fbb4cc13f28", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211882222300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f4599ef-f243-444b-a358-5da307819366", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211883510100, "endTime": 26211883535300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "6f57f837-216b-46dd-b337-144a98f1dd5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f57f837-216b-46dd-b337-144a98f1dd5e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211883510100, "endTime": 26211883535300}, "additional": {"logType": "info", "children": [], "durationId": "2f4599ef-f243-444b-a358-5da307819366", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "1bbb784e-12ef-466c-ae46-54897737abdb", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211883627300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cab2bac-5a0d-437a-8662-9a4093298a66", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211885028800, "endTime": 26211885054400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "1dbad674-b491-42c9-9361-00eda435964f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1dbad674-b491-42c9-9361-00eda435964f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211885028800, "endTime": 26211885054400}, "additional": {"logType": "info", "children": [], "durationId": "6cab2bac-5a0d-437a-8662-9a4093298a66", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "7a658fb1-930c-433e-828f-787340519459", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211885148000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58ea24d9-36b4-438c-9193-52dc3709da41", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211886492300, "endTime": 26211886517100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "22eeb93b-d55b-47ea-8164-6e32058192a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22eeb93b-d55b-47ea-8164-6e32058192a5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211886492300, "endTime": 26211886517100}, "additional": {"logType": "info", "children": [], "durationId": "58ea24d9-36b4-438c-9193-52dc3709da41", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "32bfbc6f-abb5-4d11-afca-6b77642496dc", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211886615100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32177686-07bf-4b32-9cdc-5e21743db35d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211887931700, "endTime": 26211887956200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "9b19cf72-7a4c-4e17-bfbf-b3846d3afff2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b19cf72-7a4c-4e17-bfbf-b3846d3afff2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211887931700, "endTime": 26211887956200}, "additional": {"logType": "info", "children": [], "durationId": "32177686-07bf-4b32-9cdc-5e21743db35d", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "7050c587-094c-4455-9c07-6b852ad4061c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211888124400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84265e13-853a-4e98-9045-3e08223c8c40", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211889577300, "endTime": 26211889595300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "8a1cc934-a27e-4c4d-bc34-af2d07f813a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a1cc934-a27e-4c4d-bc34-af2d07f813a2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211889577300, "endTime": 26211889595300}, "additional": {"logType": "info", "children": [], "durationId": "84265e13-853a-4e98-9045-3e08223c8c40", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "c385970a-e0a0-490f-af0a-790b4f7be8ef", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211889666900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6c40784-879d-4d50-9a1f-ef07f11e8d2a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211890464400, "endTime": 26211890480500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "f7a735d5-fb19-4d65-aea5-aa52923617a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7a735d5-fb19-4d65-aea5-aa52923617a4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211890464400, "endTime": 26211890480500}, "additional": {"logType": "info", "children": [], "durationId": "a6c40784-879d-4d50-9a1f-ef07f11e8d2a", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "a7c23f7e-9bf6-4eaa-bdc7-3227c109384f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211890546600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc710039-7823-4ab6-84e9-d8857f4b0b3e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211891375100, "endTime": 26211891392200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "cd336bc3-8bd0-4806-a8b7-51d618b9d462"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd336bc3-8bd0-4806-a8b7-51d618b9d462", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211891375100, "endTime": 26211891392200}, "additional": {"logType": "info", "children": [], "durationId": "cc710039-7823-4ab6-84e9-d8857f4b0b3e", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "57171414-3b7f-4775-92c7-4c815f543897", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211891453500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97f3ba59-4c1e-42b7-a677-788b65a2433d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211892206400, "endTime": 26211892226400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "d2fc5cd1-d2c3-498f-9fa9-6412ace4022c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2fc5cd1-d2c3-498f-9fa9-6412ace4022c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211892206400, "endTime": 26211892226400}, "additional": {"logType": "info", "children": [], "durationId": "97f3ba59-4c1e-42b7-a677-788b65a2433d", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "ac71322b-70a2-4bc5-97f5-7c534298c753", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211892304200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8517197-b875-4b05-9f4c-d8f9d7b8a7f3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211893756200, "endTime": 26211893794700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "ea8ace78-374b-450f-8c56-8ddaffa13154"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea8ace78-374b-450f-8c56-8ddaffa13154", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211893756200, "endTime": 26211893794700}, "additional": {"logType": "info", "children": [], "durationId": "b8517197-b875-4b05-9f4c-d8f9d7b8a7f3", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "b62d947f-588f-4e77-b233-ddf6590d684d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211893931500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a6493a7-eebe-4059-a40a-01d02a8bd725", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211895338400, "endTime": 26211895364400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "73ee7729-c628-4172-b561-65107dcb73d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73ee7729-c628-4172-b561-65107dcb73d5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211895338400, "endTime": 26211895364400}, "additional": {"logType": "info", "children": [], "durationId": "8a6493a7-eebe-4059-a40a-01d02a8bd725", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "84edbf09-ddff-4fbd-9e52-8e97e6a44eca", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211895451200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66bb1da9-2808-4ae8-a75c-0ed37bb7c818", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211896689000, "endTime": 26211896710600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "00e395e1-a734-41a6-9afc-655486ea9159"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "00e395e1-a734-41a6-9afc-655486ea9159", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211896689000, "endTime": 26211896710600}, "additional": {"logType": "info", "children": [], "durationId": "66bb1da9-2808-4ae8-a75c-0ed37bb7c818", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "4aadef28-a28e-43bb-b9a1-80cbdc97f36c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211896781900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5793c3d0-2905-4558-9688-94fb75c171c9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211897763100, "endTime": 26211897781500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "2985fc8b-c915-414b-8b79-99d6fa8f6bbb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2985fc8b-c915-414b-8b79-99d6fa8f6bbb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211897763100, "endTime": 26211897781500}, "additional": {"logType": "info", "children": [], "durationId": "5793c3d0-2905-4558-9688-94fb75c171c9", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "bcda0fd5-a292-4d4b-ad1b-f1759cee0f81", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211897848800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6595f9d6-0caf-45db-b49a-88a7da3453bf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211898957700, "endTime": 26211898984500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "2c478585-f91a-47ac-96c8-be985cbc90d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c478585-f91a-47ac-96c8-be985cbc90d8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211898957700, "endTime": 26211898984500}, "additional": {"logType": "info", "children": [], "durationId": "6595f9d6-0caf-45db-b49a-88a7da3453bf", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "e580a183-436c-41d8-a540-18ec721cf989", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211899085100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5391bd7b-17bb-4383-8832-548918cd7e6d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211900529900, "endTime": 26211900551600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "bff01c7e-1064-4d6a-bfdb-2dd0ab2dcbbf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bff01c7e-1064-4d6a-bfdb-2dd0ab2dcbbf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211900529900, "endTime": 26211900551600}, "additional": {"logType": "info", "children": [], "durationId": "5391bd7b-17bb-4383-8832-548918cd7e6d", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "04b06027-0fe1-4410-9d97-2c30876d3fa1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211900625300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d119c152-5405-41f1-a490-3d0818f7f6e3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211901762500, "endTime": 26211901790000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "8e8ee3d2-73c6-4501-91aa-d641f9699530"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e8ee3d2-73c6-4501-91aa-d641f9699530", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211901762500, "endTime": 26211901790000}, "additional": {"logType": "info", "children": [], "durationId": "d119c152-5405-41f1-a490-3d0818f7f6e3", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "fecebbdc-aaf7-4a48-8ff7-495f7521dc43", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211901915200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7412d260-a6db-4bce-ad67-edbc43eb6633", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211902729200, "endTime": 26211902745200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "510a57a0-6d07-44df-b017-6c7342d71b5a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "510a57a0-6d07-44df-b017-6c7342d71b5a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211902729200, "endTime": 26211902745200}, "additional": {"logType": "info", "children": [], "durationId": "7412d260-a6db-4bce-ad67-edbc43eb6633", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "a2ccdf11-db1d-4183-827f-793d4be93548", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211902806100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dddfd63-667a-4f3b-b9a9-85249906e88d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211903569700, "endTime": 26211903585100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "651e503a-468a-4537-91b0-116e92843240"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "651e503a-468a-4537-91b0-116e92843240", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211903569700, "endTime": 26211903585100}, "additional": {"logType": "info", "children": [], "durationId": "0dddfd63-667a-4f3b-b9a9-85249906e88d", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "0e865754-712f-4741-851f-07730f9605a6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211903655700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0decf41d-00a8-4d2d-9cfe-510daff69578", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211904350900, "endTime": 26211904364600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "8934bf43-45e7-4507-8b8a-2fa776aaec12"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8934bf43-45e7-4507-8b8a-2fa776aaec12", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211904350900, "endTime": 26211904364600}, "additional": {"logType": "info", "children": [], "durationId": "0decf41d-00a8-4d2d-9cfe-510daff69578", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "10ca7d07-da42-4221-adf9-a0e99fc58a40", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211904419800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a3a9d08-6b8b-4d52-9b3c-e070cc153bd7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211905066500, "endTime": 26211905079600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "c1f2f618-f75c-4a41-8a42-05744b59c400"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1f2f618-f75c-4a41-8a42-05744b59c400", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211905066500, "endTime": 26211905079600}, "additional": {"logType": "info", "children": [], "durationId": "6a3a9d08-6b8b-4d52-9b3c-e070cc153bd7", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "2b6886fb-1da0-47da-9a5b-aa4033eefa1b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211905143900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b347bfc5-db56-4d47-9c8c-0b0b42df110b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211906442000, "endTime": 26211906538900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "0ce6aa07-81d6-4f3c-b903-cd1dad989aa0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ce6aa07-81d6-4f3c-b903-cd1dad989aa0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211906442000, "endTime": 26211906538900}, "additional": {"logType": "info", "children": [], "durationId": "b347bfc5-db56-4d47-9c8c-0b0b42df110b", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "0ee0595c-9f6f-4a90-a176-1fc740d0a2e7", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211906663500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55458b69-05db-49d6-a54a-6951cba6cf74", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211907682800, "endTime": 26211907700300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "0675a249-217b-4f1c-8d7e-26f6798ffba8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0675a249-217b-4f1c-8d7e-26f6798ffba8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211907682800, "endTime": 26211907700300}, "additional": {"logType": "info", "children": [], "durationId": "55458b69-05db-49d6-a54a-6951cba6cf74", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "21f1c5bd-bf1f-4bb3-9e3c-e56084df5a65", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211907762200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70e68002-48f6-441f-af9f-e1242e0a2d22", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211908558600, "endTime": 26211908579200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "c980cb4b-c16f-47b2-b62c-177a53da1e69"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c980cb4b-c16f-47b2-b62c-177a53da1e69", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211908558600, "endTime": 26211908579200}, "additional": {"logType": "info", "children": [], "durationId": "70e68002-48f6-441f-af9f-e1242e0a2d22", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "8d26dc35-0168-415f-8204-c7e055f014ac", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211908653000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc6ba8b1-3161-4c80-982a-e06f52c2bc30", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211909392200, "endTime": 26211909407800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "3aff7ea5-458b-4e58-8d55-95fcd86548ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3aff7ea5-458b-4e58-8d55-95fcd86548ea", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211909392200, "endTime": 26211909407800}, "additional": {"logType": "info", "children": [], "durationId": "fc6ba8b1-3161-4c80-982a-e06f52c2bc30", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "f669f795-89b6-4c46-89dd-3a1c9e9b7685", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211909461500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6293d372-8db7-4997-ab3b-9bf151ee228a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211910176400, "endTime": 26211910201300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "f1eeba17-ec18-461e-a8c4-095135917b4e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1eeba17-ec18-461e-a8c4-095135917b4e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211910176400, "endTime": 26211910201300}, "additional": {"logType": "info", "children": [], "durationId": "6293d372-8db7-4997-ab3b-9bf151ee228a", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "8e002289-6fa0-4532-a0e1-7c2c3faeeefd", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211910261900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d524ca8d-3d3d-44d8-a014-2bff4e0de2fa", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211911002200, "endTime": 26211911021800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "3a284870-40ef-4f79-8cfc-cc87c7f7dffc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a284870-40ef-4f79-8cfc-cc87c7f7dffc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211911002200, "endTime": 26211911021800}, "additional": {"logType": "info", "children": [], "durationId": "d524ca8d-3d3d-44d8-a014-2bff4e0de2fa", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "c5c7333b-5306-441a-aa39-01652125be3c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211911209100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eacde5d7-df88-4ec4-bc50-f28698196e87", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211911982500, "endTime": 26211911999800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "f3c6a11b-5086-4e2a-b4e4-1dc624198c84"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f3c6a11b-5086-4e2a-b4e4-1dc624198c84", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211911982500, "endTime": 26211911999800}, "additional": {"logType": "info", "children": [], "durationId": "eacde5d7-df88-4ec4-bc50-f28698196e87", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "5c8ec5cf-999d-4791-a323-257cb09938b6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211912052600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "709d890e-a55a-498c-abe5-c40412067004", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211912750700, "endTime": 26211912774100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "5cd47f15-fcf0-44e1-af64-a2bb842dfd8b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5cd47f15-fcf0-44e1-af64-a2bb842dfd8b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211912750700, "endTime": 26211912774100}, "additional": {"logType": "info", "children": [], "durationId": "709d890e-a55a-498c-abe5-c40412067004", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "c295356c-a71b-4096-b940-9d7f31697127", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211912829400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26fe6d56-3b34-4eb0-a1fd-02c876496097", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211913534400, "endTime": 26211913549300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "9fc8087b-7426-45d6-8bde-dbd303fc9a23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9fc8087b-7426-45d6-8bde-dbd303fc9a23", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211913534400, "endTime": 26211913549300}, "additional": {"logType": "info", "children": [], "durationId": "26fe6d56-3b34-4eb0-a1fd-02c876496097", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "05f3f43b-488c-4786-bd06-ffa5f07da3e2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211913607400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f086998f-1402-4750-a798-93e3124f0854", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211914406200, "endTime": 26211914422900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "ce4fae9f-f3ef-45ca-a478-29d645dbf231"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce4fae9f-f3ef-45ca-a478-29d645dbf231", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211914406200, "endTime": 26211914422900}, "additional": {"logType": "info", "children": [], "durationId": "f086998f-1402-4750-a798-93e3124f0854", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "bddd93ca-e937-43b9-973a-5fecddee7aba", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211914487900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "def24584-492c-420b-a6f6-48a00f56c30d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211915259900, "endTime": 26211915278800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "cedd1169-718b-4b27-a236-0dc5b19c73ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cedd1169-718b-4b27-a236-0dc5b19c73ac", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211915259900, "endTime": 26211915278800}, "additional": {"logType": "info", "children": [], "durationId": "def24584-492c-420b-a6f6-48a00f56c30d", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "6cf411b3-1238-4e26-959d-ac10c2c1e117", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211915364100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5e3aa2f-8792-4d7d-85be-586cbfcdb730", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211916764600, "endTime": 26211916792800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "18ebec58-bbca-420c-bf48-6a7fb62b4842"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18ebec58-bbca-420c-bf48-6a7fb62b4842", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211916764600, "endTime": 26211916792800}, "additional": {"logType": "info", "children": [], "durationId": "b5e3aa2f-8792-4d7d-85be-586cbfcdb730", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "6381954b-9c18-4104-baa2-3dcee14421d8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211916902800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d732aee8-9e77-4a5c-83be-53f985b9f01d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211918335000, "endTime": 26211918372000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "7b1daa4f-c895-4361-8c42-0e8556c5f671"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b1daa4f-c895-4361-8c42-0e8556c5f671", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211918335000, "endTime": 26211918372000}, "additional": {"logType": "info", "children": [], "durationId": "d732aee8-9e77-4a5c-83be-53f985b9f01d", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "6ba1c435-70b3-4ee3-a123-e22d0ad28b74", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211918470500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ab9b442-85ce-480e-914d-9218de5a7434", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211919807000, "endTime": 26211919828100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "d27ebcaf-526c-4a54-bcbc-49c46b536e3f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d27ebcaf-526c-4a54-bcbc-49c46b536e3f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211919807000, "endTime": 26211919828100}, "additional": {"logType": "info", "children": [], "durationId": "0ab9b442-85ce-480e-914d-9218de5a7434", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "2c8c734c-7c6f-4733-95ea-0b1a10d15086", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211919920400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fe20c42-7959-4247-b618-6717c20da2ec", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211921374400, "endTime": 26211921398500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "75aefac7-5b5c-419c-b958-91e8ad9ad11b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "75aefac7-5b5c-419c-b958-91e8ad9ad11b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211921374400, "endTime": 26211921398500}, "additional": {"logType": "info", "children": [], "durationId": "1fe20c42-7959-4247-b618-6717c20da2ec", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "89624db8-9340-4990-997c-04942c64ff47", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211921490000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5f5a918-d496-418c-9d8b-d0bf2fe99a1b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211922949500, "endTime": 26211922969700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "2ee6efca-6e1b-4ecf-932b-53fc7f87d4a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ee6efca-6e1b-4ecf-932b-53fc7f87d4a3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211922949500, "endTime": 26211922969700}, "additional": {"logType": "info", "children": [], "durationId": "a5f5a918-d496-418c-9d8b-d0bf2fe99a1b", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "1e78f970-d5fc-4a3d-8cfa-0bd49e323523", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211923063600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "507eadbf-9943-42a0-a265-8bb152b016c2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211924343100, "endTime": 26211924364500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "55972fe8-cda1-4849-a1c1-72a3cf59db98"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "55972fe8-cda1-4849-a1c1-72a3cf59db98", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211924343100, "endTime": 26211924364500}, "additional": {"logType": "info", "children": [], "durationId": "507eadbf-9943-42a0-a265-8bb152b016c2", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "303eee88-d26c-4192-a892-14e169667904", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211924460000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d7b0085-e90a-4b98-b089-ea86838773ad", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211925732800, "endTime": 26211925750100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "de24dd78-759e-4a70-b669-6e66b001b1ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de24dd78-759e-4a70-b669-6e66b001b1ed", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211925732800, "endTime": 26211925750100}, "additional": {"logType": "info", "children": [], "durationId": "7d7b0085-e90a-4b98-b089-ea86838773ad", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "7a560f23-174f-471b-9e31-141c57013c24", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211925824900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a318daa1-fad3-4d19-b4e0-b058e76a36ad", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211927104300, "endTime": 26211927121100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "96af405a-9ccc-477e-af68-164a7e737f96"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96af405a-9ccc-477e-af68-164a7e737f96", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211927104300, "endTime": 26211927121100}, "additional": {"logType": "info", "children": [], "durationId": "a318daa1-fad3-4d19-b4e0-b058e76a36ad", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "6bbbd6e4-a227-4553-8cad-54a7cbb386ab", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211927201100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13f77dbc-e80e-4dab-860d-51420a3abdac", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211928553200, "endTime": 26211928576200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "98e3479c-f767-4c54-adc0-8de43ac6a2ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "98e3479c-f767-4c54-adc0-8de43ac6a2ec", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211928553200, "endTime": 26211928576200}, "additional": {"logType": "info", "children": [], "durationId": "13f77dbc-e80e-4dab-860d-51420a3abdac", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "de7d9120-9d54-40a0-8066-ffe97ec7c8ef", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211928682600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6df0bf43-a6d8-4496-b008-e6f647ec1ad8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211930214800, "endTime": 26211930237500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "f0d76bb8-86e5-40ea-b32f-eb4b06a41e46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0d76bb8-86e5-40ea-b32f-eb4b06a41e46", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211930214800, "endTime": 26211930237500}, "additional": {"logType": "info", "children": [], "durationId": "6df0bf43-a6d8-4496-b008-e6f647ec1ad8", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "e0b1db11-f295-462e-8bc9-872f910f92d5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211930343200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0cbc344-d93f-4722-858c-b7c43a0c0c77", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211931816100, "endTime": 26211931838100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "5722e608-b811-48c9-ab74-0ffd2697f3a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5722e608-b811-48c9-ab74-0ffd2697f3a2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211931816100, "endTime": 26211931838100}, "additional": {"logType": "info", "children": [], "durationId": "b0cbc344-d93f-4722-858c-b7c43a0c0c77", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "062fe0a4-c56a-4216-a5ca-9c6e31978cc4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211931930300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "455ae4e5-f063-4a9e-af41-54b09ca66eb0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211933819400, "endTime": 26211933838300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "c3a15e86-ad57-410a-9837-53b4fae58734"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3a15e86-ad57-410a-9837-53b4fae58734", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211933819400, "endTime": 26211933838300}, "additional": {"logType": "info", "children": [], "durationId": "455ae4e5-f063-4a9e-af41-54b09ca66eb0", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "48f14d63-28c2-4e47-a13b-e23f6fbd8ecc", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211933919300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d60afbd5-36b5-4d74-9d75-f6c1a9e0f071", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211934763100, "endTime": 26211934777100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "b540b301-03d4-44e8-b7ca-c9197faf0245"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b540b301-03d4-44e8-b7ca-c9197faf0245", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211934763100, "endTime": 26211934777100}, "additional": {"logType": "info", "children": [], "durationId": "d60afbd5-36b5-4d74-9d75-f6c1a9e0f071", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "b8d154aa-5d37-4faa-b8f9-15973a15dbfe", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211934834800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e21f1ee4-9211-4bea-bcc7-76676c48fa74", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211935493000, "endTime": 26211935506300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "a6063afa-6b0e-45f0-a004-dc9f60cab490"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6063afa-6b0e-45f0-a004-dc9f60cab490", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211935493000, "endTime": 26211935506300}, "additional": {"logType": "info", "children": [], "durationId": "e21f1ee4-9211-4bea-bcc7-76676c48fa74", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "2b1ee72c-58b0-4a52-bab8-8604ec9a3372", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211935560500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05f44f07-9eb1-462f-8422-18b4f50359cd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211936214300, "endTime": 26211936226900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "51c5636a-3126-4683-bd5a-2a6329008cb8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51c5636a-3126-4683-bd5a-2a6329008cb8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211936214300, "endTime": 26211936226900}, "additional": {"logType": "info", "children": [], "durationId": "05f44f07-9eb1-462f-8422-18b4f50359cd", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "cc7bdb3d-07be-4869-bdb0-4378cab8eb71", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211936282200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6c5d518-6e9d-4b81-841c-5e1cda9178b4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211936966000, "endTime": 26211936979500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "e48083a6-bbce-4976-bd0b-776e900d1daf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e48083a6-bbce-4976-bd0b-776e900d1daf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26211936966000, "endTime": 26211936979500}, "additional": {"logType": "info", "children": [], "durationId": "c6c5d518-6e9d-4b81-841c-5e1cda9178b4", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "48366e31-242a-426d-adec-7a658576bcf5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26212288602400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26f3bbb0-ccb0-4dba-a7aa-32f51eaf7e43", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26212289664600, "endTime": 26212289681600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "d9f60af2-c5ab-4cba-b90a-3097c03773fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d9f60af2-c5ab-4cba-b90a-3097c03773fb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26212289664600, "endTime": 26212289681600}, "additional": {"logType": "info", "children": [], "durationId": "26f3bbb0-ccb0-4dba-a7aa-32f51eaf7e43", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "70e28045-b222-45ca-8775-fad5dddad446", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26212887458100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84a6e30f-ce41-470b-b0df-66ab175c9447", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26212888986200, "endTime": 26212889001000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "2a02a068-d2de-418a-a284-86603a9194b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a02a068-d2de-418a-a284-86603a9194b8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26212888986200, "endTime": 26212889001000}, "additional": {"logType": "info", "children": [], "durationId": "84a6e30f-ce41-470b-b0df-66ab175c9447", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "dffec1d8-96b3-4181-97df-2310c8c55141", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213096585600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae50fa86-0617-41dd-b821-26d78f7a8c62", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213097818100, "endTime": 26213097834400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "77db4bd6-cec5-4c7f-a0ee-ccb5a8d7e5e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "77db4bd6-cec5-4c7f-a0ee-ccb5a8d7e5e3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213097818100, "endTime": 26213097834400}, "additional": {"logType": "info", "children": [], "durationId": "ae50fa86-0617-41dd-b821-26d78f7a8c62", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "d8b93588-1a34-4403-accd-880240763709", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213107776300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9de75822-fac8-453d-8bf3-ccb4ad365668", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213108996800, "endTime": 26213109013800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "9fe6fc9f-e8ca-4cd1-ae7c-d13f37109e55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9fe6fc9f-e8ca-4cd1-ae7c-d13f37109e55", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213108996800, "endTime": 26213109013800}, "additional": {"logType": "info", "children": [], "durationId": "9de75822-fac8-453d-8bf3-ccb4ad365668", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "9f617f3c-ae60-41fa-91d1-046cced22f81", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213112937500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aae4f162-e789-4e7e-943f-1d320fa8f05f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213114026600, "endTime": 26213114043400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "logId": "911a76fb-ccbc-4300-bda8-731160790b19"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "911a76fb-ccbc-4300-bda8-731160790b19", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213114026600, "endTime": 26213114043400}, "additional": {"logType": "info", "children": [], "durationId": "aae4f162-e789-4e7e-943f-1d320fa8f05f", "parent": "0361a942-24a9-486f-8bf9-0ab6d98f892b"}}, {"head": {"id": "0361a942-24a9-486f-8bf9-0ab6d98f892b", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Worker5", "startTime": 26203073187300, "endTime": 26213114102500}, "additional": {"logType": "error", "children": ["5b969541-b3d1-4f16-a85b-43aef075b619", "35c5bfee-d979-485c-b475-feba86ffa6c7", "2fd54b8f-7fa3-4537-b16e-c67378d95ead", "2160ca1a-b1c7-4c23-ad3c-402bc791608e", "0f791282-7940-46ea-af9d-0ab828313357", "91c53b0c-d53e-465d-80a7-301c613c4edb", "dfed7524-ae56-42d3-a9cc-1506e84bed9a", "79ff752c-c964-404d-9b04-f269e52e2077", "d8c9a09e-87d3-46e5-9ac2-f9be99cfeea5", "7552a734-9bfb-477b-b094-8a7383e3ea29", "a3e4ad52-a6a9-4e72-b25c-9c7cd7994bf6", "ff2cd06c-aa89-409b-b393-b581b22f6c47", "c9aba78b-9f21-4dbd-bdef-4bb5a05fc537", "ffe29cf4-e8e1-4ce7-a3fd-a80870ad75ea", "5b62bd2e-3d93-4062-a72d-691fe4fae04e", "d907e0f7-d5cc-4bf8-9f05-84815e25bee9", "65e904f8-9993-4d9a-9075-56c3e3d9f116", "24f821df-fd9d-43f8-a8fc-30289d5b09d3", "708642b3-30b6-4907-81b8-99d2c66318e0", "7509fb98-19b8-481f-ac0b-57bd8650791c", "e202091c-e04b-4f0f-a7d5-f3f35450e278", "a5c2fa1e-906d-43c2-99a3-2ca35c98f632", "bc37fc55-bdb6-4f94-a960-22a5f8c0e9c8", "ec4e6f3b-4df1-4c20-aaa0-b6ee3e5ef429", "66a0ff5f-4dfa-4cf5-9a6e-f6a84c576dad", "283919d5-2c99-4b1f-bd51-7683755a3241", "fe7fb5de-b814-465e-b0e4-9e5807827af6", "d06488f0-5a2a-4eee-8d08-7a1420becdce", "b9e65a1e-15fc-43c4-8f32-7e244a21f9b0", "a7f3dc0a-66b3-4933-9bfb-b981d5b37c6b", "f1f2b9c3-b5df-4dbc-b447-389fd8c1995c", "d0a7fb12-2317-46d5-a136-eefb485421b5", "c30070b1-477d-402d-9f18-6ec88b4ec9d1", "3c802585-06cd-4506-8ce1-d2b58918113e", "21e36563-d5c9-450f-94ea-b92bff4881a5", "1c94f55c-1eae-4675-a18b-21440b366710", "69eea447-845e-4604-8a79-018440e7136b", "429d2ef0-b6ec-42e7-a1e1-5abb0ce4f181", "d6bb97f6-d388-4bb2-808b-2c85a7353dc7", "366586db-ce09-40af-87c8-c68e85378cef", "73b7c083-fe9b-4f68-9ebe-7759086f065f", "7c2c5df2-d30b-4dbf-bb6b-d9d8a05dc985", "dffdaae1-7e41-4207-a187-a49687e263e8", "77fc07ea-c58d-429d-9d81-a15d3341e9f2", "bda01b7f-97ca-40a5-8fcf-ce730e68b32d", "bad969e2-ee0a-4bd7-a82c-1eb73e3b0869", "a2811a16-1e37-4c3e-a8ed-0532e9492894", "a68c291b-1a2a-483b-83fc-79f78fcf2f53", "3f1b0273-2461-4b82-869b-23cb0f749cf7", "e30a2284-d138-4321-98b5-595f661480f1", "4c4b02b3-760b-4e32-ab31-ad4f9c53717b", "0e47812d-6d90-4e5a-ae5f-37ce4e73cc46", "66420994-d6b0-4364-b488-d71b55f39400", "e24298c4-60a4-4b2b-963b-a4f381e2019f", "66e70d08-de11-41ce-ab25-0d17e38461b8", "108ee235-fdb3-429c-9f0e-91ee9e986a29", "8f77ae28-1b9c-467c-826b-6d186f7f68de", "3bbfe7b4-9e3b-4d90-bba7-3d9420311ffd", "59367388-e787-4cbd-8f5d-c197208cc300", "c9ce4ca3-f345-4565-8b47-8b6006049951", "8bba4d0c-573b-43cb-8c0f-9ee843bca65e", "a4e86527-f79a-40ef-a84c-86f76aa3de60", "acae2052-f308-4534-a71c-f7af3f7b44a8", "bdd5b282-d661-42c0-8a36-f9c41c633b2d", "a03be638-abc8-4fff-91d3-ec07ae2c4b96", "77ae02ae-abb6-4385-b764-a7a233642849", "6f57f837-216b-46dd-b337-144a98f1dd5e", "1dbad674-b491-42c9-9361-00eda435964f", "22eeb93b-d55b-47ea-8164-6e32058192a5", "9b19cf72-7a4c-4e17-bfbf-b3846d3afff2", "8a1cc934-a27e-4c4d-bc34-af2d07f813a2", "f7a735d5-fb19-4d65-aea5-aa52923617a4", "cd336bc3-8bd0-4806-a8b7-51d618b9d462", "d2fc5cd1-d2c3-498f-9fa9-6412ace4022c", "ea8ace78-374b-450f-8c56-8ddaffa13154", "73ee7729-c628-4172-b561-65107dcb73d5", "00e395e1-a734-41a6-9afc-655486ea9159", "2985fc8b-c915-414b-8b79-99d6fa8f6bbb", "2c478585-f91a-47ac-96c8-be985cbc90d8", "bff01c7e-1064-4d6a-bfdb-2dd0ab2dcbbf", "8e8ee3d2-73c6-4501-91aa-d641f9699530", "510a57a0-6d07-44df-b017-6c7342d71b5a", "651e503a-468a-4537-91b0-116e92843240", "8934bf43-45e7-4507-8b8a-2fa776aaec12", "c1f2f618-f75c-4a41-8a42-05744b59c400", "0ce6aa07-81d6-4f3c-b903-cd1dad989aa0", "0675a249-217b-4f1c-8d7e-26f6798ffba8", "c980cb4b-c16f-47b2-b62c-177a53da1e69", "3aff7ea5-458b-4e58-8d55-95fcd86548ea", "f1eeba17-ec18-461e-a8c4-095135917b4e", "3a284870-40ef-4f79-8cfc-cc87c7f7dffc", "f3c6a11b-5086-4e2a-b4e4-1dc624198c84", "5cd47f15-fcf0-44e1-af64-a2bb842dfd8b", "9fc8087b-7426-45d6-8bde-dbd303fc9a23", "ce4fae9f-f3ef-45ca-a478-29d645dbf231", "cedd1169-718b-4b27-a236-0dc5b19c73ac", "18ebec58-bbca-420c-bf48-6a7fb62b4842", "7b1daa4f-c895-4361-8c42-0e8556c5f671", "d27ebcaf-526c-4a54-bcbc-49c46b536e3f", "75aefac7-5b5c-419c-b958-91e8ad9ad11b", "2ee6efca-6e1b-4ecf-932b-53fc7f87d4a3", "55972fe8-cda1-4849-a1c1-72a3cf59db98", "de24dd78-759e-4a70-b669-6e66b001b1ed", "96af405a-9ccc-477e-af68-164a7e737f96", "98e3479c-f767-4c54-adc0-8de43ac6a2ec", "f0d76bb8-86e5-40ea-b32f-eb4b06a41e46", "5722e608-b811-48c9-ab74-0ffd2697f3a2", "c3a15e86-ad57-410a-9837-53b4fae58734", "b540b301-03d4-44e8-b7ca-c9197faf0245", "a6063afa-6b0e-45f0-a004-dc9f60cab490", "51c5636a-3126-4683-bd5a-2a6329008cb8", "e48083a6-bbce-4976-bd0b-776e900d1daf", "d9f60af2-c5ab-4cba-b90a-3097c03773fb", "2a02a068-d2de-418a-a284-86603a9194b8", "77db4bd6-cec5-4c7f-a0ee-ccb5a8d7e5e3", "9fe6fc9f-e8ca-4cd1-ae7c-d13f37109e55", "911a76fb-ccbc-4300-bda8-731160790b19"], "durationId": "811f12c1-19ed-495b-8deb-e8cd2deb6251", "parent": "be65c15e-d264-4de3-ad37-9c1db591aa96"}}, {"head": {"id": "408df08b-ccb0-4e6b-99cc-75b9d6958567", "name": "default@PreviewArkTS watch work[5] failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213114258700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be65c15e-d264-4de3-ad37-9c1db591aa96", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26203010715400, "endTime": 26213114366400}, "additional": {"logType": "error", "children": ["0361a942-24a9-486f-8bf9-0ab6d98f892b"], "durationId": "a2e746d9-ffe0-4487-90c3-15124f62b7bf"}}, {"head": {"id": "0648963f-ba27-404c-8832-ba5ddf0f08d4", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213114421200}, "additional": {"logType": "debug", "children": [], "durationId": "a2e746d9-ffe0-4487-90c3-15124f62b7bf"}}, {"head": {"id": "8fff420b-ac3d-48d0-a6f6-28f13e51b410", "name": "ERROR: stacktrace = Error: Compilation failed\n    at handleResponse (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213115012100}, "additional": {"logType": "debug", "children": [], "durationId": "a2e746d9-ffe0-4487-90c3-15124f62b7bf"}}, {"head": {"id": "e844d734-d630-463a-8467-e0fb717476f4", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213124645800, "endTime": 26213124699300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3c70e015-2e9c-4aec-83ca-a21c6a756548", "logId": "ba3867ac-75cd-48c3-b8ba-331a65da773b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ba3867ac-75cd-48c3-b8ba-331a65da773b", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213124645800, "endTime": 26213124699300}, "additional": {"logType": "info", "children": [], "durationId": "e844d734-d630-463a-8467-e0fb717476f4"}}, {"head": {"id": "e073d658-7c7c-43f1-ab9f-fc19cba97e91", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26202322785300, "endTime": 26213124808600}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 16, "minute": 22}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "0fd95528-7a2b-451c-bc0a-bd443f73e2fb", "name": "BUILD FAILED in 10 s 802 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213124839000}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "739d851e-e450-40e7-94db-43eccefbba07", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213124968300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "423ec05e-c636-4988-a84a-6e1e8069d9be", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213124997000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38ccbdeb-e70b-442e-ad45-a4f788f30469", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213125018900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f175c412-835c-4233-b17b-3d1c60f25c88", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213125040200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e95ded81-7a81-4452-bc65-d83632046b01", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213125066000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fee01ec1-db57-4446-92b8-8ef72860c34e", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213125088600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64f1527f-188f-43cc-aec2-8ac1cb1f45d4", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213125107600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f27edd4c-312f-4bab-bbdf-17810dea8a6c", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213125126600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34954533-2b22-4b4b-9ade-746209512352", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213125145500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3aa5e0b2-741d-4152-a964-aaf03fc0da41", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213125164300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2e9d5ed-0c3f-48ea-9095-962bb7ac0c6f", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213128435100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51ee5850-9cef-4d1b-b940-b9f73ad63806", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213129286000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3a3571a-7a9d-4aff-9210-4a5b8f7939fb", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213129566800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "282f555c-1671-42ad-8b2c-cd72983e55a6", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213129858300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1512c60b-2e05-4bce-a9b4-440524ce9372", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213130550200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7c0ff18-1a67-4a42-a6fc-286dbdd822dd", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213139844400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faef4424-890a-4c1d-a82e-1c9eec844ff0", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213140187600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86f57aac-a37a-4280-acb2-41656550ff86", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213140426000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49aa56d7-052d-47dc-b4c8-42ac5b8c9e5c", "name": "Update task entry:default@PreviewArkTS output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213140683200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6935e78c-6c20-4b80-b60d-4340f89bc5a7", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:16 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 26213141557400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}