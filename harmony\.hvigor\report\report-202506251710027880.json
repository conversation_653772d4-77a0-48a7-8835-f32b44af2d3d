{"version": "2.0", "ppid": 21780, "events": [{"head": {"id": "3b5b5cd6-31d8-483d-a86d-c77f925ad80f", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140977033000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41ee7e8a-0663-473a-89d2-06c52c2c3004", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140993616900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed960de8-4c75-4739-9dc6-0c0202f7cc44", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 28140994126800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf8f0b59-4a29-40a0-98e3-678f0a0223cc", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045531794000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d79182f8-f7a8-42c0-8ac4-67af22f18e4e", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045540665800, "endTime": 29045694272600}, "additional": {"children": ["1023feef-714c-42b4-8067-81ae8874a07e", "ddd1367e-24da-4b24-842a-f340aca939c5", "41ef130b-62d2-489c-97d5-906bb9c39c71", "fbaaf390-e80e-4027-8f16-bc74249e4e29", "09f8d298-ba01-471e-bcd8-a1f719a8c7db", "7f62ccf8-69da-4adf-bcaa-ce1d7ae152ef", "3f8d3ee6-1a0a-4e54-9fbb-73b841a12b1a"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "2948c612-5f5a-42fa-a7ab-461047207255"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1023feef-714c-42b4-8067-81ae8874a07e", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045540667500, "endTime": 29045555605400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d79182f8-f7a8-42c0-8ac4-67af22f18e4e", "logId": "f3bea178-b6bb-41e3-94b3-e59ef1414e64"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ddd1367e-24da-4b24-842a-f340aca939c5", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045555619800, "endTime": 29045693207400}, "additional": {"children": ["37ee2038-958a-49a3-80dd-59f1505a39de", "9e86651b-4f73-4173-ab55-83762d96f13a", "ed9a8da1-8804-4735-86d4-1092585b4c74", "8fc42e4e-6806-432b-b7e2-50cea72452fb", "37aec111-016a-464b-8d54-ab7d9db6474c", "d9d28f33-25fd-42e9-89a1-03d7ae827876", "00a166c3-0c6b-4308-b2c8-38d01ba9df00", "e0fc4d7c-bb88-4d67-a3f3-b04e587d1da5", "3008481a-a490-41ef-b287-cc5d398d81f6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d79182f8-f7a8-42c0-8ac4-67af22f18e4e", "logId": "e9f09557-1a18-4adf-9de1-ad7e56693424"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41ef130b-62d2-489c-97d5-906bb9c39c71", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045693219700, "endTime": 29045694258900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d79182f8-f7a8-42c0-8ac4-67af22f18e4e", "logId": "1c7a4cb5-e11c-498e-8eb9-b3900995072a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fbaaf390-e80e-4027-8f16-bc74249e4e29", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045694265400, "endTime": 29045694271100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d79182f8-f7a8-42c0-8ac4-67af22f18e4e", "logId": "10702a80-8e9c-4f1a-8704-2e2b6b8f39ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "09f8d298-ba01-471e-bcd8-a1f719a8c7db", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045544419400, "endTime": 29045544473500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d79182f8-f7a8-42c0-8ac4-67af22f18e4e", "logId": "617ac92d-6b5a-4a31-adbe-c049f17c2fbc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "617ac92d-6b5a-4a31-adbe-c049f17c2fbc", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045544419400, "endTime": 29045544473500}, "additional": {"logType": "info", "children": [], "durationId": "09f8d298-ba01-471e-bcd8-a1f719a8c7db", "parent": "2948c612-5f5a-42fa-a7ab-461047207255"}}, {"head": {"id": "7f62ccf8-69da-4adf-bcaa-ce1d7ae152ef", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045550474300, "endTime": 29045550492900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d79182f8-f7a8-42c0-8ac4-67af22f18e4e", "logId": "1ed1f4fc-ec3c-4bcf-986c-eac62a1cc01f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ed1f4fc-ec3c-4bcf-986c-eac62a1cc01f", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045550474300, "endTime": 29045550492900}, "additional": {"logType": "info", "children": [], "durationId": "7f62ccf8-69da-4adf-bcaa-ce1d7ae152ef", "parent": "2948c612-5f5a-42fa-a7ab-461047207255"}}, {"head": {"id": "bb4ef177-32a1-48a4-803c-ca172eee00b9", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045550553700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3dab4c5-3d13-41a6-be54-0ed4c3749195", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045555395700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3bea178-b6bb-41e3-94b3-e59ef1414e64", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045540667500, "endTime": 29045555605400}, "additional": {"logType": "info", "children": [], "durationId": "1023feef-714c-42b4-8067-81ae8874a07e", "parent": "2948c612-5f5a-42fa-a7ab-461047207255"}}, {"head": {"id": "37ee2038-958a-49a3-80dd-59f1505a39de", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045561150300, "endTime": 29045561170200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ddd1367e-24da-4b24-842a-f340aca939c5", "logId": "eeaf8f4f-898b-4230-a384-5fb22d32512c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e86651b-4f73-4173-ab55-83762d96f13a", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045561185700, "endTime": 29045564886700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ddd1367e-24da-4b24-842a-f340aca939c5", "logId": "aa7b94de-b7a2-45cb-b6ed-b9a8a31ac876"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed9a8da1-8804-4735-86d4-1092585b4c74", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045564896200, "endTime": 29045640626500}, "additional": {"children": ["cb7def3f-6a67-421f-8d97-3781473f16f1", "1cf29bee-05d2-4ec7-93ff-b53bab5a433a", "6a10683c-e2f2-46c7-a72f-567ccc962eb7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ddd1367e-24da-4b24-842a-f340aca939c5", "logId": "b42712b1-799a-4edb-84a2-431e203a5122"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8fc42e4e-6806-432b-b7e2-50cea72452fb", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045640637100, "endTime": 29045662015100}, "additional": {"children": ["af91fa64-1461-4fb4-a415-9e5e6d3d6387"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ddd1367e-24da-4b24-842a-f340aca939c5", "logId": "fe28825c-1176-469b-8cda-f019fd4263a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "37aec111-016a-464b-8d54-ab7d9db6474c", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045662178600, "endTime": 29045674486200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ddd1367e-24da-4b24-842a-f340aca939c5", "logId": "43066c5d-1c84-4944-be52-f876b26f30b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d9d28f33-25fd-42e9-89a1-03d7ae827876", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045675458900, "endTime": 29045683245500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ddd1367e-24da-4b24-842a-f340aca939c5", "logId": "f34ff29a-ff66-4e74-8c8d-27d30d503318"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "00a166c3-0c6b-4308-b2c8-38d01ba9df00", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045683260700, "endTime": 29045692791600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ddd1367e-24da-4b24-842a-f340aca939c5", "logId": "dfdf54e5-e2a6-427b-88d4-41f8f6f9daf9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e0fc4d7c-bb88-4d67-a3f3-b04e587d1da5", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045692810100, "endTime": 29045693197100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ddd1367e-24da-4b24-842a-f340aca939c5", "logId": "ce005aa3-29d1-4313-adec-684606a69a8a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eeaf8f4f-898b-4230-a384-5fb22d32512c", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045561150300, "endTime": 29045561170200}, "additional": {"logType": "info", "children": [], "durationId": "37ee2038-958a-49a3-80dd-59f1505a39de", "parent": "e9f09557-1a18-4adf-9de1-ad7e56693424"}}, {"head": {"id": "aa7b94de-b7a2-45cb-b6ed-b9a8a31ac876", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045561185700, "endTime": 29045564886700}, "additional": {"logType": "info", "children": [], "durationId": "9e86651b-4f73-4173-ab55-83762d96f13a", "parent": "e9f09557-1a18-4adf-9de1-ad7e56693424"}}, {"head": {"id": "cb7def3f-6a67-421f-8d97-3781473f16f1", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045565457900, "endTime": 29045565474700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed9a8da1-8804-4735-86d4-1092585b4c74", "logId": "23ebfd0f-0b5c-40f1-8445-9f978130ae1d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23ebfd0f-0b5c-40f1-8445-9f978130ae1d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045565457900, "endTime": 29045565474700}, "additional": {"logType": "info", "children": [], "durationId": "cb7def3f-6a67-421f-8d97-3781473f16f1", "parent": "b42712b1-799a-4edb-84a2-431e203a5122"}}, {"head": {"id": "1cf29bee-05d2-4ec7-93ff-b53bab5a433a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045566995300, "endTime": 29045640027100}, "additional": {"children": ["9c0d2d89-eaa1-43d3-849c-49a03c78349f", "3ae2bcff-8043-4608-a14a-12c7367f3f3b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed9a8da1-8804-4735-86d4-1092585b4c74", "logId": "906a5043-2fc7-4271-abcf-c5028e993297"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c0d2d89-eaa1-43d3-849c-49a03c78349f", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045566996000, "endTime": 29045571763900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1cf29bee-05d2-4ec7-93ff-b53bab5a433a", "logId": "bfa97474-fff6-4b61-b0ec-c043c01fa98e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ae2bcff-8043-4608-a14a-12c7367f3f3b", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045571775400, "endTime": 29045640020200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1cf29bee-05d2-4ec7-93ff-b53bab5a433a", "logId": "db088148-9d26-433c-bf1d-2610c26c41c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b94ee7c7-2ff6-433e-b68a-27d4aee80c5d", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045567000900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9388b06c-c29b-4fa7-8c4a-5df7b7178af5", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045571617900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfa97474-fff6-4b61-b0ec-c043c01fa98e", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045566996000, "endTime": 29045571763900}, "additional": {"logType": "info", "children": [], "durationId": "9c0d2d89-eaa1-43d3-849c-49a03c78349f", "parent": "906a5043-2fc7-4271-abcf-c5028e993297"}}, {"head": {"id": "39cfcde3-6079-4a9b-ae22-0051621cdfe1", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045571784800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87f013fa-567f-4ba0-893c-3b191bb030cd", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045578848100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0e1dd90-23c7-47c2-a22b-6c0fad769e44", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045578985600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "affb7a45-1017-4465-a2d8-c9d13fb64a76", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045579106200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9682dc45-3563-4927-90af-2a66679925b5", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045579264700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c808f17-4c9c-4125-803b-6cce635c03d7", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045582010600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c683fe9-02de-4bda-b060-f4b9196a73b8", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045586313900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e292511-716e-44e3-afe6-f86d9f7c1aa9", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045595682300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d46f7e6-be73-430e-8101-736b60361d2c", "name": "Sdk init in 31 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045617788100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60eb103e-2397-4f31-aeca-4554ead20f5c", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045617946600}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 10}, "markType": "other"}}, {"head": {"id": "54512552-00c2-44ce-83b4-12dca6d96d10", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045617965700}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 10}, "markType": "other"}}, {"head": {"id": "65c86e01-2a4b-4da1-9803-0fc6af2b2e45", "name": "Project task initialization takes 20 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045639818500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "776a97ab-0764-4d70-a096-0e801d5e8235", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045639923400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ad112e3-00fd-4a40-97a4-f6d5b7d4944d", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045639957300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bfe96e3-8801-405f-81a0-3a1e7c23c0ec", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045639995700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db088148-9d26-433c-bf1d-2610c26c41c6", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045571775400, "endTime": 29045640020200}, "additional": {"logType": "info", "children": [], "durationId": "3ae2bcff-8043-4608-a14a-12c7367f3f3b", "parent": "906a5043-2fc7-4271-abcf-c5028e993297"}}, {"head": {"id": "906a5043-2fc7-4271-abcf-c5028e993297", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045566995300, "endTime": 29045640027100}, "additional": {"logType": "info", "children": ["bfa97474-fff6-4b61-b0ec-c043c01fa98e", "db088148-9d26-433c-bf1d-2610c26c41c6"], "durationId": "1cf29bee-05d2-4ec7-93ff-b53bab5a433a", "parent": "b42712b1-799a-4edb-84a2-431e203a5122"}}, {"head": {"id": "6a10683c-e2f2-46c7-a72f-567ccc962eb7", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045640603500, "endTime": 29045640617100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed9a8da1-8804-4735-86d4-1092585b4c74", "logId": "07fb0e15-98a4-4e3d-83ac-41d4460d4873"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "07fb0e15-98a4-4e3d-83ac-41d4460d4873", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045640603500, "endTime": 29045640617100}, "additional": {"logType": "info", "children": [], "durationId": "6a10683c-e2f2-46c7-a72f-567ccc962eb7", "parent": "b42712b1-799a-4edb-84a2-431e203a5122"}}, {"head": {"id": "b42712b1-799a-4edb-84a2-431e203a5122", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045564896200, "endTime": 29045640626500}, "additional": {"logType": "info", "children": ["23ebfd0f-0b5c-40f1-8445-9f978130ae1d", "906a5043-2fc7-4271-abcf-c5028e993297", "07fb0e15-98a4-4e3d-83ac-41d4460d4873"], "durationId": "ed9a8da1-8804-4735-86d4-1092585b4c74", "parent": "e9f09557-1a18-4adf-9de1-ad7e56693424"}}, {"head": {"id": "af91fa64-1461-4fb4-a415-9e5e6d3d6387", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045641167700, "endTime": 29045662001700}, "additional": {"children": ["d82209ff-fdc5-4d97-b60e-e9e573ff670b", "c59d9a0e-f7ef-446d-9866-d886c2f3918e", "a1806678-3896-48e8-83c9-23bcd5ffb538"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8fc42e4e-6806-432b-b7e2-50cea72452fb", "logId": "78c95e95-5ad5-42ae-8c11-7ec5c85d2bbe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d82209ff-fdc5-4d97-b60e-e9e573ff670b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045643753900, "endTime": 29045643771000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "af91fa64-1461-4fb4-a415-9e5e6d3d6387", "logId": "08031aa0-bb40-43f5-98ab-4e8dc1688f2c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "08031aa0-bb40-43f5-98ab-4e8dc1688f2c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045643753900, "endTime": 29045643771000}, "additional": {"logType": "info", "children": [], "durationId": "d82209ff-fdc5-4d97-b60e-e9e573ff670b", "parent": "78c95e95-5ad5-42ae-8c11-7ec5c85d2bbe"}}, {"head": {"id": "c59d9a0e-f7ef-446d-9866-d886c2f3918e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045645499000, "endTime": 29045660752800}, "additional": {"children": ["a55aadd0-8aa9-40b8-b902-c6702a13de9d", "78b76a3a-c59c-43db-941b-911018a513ed"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "af91fa64-1461-4fb4-a415-9e5e6d3d6387", "logId": "dc1bdbcf-44ce-48e4-a7ed-c6bf5c32689a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a55aadd0-8aa9-40b8-b902-c6702a13de9d", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045645499900, "endTime": 29045649640900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c59d9a0e-f7ef-446d-9866-d886c2f3918e", "logId": "a19bda56-f986-4fe1-af48-85b563d2d81b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78b76a3a-c59c-43db-941b-911018a513ed", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045649656800, "endTime": 29045660744900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c59d9a0e-f7ef-446d-9866-d886c2f3918e", "logId": "50e2d605-dbf0-4511-b555-6ecfdeb9d93f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b20f698b-665f-43f9-bace-88be6b51306d", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045645505100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2397265-5d22-4e61-a561-1fddee451661", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045649484900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a19bda56-f986-4fe1-af48-85b563d2d81b", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045645499900, "endTime": 29045649640900}, "additional": {"logType": "info", "children": [], "durationId": "a55aadd0-8aa9-40b8-b902-c6702a13de9d", "parent": "dc1bdbcf-44ce-48e4-a7ed-c6bf5c32689a"}}, {"head": {"id": "6b23d31a-dd6e-4798-86be-719cfbf16608", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045649677900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc19425c-bd03-4298-b859-8c09f90a63ff", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045656707900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "671c62cc-fed6-48ec-bb71-5384bd138d10", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045656814600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4cc0f8b-3007-402d-b75a-68929fec1d4c", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045656978600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bad2fc4-5417-4b89-bd1c-fe71022f7e54", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045657282900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5419427e-5f03-4f2a-b6ad-8608084b9ecc", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045657378500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d40c383-6976-406a-9b8b-d55515ac8412", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045657438200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a331857-383c-426e-8054-b4ed7b60bf4f", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045657516000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f84c8652-e622-4f2b-80d0-e1e9ab5e16c5", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045660474100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11f6361b-3fca-40ef-90c2-2de3c886cf35", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045660642500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc2fbf49-2577-4cfb-8503-58b9a3086875", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045660685500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fd6a630-a7e8-4f4f-bfc8-4a155c8da978", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045660711800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50e2d605-dbf0-4511-b555-6ecfdeb9d93f", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045649656800, "endTime": 29045660744900}, "additional": {"logType": "info", "children": [], "durationId": "78b76a3a-c59c-43db-941b-911018a513ed", "parent": "dc1bdbcf-44ce-48e4-a7ed-c6bf5c32689a"}}, {"head": {"id": "dc1bdbcf-44ce-48e4-a7ed-c6bf5c32689a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045645499000, "endTime": 29045660752800}, "additional": {"logType": "info", "children": ["a19bda56-f986-4fe1-af48-85b563d2d81b", "50e2d605-dbf0-4511-b555-6ecfdeb9d93f"], "durationId": "c59d9a0e-f7ef-446d-9866-d886c2f3918e", "parent": "78c95e95-5ad5-42ae-8c11-7ec5c85d2bbe"}}, {"head": {"id": "a1806678-3896-48e8-83c9-23bcd5ffb538", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045661974500, "endTime": 29045661989500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "af91fa64-1461-4fb4-a415-9e5e6d3d6387", "logId": "1abd554c-54a4-44a4-96cc-3520e4fe9bd2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1abd554c-54a4-44a4-96cc-3520e4fe9bd2", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045661974500, "endTime": 29045661989500}, "additional": {"logType": "info", "children": [], "durationId": "a1806678-3896-48e8-83c9-23bcd5ffb538", "parent": "78c95e95-5ad5-42ae-8c11-7ec5c85d2bbe"}}, {"head": {"id": "78c95e95-5ad5-42ae-8c11-7ec5c85d2bbe", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045641167700, "endTime": 29045662001700}, "additional": {"logType": "info", "children": ["08031aa0-bb40-43f5-98ab-4e8dc1688f2c", "dc1bdbcf-44ce-48e4-a7ed-c6bf5c32689a", "1abd554c-54a4-44a4-96cc-3520e4fe9bd2"], "durationId": "af91fa64-1461-4fb4-a415-9e5e6d3d6387", "parent": "fe28825c-1176-469b-8cda-f019fd4263a9"}}, {"head": {"id": "fe28825c-1176-469b-8cda-f019fd4263a9", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045640637100, "endTime": 29045662015100}, "additional": {"logType": "info", "children": ["78c95e95-5ad5-42ae-8c11-7ec5c85d2bbe"], "durationId": "8fc42e4e-6806-432b-b7e2-50cea72452fb", "parent": "e9f09557-1a18-4adf-9de1-ad7e56693424"}}, {"head": {"id": "da84ba04-3bf8-45e6-9aec-f4cf1d770472", "name": "watch files: [\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\hvigorfile.ts',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045674097700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d76fa9c7-43c0-4d71-acd4-0773d1932835", "name": "hvigorfile, resolve hvigorfile dependencies in 13 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045674426700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43066c5d-1c84-4944-be52-f876b26f30b5", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045662178600, "endTime": 29045674486200}, "additional": {"logType": "info", "children": [], "durationId": "37aec111-016a-464b-8d54-ab7d9db6474c", "parent": "e9f09557-1a18-4adf-9de1-ad7e56693424"}}, {"head": {"id": "3008481a-a490-41ef-b287-cc5d398d81f6", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045675275900, "endTime": 29045675452200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ddd1367e-24da-4b24-842a-f340aca939c5", "logId": "4ce72540-4fb9-4cb6-b5e9-6601a7b8669c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a9e302d-62df-41e3-b0ce-1c53656063d2", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045675308700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ce72540-4fb9-4cb6-b5e9-6601a7b8669c", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045675275900, "endTime": 29045675452200}, "additional": {"logType": "info", "children": [], "durationId": "3008481a-a490-41ef-b287-cc5d398d81f6", "parent": "e9f09557-1a18-4adf-9de1-ad7e56693424"}}, {"head": {"id": "81a3b44a-b929-4ade-abef-08f3c1d2546d", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045676764000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7eaab629-be25-4645-b27e-64c09a1eb18a", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045682466000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f34ff29a-ff66-4e74-8c8d-27d30d503318", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045675458900, "endTime": 29045683245500}, "additional": {"logType": "info", "children": [], "durationId": "d9d28f33-25fd-42e9-89a1-03d7ae827876", "parent": "e9f09557-1a18-4adf-9de1-ad7e56693424"}}, {"head": {"id": "6457d974-4c3f-461b-bbb3-592938a5278c", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045683280400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba00f243-455e-4f8f-9d0d-123302fd86c9", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045688183600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2da05468-61c2-46e4-9512-8fa1fe09c1cc", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045688316200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1943befe-13b4-4040-bdd1-9e457b3fb995", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045688532900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "094a40e6-7de1-491e-b5f8-11e3b96d1198", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045690179900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7430822-9c25-42bc-9f49-b6dd59c6d409", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045690252300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfdf54e5-e2a6-427b-88d4-41f8f6f9daf9", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045683260700, "endTime": 29045692791600}, "additional": {"logType": "info", "children": [], "durationId": "00a166c3-0c6b-4308-b2c8-38d01ba9df00", "parent": "e9f09557-1a18-4adf-9de1-ad7e56693424"}}, {"head": {"id": "a6015680-de39-46e1-9be1-2d85017a3915", "name": "Configuration phase cost:132 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045692839200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce005aa3-29d1-4313-adec-684606a69a8a", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045692810100, "endTime": 29045693197100}, "additional": {"logType": "info", "children": [], "durationId": "e0fc4d7c-bb88-4d67-a3f3-b04e587d1da5", "parent": "e9f09557-1a18-4adf-9de1-ad7e56693424"}}, {"head": {"id": "e9f09557-1a18-4adf-9de1-ad7e56693424", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045555619800, "endTime": 29045693207400}, "additional": {"logType": "info", "children": ["eeaf8f4f-898b-4230-a384-5fb22d32512c", "aa7b94de-b7a2-45cb-b6ed-b9a8a31ac876", "b42712b1-799a-4edb-84a2-431e203a5122", "fe28825c-1176-469b-8cda-f019fd4263a9", "43066c5d-1c84-4944-be52-f876b26f30b5", "f34ff29a-ff66-4e74-8c8d-27d30d503318", "dfdf54e5-e2a6-427b-88d4-41f8f6f9daf9", "ce005aa3-29d1-4313-adec-684606a69a8a", "4ce72540-4fb9-4cb6-b5e9-6601a7b8669c"], "durationId": "ddd1367e-24da-4b24-842a-f340aca939c5", "parent": "2948c612-5f5a-42fa-a7ab-461047207255"}}, {"head": {"id": "3f8d3ee6-1a0a-4e54-9fbb-73b841a12b1a", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045694236700, "endTime": 29045694252200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d79182f8-f7a8-42c0-8ac4-67af22f18e4e", "logId": "7fe97175-ff67-4e7b-a5a6-f6d13498964f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7fe97175-ff67-4e7b-a5a6-f6d13498964f", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045694236700, "endTime": 29045694252200}, "additional": {"logType": "info", "children": [], "durationId": "3f8d3ee6-1a0a-4e54-9fbb-73b841a12b1a", "parent": "2948c612-5f5a-42fa-a7ab-461047207255"}}, {"head": {"id": "1c7a4cb5-e11c-498e-8eb9-b3900995072a", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045693219700, "endTime": 29045694258900}, "additional": {"logType": "info", "children": [], "durationId": "41ef130b-62d2-489c-97d5-906bb9c39c71", "parent": "2948c612-5f5a-42fa-a7ab-461047207255"}}, {"head": {"id": "10702a80-8e9c-4f1a-8704-2e2b6b8f39ac", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045694265400, "endTime": 29045694271100}, "additional": {"logType": "info", "children": [], "durationId": "fbaaf390-e80e-4027-8f16-bc74249e4e29", "parent": "2948c612-5f5a-42fa-a7ab-461047207255"}}, {"head": {"id": "2948c612-5f5a-42fa-a7ab-461047207255", "name": "init", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045540665800, "endTime": 29045694272600}, "additional": {"logType": "info", "children": ["f3bea178-b6bb-41e3-94b3-e59ef1414e64", "e9f09557-1a18-4adf-9de1-ad7e56693424", "1c7a4cb5-e11c-498e-8eb9-b3900995072a", "10702a80-8e9c-4f1a-8704-2e2b6b8f39ac", "617ac92d-6b5a-4a31-adbe-c049f17c2fbc", "1ed1f4fc-ec3c-4bcf-986c-eac62a1cc01f", "7fe97175-ff67-4e7b-a5a6-f6d13498964f"], "durationId": "d79182f8-f7a8-42c0-8ac4-67af22f18e4e"}}, {"head": {"id": "1744e7bb-aa1e-4039-a8ec-a230473ff053", "name": "Configuration task cost before running: 158 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045694379100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15bc5e89-b095-43d4-a426-02cbb8cc8e9b", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045698507100, "endTime": 29045705437200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "3c2fe040-3bd6-4766-b02c-bc927531b0b9", "logId": "095ecc61-01bb-4606-b665-0c68d80a0775"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c2fe040-3bd6-4766-b02c-bc927531b0b9", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045695743700}, "additional": {"logType": "detail", "children": [], "durationId": "15bc5e89-b095-43d4-a426-02cbb8cc8e9b"}}, {"head": {"id": "7dfaa59f-2a12-43d5-95fb-de11e7e38e59", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045696172100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7390e4a4-e79f-41cc-a6b9-6f2f9234a5d2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045696265400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d937b19-feb1-4905-a809-6f4a21fc6ad5", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045698517500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b27173a-c29c-48d2-a56f-0089c468e9a0", "name": "Incremental task entry:default@PreBuild pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045705193500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7a43257-0b18-47a5-8d1e-5eb2cd1db2de", "name": "entry : default@PreBuild cost memory 0.28098297119140625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045705351800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "095ecc61-01bb-4606-b665-0c68d80a0775", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045698507100, "endTime": 29045705437200}, "additional": {"logType": "info", "children": [], "durationId": "15bc5e89-b095-43d4-a426-02cbb8cc8e9b"}}, {"head": {"id": "8f449d71-826d-4ecd-b7ca-9af174b2e823", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045709563600, "endTime": 29045711224500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "e91f5171-3cba-4e39-be21-978c96cba39b", "logId": "961a8f65-d32a-4efd-9f34-cfb5e00ab3b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e91f5171-3cba-4e39-be21-978c96cba39b", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045708475600}, "additional": {"logType": "detail", "children": [], "durationId": "8f449d71-826d-4ecd-b7ca-9af174b2e823"}}, {"head": {"id": "c4cf2489-2c89-41f8-9632-a2997bc5a80e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045708894300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5278516-d558-4a9a-a809-32929ab1863f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045708976400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a773940-3791-4b67-b775-1e77d0bd6577", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045709570500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7ba8bf8-3531-4732-9fdd-3f523bb04389", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045711067000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4559283-4128-4cbe-af9c-7150b2550d5e", "name": "entry : default@MergeProfile cost memory 0.11238861083984375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045711173600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "961a8f65-d32a-4efd-9f34-cfb5e00ab3b2", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045709563600, "endTime": 29045711224500}, "additional": {"logType": "info", "children": [], "durationId": "8f449d71-826d-4ecd-b7ca-9af174b2e823"}}, {"head": {"id": "7314789b-a03f-434c-ab11-d2f5ace55a3b", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045713871500, "endTime": 29045715829300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f79e5611-7241-472d-8931-bbe575f7eb46", "logId": "0c15cdf3-d774-428b-b19b-e5224881df1d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f79e5611-7241-472d-8931-bbe575f7eb46", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045712741700}, "additional": {"logType": "detail", "children": [], "durationId": "7314789b-a03f-434c-ab11-d2f5ace55a3b"}}, {"head": {"id": "a92c3cdd-8247-4f03-b819-1063ef40bb5f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045713192600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bc46f25-2fa7-417f-b644-8613ef428303", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045713278900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c0652b3-84bd-4da5-8d42-2c557929d508", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045713878900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b92f129b-629a-4936-a4bf-ddb2cde78ae1", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045714609400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3df57720-0438-4b49-aaee-3f3284a5338f", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045715683900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e882eb6-5410-41de-aabc-89ca81074aaa", "name": "entry : default@CreateBuildProfile cost memory 0.09790802001953125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045715760000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c15cdf3-d774-428b-b19b-e5224881df1d", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045713871500, "endTime": 29045715829300}, "additional": {"logType": "info", "children": [], "durationId": "7314789b-a03f-434c-ab11-d2f5ace55a3b"}}, {"head": {"id": "11d78e21-4eb0-4a43-8538-8c5fe0803a4f", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045718241900, "endTime": 29045718521100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "589c8a05-dbd9-47fc-bab7-689cf5e48446", "logId": "cb967120-5d73-408c-b7af-fbf179915645"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "589c8a05-dbd9-47fc-bab7-689cf5e48446", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045717011100}, "additional": {"logType": "detail", "children": [], "durationId": "11d78e21-4eb0-4a43-8538-8c5fe0803a4f"}}, {"head": {"id": "b75f977d-176a-47c2-b8fb-d3fcd867c2ff", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045717423600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcb86601-3e3d-488d-8667-54412ed487cd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045717492600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9eab402-90a3-4eea-b6d8-b088a078f749", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045718249400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15cd5538-4cdc-49af-a320-3c2a0369988d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045718339700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc36f476-e901-4c19-a8ad-1d750a2cfacc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045718370900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b7d1c82-8aba-40cd-8a06-40c75bcac1ba", "name": "entry : default@PreCheckSyscap cost memory 0.03691864013671875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045718418400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "871b678d-5308-49cc-868d-35345c067a6f", "name": "runTaskFromQueue task cost before running: 182 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045718493400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb967120-5d73-408c-b7af-fbf179915645", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045718241900, "endTime": 29045718521100, "totalTime": 231900}, "additional": {"logType": "info", "children": [], "durationId": "11d78e21-4eb0-4a43-8538-8c5fe0803a4f"}}, {"head": {"id": "987e6671-cefb-47f0-afd4-5b08a5673821", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045726330300, "endTime": 29045727299000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a0407822-12e9-4eea-ac49-c28a6a7f4e05", "logId": "741c2b98-f840-48e7-a758-5aee083a6c5c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0407822-12e9-4eea-ac49-c28a6a7f4e05", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045720667200}, "additional": {"logType": "detail", "children": [], "durationId": "987e6671-cefb-47f0-afd4-5b08a5673821"}}, {"head": {"id": "cb8ce373-f145-45b3-8731-350327564bf5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045721277700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66ab8281-71bf-42c4-b032-7503e5201dc7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045721367200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23e8ed07-bd87-4b73-9149-2546b2d3f8e3", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045726340700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f632b95-a70a-4858-83f1-ba596ba5e35b", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045726552900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80a8785e-1f98-44e0-be24-de2544793172", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045727103200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a63f3692-31e1-4f58-893e-ab982c84b77d", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0661163330078125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045727214000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "741c2b98-f840-48e7-a758-5aee083a6c5c", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045726330300, "endTime": 29045727299000}, "additional": {"logType": "info", "children": [], "durationId": "987e6671-cefb-47f0-afd4-5b08a5673821"}}, {"head": {"id": "f7ead0ce-0940-40e6-8253-a087200e14af", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045730715800, "endTime": 29045731791300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "7e2f7493-2a1f-4ee3-883b-2b652501da64", "logId": "c990066e-e86c-466d-848b-9a69e79b17c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e2f7493-2a1f-4ee3-883b-2b652501da64", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045729172500}, "additional": {"logType": "detail", "children": [], "durationId": "f7ead0ce-0940-40e6-8253-a087200e14af"}}, {"head": {"id": "f59e1b60-74e3-46c1-9a35-6307212a0b22", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045729650300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5de516a6-7b8e-481f-b7ab-8d8a75450098", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045729746400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1847b349-14af-468a-8c4d-a36de851a41a", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045730724200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15466099-b616-43a1-b7cd-467f68ab0c7b", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045731598100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cc68bfd-2cba-456a-9b4d-e56adfa1328e", "name": "entry : default@ProcessProfile cost memory 0.05686187744140625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045731716800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c990066e-e86c-466d-848b-9a69e79b17c5", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045730715800, "endTime": 29045731791300}, "additional": {"logType": "info", "children": [], "durationId": "f7ead0ce-0940-40e6-8253-a087200e14af"}}, {"head": {"id": "584ec36c-1200-44ae-998b-d1a93f22524d", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045735573900, "endTime": 29045740498400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0cb79be8-1616-485b-9cfc-04d672a6a5c3", "logId": "255db5ae-966f-46d6-94d3-55aee7b807fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0cb79be8-1616-485b-9cfc-04d672a6a5c3", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045733234600}, "additional": {"logType": "detail", "children": [], "durationId": "584ec36c-1200-44ae-998b-d1a93f22524d"}}, {"head": {"id": "b82e1358-b6ed-4ade-8f0b-14f8be56e85b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045733731500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "267ae983-6931-4d4c-91b2-a0a1da2e7782", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045733820900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee4e75ff-40f0-45bf-af01-777ca1355ac5", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045735585000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8851fe8-47f8-48cc-a747-27342b820c3f", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045740275500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d54215bf-cd41-4133-b7ab-1dccc57931fb", "name": "entry : default@ProcessRouterMap cost memory 0.19753265380859375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045740442900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "255db5ae-966f-46d6-94d3-55aee7b807fb", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045735573900, "endTime": 29045740498400}, "additional": {"logType": "info", "children": [], "durationId": "584ec36c-1200-44ae-998b-d1a93f22524d"}}, {"head": {"id": "e78bbf16-ab4e-4577-9bff-83f0f31a2581", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045745845100, "endTime": 29045748031200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "6c8cbb81-76c8-4f54-b297-de183d25018e", "logId": "f23d2cc4-a204-433c-b5fc-4e1fca40ee17"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c8cbb81-76c8-4f54-b297-de183d25018e", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045742842400}, "additional": {"logType": "detail", "children": [], "durationId": "e78bbf16-ab4e-4577-9bff-83f0f31a2581"}}, {"head": {"id": "e1444635-e21c-4121-a485-511df8728f00", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045743296700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43a91390-cb45-4644-94a0-14fd29081eca", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045743394500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9aa8fb9a-d0e8-49b9-ba03-077736d27645", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045744143800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88d7613b-2001-436d-bcd5-9ed0714930a7", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045746753700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7119fb9e-2acb-4293-95fb-8eb79923ce0c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045746894300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8458c8d1-c49b-4d06-9e27-bace795f3500", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045746931900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10019e43-09b3-40d8-98c2-01068d283939", "name": "entry : default@PreviewProcessResource cost memory 0.07018280029296875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045746987000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b651ea1-2bb3-4593-805e-076e8d3a882f", "name": "runTaskFromQueue task cost before running: 211 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045747951400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f23d2cc4-a204-433c-b5fc-4e1fca40ee17", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045745845100, "endTime": 29045748031200, "totalTime": 1208700}, "additional": {"logType": "info", "children": [], "durationId": "e78bbf16-ab4e-4577-9bff-83f0f31a2581"}}, {"head": {"id": "1ee3cdf5-2b41-4fc3-ad23-41e27ae3109e", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045753955600, "endTime": 29045771390100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "41b83add-ff8d-4639-8531-078038089ff6", "logId": "e143191a-b468-419c-bd9b-4e90671cd9e5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41b83add-ff8d-4639-8531-078038089ff6", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045750078100}, "additional": {"logType": "detail", "children": [], "durationId": "1ee3cdf5-2b41-4fc3-ad23-41e27ae3109e"}}, {"head": {"id": "3cd48c60-1938-4e74-b7da-6df4cea376bb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045750961100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0238df4-efda-4d1f-9633-05e937b0bd92", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045751044300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f7f1ceb-e2aa-4f9a-a4d6-b72316690fe3", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045753969000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c7e97d8-0c85-4630-8dd2-3208550aef6f", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045771155400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9e81c37-306b-41f7-8f65-e0c96bb61823", "name": "entry : default@GenerateLoaderJson cost memory 0.756561279296875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045771331200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e143191a-b468-419c-bd9b-4e90671cd9e5", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045753955600, "endTime": 29045771390100}, "additional": {"logType": "info", "children": [], "durationId": "1ee3cdf5-2b41-4fc3-ad23-41e27ae3109e"}}, {"head": {"id": "b84428fb-c955-4387-bdda-b0122f218228", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045780609400, "endTime": 29045902047400}, "additional": {"children": ["da17d879-22ee-4ead-a540-e80dda5fb7fc", "2927c984-7972-42f8-8984-c310645e6664", "1049abca-5e2c-4113-875d-1849bd068095"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The input file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources' has been changed."], "detailId": "4a6439eb-168c-4cc4-a617-6d5afbc5324c", "logId": "3e1aee95-0890-44e0-bc37-f0d7acdee71a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a6439eb-168c-4cc4-a617-6d5afbc5324c", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045777317000}, "additional": {"logType": "detail", "children": [], "durationId": "b84428fb-c955-4387-bdda-b0122f218228"}}, {"head": {"id": "19020fb1-ac53-419d-be8c-f45ddee6fe51", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045777778900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "742d488e-f4c5-4fd2-86aa-5115e2ad0a1b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045777914000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "481bc685-700b-4790-ac1b-9b0717b65fc6", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045778791700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98ef638d-4d99-4a14-b718-7f8c1c26a2fd", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045780634100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1b1527f-2702-4fd8-9919-e6945c37420a", "name": "entry:default@PreviewCompileResource is not up-to-date, since the input file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045789848100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4fb7c9a-42c5-4b00-911e-8440573644be", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045790002300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da17d879-22ee-4ead-a540-e80dda5fb7fc", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045791570400, "endTime": 29045812715500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b84428fb-c955-4387-bdda-b0122f218228", "logId": "1bf0c6b0-65a1-4cba-b660-eda9c784c2b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1bf0c6b0-65a1-4cba-b660-eda9c784c2b6", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045791570400, "endTime": 29045812715500}, "additional": {"logType": "info", "children": [], "durationId": "da17d879-22ee-4ead-a540-e80dda5fb7fc", "parent": "3e1aee95-0890-44e0-bc37-f0d7acdee71a"}}, {"head": {"id": "8ea94178-124e-4886-97d5-d70e8538bed5", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045813065400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2927c984-7972-42f8-8984-c310645e6664", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045814070100, "endTime": 29045853351900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b84428fb-c955-4387-bdda-b0122f218228", "logId": "6bafef8a-47f4-4c16-9777-88a06dfe4974"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "98a5067d-f275-4a73-8161-eab191554aec", "name": "current process  memoryUsage: {\n  rss: 100130816,\n  heapTotal: 122249216,\n  heapUsed: 114353048,\n  external: 3141625,\n  arrayBuffers: 135526\n} os memoryUsage :13.245079040527344", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045814950400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4523f761-38f0-444c-841b-9a6215972f2c", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045850635400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bafef8a-47f4-4c16-9777-88a06dfe4974", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045814070100, "endTime": 29045853351900}, "additional": {"logType": "info", "children": [], "durationId": "2927c984-7972-42f8-8984-c310645e6664", "parent": "3e1aee95-0890-44e0-bc37-f0d7acdee71a"}}, {"head": {"id": "46dbd7c1-7414-4963-ae0f-9217b6944741", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045853505800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1049abca-5e2c-4113-875d-1849bd068095", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045854511600, "endTime": 29045902051200}, "additional": {"children": [], "state": "failed", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b84428fb-c955-4387-bdda-b0122f218228"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd59934b-4317-4910-89d1-2d3aef3c13e3", "name": "current process  memoryUsage: {\n  rss: 100331520,\n  heapTotal: 122249216,\n  heapUsed: 114618856,\n  external: 3141751,\n  arrayBuffers: 135667\n} os memoryUsage :13.25235366821289", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045855453300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fe97795-4693-4301-a2f1-9b2c6122c9bf", "name": "ERROR: command = D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe -x D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources -o D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045901334600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e1aee95-0890-44e0-bc37-f0d7acdee71a", "name": "Failed :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045780609400, "endTime": 29045902047400}, "additional": {"logType": "error", "children": ["1bf0c6b0-65a1-4cba-b660-eda9c784c2b6", "6bafef8a-47f4-4c16-9777-88a06dfe4974"], "durationId": "b84428fb-c955-4387-bdda-b0122f218228"}}, {"head": {"id": "3ccebc7e-61bb-46e5-b418-f0a4cfb66c64", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045902203300}, "additional": {"logType": "debug", "children": [], "durationId": "b84428fb-c955-4387-bdda-b0122f218228"}}, {"head": {"id": "3983ff0c-7b89-4339-842a-421e47b36905", "name": "ERROR: stacktrace = Error: Tools execution failed.\r\nError: the name '钱包' can only contain [a-zA-Z0-9_].\r\nError: invalid idName '钱包'.\r\nat D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources\\base\\media\\钱包.png\r\nSolutions:\r\n> Modify the name '钱包' to match [a-zA-Z0-9_].\r\n\t Detail: Please check the message from tools.\n    at OhosLogger.errorMessageExit (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\log\\hvigor-log.js:1:3224)\n    at OhosLogger._printErrorAndExit (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor-ohos-plugin\\src\\utils\\log\\ohos-logger.js:1:1978)\n    at ProcessUtils.handleException (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor-ohos-plugin\\src\\utils\\process-utils.js:1:5395)\n    at ProcessUtils.execute (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor-ohos-plugin\\src\\utils\\process-utils.js:1:3613)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PreviewCompileResource.executeCommand (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor-ohos-plugin\\src\\tasks\\abstract\\abstract-compile-resource.js:1:3730)\n    at async PreviewCompileResource.compileLink (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor-ohos-plugin\\src\\tasks\\abstract\\abstract-compile-resource.js:1:5074)\n    at async PreviewCompileResource.previewCompileLink (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor-ohos-plugin\\src\\tasks\\abstract\\abstract-compile-resource.js:1:5585)\n    at async PreviewCompileResource.invokeRestool (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor-ohos-plugin\\src\\tasks\\preview-compile-resource.js:1:1518)\n    at async PreviewCompileResource.compilationProcess (D:\\app\\devecostudio\\DevEco Studio\\tools\\hvigor\\hvigor-ohos-plugin\\src\\tasks\\abstract\\abstract-compile-resource.js:1:3025)", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29046118940700}, "additional": {"logType": "debug", "children": [], "durationId": "b84428fb-c955-4387-bdda-b0122f218228"}}, {"head": {"id": "5ed63698-8c40-4c82-a9bf-5c6e055281f9", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29046128287700, "endTime": 29046128311500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb119d57-c964-4e47-8d65-daf8b7a7aec0", "logId": "807ba38f-a120-4ff9-8639-3ab11ebbd61f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "807ba38f-a120-4ff9-8639-3ab11ebbd61f", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29046128287700, "endTime": 29046128311500}, "additional": {"logType": "info", "children": [], "durationId": "5ed63698-8c40-4c82-a9bf-5c6e055281f9"}}, {"head": {"id": "29f0d5f9-c5bc-42ea-bba0-d8e0bb5758aa", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29045537159700, "endTime": 29046128411000}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 10}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "60fa8c8d-7fa8-4e6f-a64e-1233b11e0184", "name": "BUILD FAILED in 592 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29046128435200}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "d0c41977-b25e-426c-b832-1ba30781d7a8", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29046128580300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42c24964-7aa4-4c95-ad35-9e5eb7be2ce3", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29046128625900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6385aa4f-211a-4300-91e0-594520135a2d", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29046128655500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2a14954-39cd-42b2-8989-07327f0dc2bc", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29046128676000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "306c84f7-ea43-493b-8f7a-b058e746a99b", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29046128698200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a4d3906-995d-4f45-84ea-5683183eeb22", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29046128721100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a9f2b0e-0b8d-421d-9515-d96b4ca8e32f", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29046128741100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad166446-20f7-48e5-9b02-82250d87ba96", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources cache from map.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29046129435800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c66715a0-8e47-499a-a50a-a30fff4f2b85", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29046129571500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f966f04-df74-4607-8ae2-5b61d9fce630", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29046129866200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0c4053a-4684-4c52-b65c-a5e80464a1dd", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29046134817700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bf36e65-b4a6-42d9-9cff-e63f26b56e39", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:7 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29046135541600}, "additional": {"logType": "debug", "children": []}}], "workLog": []}