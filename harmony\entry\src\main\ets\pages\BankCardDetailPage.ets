import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { BankCardApi } from '../api/BankCardApi';
import { BankCard } from '../common/types/index';
import { tempDataManager } from '../common/storage/TempDataManager';
import { storageManager } from '../common/storage/StorageManager';

@Entry
@Component
struct BankCardDetailPage {
  @State cardDetail: BankCard | null = null;
  @State isLoading: boolean = true;
  @State cardId: number = 0;

  aboutToAppear() {
    // 获取传入的银行卡ID
    const params = router.getParams() as Record<string, number>;
    this.cardId = params?.cardId || 0;

    if (this.cardId > 0) {
      this.loadCardDetail();
    } else {
      promptAction.showToast({ message: '银行卡信息错误' });
      router.back();
    }
  }

  async loadCardDetail() {
    try {
      this.isLoading = true;
      this.cardDetail = await BankCardApi.getCardDetail(this.cardId);
    } catch (error) {
      console.error('获取银行卡详情失败:', error);
      promptAction.showToast({ message: '获取银行卡详情失败' });
      router.back();
    } finally {
      this.isLoading = false;
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button('返回')
          .fontSize(16)
          .fontColor('#1976D2')
          .backgroundColor(Color.Transparent)
          .onClick(() => {
            router.back();
          })

        Text('银行卡详情')
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('')
          .width(60) // 占位，保持标题居中
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor('#FFFFFF')

      if (this.isLoading) {
        // 加载状态
        Column() {
          LoadingProgress()
            .width(40)
            .height(40)
            .color('#1976D2')

          Text('加载中...')
            .fontSize(14)
            .fontColor('#999999')
            .margin({ top: 16 })
        }
        .width('100%')
        .height('100%')
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
        .backgroundColor('#F5F5F5')
      } else if (this.cardDetail) {
        // 银行卡详情内容
        Scroll() {
          Column() {
            // 银行卡卡片显示
            this.BankCardDisplay()

            // 银行卡信息列表
            this.CardInfoList()

            // 解绑按钮
            this.UnbindButton()
          }
          .padding(16)
        }
        .layoutWeight(1)
        .backgroundColor('#F5F5F5')
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  BankCardDisplay() {
    // 按照用户提供的格式设计银行卡
    Column() {
      // 银行卡卡片
      Stack({ alignContent: Alignment.TopStart }) {
        Column() {
          // 银行名称和卡类型
          Row() {
            Text(this.cardDetail?.bankName || '中国工商银行')
              .fontSize(18)
              .fontColor('#FFFFFF')
              .fontWeight(FontWeight.Bold)

            Blank()

            Text(this.getCardTypeText())
              .fontSize(14)
              .fontColor('#FFFFFF')
              .backgroundColor('rgba(255,255,255,0.2)')
              .padding({ left: 8, right: 8, top: 4, bottom: 4 })
              .borderRadius(12)
          }
          .width('100%')
          .margin({ bottom: 30 })

          // 卡号显示
          Text(`**** **** **** ${this.cardDetail?.cardNo?.slice(-4) || '8888'}`)
            .fontSize(22)
            .fontColor('#FFFFFF')
            .fontWeight(FontWeight.Medium)
            .letterSpacing(3)
            .margin({ bottom: 30 })

          // 持卡人姓名和绑定状态
          Row() {
            Text(this.cardDetail?.holderName || '罗曼勾')
              .fontSize(16)
              .fontColor('#FFFFFF')

            Blank()

            Text(this.cardDetail?.status === 1 ? '已绑定' : '未绑定')
              .fontSize(14)
              .fontColor('#FFFFFF')
              .backgroundColor('rgba(255,255,255,0.2)')
              .padding({ left: 8, right: 8, top: 4, bottom: 4 })
              .borderRadius(12)
          }
          .width('100%')
        }
        .width('100%')
        .padding(20)
      }
      .width('100%')
      .height(200)
      .backgroundColor('#1976D2')
      .borderRadius(16)
      .shadow({
        radius: 8,
        color: 'rgba(0,0,0,0.2)',
        offsetX: 0,
        offsetY: 6
      })

      // 银行卡装饰图案
      Image($r('app.media.ic_bank_card'))
        .width(100)
        .height(100)
        .fillColor('rgba(255,255,255,0.1)')
        .position({ x: '65%', y: '10%' })
    }
    .width('100%')
    .margin({ top: 16, bottom: 24 })
  }

  @Builder
  CardInfoList() {
    // 银行卡信息列表
    Column() {
      Text('银行卡信息')
        .fontSize(18)
        .fontWeight(FontWeight.Medium)
        .fontColor('#333333')
        .margin({ bottom: 16 })

      // 信息项列表
      Column() {
        this.InfoItem('银行名称', this.cardDetail?.bankName || '中国工商银行')
        this.InfoItem('卡片类型', this.getCardTypeText())
        this.InfoItem('持卡人姓名', this.cardDetail?.holderName || '罗曼勾')
        this.InfoItem('卡号', `${this.cardDetail?.cardNo?.slice(-4) || '8888'} ************ ${this.cardDetail?.cardNo?.slice(-4) || '8888'}`)
        this.InfoItem('绑定状态', this.cardDetail?.status === 1 ? '已绑定' : '未绑定')
        this.InfoItem('绑定时间', this.formatBindTime())
      }
      .backgroundColor('#FFFFFF')
      .borderRadius(12)
      .padding(16)
    }
    .width('100%')
    .alignItems(HorizontalAlign.Start)
    .margin({ bottom: 30 })
  }

  @Builder
  InfoItem(label: string, value: string) {
    Row() {
      Text(label)
        .fontSize(16)
        .fontColor('#666666')
        .width(100)

      Text(value)
        .fontSize(16)
        .fontColor('#333333')
        .layoutWeight(1)
        .textAlign(TextAlign.End)
    }
    .width('100%')
    .height(50)
    .justifyContent(FlexAlign.SpaceBetween)
    .alignItems(VerticalAlign.Center)
    .border({ width: { bottom: 1 }, color: '#F0F0F0' })
  }

  @Builder
  UnbindButton() {
    // 解绑银行卡按钮
    Column() {
      Button('解绑银行卡')
        .width('100%')
        .height(50)
        .fontSize(16)
        .fontColor('#FF5722')
        .backgroundColor('#FFFFFF')
        .border({ width: 1, color: '#FF5722' })
        .borderRadius(25)
        .onClick(() => {
          this.showUnbindDialog();
        })

      Text('解绑后将无法使用此银行卡进行充值和消费')
        .fontSize(12)
        .fontColor('#999999')
        .textAlign(TextAlign.Center)
        .margin({ top: 8 })
    }
    .width('100%')
    .alignItems(HorizontalAlign.Center)
  }

  private formatBindTime(): string {
    if (this.cardDetail?.createTime) {
      return this.cardDetail.createTime.slice(0, 16).replace('T', ' ');
    }
    return '2025/06/23 14:30';
  }

  private getCardTypeText(): string {
    if (this.cardDetail?.cardType === 'CREDIT') {
      return '信用卡';
    }
    return '储蓄卡';
  }

  private showUnbindDialog() {
    // 显示解绑确认对话框
    promptAction.showDialog({
      title: '确认解绑',
      message: '确定要解绑此银行卡吗？解绑后将无法使用此卡进行支付。',
      buttons: [
        { text: '取消', color: '#666666' },
        { text: '确认解绑', color: '#FF5722' }
      ]
    }).then((result) => {
      if (result.index === 1) {
        this.unbindCard();
      }
    });
  }

  private async unbindCard() {
    try {
      await BankCardApi.unbindCard(this.cardId);
      promptAction.showToast({ message: '解绑成功' });
      router.back();
    } catch (error) {
      console.error('解绑银行卡失败:', error);
      promptAction.showToast({ message: '解绑失败，请重试' });
    }
  }

  /**
   * 使用银行卡进行支付
   */
  private useCardForPayment() {
    if (!this.cardDetail) return;

    console.log('使用银行卡支付:', this.cardDetail.bankName, this.cardDetail.cardNo);

    // 跳转到支付页面，传递银行卡信息
    router.pushUrl({
      url: 'pages/PaymentPage',
      params: {
        selectedCard: {
          cardId: this.cardDetail.cardId,
          bankName: this.cardDetail.bankName,
          cardNo: this.cardDetail.cardNo,
          cardType: this.cardDetail.cardType,
          holderName: this.cardDetail.holderName,
          maskedCardNo: this.maskCardNumber(this.cardDetail.cardNo)
        },
        paymentMethod: 'bankCard'
      }
    }).catch((error: Error) => {
      console.error('跳转支付页面失败:', error);
      promptAction.showToast({ message: '打开支付页面失败' });
    });
  }

  /**
   * 使用银行卡进行转账
   */
  private useCardForTransfer() {
    if (!this.cardDetail) return;

    console.log('使用银行卡转账:', this.cardDetail.bankName, this.cardDetail.cardNo);

    // 跳转到钱包操作页面（转账功能），传递银行卡信息
    router.pushUrl({
      url: 'pages/WalletOperationPage',
      params: {
        operationType: 'transfer',
        selectedCard: {
          cardId: this.cardDetail.cardId,
          bankName: this.cardDetail.bankName,
          cardNo: this.cardDetail.cardNo,
          cardType: this.cardDetail.cardType,
          holderName: this.cardDetail.holderName,
          maskedCardNo: this.maskCardNumber(this.cardDetail.cardNo)
        },
        transferMethod: 'bankCard'
      }
    }).catch((error: Error) => {
      console.error('跳转钱包操作页面失败:', error);
      promptAction.showToast({ message: '打开钱包操作页面失败' });
    });
  }

  /**
   * 格式化银行卡号显示（带空格分隔）
   */
  private formatCardNumberWithSpaces(): string {
    if (!this.cardDetail?.cardNo) return '';

    // 脱敏处理
    const maskedCardNo = this.maskCardNumber(this.cardDetail.cardNo);

    // 添加空格分隔，每4位一组
    return maskedCardNo.replace(/(.{4})/g, '$1 ').trim();
  }

  /**
   * 获取银行卡渐变色
   */
  private getBankCardGradient(bankName: string): Array<[string, number]> {
    const gradients: Record<string, Array<[string, number]>> = {
      '中国工商银行': [['#C41E3A', 0], ['#8B0000', 1]],
      '中国建设银行': [['#003DA5', 0], ['#001F5C', 1]],
      '中国农业银行': [['#00A651', 0], ['#006B35', 1]],
      '中国银行': [['#B8860B', 0], ['#8B6914', 1]],
      '招商银行': [['#DC143C', 0], ['#8B0000', 1]],
      '交通银行': [['#0066CC', 0], ['#003D7A', 1]],
      '中信银行': [['#FF6B35', 0], ['#CC4125', 1]],
      '光大银行': [['#9932CC', 0], ['#6A1B9A', 1]],
      '华夏银行': [['#FF1744', 0], ['#C62828', 1]],
      '民生银行': [['#00BCD4', 0], ['#0097A7', 1]],
      '广发银行': [['#FF5722', 0], ['#D84315', 1]],
      '平安银行': [['#FF9800', 0], ['#F57C00', 1]]
    };

    return gradients[bankName] || [['#1976D2', 0], ['#0D47A1', 1]];
  }

  private maskCardNumber(cardNo: string): string {
    if (!cardNo || cardNo.length < 8) {
      return cardNo;
    }

    // 显示前4位和后4位
    const start = cardNo.substring(0, 4);
    const end = cardNo.substring(cardNo.length - 4);
    const middle = '*'.repeat(cardNo.length - 8);

    return `${start} ${middle} ${end}`;
  }

  private formatDateTime(dateTime: string): string {
    if (!dateTime) return '';

    const date = new Date(dateTime);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  }
}