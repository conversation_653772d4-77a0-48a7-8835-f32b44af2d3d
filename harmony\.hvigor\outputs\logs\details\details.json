{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug"}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"PreCheckSyscap": 276700, "PreviewProcessResource": 3890400, "PreviewCompileResource": 207783100, "PreviewHookCompileResource": 233200, "CopyPreviewProfile": 6804300, "ReplacePreviewerPage": 275300, "buildPreviewerResource": 208100, "PreviewUpdateAssets": 4258400}}, "TOTAL_TIME": 8575007900, "BUILD_ID": "202506251619280360", "ERROR_MESSAGE": {"CODE": "00000", "TIMESTAMP": "1750839576598"}}}