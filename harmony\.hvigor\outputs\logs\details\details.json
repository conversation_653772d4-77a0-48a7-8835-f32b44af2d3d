{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": true}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"PreCheckSyscap": 631100, "PreviewProcessResource": 6890800, "PreviewCompileResource": 313151000, "PreviewHookCompileResource": 766300, "CopyPreviewProfile": 19748600, "ReplacePreviewerPage": 999800, "buildPreviewerResource": 890700, "PreviewUpdateAssets": 11625600, "PreviewArkTS": 10548165400, "PreviewBuild": 195500}}, "TOTAL_TIME": 11531779500, "BUILD_ID": "202506251755150300"}}