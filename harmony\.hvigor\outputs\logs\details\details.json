{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug"}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"init": 633700, "PreCheckSyscap": 581000, "PreviewProcessResource": 5213000, "PreviewHookCompileResource": 323000, "ReplacePreviewerPage": 505800, "buildPreviewerResource": 325300}, "APP": {"init": 306100}}, "BUILD_ID": "202506251605040510", "ERROR_MESSAGE": {"CODE": "00000", "TIMESTAMP": "1750838712936"}, "TOTAL_TIME": 8903009900}}