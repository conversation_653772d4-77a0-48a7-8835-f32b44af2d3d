{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": true}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"PreCheckSyscap": 628200, "PreviewProcessResource": 3109500, "PreviewCompileResource": 211122400, "PreviewHookCompileResource": 208500, "CopyPreviewProfile": 6239400, "ReplacePreviewerPage": 396800, "buildPreviewerResource": 217800, "PreviewUpdateAssets": 4218300, "PreviewArkTS": 8627845900, "PreviewBuild": 892600}}, "TOTAL_TIME": 9115366600, "BUILD_ID": "202506251719394030"}}