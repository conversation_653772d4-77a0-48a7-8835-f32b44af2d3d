{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": true}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"init": 313500, "PreBuild": 165481800, "MergeProfile": 7202500, "CreateBuildProfile": 3531500, "PreCheckSyscap": 330100, "ProcessProfile": 93529500, "ProcessRouterMap": 12980900, "PreviewProcessResource": 3117500, "GenerateLoaderJson": 29765500, "PreviewCompileResource": 197615000, "PreviewHookCompileResource": 447200, "CopyPreviewProfile": 10730800, "ReplacePreviewerPage": 560900, "buildPreviewerResource": 342400, "PreviewUpdateAssets": 5690500}, "APP": {"init": 455200}}, "TOTAL_TIME": 9400752300, "BUILD_ID": "202506251625386800", "ERROR_MESSAGE": {"CODE": "00000", "TIMESTAMP": "1750839948070"}}}