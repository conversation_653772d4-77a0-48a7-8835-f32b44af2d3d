{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug"}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"PreCheckSyscap": 1372800, "PreviewProcessResource": 14380400, "PreviewCompileResource": 319016800, "PreviewHookCompileResource": 829000, "CopyPreviewProfile": 23456300, "ReplacePreviewerPage": 1087300, "buildPreviewerResource": 917000, "PreviewUpdateAssets": 14558800}}, "TOTAL_TIME": 11994266900, "BUILD_ID": "202506251611291080", "ERROR_MESSAGE": {"CODE": "00000", "TIMESTAMP": "1750839101092"}}}