{"version": "2.0", "ppid": 21780, "events": [{"head": {"id": "caf3eb64-b8d1-416c-a63f-a64a903ae66c", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31769361221100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43910370-4508-4df1-94ff-b9e6d056ef82", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31879653560600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42199cfc-1528-477e-aeb6-61eb8a3ddce7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31879656957500, "endTime": 31879657020200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5f3e2a3b-38a0-4ea3-bfac-0136199f364d", "logId": "f6fc1b4d-29ce-4b09-9d21-d571c6c5b35b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f6fc1b4d-29ce-4b09-9d21-d571c6c5b35b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31879656957500, "endTime": 31879657020200}, "additional": {"logType": "info", "children": [], "durationId": "42199cfc-1528-477e-aeb6-61eb8a3ddce7"}}, {"head": {"id": "afb86392-84b6-408d-903c-aba1c7eaedb8", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31881456384500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74039a16-2fe5-46e4-b531-7403905cf775", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31881457865500, "endTime": 31881457885600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5f3e2a3b-38a0-4ea3-bfac-0136199f364d", "logId": "e8483186-40bb-4774-9b1a-49dbc4559a11"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e8483186-40bb-4774-9b1a-49dbc4559a11", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31881457865500, "endTime": 31881457885600}, "additional": {"logType": "info", "children": [], "durationId": "74039a16-2fe5-46e4-b531-7403905cf775"}}, {"head": {"id": "0196d5f0-ceb6-42bf-9fee-0225739ae577", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31917070031700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3ab2de6-779f-47dc-aa85-b36d8d8a047c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31917072937500, "endTime": 31917072985000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5f3e2a3b-38a0-4ea3-bfac-0136199f364d", "logId": "85e0666b-62e7-4553-bf36-f3200ccde3c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85e0666b-62e7-4553-bf36-f3200ccde3c0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31917072937500, "endTime": 31917072985000}, "additional": {"logType": "info", "children": [], "durationId": "f3ab2de6-779f-47dc-aa85-b36d8d8a047c"}}, {"head": {"id": "647b6c87-f5e9-4a5a-a551-ca6413f2fb38", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31918451720100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "795a6bea-faa6-4521-a22d-b00242c5f764", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31918453743200, "endTime": 31918453761700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5f3e2a3b-38a0-4ea3-bfac-0136199f364d", "logId": "f4e01fd7-13bd-408a-b3da-b068d842ab64"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4e01fd7-13bd-408a-b3da-b068d842ab64", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31918453743200, "endTime": 31918453761700}, "additional": {"logType": "info", "children": [], "durationId": "795a6bea-faa6-4521-a22d-b00242c5f764"}}, {"head": {"id": "c66dd502-25e8-4131-ad50-2e788c74638d", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31980356054000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c661a326-fb75-4f6b-bbaa-52e4a05f9bad", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31980356270900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6343fefd-4644-4c06-941d-26ce466380bb", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981019958500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fca46aa-7375-438b-a881-7874ee7a4f74", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981026443700, "endTime": 31981162698700}, "additional": {"children": ["c144a1db-fdf2-48e1-ab7d-85dfbba9c713", "62be8632-cbd9-4b04-9885-fac5e42bf068", "c8cab5d9-6944-4307-bc8d-e192dbf767b4", "8a133d11-7b30-4edf-88a3-d652e668bfd8", "57930cbc-5f90-4c90-8efb-b96befc4b160", "2450a9ed-eec4-40fb-8772-9d1f420dce9a", "71bd4e46-1fac-4f7e-bc3c-f0dce73c2e06"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "89f31596-76b4-45bb-89cb-ff6e66ac9b67"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c144a1db-fdf2-48e1-ab7d-85dfbba9c713", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981026445200, "endTime": 31981036681500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1fca46aa-7375-438b-a881-7874ee7a4f74", "logId": "bc35d08f-2913-484a-806e-78fa01925093"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62be8632-cbd9-4b04-9885-fac5e42bf068", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981036700300, "endTime": 31981161650000}, "additional": {"children": ["3bfe43d0-a242-4f24-8338-bf5c11081985", "5ba2fdaa-64a4-46fd-ad00-8560e77b17d7", "86c27e7d-0c50-4852-a0f4-2dd61a90f0a2", "0a29bb22-ed7d-4575-9c1b-15c218624d3f", "0c8f8756-47b0-4210-9d59-7e194747bdd5", "21a4b8fa-edaf-4cd9-905f-3ff244d83553", "b304c826-a2cf-445a-87e3-51131ce35988", "226ce3d2-617b-4a2d-aa56-cd4cc1cf2fb5", "865ead7c-4119-428f-ab3d-12c93360aeeb"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1fca46aa-7375-438b-a881-7874ee7a4f74", "logId": "c0f60841-23f0-4b40-aff0-a57644fdcc73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8cab5d9-6944-4307-bc8d-e192dbf767b4", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981161670800, "endTime": 31981162692000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1fca46aa-7375-438b-a881-7874ee7a4f74", "logId": "c28f92a7-9c01-4020-a1ab-a5c62af49fe4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a133d11-7b30-4edf-88a3-d652e668bfd8", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981162694500, "endTime": 31981162696400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1fca46aa-7375-438b-a881-7874ee7a4f74", "logId": "37024345-980f-4174-9d35-9fa6e7a7ec01"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "57930cbc-5f90-4c90-8efb-b96befc4b160", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981029005300, "endTime": 31981029042500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1fca46aa-7375-438b-a881-7874ee7a4f74", "logId": "777dd16f-bd1b-49dd-b744-4beafbbc39db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "777dd16f-bd1b-49dd-b744-4beafbbc39db", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981029005300, "endTime": 31981029042500}, "additional": {"logType": "info", "children": [], "durationId": "57930cbc-5f90-4c90-8efb-b96befc4b160", "parent": "89f31596-76b4-45bb-89cb-ff6e66ac9b67"}}, {"head": {"id": "2450a9ed-eec4-40fb-8772-9d1f420dce9a", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981033454400, "endTime": 31981033474100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1fca46aa-7375-438b-a881-7874ee7a4f74", "logId": "11f596b4-cbaa-49ff-8488-4f9cc8c2424a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11f596b4-cbaa-49ff-8488-4f9cc8c2424a", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981033454400, "endTime": 31981033474100}, "additional": {"logType": "info", "children": [], "durationId": "2450a9ed-eec4-40fb-8772-9d1f420dce9a", "parent": "89f31596-76b4-45bb-89cb-ff6e66ac9b67"}}, {"head": {"id": "a29015b5-5d50-4e23-bd97-409773ccc788", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981033526400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0802c790-61d0-484c-b58a-2cead15183e3", "name": "Cache service initialization finished in 3 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981036571800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc35d08f-2913-484a-806e-78fa01925093", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981026445200, "endTime": 31981036681500}, "additional": {"logType": "info", "children": [], "durationId": "c144a1db-fdf2-48e1-ab7d-85dfbba9c713", "parent": "89f31596-76b4-45bb-89cb-ff6e66ac9b67"}}, {"head": {"id": "3bfe43d0-a242-4f24-8338-bf5c11081985", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981040874500, "endTime": 31981040883200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "62be8632-cbd9-4b04-9885-fac5e42bf068", "logId": "77aa395c-e07c-4f9f-8254-90a568079a0c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ba2fdaa-64a4-46fd-ad00-8560e77b17d7", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981040897100, "endTime": 31981045174300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "62be8632-cbd9-4b04-9885-fac5e42bf068", "logId": "ba420f63-a1ef-4890-9d1b-aad3fb4cc778"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "86c27e7d-0c50-4852-a0f4-2dd61a90f0a2", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981045190000, "endTime": 31981108736400}, "additional": {"children": ["430ed26f-db6e-44c4-a325-8ed45bba3b22", "39e7629d-f005-4a69-938d-3a727dbc4525", "ec64ea88-d18a-483a-bdbf-d71894fd40b5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "62be8632-cbd9-4b04-9885-fac5e42bf068", "logId": "5758f1cc-b8cf-4be6-9fd8-3c8720c005e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a29bb22-ed7d-4575-9c1b-15c218624d3f", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981108747200, "endTime": 31981129138900}, "additional": {"children": ["22559ce1-4084-48da-8d70-99dbf730ee32"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "62be8632-cbd9-4b04-9885-fac5e42bf068", "logId": "ef685387-0c4f-4a54-bde2-e890f306574b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c8f8756-47b0-4210-9d59-7e194747bdd5", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981129144800, "endTime": 31981141639500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "62be8632-cbd9-4b04-9885-fac5e42bf068", "logId": "d639c012-37b4-406a-adb2-236546a1603c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21a4b8fa-edaf-4cd9-905f-3ff244d83553", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981142576900, "endTime": 31981150821700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "62be8632-cbd9-4b04-9885-fac5e42bf068", "logId": "1c643e67-472e-49e4-9a58-b90370b4bc5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b304c826-a2cf-445a-87e3-51131ce35988", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981150848300, "endTime": 31981161523000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "62be8632-cbd9-4b04-9885-fac5e42bf068", "logId": "d51767b7-0a0c-4f96-9439-ea1308c1f68f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "226ce3d2-617b-4a2d-aa56-cd4cc1cf2fb5", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981161544500, "endTime": 31981161640000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "62be8632-cbd9-4b04-9885-fac5e42bf068", "logId": "17510d61-0bba-4c04-b765-a280e60f6c6b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "77aa395c-e07c-4f9f-8254-90a568079a0c", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981040874500, "endTime": 31981040883200}, "additional": {"logType": "info", "children": [], "durationId": "3bfe43d0-a242-4f24-8338-bf5c11081985", "parent": "c0f60841-23f0-4b40-aff0-a57644fdcc73"}}, {"head": {"id": "ba420f63-a1ef-4890-9d1b-aad3fb4cc778", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981040897100, "endTime": 31981045174300}, "additional": {"logType": "info", "children": [], "durationId": "5ba2fdaa-64a4-46fd-ad00-8560e77b17d7", "parent": "c0f60841-23f0-4b40-aff0-a57644fdcc73"}}, {"head": {"id": "430ed26f-db6e-44c4-a325-8ed45bba3b22", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981045698300, "endTime": 31981045716400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86c27e7d-0c50-4852-a0f4-2dd61a90f0a2", "logId": "29adefd5-ef2b-4368-9e13-8a6c4e162c13"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29adefd5-ef2b-4368-9e13-8a6c4e162c13", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981045698300, "endTime": 31981045716400}, "additional": {"logType": "info", "children": [], "durationId": "430ed26f-db6e-44c4-a325-8ed45bba3b22", "parent": "5758f1cc-b8cf-4be6-9fd8-3c8720c005e4"}}, {"head": {"id": "39e7629d-f005-4a69-938d-3a727dbc4525", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981047254300, "endTime": 31981107765800}, "additional": {"children": ["eebdcb8b-6cb8-4aa5-9f03-b76c6734a7d9", "c09d0bf6-cf64-400a-bc0f-3e15b6c34350"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86c27e7d-0c50-4852-a0f4-2dd61a90f0a2", "logId": "34e412a2-db56-42a8-9f8f-863aea936a8a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eebdcb8b-6cb8-4aa5-9f03-b76c6734a7d9", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981047255400, "endTime": 31981050747400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "39e7629d-f005-4a69-938d-3a727dbc4525", "logId": "76ba7c38-3c80-4808-aaf3-3c29e5db8d08"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c09d0bf6-cf64-400a-bc0f-3e15b6c34350", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981050766700, "endTime": 31981107745800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "39e7629d-f005-4a69-938d-3a727dbc4525", "logId": "ebb18ce7-7079-41fb-a754-13971fe51d36"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7eb25117-65a7-4742-a40c-f76006a3e1e0", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981047259100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc3661a8-efb2-48a9-b436-cf43729d553c", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981050626400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76ba7c38-3c80-4808-aaf3-3c29e5db8d08", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981047255400, "endTime": 31981050747400}, "additional": {"logType": "info", "children": [], "durationId": "eebdcb8b-6cb8-4aa5-9f03-b76c6734a7d9", "parent": "34e412a2-db56-42a8-9f8f-863aea936a8a"}}, {"head": {"id": "1d6e5533-ffc3-4034-84c1-7ad52560def9", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981050775500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81e049a1-6c55-43a6-a523-1895bacd941d", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981056740200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9e68e8c-19d6-4b83-bb90-b25a17c2bb40", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981056838600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7eafb6dd-6950-4a63-8498-8a00e4f0923c", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981057139400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5f41938-891c-4b15-91b6-1db624193874", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981057288500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b54f301e-c92d-46bc-9dbf-065965499544", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981059554700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "523a59e6-5484-43ef-ba07-fe11ba07d589", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981062700400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff72a3ee-bb3c-4236-8e83-ecc47e063389", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981070022900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ff515dd-7e33-46ab-a446-458831665090", "name": "Sdk init in 26 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981089348300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc25fdbf-e05b-44b4-85b2-d7543cea804b", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981089467700}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 58}, "markType": "other"}}, {"head": {"id": "fb4cb66b-d437-4213-ab36-e09814e9ff0b", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981089481200}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 58}, "markType": "other"}}, {"head": {"id": "1a6a35d5-d676-45b2-a9d8-993368560e2f", "name": "Project task initialization takes 17 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981107234800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e665ea9-b885-4c03-8ccf-75ffe0950709", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981107451700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "300e4000-9731-43e0-a047-b1917fc65a88", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981107492300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37107b5c-d173-4520-a07c-c5a62ffa52ee", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981107660300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebb18ce7-7079-41fb-a754-13971fe51d36", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981050766700, "endTime": 31981107745800}, "additional": {"logType": "info", "children": [], "durationId": "c09d0bf6-cf64-400a-bc0f-3e15b6c34350", "parent": "34e412a2-db56-42a8-9f8f-863aea936a8a"}}, {"head": {"id": "34e412a2-db56-42a8-9f8f-863aea936a8a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981047254300, "endTime": 31981107765800}, "additional": {"logType": "info", "children": ["76ba7c38-3c80-4808-aaf3-3c29e5db8d08", "ebb18ce7-7079-41fb-a754-13971fe51d36"], "durationId": "39e7629d-f005-4a69-938d-3a727dbc4525", "parent": "5758f1cc-b8cf-4be6-9fd8-3c8720c005e4"}}, {"head": {"id": "ec64ea88-d18a-483a-bdbf-d71894fd40b5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981108710600, "endTime": 31981108724600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86c27e7d-0c50-4852-a0f4-2dd61a90f0a2", "logId": "ba5775ce-0d89-4ca6-a665-fd8870f53ce6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ba5775ce-0d89-4ca6-a665-fd8870f53ce6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981108710600, "endTime": 31981108724600}, "additional": {"logType": "info", "children": [], "durationId": "ec64ea88-d18a-483a-bdbf-d71894fd40b5", "parent": "5758f1cc-b8cf-4be6-9fd8-3c8720c005e4"}}, {"head": {"id": "5758f1cc-b8cf-4be6-9fd8-3c8720c005e4", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981045190000, "endTime": 31981108736400}, "additional": {"logType": "info", "children": ["29adefd5-ef2b-4368-9e13-8a6c4e162c13", "34e412a2-db56-42a8-9f8f-863aea936a8a", "ba5775ce-0d89-4ca6-a665-fd8870f53ce6"], "durationId": "86c27e7d-0c50-4852-a0f4-2dd61a90f0a2", "parent": "c0f60841-23f0-4b40-aff0-a57644fdcc73"}}, {"head": {"id": "22559ce1-4084-48da-8d70-99dbf730ee32", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981109315800, "endTime": 31981129130700}, "additional": {"children": ["d6852259-c2ef-4704-9952-7c9aa919d66a", "4c33d7c9-d253-484f-a5de-8c8e8b498aeb", "f635a2cc-d8b0-4c4d-a259-de82e5322570"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0a29bb22-ed7d-4575-9c1b-15c218624d3f", "logId": "f73fc141-ed8c-4dc7-9722-61b322b7a25d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6852259-c2ef-4704-9952-7c9aa919d66a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981111820000, "endTime": 31981111836600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "22559ce1-4084-48da-8d70-99dbf730ee32", "logId": "a8fb2e29-14f6-450f-a985-34cc3cf1d6e8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8fb2e29-14f6-450f-a985-34cc3cf1d6e8", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981111820000, "endTime": 31981111836600}, "additional": {"logType": "info", "children": [], "durationId": "d6852259-c2ef-4704-9952-7c9aa919d66a", "parent": "f73fc141-ed8c-4dc7-9722-61b322b7a25d"}}, {"head": {"id": "4c33d7c9-d253-484f-a5de-8c8e8b498aeb", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981113712600, "endTime": 31981127962600}, "additional": {"children": ["aa08531d-90af-4121-bfd5-36c0d12b3404", "2e708857-7f02-467a-9216-1ababf7a8362"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "22559ce1-4084-48da-8d70-99dbf730ee32", "logId": "5eace700-9cf7-40a8-a8fa-ac4cd6672807"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa08531d-90af-4121-bfd5-36c0d12b3404", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981113713900, "endTime": 31981116617300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4c33d7c9-d253-484f-a5de-8c8e8b498aeb", "logId": "e1936bdb-e837-4f13-8b93-125d08a32b9b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e708857-7f02-467a-9216-1ababf7a8362", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981116636200, "endTime": 31981127947900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4c33d7c9-d253-484f-a5de-8c8e8b498aeb", "logId": "f70c506c-17e7-4eee-b256-a09d0350fe9d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e41a1bb-e6a1-4209-9a1a-c24ef5481244", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981113717200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7afacc15-f94c-4e12-974f-5fb67ef9ba07", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981116494000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1936bdb-e837-4f13-8b93-125d08a32b9b", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981113713900, "endTime": 31981116617300}, "additional": {"logType": "info", "children": [], "durationId": "aa08531d-90af-4121-bfd5-36c0d12b3404", "parent": "5eace700-9cf7-40a8-a8fa-ac4cd6672807"}}, {"head": {"id": "2b6d24d8-48d4-43b6-a65e-19705388add5", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981116642500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0499314d-76d2-41c6-8f06-b0df1c3e3a49", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981124885600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f14a3ac-0692-4278-ba74-71dfb8f26398", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981125004900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7d8b0ba-0c5d-4cd7-b489-f8dbf2146c3e", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981125130500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cb565a0-00da-4439-ac3e-e648ee50802c", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981125195600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1fd8c18-7fd9-44d0-8ce0-cff0d7729c64", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981125237700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d26a2b33-ec64-4a23-99aa-9f4249ac0882", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981125266900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19c0178a-8efe-4513-a54f-33fadf74b44b", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981125296300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "251bf52c-d200-47c3-97d0-f9bac8fdc9b2", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981127742300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95688f82-439a-46de-8e83-021b579a960f", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981127863800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4422bcc0-d8d0-4ee7-8185-cd1b8fa073e6", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981127895500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "243f627d-15be-4ab6-9f33-e6ad11d22088", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981127918000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f70c506c-17e7-4eee-b256-a09d0350fe9d", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981116636200, "endTime": 31981127947900}, "additional": {"logType": "info", "children": [], "durationId": "2e708857-7f02-467a-9216-1ababf7a8362", "parent": "5eace700-9cf7-40a8-a8fa-ac4cd6672807"}}, {"head": {"id": "5eace700-9cf7-40a8-a8fa-ac4cd6672807", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981113712600, "endTime": 31981127962600}, "additional": {"logType": "info", "children": ["e1936bdb-e837-4f13-8b93-125d08a32b9b", "f70c506c-17e7-4eee-b256-a09d0350fe9d"], "durationId": "4c33d7c9-d253-484f-a5de-8c8e8b498aeb", "parent": "f73fc141-ed8c-4dc7-9722-61b322b7a25d"}}, {"head": {"id": "f635a2cc-d8b0-4c4d-a259-de82e5322570", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981129105400, "endTime": 31981129118000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "22559ce1-4084-48da-8d70-99dbf730ee32", "logId": "906a804a-8907-4e99-9cf8-b276154849bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "906a804a-8907-4e99-9cf8-b276154849bf", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981129105400, "endTime": 31981129118000}, "additional": {"logType": "info", "children": [], "durationId": "f635a2cc-d8b0-4c4d-a259-de82e5322570", "parent": "f73fc141-ed8c-4dc7-9722-61b322b7a25d"}}, {"head": {"id": "f73fc141-ed8c-4dc7-9722-61b322b7a25d", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981109315800, "endTime": 31981129130700}, "additional": {"logType": "info", "children": ["a8fb2e29-14f6-450f-a985-34cc3cf1d6e8", "5eace700-9cf7-40a8-a8fa-ac4cd6672807", "906a804a-8907-4e99-9cf8-b276154849bf"], "durationId": "22559ce1-4084-48da-8d70-99dbf730ee32", "parent": "ef685387-0c4f-4a54-bde2-e890f306574b"}}, {"head": {"id": "ef685387-0c4f-4a54-bde2-e890f306574b", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981108747200, "endTime": 31981129138900}, "additional": {"logType": "info", "children": ["f73fc141-ed8c-4dc7-9722-61b322b7a25d"], "durationId": "0a29bb22-ed7d-4575-9c1b-15c218624d3f", "parent": "c0f60841-23f0-4b40-aff0-a57644fdcc73"}}, {"head": {"id": "1806fe19-c337-4ed3-ab54-6fd843948678", "name": "watch files: [\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\hvigorfile.ts',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981141334600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6317fc0-ffc6-49c4-b7e9-8bf3764d0eb0", "name": "hvigorfile, resolve hvigorfile dependencies in 13 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981141587600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d639c012-37b4-406a-adb2-236546a1603c", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981129144800, "endTime": 31981141639500}, "additional": {"logType": "info", "children": [], "durationId": "0c8f8756-47b0-4210-9d59-7e194747bdd5", "parent": "c0f60841-23f0-4b40-aff0-a57644fdcc73"}}, {"head": {"id": "865ead7c-4119-428f-ab3d-12c93360aeeb", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981142405600, "endTime": 31981142564500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "62be8632-cbd9-4b04-9885-fac5e42bf068", "logId": "a52cf1ac-877a-43fc-904c-28ee099d1de3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec50ec83-b271-41da-bd98-b168c45ed947", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981142426700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a52cf1ac-877a-43fc-904c-28ee099d1de3", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981142405600, "endTime": 31981142564500}, "additional": {"logType": "info", "children": [], "durationId": "865ead7c-4119-428f-ab3d-12c93360aeeb", "parent": "c0f60841-23f0-4b40-aff0-a57644fdcc73"}}, {"head": {"id": "01655250-ed07-46d1-b876-6d84320c521a", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981143839600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b97a962e-1cdb-41dc-bf4c-7b7fdc5477a5", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981149920400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c643e67-472e-49e4-9a58-b90370b4bc5e", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981142576900, "endTime": 31981150821700}, "additional": {"logType": "info", "children": [], "durationId": "21a4b8fa-edaf-4cd9-905f-3ff244d83553", "parent": "c0f60841-23f0-4b40-aff0-a57644fdcc73"}}, {"head": {"id": "36ff5b45-f8d1-4571-8d9e-011630766dac", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981150863300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9d2fc97-bf78-4da9-b5d1-1d0bea1b9459", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981156941000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23fb846d-41a9-4b8e-bb70-1ca19c26aeaf", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981157101000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14ec5e29-6599-4365-a29c-c74a48391cb1", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981157308300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "198f1f47-6ba9-427b-a2d6-6d5c9b9e555a", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981159197600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cdb11bc-48de-4c1c-b0a0-755bf8b79fa2", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981159296100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d51767b7-0a0c-4f96-9439-ea1308c1f68f", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981150848300, "endTime": 31981161523000}, "additional": {"logType": "info", "children": [], "durationId": "b304c826-a2cf-445a-87e3-51131ce35988", "parent": "c0f60841-23f0-4b40-aff0-a57644fdcc73"}}, {"head": {"id": "9ec8ae25-aa15-4fa9-99b8-e0879e27137c", "name": "Configuration phase cost:121 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981161565200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17510d61-0bba-4c04-b765-a280e60f6c6b", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981161544500, "endTime": 31981161640000}, "additional": {"logType": "info", "children": [], "durationId": "226ce3d2-617b-4a2d-aa56-cd4cc1cf2fb5", "parent": "c0f60841-23f0-4b40-aff0-a57644fdcc73"}}, {"head": {"id": "c0f60841-23f0-4b40-aff0-a57644fdcc73", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981036700300, "endTime": 31981161650000}, "additional": {"logType": "info", "children": ["77aa395c-e07c-4f9f-8254-90a568079a0c", "ba420f63-a1ef-4890-9d1b-aad3fb4cc778", "5758f1cc-b8cf-4be6-9fd8-3c8720c005e4", "ef685387-0c4f-4a54-bde2-e890f306574b", "d639c012-37b4-406a-adb2-236546a1603c", "1c643e67-472e-49e4-9a58-b90370b4bc5e", "d51767b7-0a0c-4f96-9439-ea1308c1f68f", "17510d61-0bba-4c04-b765-a280e60f6c6b", "a52cf1ac-877a-43fc-904c-28ee099d1de3"], "durationId": "62be8632-cbd9-4b04-9885-fac5e42bf068", "parent": "89f31596-76b4-45bb-89cb-ff6e66ac9b67"}}, {"head": {"id": "71bd4e46-1fac-4f7e-bc3c-f0dce73c2e06", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981162667000, "endTime": 31981162681200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1fca46aa-7375-438b-a881-7874ee7a4f74", "logId": "328b7883-a25f-4a7b-b0ae-ac79795f4a8b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "328b7883-a25f-4a7b-b0ae-ac79795f4a8b", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981162667000, "endTime": 31981162681200}, "additional": {"logType": "info", "children": [], "durationId": "71bd4e46-1fac-4f7e-bc3c-f0dce73c2e06", "parent": "89f31596-76b4-45bb-89cb-ff6e66ac9b67"}}, {"head": {"id": "c28f92a7-9c01-4020-a1ab-a5c62af49fe4", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981161670800, "endTime": 31981162692000}, "additional": {"logType": "info", "children": [], "durationId": "c8cab5d9-6944-4307-bc8d-e192dbf767b4", "parent": "89f31596-76b4-45bb-89cb-ff6e66ac9b67"}}, {"head": {"id": "37024345-980f-4174-9d35-9fa6e7a7ec01", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981162694500, "endTime": 31981162696400}, "additional": {"logType": "info", "children": [], "durationId": "8a133d11-7b30-4edf-88a3-d652e668bfd8", "parent": "89f31596-76b4-45bb-89cb-ff6e66ac9b67"}}, {"head": {"id": "89f31596-76b4-45bb-89cb-ff6e66ac9b67", "name": "init", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981026443700, "endTime": 31981162698700}, "additional": {"logType": "info", "children": ["bc35d08f-2913-484a-806e-78fa01925093", "c0f60841-23f0-4b40-aff0-a57644fdcc73", "c28f92a7-9c01-4020-a1ab-a5c62af49fe4", "37024345-980f-4174-9d35-9fa6e7a7ec01", "777dd16f-bd1b-49dd-b744-4beafbbc39db", "11f596b4-cbaa-49ff-8488-4f9cc8c2424a", "328b7883-a25f-4a7b-b0ae-ac79795f4a8b"], "durationId": "1fca46aa-7375-438b-a881-7874ee7a4f74"}}, {"head": {"id": "73f6d934-63e8-4071-ab11-588e29e5d5a7", "name": "Configuration task cost before running: 140 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981162800500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e25cf188-d73c-416c-9c52-956762015bf9", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981167361200, "endTime": 31981175659600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "32407f02-9072-4dff-ba1a-65914a968964", "logId": "460af8a7-2789-4ac3-bc82-51d38e9fc036"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "32407f02-9072-4dff-ba1a-65914a968964", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981164092900}, "additional": {"logType": "detail", "children": [], "durationId": "e25cf188-d73c-416c-9c52-956762015bf9"}}, {"head": {"id": "505a999a-5bf3-42c4-a780-69683ccf2fc5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981164523300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77278218-8834-4faf-b2e0-1290000c2074", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981164656300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e800ba7b-7709-4319-aabc-399b48a7b0d7", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981167374900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a501d49b-2132-4fbd-a1ea-8980d422c8ba", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981175428400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16ae45e2-d08a-4674-bb67-23023d717662", "name": "entry : default@PreBuild cost memory 0.27367401123046875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981175561000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "460af8a7-2789-4ac3-bc82-51d38e9fc036", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981167361200, "endTime": 31981175659600}, "additional": {"logType": "info", "children": [], "durationId": "e25cf188-d73c-416c-9c52-956762015bf9"}}, {"head": {"id": "8c072321-5faa-4306-b634-993ee695dfc8", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981179621700, "endTime": 31981181210800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "fd032091-166a-4902-80c3-e3d1ee7e3dff", "logId": "1cdb41a9-ec7c-47b5-b42f-00aaedad6856"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fd032091-166a-4902-80c3-e3d1ee7e3dff", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981178580400}, "additional": {"logType": "detail", "children": [], "durationId": "8c072321-5faa-4306-b634-993ee695dfc8"}}, {"head": {"id": "1679e039-e55e-46fb-a1a3-d40883a09140", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981178998600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f98d6bac-9277-46ef-89c0-29b3825333b0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981179077200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b298559-09c3-4d7f-bc8c-ab15fd72d7b6", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981179629000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f97eaca2-0c56-4788-8fac-d88db7309b05", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981181029200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f438a00a-a9e3-48f3-944b-e1f8322da4f4", "name": "entry : default@MergeProfile cost memory 0.10744476318359375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981181143100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cdb41a9-ec7c-47b5-b42f-00aaedad6856", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981179621700, "endTime": 31981181210800}, "additional": {"logType": "info", "children": [], "durationId": "8c072321-5faa-4306-b634-993ee695dfc8"}}, {"head": {"id": "bc0fbab1-3a9e-4d1c-a03b-e43832d99f21", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981184414900, "endTime": 31981186370000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5364a3b3-9e05-404e-93d9-b57b77de5e29", "logId": "f853b85c-602f-4315-a103-13a17dd1bffc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5364a3b3-9e05-404e-93d9-b57b77de5e29", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981183024900}, "additional": {"logType": "detail", "children": [], "durationId": "bc0fbab1-3a9e-4d1c-a03b-e43832d99f21"}}, {"head": {"id": "783b5922-a6e0-4633-bb5a-69537a76bdd8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981183623800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86fb0928-9020-4e50-ae83-09b87dd84ade", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981183715200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d9df9d6-131f-42ad-8b13-e1795b8ca140", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981184423100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1852891-ab47-4403-9c33-ac381fab6fc8", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981185260500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0090e60-27ec-46c6-b54e-baf5a6856e7e", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981186214400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "525bad64-60db-4774-a286-1a0489cb3d1d", "name": "entry : default@CreateBuildProfile cost memory 0.095367431640625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981186321000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f853b85c-602f-4315-a103-13a17dd1bffc", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981184414900, "endTime": 31981186370000}, "additional": {"logType": "info", "children": [], "durationId": "bc0fbab1-3a9e-4d1c-a03b-e43832d99f21"}}, {"head": {"id": "642a42c8-d9ff-4721-b4fa-2db88c31eb41", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981188873000, "endTime": 31981189149400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "45e5ba13-cf69-4cb2-8c9c-d79e17d3814a", "logId": "83b9899d-0d17-4159-b726-fc976e2e921a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "45e5ba13-cf69-4cb2-8c9c-d79e17d3814a", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981187702800}, "additional": {"logType": "detail", "children": [], "durationId": "642a42c8-d9ff-4721-b4fa-2db88c31eb41"}}, {"head": {"id": "c3bfdd0b-ae68-46c4-bc09-54a1bf1109cb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981188145200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0eddb42-591b-4dc6-b712-624f5cd4fb83", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981188227200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a24de06e-2e6d-439b-8ad2-6c5f6df9ee16", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981188880600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "417abe7e-8a3a-4bbb-a049-a94a43d16f26", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981188976200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "620de838-33a0-4c0c-8dbb-eab0edb4fe03", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981189004800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c13cd73-56e8-4d29-9f47-eefdde22fa23", "name": "entry : default@PreCheckSyscap cost memory 0.03680419921875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981189050900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "100cd803-4729-43a0-bded-e922147163fe", "name": "runTaskFromQueue task cost before running: 166 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981189119700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83b9899d-0d17-4159-b726-fc976e2e921a", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981188873000, "endTime": 31981189149400, "totalTime": 213200}, "additional": {"logType": "info", "children": [], "durationId": "642a42c8-d9ff-4721-b4fa-2db88c31eb41"}}, {"head": {"id": "be861053-16e8-4d37-852f-7015451f80c8", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981195726800, "endTime": 31981196539500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8a3b1542-42ec-4eca-b0b7-f0103cd1f63a", "logId": "be10a93d-37f8-4263-a3c1-bf39e25b849b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a3b1542-42ec-4eca-b0b7-f0103cd1f63a", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981190378100}, "additional": {"logType": "detail", "children": [], "durationId": "be861053-16e8-4d37-852f-7015451f80c8"}}, {"head": {"id": "2df24d50-79ba-4522-a7b1-9b7423eb0832", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981190828400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5449cd94-7348-42f7-a541-f7dc5a964e19", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981190906800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a93a4cda-4202-4544-9ace-5752e347e2e0", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981195737600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b65cf2a-5eda-4493-b71a-be3466423906", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981195905100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecdd581f-78c5-4ea6-877f-d99eaf41af03", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981196420000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5bfe5be-80b0-47ec-8297-b027ffea08fd", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06340789794921875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981196496900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be10a93d-37f8-4263-a3c1-bf39e25b849b", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981195726800, "endTime": 31981196539500}, "additional": {"logType": "info", "children": [], "durationId": "be861053-16e8-4d37-852f-7015451f80c8"}}, {"head": {"id": "926b143c-aa0c-4df9-866c-145cf3063a87", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981199533100, "endTime": 31981201051400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "a985dfb1-efb4-49f8-b137-86cafee6d0e2", "logId": "f0c79547-c915-458c-b612-b54cf58595da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a985dfb1-efb4-49f8-b137-86cafee6d0e2", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981197847700}, "additional": {"logType": "detail", "children": [], "durationId": "926b143c-aa0c-4df9-866c-145cf3063a87"}}, {"head": {"id": "c2a2c03e-36d7-4e02-ba44-645f2dbcbb5c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981198265500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e08ab5ba-e3be-4381-8e3e-66d53ee45e11", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981198334700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19adb4b8-abf7-4c06-a74a-8024ecbb3a18", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981199547700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "411944f9-091e-4235-a40b-895fad8c02e0", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981200809600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e3b54b5-5563-4217-be75-6244df016b3f", "name": "entry : default@ProcessProfile cost memory 0.05536651611328125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981200951800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0c79547-c915-458c-b612-b54cf58595da", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981199533100, "endTime": 31981201051400}, "additional": {"logType": "info", "children": [], "durationId": "926b143c-aa0c-4df9-866c-145cf3063a87"}}, {"head": {"id": "a2b32892-9c6c-4d71-b245-7a81bbfbbd41", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981205221700, "endTime": 31981210946000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "124078a9-d8c2-45e2-9294-bb4e55a0e64b", "logId": "4a9e3f07-ce0e-4c2b-93f7-18b06e38fab5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "124078a9-d8c2-45e2-9294-bb4e55a0e64b", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981202979200}, "additional": {"logType": "detail", "children": [], "durationId": "a2b32892-9c6c-4d71-b245-7a81bbfbbd41"}}, {"head": {"id": "1b568f21-79c1-4043-9ed5-aec8720f619c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981203677000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "870e15e8-c7c1-4813-8fd7-7894fbd0f46e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981203768600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea46b15e-ca07-4440-aea6-4e027526f9dc", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981205229300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3654d481-8d0d-4061-ae65-153f69a10615", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981210782700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c673b64-9a51-4e83-bf41-6ceefaf89d82", "name": "entry : default@ProcessRouterMap cost memory -1.416534423828125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981210899900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a9e3f07-ce0e-4c2b-93f7-18b06e38fab5", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981205221700, "endTime": 31981210946000}, "additional": {"logType": "info", "children": [], "durationId": "a2b32892-9c6c-4d71-b245-7a81bbfbbd41"}}, {"head": {"id": "ad9fa577-c957-4f14-860b-16d094d044b8", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981217447500, "endTime": 31981219839700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "5ad4f171-0bf3-430f-a012-c865e8d3c337", "logId": "99eff52e-7c2c-4f79-8f00-b3ac8b3e67c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ad4f171-0bf3-430f-a012-c865e8d3c337", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981213568000}, "additional": {"logType": "detail", "children": [], "durationId": "ad9fa577-c957-4f14-860b-16d094d044b8"}}, {"head": {"id": "97eb56e6-1e56-4d3a-9da5-b2852142f49a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981213981900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb3bb677-8b69-4eb0-a524-cea1550dbc11", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981214051500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0413ee7-6d13-4b66-aa8e-a41aad12e173", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981215223500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b8d7043-bd73-48df-b923-27198fc696fd", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981218367400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f0b073e-57e7-4c33-bcab-8eda722276f1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981218498200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1269efea-b4db-4838-9d0f-2b651f035285", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981218531000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b938c16f-11e2-433b-89a3-df14bafc880d", "name": "entry : default@PreviewProcessResource cost memory 0.06966400146484375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981218580200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edf79bb5-a33c-4a73-9735-472b276cab6c", "name": "runTaskFromQueue task cost before running: 197 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981219744500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99eff52e-7c2c-4f79-8f00-b3ac8b3e67c3", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981217447500, "endTime": 31981219839700, "totalTime": 1172700}, "additional": {"logType": "info", "children": [], "durationId": "ad9fa577-c957-4f14-860b-16d094d044b8"}}, {"head": {"id": "5f3df1fa-21ad-4f5b-b358-07d87899d3ac", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981225696900, "endTime": 31981243997100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "466a6308-ce9d-41c1-b658-bbf1afff2d65", "logId": "014ac4ed-2dc2-4795-99ae-d148cd396060"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "466a6308-ce9d-41c1-b658-bbf1afff2d65", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981222236700}, "additional": {"logType": "detail", "children": [], "durationId": "5f3df1fa-21ad-4f5b-b358-07d87899d3ac"}}, {"head": {"id": "22216535-1855-4ab7-be77-e4fa4cb4d30f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981222656200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12bffa3a-02b4-4d14-b4d9-7a08d81e0f3c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981222738800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c1ce018-3765-41c7-8c6c-6bc0697c2a39", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981225710400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bd2eb71-fc97-459c-b60f-39506e51d8b4", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981243776900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a21f68e9-f5b2-44ec-852c-10dea15fd070", "name": "entry : default@GenerateLoaderJson cost memory 0.7459487915039062", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981243907000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "014ac4ed-2dc2-4795-99ae-d148cd396060", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981225696900, "endTime": 31981243997100}, "additional": {"logType": "info", "children": [], "durationId": "5f3df1fa-21ad-4f5b-b358-07d87899d3ac"}}, {"head": {"id": "dbdb082b-5f34-49f1-980f-b6dc6bd37048", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981254345100, "endTime": 31981442689500}, "additional": {"children": ["97c49ac4-fd5a-4477-9d1a-59f4f014f870", "66d7b6fb-2777-4617-9281-8c2341e29d5a", "87353bb7-1d3b-473b-8405-5b6b98ad9b0e", "dd8a41a5-8c95-4ac3-8953-47015c69a733"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "8f1fcc08-e173-4f08-b069-6043dbbc8c1f", "logId": "24b4b2fa-260a-4b3a-a675-e3371442d0f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f1fcc08-e173-4f08-b069-6043dbbc8c1f", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981251137000}, "additional": {"logType": "detail", "children": [], "durationId": "dbdb082b-5f34-49f1-980f-b6dc6bd37048"}}, {"head": {"id": "fb35a638-7f1e-4960-94d1-e454b6ebc036", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981251673900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e29baf3c-b7b4-4a5d-a528-b65b7284f655", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981251789300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4767cdf-1513-4c22-ab9a-45ec895ee1f1", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981252566500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "461130ae-b923-4dbe-8778-2445282c03c0", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981254372100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dfdb65f-4d5b-46de-bd70-cae1d88e2bd8", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981275215500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fd5664f-4c2e-46d1-8ff8-8c752d404590", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 21 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981275354400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97c49ac4-fd5a-4477-9d1a-59f4f014f870", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981276262800, "endTime": 31981297744900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dbdb082b-5f34-49f1-980f-b6dc6bd37048", "logId": "187d89a4-5c83-4ab9-b6c1-28ebc1c425b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "187d89a4-5c83-4ab9-b6c1-28ebc1c425b3", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981276262800, "endTime": 31981297744900}, "additional": {"logType": "info", "children": [], "durationId": "97c49ac4-fd5a-4477-9d1a-59f4f014f870", "parent": "24b4b2fa-260a-4b3a-a675-e3371442d0f8"}}, {"head": {"id": "27296c26-ee78-4ceb-9820-5d1537075e2d", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981297875600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66d7b6fb-2777-4617-9281-8c2341e29d5a", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981298677500, "endTime": 31981333172700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dbdb082b-5f34-49f1-980f-b6dc6bd37048", "logId": "a77bc90d-dc19-403f-aa3a-4ae38c303296"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "edc7aafe-09df-4233-9f3f-9cff8881dc5b", "name": "current process  memoryUsage: {\n  rss: 113168384,\n  heapTotal: 128016384,\n  heapUsed: 116084128,\n  external: 3158649,\n  arrayBuffers: 152550\n} os memoryUsage :12.924495697021484", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981299617800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d6d8bcd-1b84-4ee8-999d-5667ba0d661e", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981329991200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a77bc90d-dc19-403f-aa3a-4ae38c303296", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981298677500, "endTime": 31981333172700}, "additional": {"logType": "info", "children": [], "durationId": "66d7b6fb-2777-4617-9281-8c2341e29d5a", "parent": "24b4b2fa-260a-4b3a-a675-e3371442d0f8"}}, {"head": {"id": "de8e7517-d478-4792-bd24-7a946761124c", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981333289100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87353bb7-1d3b-473b-8405-5b6b98ad9b0e", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981334206100, "endTime": 31981376434300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dbdb082b-5f34-49f1-980f-b6dc6bd37048", "logId": "6571d87a-c33b-4e05-949d-a5db05977a53"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b069a3b7-2a14-4a48-be78-37b96acec60b", "name": "current process  memoryUsage: {\n  rss: 113184768,\n  heapTotal: 128016384,\n  heapUsed: 114815288,\n  external: 3141744,\n  arrayBuffers: 135660\n} os memoryUsage :12.919097900390625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981334974400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "553a0f56-04f5-4fab-8e10-fda9cb6ba7dd", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981372741300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6571d87a-c33b-4e05-949d-a5db05977a53", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981334206100, "endTime": 31981376434300}, "additional": {"logType": "info", "children": [], "durationId": "87353bb7-1d3b-473b-8405-5b6b98ad9b0e", "parent": "24b4b2fa-260a-4b3a-a675-e3371442d0f8"}}, {"head": {"id": "0183d48c-07f4-4b14-94c6-ef652af26c5b", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981376593200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd8a41a5-8c95-4ac3-8953-47015c69a733", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981377652000, "endTime": 31981441390000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dbdb082b-5f34-49f1-980f-b6dc6bd37048", "logId": "73c76435-a28c-4229-8db5-4e7673dca294"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "457d4e76-7ddd-4cb3-a5c3-87603303de57", "name": "current process  memoryUsage: {\n  rss: 113192960,\n  heapTotal: 128016384,\n  heapUsed: 115095680,\n  external: 3141870,\n  arrayBuffers: 136670\n} os memoryUsage :12.94284439086914", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981378354100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3437655c-85f4-4c7b-96aa-41d97897edea", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981438016200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73c76435-a28c-4229-8db5-4e7673dca294", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981377652000, "endTime": 31981441390000}, "additional": {"logType": "info", "children": [], "durationId": "dd8a41a5-8c95-4ac3-8953-47015c69a733", "parent": "24b4b2fa-260a-4b3a-a675-e3371442d0f8"}}, {"head": {"id": "ff154733-a5f1-4cf7-88c8-a55915c1357c", "name": "entry : default@PreviewCompileResource cost memory 0.274688720703125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981442489900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ba21edc-b633-49bc-b7e7-18cb89e277d2", "name": "runTaskFromQueue task cost before running: 420 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981442644900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24b4b2fa-260a-4b3a-a675-e3371442d0f8", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981254345100, "endTime": 31981442689500, "totalTime": 188260400}, "additional": {"logType": "info", "children": ["187d89a4-5c83-4ab9-b6c1-28ebc1c425b3", "a77bc90d-dc19-403f-aa3a-4ae38c303296", "6571d87a-c33b-4e05-949d-a5db05977a53", "73c76435-a28c-4229-8db5-4e7673dca294"], "durationId": "dbdb082b-5f34-49f1-980f-b6dc6bd37048"}}, {"head": {"id": "aa119856-ac5c-49f2-874a-9da448530d15", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981445502400, "endTime": 31981445720700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "d603ab0e-9766-42c2-8bef-8a83986e9499", "logId": "d8111e2f-220e-43ed-866a-ae85763b83a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d603ab0e-9766-42c2-8bef-8a83986e9499", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981444815600}, "additional": {"logType": "detail", "children": [], "durationId": "aa119856-ac5c-49f2-874a-9da448530d15"}}, {"head": {"id": "484a2634-e2cc-4477-a6dc-82ba86c6b5e0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981445373700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b89abf7-6119-4431-8943-c01ccfd57f01", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981445444500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e60bc43-3cd1-405c-899f-275083b355f3", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981445507600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15f07b50-2806-4751-950b-c430a30d3ad8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981445558400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f06ca1a2-4fb7-4328-ac13-ded72a0b22d2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981445579000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "425eb458-9d8a-4bb5-8288-5f1dde5513c7", "name": "entry : default@PreviewHookCompileResource cost memory 0.0379180908203125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981445614300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfbb215b-b58c-4bb7-9f42-233700133ffc", "name": "runTaskFromQueue task cost before running: 423 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981445691400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8111e2f-220e-43ed-866a-ae85763b83a5", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981445502400, "endTime": 31981445720700, "totalTime": 174200}, "additional": {"logType": "info", "children": [], "durationId": "aa119856-ac5c-49f2-874a-9da448530d15"}}, {"head": {"id": "49621020-7d4c-4a2a-95f7-f7e610515430", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981447934100, "endTime": 31981453588800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "9ac97952-58dd-4b59-b3ba-9abfdb1ca98d", "logId": "c9899ddd-4266-403e-b23d-19371cb488cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ac97952-58dd-4b59-b3ba-9abfdb1ca98d", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981446953400}, "additional": {"logType": "detail", "children": [], "durationId": "49621020-7d4c-4a2a-95f7-f7e610515430"}}, {"head": {"id": "1cf38042-414a-4f5b-9cd4-13c167f5db26", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981447377900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb1ae378-fd94-4a76-831c-c756866f260b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981447451200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24a974b8-b064-4e15-839c-0e3fa78d9868", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981447940800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dacf100-7e8e-4e10-958d-6ef4b4fd7be6", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981449057100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1ebab18-ba0b-47f6-98f0-726ba8ee309f", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981449131600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e44201cc-8a0a-450a-9212-3a482493742d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981449181100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62862331-78c7-486d-8f32-756ca863c59e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981449204200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac772992-afb2-4554-9109-460290e454e4", "name": "entry : default@CopyPreviewProfile cost memory 0.2194976806640625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981453414200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a82a9de-c94d-47f9-94e9-f5d9392dda1c", "name": "runTaskFromQueue task cost before running: 430 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981453546200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9899ddd-4266-403e-b23d-19371cb488cd", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981447934100, "endTime": 31981453588800, "totalTime": 5590600}, "additional": {"logType": "info", "children": [], "durationId": "49621020-7d4c-4a2a-95f7-f7e610515430"}}, {"head": {"id": "4b58a52b-1c8e-45df-9596-2e527dff9739", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981456806400, "endTime": 31981457085700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "00bf6c93-470f-4677-bd1e-69397bb4626f", "logId": "61f62eec-4c4e-4392-a7e2-f9a61fab408d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "00bf6c93-470f-4677-bd1e-69397bb4626f", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981455202800}, "additional": {"logType": "detail", "children": [], "durationId": "4b58a52b-1c8e-45df-9596-2e527dff9739"}}, {"head": {"id": "508638f0-be49-4a25-82ec-0875220cb4b2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981455888400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04567136-f440-45ba-9013-14f6c70ed646", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981456014400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abeb587d-39d2-4736-9dbc-f71d4532e94b", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981456815600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a3d80a2-1246-4e34-8a2b-dc1d866fce00", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981456920200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aac2b81d-8eca-46cc-be71-4d94edcd67c9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981456950300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d0d6d0f-f6a1-41a9-888a-447e20d69f85", "name": "entry : default@ReplacePreviewerPage cost memory 0.037872314453125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981457010200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17747b33-9ddf-4aec-9334-045c0e006969", "name": "runTaskFromQueue task cost before running: 434 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981457058100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61f62eec-4c4e-4392-a7e2-f9a61fab408d", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981456806400, "endTime": 31981457085700, "totalTime": 239300}, "additional": {"logType": "info", "children": [], "durationId": "4b58a52b-1c8e-45df-9596-2e527dff9739"}}, {"head": {"id": "ea9710a9-b95f-4754-91da-ba96e5e9ae85", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981458396200, "endTime": 31981458573700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "5da6f42d-ca78-4058-8f7b-10bbdebb6bb0", "logId": "b206f543-1f2d-45cb-8d73-4d1d189796a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5da6f42d-ca78-4058-8f7b-10bbdebb6bb0", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981458355200}, "additional": {"logType": "detail", "children": [], "durationId": "ea9710a9-b95f-4754-91da-ba96e5e9ae85"}}, {"head": {"id": "3e2075f6-387d-406d-8051-298d67205bb1", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981458401800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3793cb5c-2f04-423f-a801-9a12fa3c3909", "name": "entry : buildPreviewerResource cost memory 0.0116424560546875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981458492600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a598472-c096-4861-9359-f8dac8d653ee", "name": "runTaskFromQueue task cost before running: 435 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981458544300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b206f543-1f2d-45cb-8d73-4d1d189796a6", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981458396200, "endTime": 31981458573700, "totalTime": 135700}, "additional": {"logType": "info", "children": [], "durationId": "ea9710a9-b95f-4754-91da-ba96e5e9ae85"}}, {"head": {"id": "8e7e5774-b4a7-4e94-aba2-594b80f5826e", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981461584100, "endTime": 31981464978600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "ce190d43-82c8-4b71-bb45-7a628a57e2ed", "logId": "08721092-2fa2-42ed-8b17-85ab1b1687ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce190d43-82c8-4b71-bb45-7a628a57e2ed", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981459739300}, "additional": {"logType": "detail", "children": [], "durationId": "8e7e5774-b4a7-4e94-aba2-594b80f5826e"}}, {"head": {"id": "75799b1a-663d-4fae-b77e-3bef7f4e6090", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981460187100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d66fca11-4e91-4726-8e9e-2b9a31a6737d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981460255900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "221be8a4-51d9-4b3b-8ba6-638c86e9cfd9", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981461591700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78265092-e64d-4271-84cd-ddd82fb83727", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981463194200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c03d6fd-956b-4032-880a-dfd5a9e0883e", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981463276700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be93838c-d5c6-4028-8d88-e82729463169", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981463330400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d804398d-b63f-4883-b7b9-dad40b2270a7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981463354400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0c5c0f4-6b5b-47fa-8bbd-01ecbfd70c51", "name": "entry : default@PreviewUpdateAssets cost memory 0.1397552490234375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981464819000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43b10acb-802d-4c85-b4ee-e37c28ff3355", "name": "runTaskFromQueue task cost before running: 442 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981464936700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08721092-2fa2-42ed-8b17-85ab1b1687ca", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981461584100, "endTime": 31981464978600, "totalTime": 3335700}, "additional": {"logType": "info", "children": [], "durationId": "8e7e5774-b4a7-4e94-aba2-594b80f5826e"}}, {"head": {"id": "e770df06-c442-4d19-aad5-d1177acdf336", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981471126700, "endTime": 31991715353900}, "additional": {"children": ["d5d26c75-de79-4907-b382-103d86f06bfc"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "1e94bc5c-7d6e-471a-a2ff-90ff44cf69ef", "logId": "0288d851-afc3-4afb-ae90-25b11c1e8abc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e94bc5c-7d6e-471a-a2ff-90ff44cf69ef", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981466918200}, "additional": {"logType": "detail", "children": [], "durationId": "e770df06-c442-4d19-aad5-d1177acdf336"}}, {"head": {"id": "c302faf7-f31b-4d5f-a066-acc18ddd6a24", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981467327600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21e41e62-60af-4631-9548-d8a2200a2d04", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981467405900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a6bf82f-5d99-43f0-bbcf-bbb0e2d8b571", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981471161900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b308218-218b-4675-aa67-08f193e95143", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981479732500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73eb473d-2d31-43c5-81e3-bce8e39f2d6f", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981479854700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5d26c75-de79-4907-b382-103d86f06bfc", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker21", "startTime": 31981491251100, "endTime": 31991710618900}, "additional": {"children": ["5e1ad199-9f7d-4221-9416-95297293f07d", "60198b5e-fade-4004-8555-7c6b98b12fd8", "f73be11e-8ed6-4903-98dd-e0fbde0c31b2", "97aa3c24-a8e8-4b84-95ae-a391825758a5", "fc56d5dc-4bcb-4b57-b3d0-961b4ec76d53", "ce78e50c-5950-4e22-a8bb-daca36f85337"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "e770df06-c442-4d19-aad5-d1177acdf336", "logId": "6a90f888-0d48-49cc-9ac5-3cd0c79c5be0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a85b9200-abed-433c-82ac-9b462c29a0ed", "name": "entry : default@PreviewArkTS cost memory 1.1218719482421875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981493135500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2770469-4940-4db8-a891-b6ec9faac2de", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31985013222600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e1ad199-9f7d-4221-9416-95297293f07d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker21", "startTime": 31985014135500, "endTime": 31985014151600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d5d26c75-de79-4907-b382-103d86f06bfc", "logId": "3c634df2-0013-42c0-9e4f-8fe4006ed743"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c634df2-0013-42c0-9e4f-8fe4006ed743", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31985014135500, "endTime": 31985014151600}, "additional": {"logType": "info", "children": [], "durationId": "5e1ad199-9f7d-4221-9416-95297293f07d", "parent": "6a90f888-0d48-49cc-9ac5-3cd0c79c5be0"}}, {"head": {"id": "5d6efe15-2e1a-46a0-810f-5fec39794474", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991709163100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60198b5e-fade-4004-8555-7c6b98b12fd8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker21", "startTime": 31991710386200, "endTime": 31991710426800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d5d26c75-de79-4907-b382-103d86f06bfc", "logId": "da2bd4a0-1106-4af3-9c15-09df2c8ac4d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da2bd4a0-1106-4af3-9c15-09df2c8ac4d8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991710386200, "endTime": 31991710426800}, "additional": {"logType": "info", "children": [], "durationId": "60198b5e-fade-4004-8555-7c6b98b12fd8", "parent": "6a90f888-0d48-49cc-9ac5-3cd0c79c5be0"}}, {"head": {"id": "6a90f888-0d48-49cc-9ac5-3cd0c79c5be0", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Worker21", "startTime": 31981491251100, "endTime": 31991710618900}, "additional": {"logType": "info", "children": ["3c634df2-0013-42c0-9e4f-8fe4006ed743", "da2bd4a0-1106-4af3-9c15-09df2c8ac4d8", "c6cee7fd-9723-4d67-894f-e230ca2dd2f0", "707b7895-f5fb-4b0f-92d0-ae6be2156e27", "c9e99c67-cf0c-4339-9cbb-1739be86d506", "37f4e28b-1002-4220-b620-0089bc0d5e16"], "durationId": "d5d26c75-de79-4907-b382-103d86f06bfc", "parent": "0288d851-afc3-4afb-ae90-25b11c1e8abc"}}, {"head": {"id": "f73be11e-8ed6-4903-98dd-e0fbde0c31b2", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker21", "startTime": 31983861655900, "endTime": 31984972680900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d5d26c75-de79-4907-b382-103d86f06bfc", "logId": "c6cee7fd-9723-4d67-894f-e230ca2dd2f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c6cee7fd-9723-4d67-894f-e230ca2dd2f0", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31983861655900, "endTime": 31984972680900}, "additional": {"logType": "info", "children": [], "durationId": "f73be11e-8ed6-4903-98dd-e0fbde0c31b2", "parent": "6a90f888-0d48-49cc-9ac5-3cd0c79c5be0"}}, {"head": {"id": "97aa3c24-a8e8-4b84-95ae-a391825758a5", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker21", "startTime": 31984973006400, "endTime": 31984982058100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d5d26c75-de79-4907-b382-103d86f06bfc", "logId": "707b7895-f5fb-4b0f-92d0-ae6be2156e27"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "707b7895-f5fb-4b0f-92d0-ae6be2156e27", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31984973006400, "endTime": 31984982058100}, "additional": {"logType": "info", "children": [], "durationId": "97aa3c24-a8e8-4b84-95ae-a391825758a5", "parent": "6a90f888-0d48-49cc-9ac5-3cd0c79c5be0"}}, {"head": {"id": "fc56d5dc-4bcb-4b57-b3d0-961b4ec76d53", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker21", "startTime": 31984982187600, "endTime": 31984982194400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d5d26c75-de79-4907-b382-103d86f06bfc", "logId": "c9e99c67-cf0c-4339-9cbb-1739be86d506"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9e99c67-cf0c-4339-9cbb-1739be86d506", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31984982187600, "endTime": 31984982194400}, "additional": {"logType": "info", "children": [], "durationId": "fc56d5dc-4bcb-4b57-b3d0-961b4ec76d53", "parent": "6a90f888-0d48-49cc-9ac5-3cd0c79c5be0"}}, {"head": {"id": "ce78e50c-5950-4e22-a8bb-daca36f85337", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker21", "startTime": 31984982257000, "endTime": 31991709181900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d5d26c75-de79-4907-b382-103d86f06bfc", "logId": "37f4e28b-1002-4220-b620-0089bc0d5e16"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "37f4e28b-1002-4220-b620-0089bc0d5e16", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31984982257000, "endTime": 31991709181900}, "additional": {"logType": "info", "children": [], "durationId": "ce78e50c-5950-4e22-a8bb-daca36f85337", "parent": "6a90f888-0d48-49cc-9ac5-3cd0c79c5be0"}}, {"head": {"id": "0288d851-afc3-4afb-ae90-25b11c1e8abc", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981471126700, "endTime": 31991715353900, "totalTime": 10244214000}, "additional": {"logType": "info", "children": ["6a90f888-0d48-49cc-9ac5-3cd0c79c5be0"], "durationId": "e770df06-c442-4d19-aad5-d1177acdf336"}}, {"head": {"id": "a3bef130-abbd-4e3e-ae66-bb8cf2197c1d", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991719288500, "endTime": 31991719537300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "7966e9d0-76df-4725-902d-cd0001f75764", "logId": "75cd09f9-a588-4f6e-8e6c-ccf338b60e53"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7966e9d0-76df-4725-902d-cd0001f75764", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991719248300}, "additional": {"logType": "detail", "children": [], "durationId": "a3bef130-abbd-4e3e-ae66-bb8cf2197c1d"}}, {"head": {"id": "646a3241-7f57-4e54-ae1e-4dd147c41242", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991719336500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08d00fe6-dcb6-42fa-b7f5-81a0f5491418", "name": "entry : PreviewBuild cost memory 0.02347564697265625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991719449600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45ba9c9a-348a-4e74-ace3-a58a7b507bee", "name": "runTaskFromQueue task cost before running: 10 s 696 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991719507900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75cd09f9-a588-4f6e-8e6c-ccf338b60e53", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991719288500, "endTime": 31991719537300, "totalTime": 205300}, "additional": {"logType": "info", "children": [], "durationId": "a3bef130-abbd-4e3e-ae66-bb8cf2197c1d"}}, {"head": {"id": "7a71aee8-3c85-43ef-81f1-d35c192c38b7", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991728875700, "endTime": 31991728900000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6ad275d7-776d-4cea-a9f9-276c6c858721", "logId": "5ca21fb5-44a3-4755-84df-945887922ef7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ca21fb5-44a3-4755-84df-945887922ef7", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991728875700, "endTime": 31991728900000}, "additional": {"logType": "info", "children": [], "durationId": "7a71aee8-3c85-43ef-81f1-d35c192c38b7"}}, {"head": {"id": "8adc61bb-c91c-42dc-a670-33581440d469", "name": "BUILD SUCCESSFUL in 10 s 706 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991728946300}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "6f134de4-b4e0-480a-b04d-ab809e8faf71", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31981023619400, "endTime": 31991729198700}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 59}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "c6862ff8-f6cb-485b-a696-aba17047b70a", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991729218800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8ca8cb2-6a2c-49a2-9fcf-50119fe5d571", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991729255700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f577fa19-f7ee-4e69-b56e-de807f84d453", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991729279100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58501a2b-40ae-4eda-8705-b3bff5e7d30d", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991729302500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4cf3fce-ff86-40ac-9be0-69a2c9f2a7d4", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991729326500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1c53bab-7464-4c53-9518-bdb33d23ca23", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991729353100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "506a1d0b-70f2-4038-b2cd-b96f1dcd119d", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991729377200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ff6f616-5f27-4a74-976e-8e14b0001d8f", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991730328100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54e9e83d-d8e5-4c73-9449-339aa7307127", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991738462700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4bbea49-f0f2-4679-a7cd-1ef3f2dbd78a", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991738915800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27816e82-c9de-4a59-a0f3-4f3ac1662fd3", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991748467600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee593a7a-c8d8-4d1b-a6ff-046e8a7ae72f", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:21 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991749723000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f93c9cb-22f4-4197-81e6-c4144c98913f", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991750094500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4133243f-0b82-48af-b3f1-084f31309025", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991751218700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "202bdb49-81b8-466d-990f-5dfe224a60bd", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991752195400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48e74dfe-dcba-4eb7-9942-fa7c4c5137d4", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991752705600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a84ea6d6-8cf1-4376-85e2-7054514f486f", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991753010000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "531ec191-61ca-4205-b18b-271def1becba", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991753375000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b577c198-9aea-45c8-a712-0b24de623433", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991758463600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffd66979-6997-4d24-b8e0-eb45df99e526", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991759808000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4da5db9d-2706-4c88-9bc2-32ead73660e9", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991759910300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47ed55b4-711b-4c52-867c-a1d4370a6577", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991760164700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41a55453-2917-41c8-8262-ed7628d188f5", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991760985100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b0744bf-8410-4316-af2c-8ceae9788350", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991770139900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f5d1c58-1f49-4844-8b7f-2377e11c73e6", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991770486300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf8688cf-a82a-47a2-9fc9-f39264168256", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991770960600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4187b7b3-831a-4f7c-a252-e3d051022c2f", "name": "Update task entry:default@PreviewArkTS output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991771237700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c638606-cc3b-4b2b-9eb3-196797f4d7c1", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:19 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 31991771646900}, "additional": {"logType": "debug", "children": []}}], "workLog": []}