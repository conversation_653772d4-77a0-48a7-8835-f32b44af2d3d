import { httpClient } from "@normalized:N&&&entry/src/main/ets/common/http/HttpClient&";
import type { UserInfo, UserLoginRequest, UserRegisterRequest, UpdatePayPasswordRequest, UpdatePayLimitRequest, SpringBootUserResponse } from '../common/types/index';
/**
 * 用户相关API接口
 */
export class UserApi {
    /**
     * 用户登录 - 适配SpringBoot3后端
     */
    static async login(loginData: UserLoginRequest): Promise<SpringBootUserResponse> {
        const response = await httpClient.post<SpringBootUserResponse>('/user/login', loginData);
        return response.data;
    }
    /**
     * 用户注册
     */
    static async register(registerData: UserRegisterRequest) {
        await httpClient.post<void>('/user/register', registerData);
    }
    /**
     * 获取用户信息
     */
    static async getUserInfo(): Promise<UserInfo> {
        const response = await httpClient.get<UserInfo>('/user/info');
        return response.data;
    }
    /**
     * 修改支付密码
     */
    static async updatePayPassword(data: UpdatePayPasswordRequest) {
        await httpClient.put<void>('/user/pay-password', data);
    }
    /**
     * 设置支付限额
     */
    static async updatePayLimit(data: UpdatePayLimitRequest) {
        await httpClient.put<void>('/user/pay-limit', data);
    }
}
