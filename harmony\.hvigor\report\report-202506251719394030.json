{"version": "2.0", "ppid": 21780, "events": [{"head": {"id": "5f9e1d1e-5a2e-48bb-839e-6ad11fb74a88", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29611996922600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ee692ce-59df-4170-948f-32ebc4ef17e1", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29621576240700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51a1de13-1629-4a51-bbd1-2d67d35451fd", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29621576742900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d100e4f-bf16-4ec2-b82b-c5b7f78a58b3", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622148639800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc86f903-6faf-473a-9412-a4c8455e9d49", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622154418000, "endTime": 29622298613400}, "additional": {"children": ["e1161871-8086-4bde-95ed-d7f9c3c8490c", "cf0d2e92-1e37-468d-a672-9a86542035cc", "85e0dc89-5e4e-4ae9-a849-1014a64df986", "c06312f2-a2ad-468d-ac75-c2cc3746facc", "92f946bd-09a6-4436-9ff0-3f44d92becc4", "faa002a2-cdb8-4ff6-836a-8cf0d178965e", "540ce846-c49d-432e-aed6-16bfa4c609b1"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "0623ee74-c937-4ef7-8acc-f53b0501cb2d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1161871-8086-4bde-95ed-d7f9c3c8490c", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622154419200, "endTime": 29622166062500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fc86f903-6faf-473a-9412-a4c8455e9d49", "logId": "aaed5d20-31f8-45af-9c3e-d7022c765ba3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf0d2e92-1e37-468d-a672-9a86542035cc", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622166076400, "endTime": 29622297167600}, "additional": {"children": ["3c02028c-0d11-4759-9af9-9f62567f835c", "9ec51830-7607-49fc-8e24-be474c2072a7", "8dd44547-97c2-4d8b-bf97-6dcdaba32acf", "442e2508-e728-49e2-8295-96fee3db0027", "45581eaf-61e8-4782-8f7e-cb3b171c8b36", "5983a32e-0c7b-4e12-83e5-a7098e54dbe3", "e85c6cfc-587d-4aa0-8ef9-c87f602ad2a9", "6ae6f675-18a4-4915-b41c-84e4060a30aa", "ffe8e102-2b76-4a85-8c75-bb3b25e4a503"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fc86f903-6faf-473a-9412-a4c8455e9d49", "logId": "b6800dff-a515-4b19-85db-9c9d50f68d2d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85e0dc89-5e4e-4ae9-a849-1014a64df986", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622297183800, "endTime": 29622298606800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fc86f903-6faf-473a-9412-a4c8455e9d49", "logId": "d93124b3-d439-4a11-aff5-077b49dfe484"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c06312f2-a2ad-468d-ac75-c2cc3746facc", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622298610000, "endTime": 29622298611300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fc86f903-6faf-473a-9412-a4c8455e9d49", "logId": "af0b9b9f-4cb2-41c0-a8fc-c20c5b58aeef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92f946bd-09a6-4436-9ff0-3f44d92becc4", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622157604200, "endTime": 29622157645000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fc86f903-6faf-473a-9412-a4c8455e9d49", "logId": "c7b22bbb-dc82-44fb-af62-55b5d4ce47c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7b22bbb-dc82-44fb-af62-55b5d4ce47c2", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622157604200, "endTime": 29622157645000}, "additional": {"logType": "info", "children": [], "durationId": "92f946bd-09a6-4436-9ff0-3f44d92becc4", "parent": "0623ee74-c937-4ef7-8acc-f53b0501cb2d"}}, {"head": {"id": "faa002a2-cdb8-4ff6-836a-8cf0d178965e", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622162954600, "endTime": 29622162972000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fc86f903-6faf-473a-9412-a4c8455e9d49", "logId": "c51b6a32-5d01-400b-82ef-12db35fbb843"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c51b6a32-5d01-400b-82ef-12db35fbb843", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622162954600, "endTime": 29622162972000}, "additional": {"logType": "info", "children": [], "durationId": "faa002a2-cdb8-4ff6-836a-8cf0d178965e", "parent": "0623ee74-c937-4ef7-8acc-f53b0501cb2d"}}, {"head": {"id": "67d7ed0b-103a-4521-8672-46d07fe6adbb", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622163011400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d22f64bd-bc5e-4ab7-b90d-ccfa07b83c4d", "name": "Cache service initialization finished in 3 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622165951000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaed5d20-31f8-45af-9c3e-d7022c765ba3", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622154419200, "endTime": 29622166062500}, "additional": {"logType": "info", "children": [], "durationId": "e1161871-8086-4bde-95ed-d7f9c3c8490c", "parent": "0623ee74-c937-4ef7-8acc-f53b0501cb2d"}}, {"head": {"id": "3c02028c-0d11-4759-9af9-9f62567f835c", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622171186700, "endTime": 29622171193400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cf0d2e92-1e37-468d-a672-9a86542035cc", "logId": "991c58b3-6b15-41a0-ba66-5276c5ee021f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ec51830-7607-49fc-8e24-be474c2072a7", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622171204800, "endTime": 29622174464000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cf0d2e92-1e37-468d-a672-9a86542035cc", "logId": "39755035-f4d9-4a4a-9e6f-308c9e143e47"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8dd44547-97c2-4d8b-bf97-6dcdaba32acf", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622174475900, "endTime": 29622242985200}, "additional": {"children": ["e362e274-d84d-4e71-bb4f-9a0227473c36", "1f5df055-27f5-4a33-9db6-33a35ef37a3b", "a80a18ab-0a8a-41b4-9470-0ec47a3d6da8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cf0d2e92-1e37-468d-a672-9a86542035cc", "logId": "fb2a336a-5ec1-4302-974c-9ba40b72d82a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "442e2508-e728-49e2-8295-96fee3db0027", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622242996500, "endTime": 29622262226100}, "additional": {"children": ["73ada67f-fa18-499d-8984-ee01b3000045"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cf0d2e92-1e37-468d-a672-9a86542035cc", "logId": "e16e67d7-4b2c-4a11-bbd1-fc002e3af2d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "45581eaf-61e8-4782-8f7e-cb3b171c8b36", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622262231600, "endTime": 29622277586300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cf0d2e92-1e37-468d-a672-9a86542035cc", "logId": "4b7e131b-e6bf-4eb1-b626-9b97ef1c99c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5983a32e-0c7b-4e12-83e5-a7098e54dbe3", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622278611200, "endTime": 29622288363600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cf0d2e92-1e37-468d-a672-9a86542035cc", "logId": "796801c8-dce1-4c32-8886-1f6b02a26253"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e85c6cfc-587d-4aa0-8ef9-c87f602ad2a9", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622288387700, "endTime": 29622297032800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cf0d2e92-1e37-468d-a672-9a86542035cc", "logId": "e8a57af8-941c-4c56-be71-6c5846b6e35d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ae6f675-18a4-4915-b41c-84e4060a30aa", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622297063600, "endTime": 29622297159100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cf0d2e92-1e37-468d-a672-9a86542035cc", "logId": "761d0dc2-541f-410f-bc10-4a062313dbb5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "991c58b3-6b15-41a0-ba66-5276c5ee021f", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622171186700, "endTime": 29622171193400}, "additional": {"logType": "info", "children": [], "durationId": "3c02028c-0d11-4759-9af9-9f62567f835c", "parent": "b6800dff-a515-4b19-85db-9c9d50f68d2d"}}, {"head": {"id": "39755035-f4d9-4a4a-9e6f-308c9e143e47", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622171204800, "endTime": 29622174464000}, "additional": {"logType": "info", "children": [], "durationId": "9ec51830-7607-49fc-8e24-be474c2072a7", "parent": "b6800dff-a515-4b19-85db-9c9d50f68d2d"}}, {"head": {"id": "e362e274-d84d-4e71-bb4f-9a0227473c36", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622174986800, "endTime": 29622175004600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8dd44547-97c2-4d8b-bf97-6dcdaba32acf", "logId": "4661c5ec-ec89-40df-9182-a7fd5f25a10a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4661c5ec-ec89-40df-9182-a7fd5f25a10a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622174986800, "endTime": 29622175004600}, "additional": {"logType": "info", "children": [], "durationId": "e362e274-d84d-4e71-bb4f-9a0227473c36", "parent": "fb2a336a-5ec1-4302-974c-9ba40b72d82a"}}, {"head": {"id": "1f5df055-27f5-4a33-9db6-33a35ef37a3b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622176714000, "endTime": 29622242311100}, "additional": {"children": ["1f01149f-95a2-4859-85cb-0b8cf505db88", "7150b630-1757-494c-ab43-97fac4302aa2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8dd44547-97c2-4d8b-bf97-6dcdaba32acf", "logId": "23e2d9eb-3a79-4810-8617-27522bca2957"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1f01149f-95a2-4859-85cb-0b8cf505db88", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622176714900, "endTime": 29622179777700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f5df055-27f5-4a33-9db6-33a35ef37a3b", "logId": "6852af73-9fba-4462-b7b5-2192aaf78a73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7150b630-1757-494c-ab43-97fac4302aa2", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622179794300, "endTime": 29622242297800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f5df055-27f5-4a33-9db6-33a35ef37a3b", "logId": "c4246920-0f89-4011-b42b-988b16b6a592"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4d6534af-13fd-4795-9fa3-d3c5204fcbbc", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622176719500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd663e47-7ad9-42cc-aebe-b1bebff76278", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622179679600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6852af73-9fba-4462-b7b5-2192aaf78a73", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622176714900, "endTime": 29622179777700}, "additional": {"logType": "info", "children": [], "durationId": "1f01149f-95a2-4859-85cb-0b8cf505db88", "parent": "23e2d9eb-3a79-4810-8617-27522bca2957"}}, {"head": {"id": "9febbb6c-0d10-4f35-8fc7-c375eb890f7a", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622179802500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04c312a2-1a45-413f-9670-5c5b123759ad", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622185284600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a086172b-5ee0-4f42-85ce-3c22d1320395", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622185383900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ed482df-903e-4dff-8bc5-421c3177b14d", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622185468100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d91dfef0-c913-422d-95b7-a3940a1fc9e5", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622185528400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8706e1a6-28bc-4158-a00a-dcbbf581d28e", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622187560000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07974b6a-36a9-499d-b242-fd9cdf8970f3", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622190968900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "501f61ff-0fbd-4e70-83e2-af5d49648469", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622201566800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e92d793-7cd2-4618-84e3-332891724baa", "name": "Sdk init in 29 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622221254500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4196d29a-8485-4b9d-95cc-9ceb6b08264e", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622221397900}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 19}, "markType": "other"}}, {"head": {"id": "e90286c8-a6e7-464b-b5b8-6681251bfdd7", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622221409100}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 19}, "markType": "other"}}, {"head": {"id": "8a03ceff-2f00-4757-bdac-0a2a314885cf", "name": "Project task initialization takes 20 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622241770200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba76b895-0011-4be8-8951-05e2ce70958e", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622241992100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f342a4bd-7aac-487e-9a40-c19427a9967e", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622242120200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2302ab0-c795-42de-b95b-0396e995a6da", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622242210900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4246920-0f89-4011-b42b-988b16b6a592", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622179794300, "endTime": 29622242297800}, "additional": {"logType": "info", "children": [], "durationId": "7150b630-1757-494c-ab43-97fac4302aa2", "parent": "23e2d9eb-3a79-4810-8617-27522bca2957"}}, {"head": {"id": "23e2d9eb-3a79-4810-8617-27522bca2957", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622176714000, "endTime": 29622242311100}, "additional": {"logType": "info", "children": ["6852af73-9fba-4462-b7b5-2192aaf78a73", "c4246920-0f89-4011-b42b-988b16b6a592"], "durationId": "1f5df055-27f5-4a33-9db6-33a35ef37a3b", "parent": "fb2a336a-5ec1-4302-974c-9ba40b72d82a"}}, {"head": {"id": "a80a18ab-0a8a-41b4-9470-0ec47a3d6da8", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622242961400, "endTime": 29622242974400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8dd44547-97c2-4d8b-bf97-6dcdaba32acf", "logId": "019444fb-8c26-4703-bab5-8f4f7b03071a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "019444fb-8c26-4703-bab5-8f4f7b03071a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622242961400, "endTime": 29622242974400}, "additional": {"logType": "info", "children": [], "durationId": "a80a18ab-0a8a-41b4-9470-0ec47a3d6da8", "parent": "fb2a336a-5ec1-4302-974c-9ba40b72d82a"}}, {"head": {"id": "fb2a336a-5ec1-4302-974c-9ba40b72d82a", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622174475900, "endTime": 29622242985200}, "additional": {"logType": "info", "children": ["4661c5ec-ec89-40df-9182-a7fd5f25a10a", "23e2d9eb-3a79-4810-8617-27522bca2957", "019444fb-8c26-4703-bab5-8f4f7b03071a"], "durationId": "8dd44547-97c2-4d8b-bf97-6dcdaba32acf", "parent": "b6800dff-a515-4b19-85db-9c9d50f68d2d"}}, {"head": {"id": "73ada67f-fa18-499d-8984-ee01b3000045", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622243561300, "endTime": 29622262217700}, "additional": {"children": ["f1b3c674-cfe1-4f22-b169-a24c28a058e4", "0bc3960e-29c5-4274-b354-e497494ae858", "432bb9eb-002b-475b-b1cc-fa813e717fec"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "442e2508-e728-49e2-8295-96fee3db0027", "logId": "eb5e99b5-42d5-47a4-abfd-53c508e585b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1b3c674-cfe1-4f22-b169-a24c28a058e4", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622246331400, "endTime": 29622246346000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ada67f-fa18-499d-8984-ee01b3000045", "logId": "141381ec-6803-4c06-88b4-2a60027964c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "141381ec-6803-4c06-88b4-2a60027964c4", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622246331400, "endTime": 29622246346000}, "additional": {"logType": "info", "children": [], "durationId": "f1b3c674-cfe1-4f22-b169-a24c28a058e4", "parent": "eb5e99b5-42d5-47a4-abfd-53c508e585b2"}}, {"head": {"id": "0bc3960e-29c5-4274-b354-e497494ae858", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622248047400, "endTime": 29622260960400}, "additional": {"children": ["5ea88277-e368-4900-8872-3e7ddd2429b2", "549ec0d3-5908-4afc-a04f-726d4e79fe48"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ada67f-fa18-499d-8984-ee01b3000045", "logId": "338fe374-6140-4e2c-a74b-4d629499bd4c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ea88277-e368-4900-8872-3e7ddd2429b2", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622248048300, "endTime": 29622250944700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0bc3960e-29c5-4274-b354-e497494ae858", "logId": "1166090c-d362-4c72-8f90-8ed814b5b7f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "549ec0d3-5908-4afc-a04f-726d4e79fe48", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622250974200, "endTime": 29622260948400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0bc3960e-29c5-4274-b354-e497494ae858", "logId": "7a42463a-24ee-43cd-a536-b8e14fd9c83d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "650033aa-8364-4d24-90d5-8fde7aae0ba4", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622248051800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ade24f7-82ee-448c-9e15-a39d47364e2e", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622250795300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1166090c-d362-4c72-8f90-8ed814b5b7f8", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622248048300, "endTime": 29622250944700}, "additional": {"logType": "info", "children": [], "durationId": "5ea88277-e368-4900-8872-3e7ddd2429b2", "parent": "338fe374-6140-4e2c-a74b-4d629499bd4c"}}, {"head": {"id": "c6e823f0-e9a6-4b5b-bf22-05bdec93ed2f", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622251044400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff824dff-aeb4-4384-ad51-51861ef69a53", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622256961000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1725653b-be0e-40dd-8ebb-a0d536560d1a", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622257078800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d69ab803-3a11-4090-b1c0-cdcc2b51b3b0", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622257217300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0adc4cda-b508-4fc7-8d2a-67a442d9e1bc", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622257300300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9ae4b7d-af36-467e-84a3-97c4aff09111", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622257333800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1abc038d-88e9-4cd7-9867-0fc003e14dd6", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622258070900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbfa40b4-229a-409f-bc36-75a642d383d0", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622258126600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d91acdc5-5e3e-4800-bc94-a70cf97f0824", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622260661300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c8754b6-1955-4301-bcd8-ef42974e41f1", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622260770100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10e0679a-ee97-4539-ac27-d0fff32c4685", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622260809500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81756400-f77a-49ec-ac3b-e2ddc011b142", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622260833600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a42463a-24ee-43cd-a536-b8e14fd9c83d", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622250974200, "endTime": 29622260948400}, "additional": {"logType": "info", "children": [], "durationId": "549ec0d3-5908-4afc-a04f-726d4e79fe48", "parent": "338fe374-6140-4e2c-a74b-4d629499bd4c"}}, {"head": {"id": "338fe374-6140-4e2c-a74b-4d629499bd4c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622248047400, "endTime": 29622260960400}, "additional": {"logType": "info", "children": ["1166090c-d362-4c72-8f90-8ed814b5b7f8", "7a42463a-24ee-43cd-a536-b8e14fd9c83d"], "durationId": "0bc3960e-29c5-4274-b354-e497494ae858", "parent": "eb5e99b5-42d5-47a4-abfd-53c508e585b2"}}, {"head": {"id": "432bb9eb-002b-475b-b1cc-fa813e717fec", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622262193300, "endTime": 29622262205600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ada67f-fa18-499d-8984-ee01b3000045", "logId": "e27e290e-12e2-433f-a1db-76a5b9cc8824"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e27e290e-12e2-433f-a1db-76a5b9cc8824", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622262193300, "endTime": 29622262205600}, "additional": {"logType": "info", "children": [], "durationId": "432bb9eb-002b-475b-b1cc-fa813e717fec", "parent": "eb5e99b5-42d5-47a4-abfd-53c508e585b2"}}, {"head": {"id": "eb5e99b5-42d5-47a4-abfd-53c508e585b2", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622243561300, "endTime": 29622262217700}, "additional": {"logType": "info", "children": ["141381ec-6803-4c06-88b4-2a60027964c4", "338fe374-6140-4e2c-a74b-4d629499bd4c", "e27e290e-12e2-433f-a1db-76a5b9cc8824"], "durationId": "73ada67f-fa18-499d-8984-ee01b3000045", "parent": "e16e67d7-4b2c-4a11-bbd1-fc002e3af2d6"}}, {"head": {"id": "e16e67d7-4b2c-4a11-bbd1-fc002e3af2d6", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622242996500, "endTime": 29622262226100}, "additional": {"logType": "info", "children": ["eb5e99b5-42d5-47a4-abfd-53c508e585b2"], "durationId": "442e2508-e728-49e2-8295-96fee3db0027", "parent": "b6800dff-a515-4b19-85db-9c9d50f68d2d"}}, {"head": {"id": "96c59414-cd8d-4bfb-94c4-544011d71b80", "name": "watch files: [\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\hvigorfile.ts',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622277195800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d13b57a2-db71-461e-961a-a61c9c307ebc", "name": "hvigorfile, resolve hvigorfile dependencies in 16 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622277526100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b7e131b-e6bf-4eb1-b626-9b97ef1c99c3", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622262231600, "endTime": 29622277586300}, "additional": {"logType": "info", "children": [], "durationId": "45581eaf-61e8-4782-8f7e-cb3b171c8b36", "parent": "b6800dff-a515-4b19-85db-9c9d50f68d2d"}}, {"head": {"id": "ffe8e102-2b76-4a85-8c75-bb3b25e4a503", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622278432300, "endTime": 29622278600800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cf0d2e92-1e37-468d-a672-9a86542035cc", "logId": "785fe7f8-13aa-4e34-977b-1288f9cf5b7a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "728576b6-7c36-4999-8819-e70c068a9329", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622278452500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "785fe7f8-13aa-4e34-977b-1288f9cf5b7a", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622278432300, "endTime": 29622278600800}, "additional": {"logType": "info", "children": [], "durationId": "ffe8e102-2b76-4a85-8c75-bb3b25e4a503", "parent": "b6800dff-a515-4b19-85db-9c9d50f68d2d"}}, {"head": {"id": "b28baef2-d7e0-475f-bf60-53602c0b5d33", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622280069900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1b636dc-2a5e-47b7-bb2c-88bc31ab7fc3", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622287352000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "796801c8-dce1-4c32-8886-1f6b02a26253", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622278611200, "endTime": 29622288363600}, "additional": {"logType": "info", "children": [], "durationId": "5983a32e-0c7b-4e12-83e5-a7098e54dbe3", "parent": "b6800dff-a515-4b19-85db-9c9d50f68d2d"}}, {"head": {"id": "99500b9e-4b21-4c61-8992-8a72db5e0806", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622288400500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e080dc7b-678c-49a1-9fe3-9081ee9b5e44", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622292647100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f10a6aab-1e58-4b06-ae42-869493306fa1", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622292755300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d2121d9-acb7-4359-9831-b8dc996c877d", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622293016200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ddb68b5-dded-452a-9b72-94ebafe6643d", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622294728000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3260b8a7-995b-4269-baef-2fa9e310c257", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622294807900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8a57af8-941c-4c56-be71-6c5846b6e35d", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622288387700, "endTime": 29622297032800}, "additional": {"logType": "info", "children": [], "durationId": "e85c6cfc-587d-4aa0-8ef9-c87f602ad2a9", "parent": "b6800dff-a515-4b19-85db-9c9d50f68d2d"}}, {"head": {"id": "c47f27bd-79fb-4cbb-b3e1-768af7c949b3", "name": "Configuration phase cost:126 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622297083600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "761d0dc2-541f-410f-bc10-4a062313dbb5", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622297063600, "endTime": 29622297159100}, "additional": {"logType": "info", "children": [], "durationId": "6ae6f675-18a4-4915-b41c-84e4060a30aa", "parent": "b6800dff-a515-4b19-85db-9c9d50f68d2d"}}, {"head": {"id": "b6800dff-a515-4b19-85db-9c9d50f68d2d", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622166076400, "endTime": 29622297167600}, "additional": {"logType": "info", "children": ["991c58b3-6b15-41a0-ba66-5276c5ee021f", "39755035-f4d9-4a4a-9e6f-308c9e143e47", "fb2a336a-5ec1-4302-974c-9ba40b72d82a", "e16e67d7-4b2c-4a11-bbd1-fc002e3af2d6", "4b7e131b-e6bf-4eb1-b626-9b97ef1c99c3", "796801c8-dce1-4c32-8886-1f6b02a26253", "e8a57af8-941c-4c56-be71-6c5846b6e35d", "761d0dc2-541f-410f-bc10-4a062313dbb5", "785fe7f8-13aa-4e34-977b-1288f9cf5b7a"], "durationId": "cf0d2e92-1e37-468d-a672-9a86542035cc", "parent": "0623ee74-c937-4ef7-8acc-f53b0501cb2d"}}, {"head": {"id": "540ce846-c49d-432e-aed6-16bfa4c609b1", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622298582200, "endTime": 29622298596200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fc86f903-6faf-473a-9412-a4c8455e9d49", "logId": "126a8378-7fb4-4ac3-8edf-a0d68abfbc1e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "126a8378-7fb4-4ac3-8edf-a0d68abfbc1e", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622298582200, "endTime": 29622298596200}, "additional": {"logType": "info", "children": [], "durationId": "540ce846-c49d-432e-aed6-16bfa4c609b1", "parent": "0623ee74-c937-4ef7-8acc-f53b0501cb2d"}}, {"head": {"id": "d93124b3-d439-4a11-aff5-077b49dfe484", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622297183800, "endTime": 29622298606800}, "additional": {"logType": "info", "children": [], "durationId": "85e0dc89-5e4e-4ae9-a849-1014a64df986", "parent": "0623ee74-c937-4ef7-8acc-f53b0501cb2d"}}, {"head": {"id": "af0b9b9f-4cb2-41c0-a8fc-c20c5b58aeef", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622298610000, "endTime": 29622298611300}, "additional": {"logType": "info", "children": [], "durationId": "c06312f2-a2ad-468d-ac75-c2cc3746facc", "parent": "0623ee74-c937-4ef7-8acc-f53b0501cb2d"}}, {"head": {"id": "0623ee74-c937-4ef7-8acc-f53b0501cb2d", "name": "init", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622154418000, "endTime": 29622298613400}, "additional": {"logType": "info", "children": ["aaed5d20-31f8-45af-9c3e-d7022c765ba3", "b6800dff-a515-4b19-85db-9c9d50f68d2d", "d93124b3-d439-4a11-aff5-077b49dfe484", "af0b9b9f-4cb2-41c0-a8fc-c20c5b58aeef", "c7b22bbb-dc82-44fb-af62-55b5d4ce47c2", "c51b6a32-5d01-400b-82ef-12db35fbb843", "126a8378-7fb4-4ac3-8edf-a0d68abfbc1e"], "durationId": "fc86f903-6faf-473a-9412-a4c8455e9d49"}}, {"head": {"id": "b9ef3500-f9f0-4f29-8242-59168c7de488", "name": "Configuration task cost before running: 147 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622298700400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a794b47c-5039-4fe2-9719-3dd5a5b20252", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622303367400, "endTime": 29622310529200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "56ca8312-2a91-4cbe-90f5-7fb95aa16c2c", "logId": "f280cf67-f729-484b-a052-90d78f31a5b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56ca8312-2a91-4cbe-90f5-7fb95aa16c2c", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622300430200}, "additional": {"logType": "detail", "children": [], "durationId": "a794b47c-5039-4fe2-9719-3dd5a5b20252"}}, {"head": {"id": "9f4b32ad-1bf0-42dc-975a-21bc85bccfc0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622300889200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "155efe18-c86c-43cb-b8a4-d9034c4d1a30", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622301053200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfb359bf-a530-48d5-98f8-dd2d6f4e76b7", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622303377300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9691c4e-6f0a-4a6f-972a-20ac698a62b4", "name": "Incremental task entry:default@PreBuild pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622310341100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "694d4b9a-0f29-4aea-8d1b-bc14e3e5acbc", "name": "entry : default@PreBuild cost memory 0.31044769287109375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622310474400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f280cf67-f729-484b-a052-90d78f31a5b6", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622303367400, "endTime": 29622310529200}, "additional": {"logType": "info", "children": [], "durationId": "a794b47c-5039-4fe2-9719-3dd5a5b20252"}}, {"head": {"id": "3e03ff91-1145-43b9-b3ca-101ba7593d8b", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622315281600, "endTime": 29622317374200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "484e86c7-a045-40ff-af00-02617393ff0a", "logId": "301f222b-6974-4263-80cd-209ff68910a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "484e86c7-a045-40ff-af00-02617393ff0a", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622313995200}, "additional": {"logType": "detail", "children": [], "durationId": "3e03ff91-1145-43b9-b3ca-101ba7593d8b"}}, {"head": {"id": "7a57f4fc-7c9d-492a-80a2-001933cb8d9c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622314500100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2745c299-6fcc-4fb3-977d-a1ea16fdfe93", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622314595300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aca2b28a-9f74-4cb3-b32e-a1a7355167f7", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622315288900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b839ac3e-5b55-4725-9911-124c0c67cf91", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622317224900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2be8bf44-500a-42d3-9413-e4108bd368ba", "name": "entry : default@MergeProfile cost memory 0.11165618896484375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622317324600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "301f222b-6974-4263-80cd-209ff68910a2", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622315281600, "endTime": 29622317374200}, "additional": {"logType": "info", "children": [], "durationId": "3e03ff91-1145-43b9-b3ca-101ba7593d8b"}}, {"head": {"id": "b926f040-685f-4252-baa3-4f7fc2ed9ce1", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622320198400, "endTime": 29622322340400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4cd5e8a2-2625-49b8-8a97-09bde880ce2e", "logId": "2895c9d0-474c-46cf-ab10-da0d56bbb52b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4cd5e8a2-2625-49b8-8a97-09bde880ce2e", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622318908700}, "additional": {"logType": "detail", "children": [], "durationId": "b926f040-685f-4252-baa3-4f7fc2ed9ce1"}}, {"head": {"id": "331caf5f-d7e1-4c58-a9f5-2c076241958f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622319481000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45190ff0-d9ab-453f-8a60-049c32d8ebe6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622319559200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "566446f2-36e5-4f77-b257-61f55f70ec67", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622320206300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebc1c499-ec3d-4663-a928-853488d58232", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622321118800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d77af38b-f8b9-4a2b-9c6d-803ef02b8a64", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622322149600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6cf6f61-ed81-424a-9ddf-83834246f42d", "name": "entry : default@CreateBuildProfile cost memory 0.09694671630859375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622322289000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2895c9d0-474c-46cf-ab10-da0d56bbb52b", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622320198400, "endTime": 29622322340400}, "additional": {"logType": "info", "children": [], "durationId": "b926f040-685f-4252-baa3-4f7fc2ed9ce1"}}, {"head": {"id": "d09087ee-ab4f-478e-b8ac-ae2a0cec580c", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622324779400, "endTime": 29622325393700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "15a6fe44-1499-4b1c-9a62-6024824c0a66", "logId": "77b670e7-5419-48bf-878b-d56a88846717"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15a6fe44-1499-4b1c-9a62-6024824c0a66", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622323623800}, "additional": {"logType": "detail", "children": [], "durationId": "d09087ee-ab4f-478e-b8ac-ae2a0cec580c"}}, {"head": {"id": "bbaea9e9-27f9-4de1-aa04-b1c1f5bbb14c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622324038500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef0f57f3-0559-4567-abd8-492722102a15", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622324119700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d8545ef-ba8b-4a90-9b63-32820bb343b8", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622324788100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53d466bf-3179-4166-8617-746e5e9ea34d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622325012200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b91a3ba9-9775-47af-a08f-1d407d3a40cb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622325181200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2037104-ba34-4726-a9e1-46731261e462", "name": "entry : default@PreCheckSyscap cost memory 0.03699493408203125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622325295700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a046b7b6-a74a-4817-add3-71161f51fe69", "name": "runTaskFromQueue task cost before running: 174 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622325358000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77b670e7-5419-48bf-878b-d56a88846717", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622324779400, "endTime": 29622325393700, "totalTime": 561800}, "additional": {"logType": "info", "children": [], "durationId": "d09087ee-ab4f-478e-b8ac-ae2a0cec580c"}}, {"head": {"id": "18dc12be-df04-4f8b-bf1a-6f5566172809", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622333864000, "endTime": 29622334733800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ca26ae21-627d-402b-a3ca-282486c8f7a2", "logId": "6bd50def-4908-4941-abc0-d5e003f7b60a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca26ae21-627d-402b-a3ca-282486c8f7a2", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622327385800}, "additional": {"logType": "detail", "children": [], "durationId": "18dc12be-df04-4f8b-bf1a-6f5566172809"}}, {"head": {"id": "c57fd48d-864a-4f12-b389-e4892b1af6f3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622327902700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11aeefef-62ea-4701-b391-1ee8c3309310", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622328049500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21fd4ecd-c3bd-4a42-ad10-894bfe417701", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622333876300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17c3c15a-99e3-4a1f-b44e-72a04b5b9a8b", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622334053700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ceb607b2-59fe-4512-94c4-bff3a4829dd9", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622334596600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6658af87-9ee8-4fc3-824a-054efcf0684a", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06534576416015625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622334683500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bd50def-4908-4941-abc0-d5e003f7b60a", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622333864000, "endTime": 29622334733800}, "additional": {"logType": "info", "children": [], "durationId": "18dc12be-df04-4f8b-bf1a-6f5566172809"}}, {"head": {"id": "033fa331-e79f-496a-a3b3-096994e412c2", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622338412800, "endTime": 29622339392400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "7d5c4b9d-dbbb-4d54-bb6b-f6d3ccd11fd2", "logId": "984c7ba6-2a84-4151-8254-3badfcc02346"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d5c4b9d-dbbb-4d54-bb6b-f6d3ccd11fd2", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622336555300}, "additional": {"logType": "detail", "children": [], "durationId": "033fa331-e79f-496a-a3b3-096994e412c2"}}, {"head": {"id": "6e668d06-cab1-40df-baf2-f71d1f809ea7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622337220700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b38961b6-bc74-45a0-979f-945f68777802", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622337354400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "277ad548-ea0c-4433-913c-da77dc9c651f", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622338422800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "849e2b2d-a537-4a4c-966f-d8075998cbc4", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622339264800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7e32e85-4373-4a82-8848-2c15e7092107", "name": "entry : default@ProcessProfile cost memory 0.05631256103515625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622339348100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "984c7ba6-2a84-4151-8254-3badfcc02346", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622338412800, "endTime": 29622339392400}, "additional": {"logType": "info", "children": [], "durationId": "033fa331-e79f-496a-a3b3-096994e412c2"}}, {"head": {"id": "ec7c53c4-afb9-437b-a3d8-c0e64448dbb0", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622342760700, "endTime": 29622347398800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8b2f227b-a71d-451b-91bd-75f09ed37eba", "logId": "a971ae4f-66fc-4738-b8f3-adda0b9861b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b2f227b-a71d-451b-91bd-75f09ed37eba", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622340789000}, "additional": {"logType": "detail", "children": [], "durationId": "ec7c53c4-afb9-437b-a3d8-c0e64448dbb0"}}, {"head": {"id": "fb9daab6-ce80-462c-9111-7b8fee219732", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622341233600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "530ee471-0150-47d7-9607-21ae5529b3c9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622341310600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6cbad93-c0a1-4594-b7c6-cda8354bd734", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622342767800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7022bd8-c587-4d63-ad21-cbf70c549468", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622347255700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbf5c7aa-be3c-464a-85ed-cc716fd4f009", "name": "entry : default@ProcessRouterMap cost memory 0.19573974609375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622347353300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a971ae4f-66fc-4738-b8f3-adda0b9861b8", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622342760700, "endTime": 29622347398800}, "additional": {"logType": "info", "children": [], "durationId": "ec7c53c4-afb9-437b-a3d8-c0e64448dbb0"}}, {"head": {"id": "3a23a187-af3f-4167-8eb2-55204e17b8c7", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622352739800, "endTime": 29622355015000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "fb1e8083-5f4a-479e-9c77-63de9c749869", "logId": "de389cb1-5b49-4e86-86d6-9500abbab3f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb1e8083-5f4a-479e-9c77-63de9c749869", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622349882800}, "additional": {"logType": "detail", "children": [], "durationId": "3a23a187-af3f-4167-8eb2-55204e17b8c7"}}, {"head": {"id": "cd498000-c313-4edc-87a4-67b3a352e993", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622350359200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7c1d257-a2a3-4c44-b05a-3d4d183879be", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622350433200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfcf6b5c-e433-4293-8043-9b97add6c447", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622351166100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a99ac581-e95d-4e18-b5f6-c20eb98df1b5", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622353604600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fd53171-5c88-4ddc-947b-99853a388f37", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622353720700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ae0b8c6-11ce-4ce6-b2ea-d3a2756c0e2e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622353806600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c1cf0e2-5aab-4f05-8b77-b72186d4d9f0", "name": "entry : default@PreviewProcessResource cost memory 0.06989288330078125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622353909500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9143e68b-0eb8-4354-bda6-825b9a5005ca", "name": "runTaskFromQueue task cost before running: 204 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622354937100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de389cb1-5b49-4e86-86d6-9500abbab3f8", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622352739800, "endTime": 29622355015000, "totalTime": 1213600}, "additional": {"logType": "info", "children": [], "durationId": "3a23a187-af3f-4167-8eb2-55204e17b8c7"}}, {"head": {"id": "6d99f458-e89b-493f-b00b-8843316d1277", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622360821600, "endTime": 29622377524000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "199db073-e2fc-4905-b662-f8eb0f409acd", "logId": "268d8763-c369-40af-9685-b97db8cb34b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "199db073-e2fc-4905-b662-f8eb0f409acd", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622357136200}, "additional": {"logType": "detail", "children": [], "durationId": "6d99f458-e89b-493f-b00b-8843316d1277"}}, {"head": {"id": "a52d70be-16a1-43e7-852d-89992256edff", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622357558500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3ea28a0-b649-46ad-a884-86588e62b44a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622357630400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "595f2a46-25bf-456c-9127-0c151f79e56c", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622360829000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c9c45c0-0296-42e3-8815-3530b37d1f3a", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622377343500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b94f99e-b913-47f5-b998-8d386a590d7e", "name": "entry : default@GenerateLoaderJson cost memory 0.7507781982421875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622377471900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "268d8763-c369-40af-9685-b97db8cb34b6", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622360821600, "endTime": 29622377524000}, "additional": {"logType": "info", "children": [], "durationId": "6d99f458-e89b-493f-b00b-8843316d1277"}}, {"head": {"id": "d2f797b6-0c4c-4cfa-8b35-cc277d4e80ca", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622386399900, "endTime": 29622596766000}, "additional": {"children": ["e38b0357-62eb-48ef-b89a-7a4cb63f5b4c", "60630295-3c3a-4830-9db0-e01c8817d0ef", "b9b58d9f-2059-4d01-bd2a-ff50f3dc00d5", "9ac65c99-092d-4d0c-8683-55c7f7cc8ce4"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "5563b798-568c-4f78-bf26-156f3fcb18e1", "logId": "b2188382-a6c9-4777-a27a-776da62e5740"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5563b798-568c-4f78-bf26-156f3fcb18e1", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622383392400}, "additional": {"logType": "detail", "children": [], "durationId": "d2f797b6-0c4c-4cfa-8b35-cc277d4e80ca"}}, {"head": {"id": "125c21f8-d346-48b5-8f8c-98a73bad9e46", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622383792100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9448abdf-26f6-42b5-a166-250d0241c4fd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622383870400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8127a10-7323-4401-b813-7c6c4dd99584", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622384645300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77bb500f-d455-494e-9628-90e703a9f013", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622386418400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97039d66-50bf-42b6-8c91-c68f95aed771", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622407902700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a222e641-bcdb-4fb6-8691-438a34f6466d", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 21 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622408131400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e38b0357-62eb-48ef-b89a-7a4cb63f5b4c", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622409055800, "endTime": 29622430106300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d2f797b6-0c4c-4cfa-8b35-cc277d4e80ca", "logId": "62108ed5-eccf-413d-8d29-a7f57d50b545"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62108ed5-eccf-413d-8d29-a7f57d50b545", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622409055800, "endTime": 29622430106300}, "additional": {"logType": "info", "children": [], "durationId": "e38b0357-62eb-48ef-b89a-7a4cb63f5b4c", "parent": "b2188382-a6c9-4777-a27a-776da62e5740"}}, {"head": {"id": "3b3633ac-f3f3-4efa-b4c6-b2738d50f773", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622430212700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60630295-3c3a-4830-9db0-e01c8817d0ef", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622431046700, "endTime": 29622468885500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d2f797b6-0c4c-4cfa-8b35-cc277d4e80ca", "logId": "9d8c3e4a-d8b4-456f-8a62-413f2c3ebce7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d6bf73b-6766-46d6-9cb9-0a359320b22b", "name": "current process  memoryUsage: {\n  rss: 127897600,\n  heapTotal: 126181376,\n  heapUsed: 114369232,\n  external: 3141762,\n  arrayBuffers: 135663\n} os memoryUsage :13.094108581542969", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622431983900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bbadeb7-1544-4f0e-b8c9-4aa18e8e819b", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622466512600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d8c3e4a-d8b4-456f-8a62-413f2c3ebce7", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622431046700, "endTime": 29622468885500}, "additional": {"logType": "info", "children": [], "durationId": "60630295-3c3a-4830-9db0-e01c8817d0ef", "parent": "b2188382-a6c9-4777-a27a-776da62e5740"}}, {"head": {"id": "911f7559-3994-4bc7-8540-5ff5620640d5", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622469032500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9b58d9f-2059-4d01-bd2a-ff50f3dc00d5", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622470044100, "endTime": 29622514672500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d2f797b6-0c4c-4cfa-8b35-cc277d4e80ca", "logId": "0257b267-b193-48ca-8d28-9726957faf10"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1690f8a-9bd0-4c19-8b57-84aec407f2f8", "name": "current process  memoryUsage: {\n  rss: 127901696,\n  heapTotal: 126181376,\n  heapUsed: 114626912,\n  external: 3141888,\n  arrayBuffers: 135804\n} os memoryUsage :13.097492218017578", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622470956300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ec2582a-9dac-41ab-b57a-ce5c136b5357", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622511827600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0257b267-b193-48ca-8d28-9726957faf10", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622470044100, "endTime": 29622514672500}, "additional": {"logType": "info", "children": [], "durationId": "b9b58d9f-2059-4d01-bd2a-ff50f3dc00d5", "parent": "b2188382-a6c9-4777-a27a-776da62e5740"}}, {"head": {"id": "df952aa7-2031-4e12-901f-dbed9daa260e", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622515100100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ac65c99-092d-4d0c-8683-55c7f7cc8ce4", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622516439900, "endTime": 29622595495800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d2f797b6-0c4c-4cfa-8b35-cc277d4e80ca", "logId": "d11ed47f-e339-49b7-ae9e-e8b77a2a5507"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f1505cd-7980-4dd9-843f-3b94080d5c88", "name": "current process  memoryUsage: {\n  rss: 127901696,\n  heapTotal: 126181376,\n  heapUsed: 114922464,\n  external: 3142014,\n  arrayBuffers: 136814\n} os memoryUsage :13.025707244873047", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622517873100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c8f6427-4a06-448f-968f-e476ef7b15f8", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622591956200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d11ed47f-e339-49b7-ae9e-e8b77a2a5507", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622516439900, "endTime": 29622595495800}, "additional": {"logType": "info", "children": [], "durationId": "9ac65c99-092d-4d0c-8683-55c7f7cc8ce4", "parent": "b2188382-a6c9-4777-a27a-776da62e5740"}}, {"head": {"id": "2f86e48e-060a-490f-8702-990d6e7cd673", "name": "entry : default@PreviewCompileResource cost memory -0.03737640380859375", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622596530200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd59f207-046e-4e4f-b007-0d8a19822823", "name": "runTaskFromQueue task cost before running: 445 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622596709400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2188382-a6c9-4777-a27a-776da62e5740", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622386399900, "endTime": 29622596766000, "totalTime": 210256700}, "additional": {"logType": "info", "children": ["62108ed5-eccf-413d-8d29-a7f57d50b545", "9d8c3e4a-d8b4-456f-8a62-413f2c3ebce7", "0257b267-b193-48ca-8d28-9726957faf10", "d11ed47f-e339-49b7-ae9e-e8b77a2a5507"], "durationId": "d2f797b6-0c4c-4cfa-8b35-cc277d4e80ca"}}, {"head": {"id": "42491b26-7b50-4761-a274-ff5e041d1c84", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622599525200, "endTime": 29622599724500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "9b8cc07d-dd66-4b9b-a874-cd622ee0bd0a", "logId": "f0f73bdb-b941-4330-a553-995edaac6c59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b8cc07d-dd66-4b9b-a874-cd622ee0bd0a", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622598929000}, "additional": {"logType": "detail", "children": [], "durationId": "42491b26-7b50-4761-a274-ff5e041d1c84"}}, {"head": {"id": "1a68d9be-f2ed-4bda-999a-1e4e2a30ec0b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622599372500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "138224e1-dc7b-42e1-8e60-674fa1b05b5e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622599458600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acf28209-2f80-418b-9514-76ed9a7e9278", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622599530200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2e6dfab-d370-49a7-b999-b209d63fd509", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622599583700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "139fdee8-7069-4522-8097-1dd268748520", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622599605900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "592fae8a-dcb9-4a27-8c78-250e66291bb7", "name": "entry : default@PreviewHookCompileResource cost memory 0.03794097900390625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622599653700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e252128b-99da-4875-a421-607170567521", "name": "runTaskFromQueue task cost before running: 448 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622599698700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0f73bdb-b941-4330-a553-995edaac6c59", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622599525200, "endTime": 29622599724500, "totalTime": 160800}, "additional": {"logType": "info", "children": [], "durationId": "42491b26-7b50-4761-a274-ff5e041d1c84"}}, {"head": {"id": "4c536aad-63f9-4296-a490-1d3e580206c2", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622602251300, "endTime": 29622608268800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "53a7de0a-2673-4dd8-8f65-8afac5582063", "logId": "b1050915-6c92-4035-bc2e-faa49e5ecbe9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53a7de0a-2673-4dd8-8f65-8afac5582063", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622601086900}, "additional": {"logType": "detail", "children": [], "durationId": "4c536aad-63f9-4296-a490-1d3e580206c2"}}, {"head": {"id": "0117d5b4-1880-41d5-b739-5f2e1c34cbb0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622601600700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1ad916a-e71e-477d-91a3-ba8eccaf406f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622601692900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc9669c8-c747-4ff4-abd1-cf97803af3b3", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622602259800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbc7f224-9de6-4a86-87cc-b398bd97fd56", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622603350700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a9f165a-4dfa-4245-b797-cd04a59cb578", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622603444300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b983f9a-1026-4477-979d-0c91bca57c22", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622603503800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98923a2e-e260-40de-b5f5-fc17b615c6f9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622603530200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af5980d7-f979-4eb1-8f4e-36af32e62914", "name": "entry : default@CopyPreviewProfile cost memory 0.2171173095703125", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622607537200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7b911a0-f7ad-4f63-b978-befadad310c5", "name": "runTaskFromQueue task cost before running: 457 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622608186500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1050915-6c92-4035-bc2e-faa49e5ecbe9", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622602251300, "endTime": 29622608268800, "totalTime": 5899300}, "additional": {"logType": "info", "children": [], "durationId": "4c536aad-63f9-4296-a490-1d3e580206c2"}}, {"head": {"id": "6e8e2d75-1297-4051-8895-f8c50f281815", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622611162600, "endTime": 29622611546500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "c1fb8b54-041d-4570-a1f9-e87d3debac8e", "logId": "e4f370e7-f5db-4404-b8f7-334fcb34b986"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1fb8b54-041d-4570-a1f9-e87d3debac8e", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622610038400}, "additional": {"logType": "detail", "children": [], "durationId": "6e8e2d75-1297-4051-8895-f8c50f281815"}}, {"head": {"id": "04d3de12-ac10-476d-a3be-66f86981bbc4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622610497600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef2ed9a7-d62a-4316-bba0-78033a7cffb9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622610591600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85868c77-eef3-4731-ba0f-cffc07d36d06", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622611170400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a0f93a0-1d60-4f3e-bac2-f49471aa472e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622611265200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea4fadb4-c8de-439d-afc0-d4227841805b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622611295700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8ee8a54-04b2-49d6-9260-edbbcee5dc22", "name": "entry : default@ReplacePreviewerPage cost memory 0.03789520263671875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622611445900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9411464a-657e-4de4-8fb0-8727e26fb121", "name": "runTaskFromQueue task cost before running: 460 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622611511700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4f370e7-f5db-4404-b8f7-334fcb34b986", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622611162600, "endTime": 29622611546500, "totalTime": 334100}, "additional": {"logType": "info", "children": [], "durationId": "6e8e2d75-1297-4051-8895-f8c50f281815"}}, {"head": {"id": "0c5f8c3f-9722-48e1-813a-13b6b8abd9e5", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622612852600, "endTime": 29622613062500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "00b96336-1a99-431c-aaf4-4c4b931145fe", "logId": "7de79d31-be0b-4c94-bbc3-b95104df162f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "00b96336-1a99-431c-aaf4-4c4b931145fe", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622612809500}, "additional": {"logType": "detail", "children": [], "durationId": "0c5f8c3f-9722-48e1-813a-13b6b8abd9e5"}}, {"head": {"id": "19532b34-dee0-4f3c-8ad8-dda8a42abe7e", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622612858400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cf43142-bcb1-48f1-a97a-dff86879034b", "name": "entry : buildPreviewerResource cost memory 0.01165008544921875", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622612969200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d369ac9b-baa5-4f46-8f15-4ac9b8b5ca39", "name": "runTaskFromQueue task cost before running: 462 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622613025300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7de79d31-be0b-4c94-bbc3-b95104df162f", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622612852600, "endTime": 29622613062500, "totalTime": 157000}, "additional": {"logType": "info", "children": [], "durationId": "0c5f8c3f-9722-48e1-813a-13b6b8abd9e5"}}, {"head": {"id": "a88f8682-419b-469c-80a4-f1f79973e93c", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622616057700, "endTime": 29622619717500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "9a286c3e-21d0-44ef-8f7f-1333fe013d8c", "logId": "280ad136-7fc5-4a10-8e66-94b2c2635eed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a286c3e-21d0-44ef-8f7f-1333fe013d8c", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622614483300}, "additional": {"logType": "detail", "children": [], "durationId": "a88f8682-419b-469c-80a4-f1f79973e93c"}}, {"head": {"id": "92e3f845-c5db-487c-b27c-d3768e66fc1f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622615353100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7d531ac-4dec-4826-bb5c-2c68ac2677d3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622615447600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d031721-d9d2-47f0-a540-d2c1b3dd677e", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622616067300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f63d80c7-8fac-4d7f-a0be-6cd65031463d", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622617714800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c73ff73-7c72-4d80-815b-67e34716c293", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622617825200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59274ba1-11fa-4291-b0f2-842e938b8817", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622617886300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9879d930-e830-471a-bbef-c7660baf9c1b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622617913300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8014628-e514-4876-b40a-a6a1a7dd2a94", "name": "entry : default@PreviewUpdateAssets cost memory 0.14023590087890625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622619548600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51e7cc80-203c-48bf-96f2-e29792b085a7", "name": "runTaskFromQueue task cost before running: 468 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622619669100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "280ad136-7fc5-4a10-8e66-94b2c2635eed", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622616057700, "endTime": 29622619717500, "totalTime": 3589500}, "additional": {"logType": "info", "children": [], "durationId": "a88f8682-419b-469c-80a4-f1f79973e93c"}}, {"head": {"id": "6dced54b-7375-4a31-8353-543943fcd5f5", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622626550700, "endTime": 29631251952600}, "additional": {"children": ["d494c6b6-6da1-4ee7-9884-be0f5928eded"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "125d38ac-5216-4db8-ac65-6c3d9619f122", "logId": "ed664ebc-9755-4a59-a899-0d8304b7b2da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "125d38ac-5216-4db8-ac65-6c3d9619f122", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622621815600}, "additional": {"logType": "detail", "children": [], "durationId": "6dced54b-7375-4a31-8353-543943fcd5f5"}}, {"head": {"id": "ba908647-35e6-4421-a2dd-f121d4509919", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622622251300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e581ae6-c967-4952-b996-e52536fa640f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622622343500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b84b361f-75af-4f5a-95ab-eae010341ee4", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622626563700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7700ffef-611d-4a7f-b38b-419b5eadcc8a", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622636080600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0553247d-2e5f-4472-854a-f986e409e331", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622636232500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d494c6b6-6da1-4ee7-9884-be0f5928eded", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker12", "startTime": 29622649894000, "endTime": 29631249198700}, "additional": {"children": ["b9ca7244-b8ce-4bbd-86ff-b738e406723f", "5c434068-fb59-4086-b8fe-1cb590adfd7b", "3574fb2f-fb36-490e-a3ae-956d20d90c9f", "b7268f82-2922-447d-bbd4-9d642ebd00d6", "03326b43-1a0b-42d0-a6d7-708511a06b6a", "ce80d1a8-edf5-4b5c-9401-7304fc4ed692"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "6dced54b-7375-4a31-8353-543943fcd5f5", "logId": "f7abe41a-5216-4c60-84c9-1e3ca779f038"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a89074b-3bc1-498d-bd7c-69f12f000053", "name": "entry : default@PreviewArkTS cost memory -0.5673751831054688", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622652130500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "000d712b-3008-43d0-9738-2d6b713c46a4", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29626252148300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9ca7244-b8ce-4bbd-86ff-b738e406723f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker12", "startTime": 29626252927900, "endTime": 29626252942800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d494c6b6-6da1-4ee7-9884-be0f5928eded", "logId": "a5e04bf5-d50a-4536-87c8-dad1e65d78fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5e04bf5-d50a-4536-87c8-dad1e65d78fd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29626252927900, "endTime": 29626252942800}, "additional": {"logType": "info", "children": [], "durationId": "b9ca7244-b8ce-4bbd-86ff-b738e406723f", "parent": "f7abe41a-5216-4c60-84c9-1e3ca779f038"}}, {"head": {"id": "0c10e835-f30c-408c-8ac8-b4187c96e455", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631247387400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c434068-fb59-4086-b8fe-1cb590adfd7b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker12", "startTime": 29631248560200, "endTime": 29631248576100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d494c6b6-6da1-4ee7-9884-be0f5928eded", "logId": "16158ea6-ffe0-4d52-8f34-913f9e892dd6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16158ea6-ffe0-4d52-8f34-913f9e892dd6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631248560200, "endTime": 29631248576100}, "additional": {"logType": "info", "children": [], "durationId": "5c434068-fb59-4086-b8fe-1cb590adfd7b", "parent": "f7abe41a-5216-4c60-84c9-1e3ca779f038"}}, {"head": {"id": "f7abe41a-5216-4c60-84c9-1e3ca779f038", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Worker12", "startTime": 29622649894000, "endTime": 29631249198700}, "additional": {"logType": "info", "children": ["a5e04bf5-d50a-4536-87c8-dad1e65d78fd", "16158ea6-ffe0-4d52-8f34-913f9e892dd6", "12360ab4-985c-4a89-bf81-c989eaf7284f", "54448adb-9958-4a03-b777-d7377de0f55b", "9bba807f-2ce1-4e8b-a977-d8f7ecfee509", "414be063-2175-47c9-bc2f-74f018c7db73"], "durationId": "d494c6b6-6da1-4ee7-9884-be0f5928eded", "parent": "ed664ebc-9755-4a59-a899-0d8304b7b2da"}}, {"head": {"id": "3574fb2f-fb36-490e-a3ae-956d20d90c9f", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker12", "startTime": 29625048564000, "endTime": 29626200594500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d494c6b6-6da1-4ee7-9884-be0f5928eded", "logId": "12360ab4-985c-4a89-bf81-c989eaf7284f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12360ab4-985c-4a89-bf81-c989eaf7284f", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29625048564000, "endTime": 29626200594500}, "additional": {"logType": "info", "children": [], "durationId": "3574fb2f-fb36-490e-a3ae-956d20d90c9f", "parent": "f7abe41a-5216-4c60-84c9-1e3ca779f038"}}, {"head": {"id": "b7268f82-2922-447d-bbd4-9d642ebd00d6", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker12", "startTime": 29626200744200, "endTime": 29626227848100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d494c6b6-6da1-4ee7-9884-be0f5928eded", "logId": "54448adb-9958-4a03-b777-d7377de0f55b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54448adb-9958-4a03-b777-d7377de0f55b", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29626200744200, "endTime": 29626227848100}, "additional": {"logType": "info", "children": [], "durationId": "b7268f82-2922-447d-bbd4-9d642ebd00d6", "parent": "f7abe41a-5216-4c60-84c9-1e3ca779f038"}}, {"head": {"id": "03326b43-1a0b-42d0-a6d7-708511a06b6a", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker12", "startTime": 29626227926600, "endTime": 29626228022500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d494c6b6-6da1-4ee7-9884-be0f5928eded", "logId": "9bba807f-2ce1-4e8b-a977-d8f7ecfee509"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9bba807f-2ce1-4e8b-a977-d8f7ecfee509", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29626227926600, "endTime": 29626228022500}, "additional": {"logType": "info", "children": [], "durationId": "03326b43-1a0b-42d0-a6d7-708511a06b6a", "parent": "f7abe41a-5216-4c60-84c9-1e3ca779f038"}}, {"head": {"id": "ce80d1a8-edf5-4b5c-9401-7304fc4ed692", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Worker12", "startTime": 29626228060700, "endTime": 29631247399800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d494c6b6-6da1-4ee7-9884-be0f5928eded", "logId": "414be063-2175-47c9-bc2f-74f018c7db73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "414be063-2175-47c9-bc2f-74f018c7db73", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29626228060700, "endTime": 29631247399800}, "additional": {"logType": "info", "children": [], "durationId": "ce80d1a8-edf5-4b5c-9401-7304fc4ed692", "parent": "f7abe41a-5216-4c60-84c9-1e3ca779f038"}}, {"head": {"id": "ed664ebc-9755-4a59-a899-0d8304b7b2da", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622626550700, "endTime": 29631251952600, "totalTime": 8625394500}, "additional": {"logType": "info", "children": ["f7abe41a-5216-4c60-84c9-1e3ca779f038"], "durationId": "6dced54b-7375-4a31-8353-543943fcd5f5"}}, {"head": {"id": "f19d63d0-acfc-4dd1-a4cb-adb4c14610f9", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631257115300, "endTime": 29631257962200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "f4569de8-1fd0-48b9-8483-77d734eeb994", "logId": "0de92673-f9c3-443f-af8c-caa2dce2eac6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4569de8-1fd0-48b9-8483-77d734eeb994", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631257081000}, "additional": {"logType": "detail", "children": [], "durationId": "f19d63d0-acfc-4dd1-a4cb-adb4c14610f9"}}, {"head": {"id": "48f2c410-af83-4a8c-a50c-49bffced5f5a", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631257125100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a6bd61f-7316-47c8-a723-4b3193c3d9a1", "name": "entry : PreviewBuild cost memory 0.02496337890625", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631257340100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54fbca44-42d8-409e-a493-4cbce1e47b31", "name": "runTaskFromQueue task cost before running: 9 s 107 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631257748300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0de92673-f9c3-443f-af8c-caa2dce2eac6", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631257115300, "endTime": 29631257962200, "totalTime": 606000}, "additional": {"logType": "info", "children": [], "durationId": "f19d63d0-acfc-4dd1-a4cb-adb4c14610f9"}}, {"head": {"id": "76fc8ee5-eb00-478f-ad62-64d40445f8f1", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631267008700, "endTime": 29631267029700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8ac8f5b7-ded7-4b16-82d2-d4e19ec53ed4", "logId": "d13cc010-7b91-42ac-9a4b-3429f145ff0f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d13cc010-7b91-42ac-9a4b-3429f145ff0f", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631267008700, "endTime": 29631267029700}, "additional": {"logType": "info", "children": [], "durationId": "76fc8ee5-eb00-478f-ad62-64d40445f8f1"}}, {"head": {"id": "c9bbc464-ab39-4ae7-9d58-60dcbe3238b8", "name": "BUILD SUCCESSFUL in 9 s 116 ms ", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631267079700}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "1ef6ccf5-b638-450a-a90a-9004af75c458", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29622151689000, "endTime": 29631267260700}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 17, "minute": 19}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "d2621f33-047b-4d9a-97ef-5e76497cf5ff", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631267278800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ae705fb-2168-42d4-9f54-d708afa449f5", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631267312900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27818cd2-78a1-424c-9da1-0bf61723d867", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631267336700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1936b440-4b42-4451-b356-2c4247647f1a", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631267356000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97a227da-114a-48be-af2d-374cb7c490cf", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631267375700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50db3764-a16e-4f48-a706-5dc9e772be73", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631267400000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e113ef4c-5ebf-4064-bb4f-ce31fc83a063", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631267419400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca3fd26a-f3b2-4b3a-8131-9e41cbadc414", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631268598200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fb18c80-f2e9-4a41-a4c6-56b3e34b08e2", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631273992400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "daa5cdc1-05a4-4ee6-ad61-163a869df1ef", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631274288100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec7cdaa8-31a2-4cb3-9de3-4c18540135dc", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631281071700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43d14842-a709-4685-b513-6c99bc8cca20", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:15 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631281659200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07c00b17-4eeb-4cfd-8a13-d9c086ab8bb0", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631281840300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6742c73f-54dc-4dbc-8489-02bd599483c7", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631282404600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe7c58d3-b39a-4f06-9766-c90661e7a1f6", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631283049800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "461ba2cf-63dd-4137-9a9b-1b6a4c009f7e", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631283440400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1b002ee-ce56-4fce-8a47-c5c9275dfd13", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631283696300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "265f7df6-b732-4783-8040-98bf61cb3945", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631283947400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2195d4e-5f11-42d2-bf0c-fd77f6d59092", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631285998400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f44192e1-316d-4b47-919f-ad0dfb8c6aac", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631286819300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "616a0a3c-6baa-4036-b9ff-cab52fd1d3ab", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631286888400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26d0dfce-60b4-4967-934f-8f792867ba6d", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631287090500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d981f6a2-3533-4882-bd38-67068a49e077", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631287656500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e124e6bf-84ee-4966-bef5-155a2fce0253", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631295334300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e907da4e-374d-428d-b989-ed8e3fe980cb", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631295599400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "413c3d03-48e1-4ebf-913f-3636950a9edf", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631295823300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "546647b1-ef18-435f-b8af-e7b72306c999", "name": "Update task entry:default@PreviewArkTS output file:D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631296098600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f70ff1f8-8151-4ad0-a69d-a3d0e3001053", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:13 ms .", "description": "", "type": "log"}, "body": {"pid": 2352, "tid": "Main Thread", "startTime": 29631296308900}, "additional": {"logType": "debug", "children": []}}], "workLog": []}